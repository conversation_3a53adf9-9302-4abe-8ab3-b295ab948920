# 🔌 دليل التكاملات والخطط - MarketMind

## 📋 نظرة عامة

يوفر MarketMind نظام تكاملات متقدم يسمح للشركات بربط منصتهم مع الخدمات الخارجية المختلفة. النظام يدعم خطط اشتراك متعددة مع حدود مختلفة لكل خطة.

## 🎯 الخطط المتاحة

### 📦 خطة المبتدئ ($19/شهر)
- **الحملات**: 5 حملات نشطة
- **التكاملات**: 3 تكاملات فقط
- **جهات الاتصال**: 1,000 جهة اتصال
- **التحليلات**: أساسية
- **الدعم**: بريد إلكتروني
- **أدوات AI**: محدودة

**التكاملات المتاحة:**
- ✅ Facebook
- ✅ Instagram  
- ✅ Mailchimp
- ❌ Google Analytics (مميز)
- ❌ Shopify (مميز)
- ❌ HubSpot (مميز)

### 🚀 خطة المحترف ($49/شهر)
- **الحملات**: غير محدودة
- **التكاملات**: غير محدودة
- **جهات الاتصال**: 10,000 جهة اتصال
- **التحليلات**: متقدمة
- **الدعم**: أولوية
- **أدوات AI**: متقدمة

**التكاملات المتاحة:**
- ✅ جميع التكاملات الأساسية
- ✅ Google Analytics
- ✅ Shopify
- ✅ HubSpot
- ✅ Twitter
- ✅ LinkedIn

### 🏢 خطة المؤسسة ($99/شهر)
- **الحملات**: غير محدودة
- **التكاملات**: غير محدودة + مخصصة
- **جهات الاتصال**: غير محدودة
- **التحليلات**: مخصصة
- **الدعم**: مدير حساب مخصص
- **أدوات AI**: متقدمة + مخصصة

**التكاملات المتاحة:**
- ✅ جميع التكاملات
- ✅ تكاملات مخصصة
- ✅ API مخصص
- ✅ Zapier
- ✅ دعم مخصص للتكاملات

## 🔗 كيفية إضافة التكاملات

### 1️⃣ التكاملات الأساسية (OAuth)

#### Facebook/Instagram:
```javascript
// خطوات الربط:
1. انتقل إلى Facebook Developers
2. أنشئ تطبيق جديد
3. احصل على App ID و App Secret
4. أدخل البيانات في MarketMind
5. اكمل عملية OAuth
```

#### Twitter/LinkedIn:
```javascript
// خطوات الربط:
1. انتقل إلى Developer Portal
2. أنشئ تطبيق جديد
3. احصل على API Keys
4. أدخل البيانات في MarketMind
5. اكمل عملية OAuth
```

### 2️⃣ التكاملات المتقدمة (API Keys)

#### Google Analytics:
```javascript
// خطوات الربط:
1. انتقل إلى Google Cloud Console
2. فعّل Google Analytics API
3. أنشئ Service Account
4. حمّل ملف JSON
5. أضف Service Account لحساب Analytics
6. أدخل Property ID وملف JSON في MarketMind
```

#### Shopify:
```javascript
// خطوات الربط:
1. انتقل إلى إعدادات Shopify
2. اذهب إلى Apps and sales channels
3. أنشئ Private App
4. احصل على API credentials
5. أدخل Store URL وAPI Keys في MarketMind
```

#### HubSpot:
```javascript
// خطوات الربط:
1. انتقل إلى HubSpot Developer Account
2. أنشئ تطبيق جديد
3. احصل على API Key
4. أدخل API Key في MarketMind
5. اكمل عملية OAuth
```

## 🛡️ نظام الحدود والتحذيرات

### تحقق من الحدود:
```javascript
function checkIntegrationLimits(integrationId) {
    const userPlan = getCurrentUserPlan();
    const connectedCount = connectedIntegrations.length;
    
    // تحقق من حدود الخطة
    if (userPlan === 'starter' && connectedCount >= 3) {
        showUpgradeModal('وصلت للحد الأقصى من التكاملات');
        return false;
    }
    
    // تحقق من التكاملات المميزة
    if (integration.status === 'premium' && userPlan === 'starter') {
        showUpgradeModal('هذا التكامل متاح في الخطة المحترف فقط');
        return false;
    }
    
    return true;
}
```

### التحذيرات التلقائية:
- **80% من الحد**: تحذير أصفر
- **90% من الحد**: تحذير برتقالي  
- **100% من الحد**: منع الإضافة + دعوة للترقية

## 🔧 إدارة التكاملات

### إعدادات التكامل:
```javascript
// اختبار الاتصال
function testIntegrationConnection(integrationId) {
    // اختبار الاتصال مع الخدمة الخارجية
    // عرض النتيجة للمستخدم
}

// عرض السجلات
function viewIntegrationLogs(integrationId) {
    // عرض سجل العمليات والأخطاء
}

// إعادة المزامنة
function resyncIntegration(integrationId) {
    // إعادة مزامنة البيانات
}
```

### حالات التكامل:
- **🟢 متصل**: يعمل بشكل طبيعي
- **🟡 تحذير**: مشاكل طفيفة
- **🔴 خطأ**: يحتاج إعادة إعداد
- **⚪ متوقف**: تم إيقافه مؤقتاً

## 📊 مراقبة الاستخدام

### لوحة التحكم:
```javascript
// عرض حالة الخطة
function showPlanStatus(planType, limits, usage) {
    // عرض الاستخدام الحالي مقابل الحدود
    // شريط تقدم لكل نوع من الموارد
    // أزرار الترقية عند الحاجة
}

// تحذيرات الحدود
function checkLimitWarnings(limits, usage) {
    // فحص الاقتراب من الحدود
    // عرض تحذيرات مناسبة
    // اقتراح الترقية
}
```

## 🔄 ترقية الخطط

### عملية الترقية:
1. **اختيار الخطة الجديدة**
2. **مراجعة الميزات الإضافية**
3. **حساب التكلفة الإضافية**
4. **تأكيد الترقية**
5. **تطبيق فوري للميزات الجديدة**

### عملية التخفيض:
1. **اختيار الخطة الأقل**
2. **تحذير من الميزات المفقودة**
3. **حساب المبلغ المسترد**
4. **تأكيد التخفيض**
5. **تطبيق في الدورة القادمة**

## 🔐 الأمان

### حماية البيانات:
- **تشفير API Keys**: جميع المفاتيح مشفرة
- **OAuth Tokens**: تجديد تلقائي
- **Rate Limiting**: حدود للطلبات
- **Audit Logs**: سجل جميع العمليات

### أفضل الممارسات:
- استخدم HTTPS دائماً
- لا تشارك API Keys
- راجع الصلاحيات بانتظام
- فعّل التنبيهات الأمنية

## 📞 الدعم

### خطة المبتدئ:
- دعم بريد إلكتروني
- وقت الاستجابة: 24-48 ساعة
- قاعدة معرفة

### خطة المحترف:
- دعم أولوية
- وقت الاستجابة: 4-8 ساعات
- دردشة مباشرة

### خطة المؤسسة:
- مدير حساب مخصص
- وقت الاستجابة: 1-2 ساعة
- دعم هاتفي
- تدريب مخصص

## 🚀 التطوير المستقبلي

### تكاملات قادمة:
- TikTok for Business
- Pinterest Business
- Snapchat Ads
- Microsoft Advertising
- Amazon Advertising

### ميزات قادمة:
- تكاملات مخصصة للمؤسسات
- API GraphQL
- Webhooks متقدمة
- تحليلات متقدمة للتكاملات
