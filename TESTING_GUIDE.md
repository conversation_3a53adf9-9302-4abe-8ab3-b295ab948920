# دليل اختبار نظام تسجيل الدخول والتوجيه

## الحسابات التجريبية للاختبار

### 1. حساب المدير (Admin)
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password123`
- **التوجيه المتوقع**: `/admin/dashboard`

### 2. حساب تجاري (Business)
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password123`
- **التوجيه المتوقع**: `/business/kpis`

### 3. حساب شخصي (Personal)
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password123`
- **التوجيه المتوقع**: `/personal/dashboard`

## خطوات الاختبار

### 1. اختبار الوصول للصفحة الرئيسية
1. افتح المتصفح على `http://localhost:5173`
2. تأكد من ظهور الصفحة الرئيسية بشكل صحيح
3. تأكد من وجود زر "تسجيل الدخول" في الهيدر

### 2. اختبار تسجيل الدخول
1. اضغط على زر "تسجيل الدخول" في الهيدر
2. يجب أن تنتقل إلى صفحة `/login`
3. أدخل أحد الحسابات التجريبية المذكورة أعلاه
4. اضغط على "تسجيل الدخول"
5. يجب أن تنتقل تلقائياً إلى لوحة التحكم المناسبة

### 3. اختبار التوجيه حسب نوع الحساب
- **حساب المدير**: يجب الانتقال إلى `/admin/dashboard`
- **حساب تجاري**: يجب الانتقال إلى `/business/kpis`
- **حساب شخصي**: يجب الانتقال إلى `/personal/dashboard`

### 4. اختبار حماية المسارات
1. بعد تسجيل الدخول، جرب الوصول لمسار مختلف عن نوع حسابك
2. مثلاً: إذا كنت مسجل كـ "business"، جرب الوصول لـ `/admin/dashboard`
3. يجب أن يتم توجيهك تلقائياً إلى لوحة التحكم المناسبة لنوع حسابك

### 5. اختبار تسجيل الخروج
1. اضغط على زر "تسجيل الخروج" في الهيدر
2. يجب أن تنتقل إلى الصفحة الرئيسية
3. يجب أن يظهر زر "تسجيل الدخول" مرة أخرى

### 6. اختبار الوصول المباشر للمسارات المحمية
1. بدون تسجيل دخول، جرب الوصول مباشرة لـ `/personal/dashboard`
2. يجب أن يتم توجيهك إلى صفحة تسجيل الدخول
3. بعد تسجيل الدخول، يجب أن تنتقل إلى الصفحة التي كنت تحاول الوصول إليها

## المشاكل المحتملة وحلولها

### 1. خطأ في الاتصال بـ API
- تأكد من تشغيل الباك إند على `http://localhost:8000`
- النظام سيستخدم الحسابات التجريبية تلقائياً في حالة فشل الاتصال

### 2. عدم التوجيه الصحيح
- تأكد من أن المسارات محددة بشكل صحيح في `AppRouter.tsx`
- تحقق من أن `ProtectedRoute` يعمل بشكل صحيح

### 3. مشاكل في التخزين المحلي
- امسح التخزين المحلي في المتصفح إذا واجهت مشاكل
- أعد تحميل الصفحة

## الميزات المُنفذة

✅ **ربط زر تسجيل الدخول في الهيدر**
✅ **التوجيه التلقائي بعد تسجيل الدخول**
✅ **حماية المسارات بـ ProtectedRoute**
✅ **دعم أنواع المستخدمين المختلفة**
✅ **ربط مع API الباك إند**
✅ **نظام احتياطي للحسابات التجريبية**

## ملاحظات إضافية

- النظام يدعم كلاً من API الحقيقي والحسابات التجريبية
- في حالة فشل الاتصال بـ API، سيتم استخدام الحسابات التجريبية تلقائياً
- جميع المسارات محمية ولا يمكن الوصول إليها بدون تسجيل دخول
- التوجيه يتم بناءً على نوع المستخدم المحدد في قاعدة البيانات
