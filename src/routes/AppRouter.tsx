import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';

// Components
import ProtectedRoute from '../components/ProtectedRoute';

// Layouts
import PersonalLayout from '../layouts/PersonalLayout';
import BusinessLayout from '../layouts/BusinessLayout';
import AdminLayout from '../layouts/AdminLayout';

// Public Pages
import HomePage from '../pages/HomePage';
import LoginPage from '../pages/LoginPage';
import SimpleSignup from '../pages/SimpleSignup';

// Personal Pages
import PersonalDashboard from '../pages/personal/Dashboard';

// Business Pages
import SimpleKPIs from '../pages/business/SimpleKPIs';

// Admin Pages
import CentralDashboard from '../pages/admin/CentralDashboard';

// Shared Pages
import Notifications from '../pages/shared/Notifications';

// Simple placeholder components for missing pages
const SimplePage: React.FC<{ title: string; description: string }> = ({ title, description }) => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <h1>{title}</h1>
    <p>{description}</p>
  </Box>
);

const AboutPage = () => <SimplePage title="من نحن" description="تعرف على MarketMind" />;
const ContactPage = () => <SimplePage title="اتصل بنا" description="تواصل معنا" />;
const FeaturesPage = () => <SimplePage title="الميزات" description="اكتشف ميزات المنصة" />;
const PricingPage = () => <SimplePage title="الأسعار" description="خطط الأسعار المناسبة لك" />;

const AppRouter: React.FC = () => {
  return (
    <Router>
      <Suspense fallback={
        <Box 
          display="flex" 
          justifyContent="center" 
          alignItems="center" 
          minHeight="100vh"
        >
          <CircularProgress />
        </Box>
      }>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SimpleSignup />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/features" element={<FeaturesPage />} />
          <Route path="/pricing" element={<PricingPage />} />
          
          {/* Personal Routes */}
          <Route path="/personal" element={
            <ProtectedRoute requiredUserType="personal">
              <PersonalLayout>
                <PersonalDashboard />
              </PersonalLayout>
            </ProtectedRoute>
          } />

          {/* Business Routes */}
          <Route path="/business" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <SimpleKPIs />
              </BusinessLayout>
            </ProtectedRoute>
          } />
          <Route path="/business/kpis" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <SimpleKPIs />
              </BusinessLayout>
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requiredUserType="admin">
              <AdminLayout>
                <CentralDashboard />
              </AdminLayout>
            </ProtectedRoute>
          } />

          {/* Shared Routes */}
          <Route path="/notifications" element={
            <ProtectedRoute>
              <Notifications />
            </ProtectedRoute>
          } />
        </Routes>
      </Suspense>
    </Router>
  );
};

export default AppRouter;
