# 🚀 AI Marketing Platform - Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying the AI Marketing Platform in different environments.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│  (PostgreSQL)   │
│   Port: 5173    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │   (Cache)       │
                    │   Port: 6379    │
                    └─────────────────┘
```

## 🐳 Docker Deployment (Recommended)

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM minimum
- 10GB disk space

### Quick Start
```bash
# Clone repository
git clone https://github.com/your-username/ai-marketing-platform.git
cd ai-marketing-platform

# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### Production Docker Setup
```bash
# Use production configuration
docker-compose -f docker-compose.prod.yml up -d

# With SSL/HTTPS
docker-compose -f docker-compose.ssl.yml up -d
```

## 🖥️ Manual Deployment

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: 2 cores minimum, 4 cores recommended
- **Storage**: 20GB minimum

### 1. Database Setup (PostgreSQL)

#### Ubuntu/Debian
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE ai_marketing;
CREATE USER ai_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE ai_marketing TO ai_user;
\q
```

#### macOS
```bash
# Install with Homebrew
brew install postgresql
brew services start postgresql

# Create database
createdb ai_marketing
```

### 2. Redis Setup

#### Ubuntu/Debian
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### macOS
```bash
brew install redis
brew services start redis
```

### 3. Backend Deployment

```bash
# Clone repository
git clone https://github.com/your-username/ai-marketing-platform.git
cd ai-marketing-platform/backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
alembic upgrade head

# Start the server
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 4. Frontend Deployment

```bash
cd ai-marketing-platform/frontend

# Install dependencies
npm install

# Build for production
npm run build

# Serve with nginx or similar
# Copy dist/ folder to web server
```

## 🌐 Production Deployment

### 1. Server Setup (Ubuntu 20.04)

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx postgresql postgresql-contrib redis-server \
    python3 python3-pip python3-venv nodejs npm git curl

# Install Docker (optional)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Nginx Configuration

```nginx
# /etc/nginx/sites-available/ai-marketing-platform
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Frontend
    location / {
        root /var/www/ai-marketing-platform/dist;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }

    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location /static/ {
        alias /var/www/ai-marketing-platform/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 4. Systemd Service (Backend)

```ini
# /etc/systemd/system/ai-marketing-backend.service
[Unit]
Description=AI Marketing Platform Backend
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/ai-marketing-platform/backend
Environment=PATH=/opt/ai-marketing-platform/backend/venv/bin
ExecStart=/opt/ai-marketing-platform/backend/venv/bin/uvicorn main:app --host 127.0.0.1 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable ai-marketing-backend
sudo systemctl start ai-marketing-backend
```

## ☁️ Cloud Deployment

### AWS Deployment

#### 1. EC2 Instance Setup
```bash
# Launch EC2 instance (t3.medium or larger)
# Security groups: HTTP (80), HTTPS (443), SSH (22)

# Connect and setup
ssh -i your-key.pem ubuntu@your-ec2-ip
sudo apt update && sudo apt upgrade -y

# Follow manual deployment steps above
```

#### 2. RDS Database
```bash
# Create RDS PostgreSQL instance
# Update DATABASE_URL in .env
DATABASE_URL=*****************************************************/ai_marketing
```

#### 3. ElastiCache Redis
```bash
# Create ElastiCache Redis cluster
# Update REDIS_URL in .env
REDIS_URL=redis://your-elasticache-endpoint:6379
```

### Google Cloud Platform

#### 1. Compute Engine
```bash
# Create VM instance
gcloud compute instances create ai-marketing-vm \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-medium \
    --zone=us-central1-a
```

#### 2. Cloud SQL
```bash
# Create PostgreSQL instance
gcloud sql instances create ai-marketing-db \
    --database-version=POSTGRES_13 \
    --tier=db-f1-micro \
    --region=us-central1
```

### DigitalOcean

#### 1. Droplet Setup
```bash
# Create droplet (2GB RAM minimum)
# Use Docker one-click app or Ubuntu 20.04

# Follow Docker deployment steps
```

#### 2. Managed Database
```bash
# Create managed PostgreSQL database
# Update connection string in .env
```

## 🔧 Environment Configuration

### Production Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ai_marketing
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secure-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Payment Gateways
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
PAYPAL_CLIENT_ID=your-live-paypal-client-id
PAYPAL_CLIENT_SECRET=your-live-paypal-secret

# External APIs
OPENAI_API_KEY=your-openai-api-key
GOOGLE_ANALYTICS_PROPERTY_ID=your-ga-property-id
TWITTER_API_KEY=your-twitter-api-key
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token

# Email
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=AI Marketing Platform

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO
```

## 📊 Monitoring & Logging

### 1. Application Monitoring

```bash
# Install monitoring tools
pip install sentry-sdk prometheus-client

# Add to main.py
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

sentry_sdk.init(
    dsn="your-sentry-dsn",
    integrations=[FastApiIntegration()],
)
```

### 2. System Monitoring

```bash
# Install monitoring stack
docker-compose -f docker-compose.monitoring.yml up -d

# Access dashboards
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001
```

### 3. Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/ai-marketing

/var/log/ai-marketing/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload ai-marketing-backend
    endscript
}
```

## 🔒 Security Checklist

### Server Security
- [ ] Update system packages regularly
- [ ] Configure firewall (UFW/iptables)
- [ ] Disable root SSH login
- [ ] Use SSH keys instead of passwords
- [ ] Install fail2ban for intrusion prevention
- [ ] Regular security audits

### Application Security
- [ ] Use HTTPS everywhere
- [ ] Implement rate limiting
- [ ] Validate all inputs
- [ ] Use environment variables for secrets
- [ ] Regular dependency updates
- [ ] Enable CORS properly
- [ ] Implement proper authentication

### Database Security
- [ ] Use strong passwords
- [ ] Limit database access
- [ ] Regular backups
- [ ] Encrypt sensitive data
- [ ] Monitor database logs

## 🔄 Backup & Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
FILENAME="ai_marketing_backup_$DATE.sql"

pg_dump -h localhost -U ai_user ai_marketing > "$BACKUP_DIR/$FILENAME"
gzip "$BACKUP_DIR/$FILENAME"

# Keep only last 30 days
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

### File Backup
```bash
# Backup application files
rsync -av /opt/ai-marketing-platform/ /backups/app/
```

## 🚀 Performance Optimization

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_campaigns_user_id ON campaigns(user_id);
CREATE INDEX idx_analytics_date ON analytics(date_recorded);
CREATE INDEX idx_users_email ON users(email);
```

### Caching Strategy
```python
# Redis caching configuration
CACHE_TTL = 3600  # 1 hour
CACHE_PREFIX = "ai_marketing:"

# Cache frequently accessed data
@cache(ttl=CACHE_TTL)
def get_user_campaigns(user_id: int):
    return db.query(Campaign).filter(Campaign.user_id == user_id).all()
```

### CDN Setup
```bash
# Use CloudFlare or AWS CloudFront for static assets
# Configure in nginx
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 📞 Support & Troubleshooting

### Common Issues

#### 1. Database Connection Error
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection
psql -h localhost -U ai_user -d ai_marketing
```

#### 2. Redis Connection Error
```bash
# Check Redis status
sudo systemctl status redis-server

# Test connection
redis-cli ping
```

#### 3. High Memory Usage
```bash
# Monitor memory usage
htop
free -h

# Optimize Python memory
export PYTHONOPTIMIZE=1
```

### Getting Help
- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📚 Documentation: [docs.ai-marketing-platform.com](https://docs.ai-marketing-platform.com)
- 💬 Community: Discord/Slack

---

**Deployment completed successfully! 🎉**

Your AI Marketing Platform is now ready for production use.
