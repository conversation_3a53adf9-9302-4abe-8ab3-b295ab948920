import { createTheme } from '@mui/material/styles';

// إنشاء theme مخصص
export const muiTheme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
    },
    secondary: {
      main: '#764ba2',
    },
  },
  typography: {
    fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
  },
});

// Theme object مخصص للمكونات
export const theme = {
  colors: {
    background: {
      primary: '#ffffff',
      secondary: '#f8fafc',
    },
    text: {
      primary: '#1a202c',
      secondary: '#718096',
    },
    border: {
      light: '#e2e8f0',
    },
  },
  spacing: {
    base: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem',
    },
    border: {
      radius: {
        sm: '0.25rem',
        md: '0.5rem',
        lg: '0.75rem',
        xl: '1rem',
        '2xl': '1.5rem',
      },
    },
    component: {
      modal: {
        padding: '1.5rem',
        headerPadding: '1rem 1.5rem',
      },
    },
  },
  typography: {
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  effects: {
    shadows: {
      modal: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
  },
};

export default muiTheme;
