import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CircularProgress, Box } from '@mui/material';

// Components
import ProtectedRoute from '../components/ProtectedRoute';

// Layouts
import PersonalLayout from '../layouts/PersonalLayout';
import BusinessLayout from '../layouts/BusinessLayout';
import AdminLayout from '../layouts/AdminLayout';

// Public Pages
import HomePage from '../pages/HomePage';
import LoginPage from '../pages/LoginPage';
import SignupPage from '../pages/SignupPage';
import AboutPage from '../pages/AboutPage';
import ContactPage from '../pages/ContactPage';
import FeaturesPage from '../pages/FeaturesPage';
import PricingPage from '../pages/PricingPage';

// Personal Pages
import PersonalDashboard from '../pages/personal/Dashboard';
import AnalysisTools from '../pages/personal/AnalysisTools';
import AccountSettings from '../pages/personal/AccountSettings';
import Subscription from '../pages/personal/Subscription';
import Profile from '../pages/personal/Profile';

// Business Pages
import KPIs from '../pages/business/KPIs';
import TeamManagement from '../pages/business/TeamManagement';
import AdvancedAnalytics from '../pages/business/AdvancedAnalytics';
import Projects from '../pages/business/Projects';
import CustomerSegmentation from '../pages/business/CustomerSegmentation';
import ChurnPrediction from '../pages/business/ChurnPrediction';
import ContentGeneration from '../pages/business/ContentGeneration';
import CampaignOptimization from '../pages/business/CampaignOptimization';
import SalesPrediction from '../pages/business/SalesPrediction';
import SentimentAnalysis from '../pages/business/SentimentAnalysis';

// Admin Pages
import CentralDashboard from '../pages/admin/CentralDashboard';
import AdvancedUserManagement from '../pages/admin/AdvancedUserManagement';
import SystemMonitoring from '../pages/admin/SystemMonitoring';
import SubscriptionAdmin from '../pages/admin/SubscriptionAdmin';

// Shared Pages
import Notifications from '../pages/shared/Notifications';


const AppRouter: React.FC = () => {
  return (
    <Router>
      <Suspense fallback={
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <CircularProgress />
        </Box>
      }>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/features" element={<FeaturesPage />} />
          <Route path="/pricing" element={<PricingPage />} />
          
          {/* Personal Routes */}
          <Route path="/personal" element={
            <ProtectedRoute requiredUserType="personal">
              <PersonalLayout>
                <PersonalDashboard />
              </PersonalLayout>
            </ProtectedRoute>
          } />
          <Route path="/personal/dashboard" element={
            <ProtectedRoute requiredUserType="personal">
              <PersonalLayout>
                <PersonalDashboard />
              </PersonalLayout>
            </ProtectedRoute>
          } />

          {/* Business Routes */}
          <Route path="/business" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <KPIs />
              </BusinessLayout>
            </ProtectedRoute>
          } />
          <Route path="/business/dashboard" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <KPIs />
              </BusinessLayout>
            </ProtectedRoute>
          } />
          <Route path="/business/customer-segmentation" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <CustomerSegmentation />
              </BusinessLayout>
            </ProtectedRoute>
          } />
          <Route path="/business/content-generation" element={
            <ProtectedRoute requiredUserType="business">
              <BusinessLayout>
                <ContentGeneration />
              </BusinessLayout>
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requiredUserType="admin">
              <AdminLayout>
                <CentralDashboard />
              </AdminLayout>
            </ProtectedRoute>
          } />

          {/* Shared Routes */}
          <Route path="/notifications" element={
            <ProtectedRoute>
              <Notifications />
            </ProtectedRoute>
          } />
          
          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </Router>
  );
};

export default AppRouter;
