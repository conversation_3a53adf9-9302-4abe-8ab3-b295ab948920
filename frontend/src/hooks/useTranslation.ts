import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../api/apiClient';

interface TranslationData {
  [key: string]: string;
}

interface LanguageInfo {
  name: string;
  native_name: string;
  direction: 'ltr' | 'rtl';
  currency: string;
  date_format: string;
  time_format: string;
}

interface SupportedLanguages {
  [key: string]: LanguageInfo;
}

interface UseTranslationReturn {
  t: (key: string, variables?: Record<string, any>) => string;
  language: string;
  setLanguage: (lang: string) => void;
  supportedLanguages: SupportedLanguages;
  languageInfo: LanguageInfo | null;
  isRTL: boolean;
  loading: boolean;
  formatCurrency: (amount: number, currency?: string) => string;
  formatDate: (date: Date | string, format?: string) => string;
  formatDateTime: (datetime: Date | string) => string;
}

const useTranslation = (): UseTranslationReturn => {
  const [language, setCurrentLanguage] = useState<string>('en');
  const [translations, setTranslations] = useState<TranslationData>({});
  const [supportedLanguages, setSupportedLanguages] = useState<SupportedLanguages>({});
  const [languageInfo, setLanguageInfo] = useState<LanguageInfo | null>(null);
  const [loading, setLoading] = useState(true);

  // Load supported languages
  useEffect(() => {
    const loadSupportedLanguages = async () => {
      try {
        const response = await apiClient.get('/api/v1/i18n/languages');
        if (response.data.success) {
          setSupportedLanguages(response.data.languages);
        }
      } catch (error) {
        console.error('Failed to load supported languages:', error);
      }
    };

    loadSupportedLanguages();
  }, []);

  // Load user's preferred language
  useEffect(() => {
    const loadUserLanguage = async () => {
      try {
        const response = await apiClient.get('/api/v1/i18n/user/language');
        if (response.data.success) {
          setCurrentLanguage(response.data.language);
        }
      } catch (error) {
        // Use browser language or default to English
        const browserLang = navigator.language.split('-')[0];
        setCurrentLanguage(browserLang === 'ar' ? 'ar' : 'en');
      }
    };

    loadUserLanguage();
  }, []);

  // Load translations when language changes
  useEffect(() => {
    const loadTranslations = async () => {
      if (!language) return;

      setLoading(true);
      try {
        const response = await apiClient.get(`/api/v1/i18n/translations/${language}`);
        if (response.data.success) {
          setTranslations(response.data.translations);
          setLanguageInfo(response.data.language_info);
        }
      } catch (error) {
        console.error('Failed to load translations:', error);
        // Fallback to English if translation loading fails
        if (language !== 'en') {
          setCurrentLanguage('en');
        }
      } finally {
        setLoading(false);
      }
    };

    loadTranslations();
  }, [language]);

  // Translation function
  const t = useCallback((key: string, variables?: Record<string, any>): string => {
    let translation = translations[key] || key;

    // Replace variables in translation
    if (variables) {
      Object.keys(variables).forEach(variable => {
        const placeholder = `{${variable}}`;
        translation = translation.replace(new RegExp(placeholder, 'g'), String(variables[variable]));
      });
    }

    return translation;
  }, [translations]);

  // Set language and update user preference
  const setLanguage = useCallback(async (lang: string) => {
    if (lang === language) return;

    try {
      await apiClient.put('/api/v1/i18n/user/language', { language: lang });
      setCurrentLanguage(lang);
      
      // Update document direction
      document.documentElement.dir = supportedLanguages[lang]?.direction || 'ltr';
      document.documentElement.lang = lang;
    } catch (error) {
      console.error('Failed to update language preference:', error);
      // Still update locally even if server update fails
      setCurrentLanguage(lang);
    }
  }, [language, supportedLanguages]);

  // Format currency
  const formatCurrency = useCallback(async (amount: number, currency?: string): Promise<string> => {
    try {
      const response = await apiClient.post('/api/v1/i18n/format/currency', {
        amount,
        language,
        currency
      });
      if (response.data.success) {
        return response.data.formatted_amount;
      }
    } catch (error) {
      console.error('Failed to format currency:', error);
    }

    // Fallback formatting
    const currencyCode = currency || languageInfo?.currency || 'USD';
    if (currencyCode === 'EGP') {
      return `${amount.toLocaleString()} ج.م`;
    }
    return `$${amount.toLocaleString()}`;
  }, [language, languageInfo]);

  // Format date
  const formatDate = useCallback(async (date: Date | string, format?: string): Promise<string> => {
    try {
      const dateStr = typeof date === 'string' ? date : date.toISOString();
      const response = await apiClient.post('/api/v1/i18n/format/date', {
        date: dateStr,
        language,
        format
      });
      if (response.data.success) {
        return response.data.formatted_date;
      }
    } catch (error) {
      console.error('Failed to format date:', error);
    }

    // Fallback formatting
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString(language);
  }, [language]);

  // Format datetime
  const formatDateTime = useCallback(async (datetime: Date | string): Promise<string> => {
    try {
      const datetimeStr = typeof datetime === 'string' ? datetime : datetime.toISOString();
      const response = await apiClient.post('/api/v1/i18n/format/datetime', {
        datetime: datetimeStr,
        language
      });
      if (response.data.success) {
        return response.data.formatted_datetime;
      }
    } catch (error) {
      console.error('Failed to format datetime:', error);
    }

    // Fallback formatting
    const datetimeObj = typeof datetime === 'string' ? new Date(datetime) : datetime;
    return datetimeObj.toLocaleString(language);
  }, [language]);

  // Update document direction when language info changes
  useEffect(() => {
    if (languageInfo) {
      document.documentElement.dir = languageInfo.direction;
      document.documentElement.lang = language;
    }
  }, [languageInfo, language]);

  return {
    t,
    language,
    setLanguage,
    supportedLanguages,
    languageInfo,
    isRTL: languageInfo?.direction === 'rtl',
    loading,
    formatCurrency: (amount: number, currency?: string) => {
      // Return a promise for async formatting, but provide sync fallback
      formatCurrency(amount, currency);
      const currencyCode = currency || languageInfo?.currency || 'USD';
      if (currencyCode === 'EGP') {
        return `${amount.toLocaleString()} ج.م`;
      }
      return `$${amount.toLocaleString()}`;
    },
    formatDate: (date: Date | string, format?: string) => {
      // Return a promise for async formatting, but provide sync fallback
      formatDate(date, format);
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString(language);
    },
    formatDateTime: (datetime: Date | string) => {
      // Return a promise for async formatting, but provide sync fallback
      formatDateTime(datetime);
      const datetimeObj = typeof datetime === 'string' ? new Date(datetime) : datetime;
      return datetimeObj.toLocaleString(language);
    }
  };
};

export default useTranslation;
