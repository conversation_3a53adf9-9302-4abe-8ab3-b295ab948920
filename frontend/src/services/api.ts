import axios from 'axios';

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', {
      email,
      password
    });
    return response.data;
  },
  
  register: async (userData: {
    email: string;
    password: string;
    name: string;
    user_type: string;
  }) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },
  
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
  
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  }
};

// Users API
export const usersAPI = {
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    user_type?: string;
    status?: string;
  }) => {
    const response = await api.get('/users', { params });
    return response.data;
  },
  
  getUserById: async (id: number) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  
  createUser: async (userData: {
    email: string;
    password: string;
    name: string;
    user_type: string;
  }) => {
    const response = await api.post('/users', userData);
    return response.data;
  },
  
  updateUser: async (id: number, userData: Partial<{
    name: string;
    email: string;
    user_type: string;
    is_active: boolean;
  }>) => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },
  
  deleteUser: async (id: number) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },
  
  getUserStats: async () => {
    const response = await api.get('/users/stats');
    return response.data;
  }
};

// Subscriptions API
export const subscriptionsAPI = {
  getSubscriptions: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    plan?: string;
  }) => {
    const response = await api.get('/subscriptions', { params });
    return response.data;
  },
  
  getSubscriptionById: async (id: number) => {
    const response = await api.get(`/subscriptions/${id}`);
    return response.data;
  },
  
  createSubscription: async (subscriptionData: {
    user_id: number;
    plan: string;
    price: number;
    billing_cycle: string;
  }) => {
    const response = await api.post('/subscriptions', subscriptionData);
    return response.data;
  },
  
  updateSubscription: async (id: number, subscriptionData: Partial<{
    plan: string;
    price: number;
    status: string;
    billing_cycle: string;
  }>) => {
    const response = await api.put(`/subscriptions/${id}`, subscriptionData);
    return response.data;
  },
  
  cancelSubscription: async (id: number) => {
    const response = await api.post(`/subscriptions/${id}/cancel`);
    return response.data;
  },
  
  renewSubscription: async (id: number) => {
    const response = await api.post(`/subscriptions/${id}/renew`);
    return response.data;
  },
  
  getSubscriptionStats: async () => {
    const response = await api.get('/subscriptions/stats');
    return response.data;
  }
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: async () => {
    const response = await api.get('/analytics/dashboard');
    return response.data;
  },
  
  getUserAnalytics: async (params?: {
    start_date?: string;
    end_date?: string;
    user_type?: string;
  }) => {
    const response = await api.get('/analytics/users', { params });
    return response.data;
  },
  
  getRevenueAnalytics: async (params?: {
    start_date?: string;
    end_date?: string;
    period?: string;
  }) => {
    const response = await api.get('/analytics/revenue', { params });
    return response.data;
  },
  
  getSystemMetrics: async () => {
    const response = await api.get('/analytics/system');
    return response.data;
  }
};

// AI Models API
export const aiModelsAPI = {
  getModels: async () => {
    const response = await api.get('/ai/models');
    return response.data;
  },
  
  runCustomerSegmentation: async (data: any) => {
    const response = await api.post('/ai/customer-segmentation', data);
    return response.data;
  },
  
  runChurnPrediction: async (data: any) => {
    const response = await api.post('/ai/churn-prediction', data);
    return response.data;
  },
  
  runSalesPrediction: async (data: any) => {
    const response = await api.post('/ai/sales-prediction', data);
    return response.data;
  },
  
  runSentimentAnalysis: async (data: any) => {
    const response = await api.post('/ai/sentiment-analysis', data);
    return response.data;
  },
  
  optimizeCampaign: async (data: any) => {
    const response = await api.post('/ai/campaign-optimization', data);
    return response.data;
  },
  
  generatePersonalizedContent: async (data: any) => {
    const response = await api.post('/ai/personalized-content', data);
    return response.data;
  },
  
  analyzeCustomerJourney: async (data: any) => {
    const response = await api.post('/ai/customer-journey', data);
    return response.data;
  }
};

// System Monitoring API
export const systemAPI = {
  getSystemStatus: async () => {
    const response = await api.get('/system/status');
    return response.data;
  },
  
  getSystemMetrics: async (timeRange?: string) => {
    const response = await api.get('/system/metrics', { 
      params: { time_range: timeRange } 
    });
    return response.data;
  },
  
  getSystemLogs: async (params?: {
    level?: string;
    service?: string;
    limit?: number;
  }) => {
    const response = await api.get('/system/logs', { params });
    return response.data;
  },
  
  getApiMetrics: async () => {
    const response = await api.get('/system/api-metrics');
    return response.data;
  }
};

// Notifications API
export const notificationsAPI = {
  getNotifications: async (params?: {
    page?: number;
    limit?: number;
    read?: boolean;
  }) => {
    const response = await api.get('/notifications', { params });
    return response.data;
  },
  
  markAsRead: async (id: number) => {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data;
  },
  
  markAllAsRead: async () => {
    const response = await api.put('/notifications/read-all');
    return response.data;
  },
  
  deleteNotification: async (id: number) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  }
};

// AI Services API
export const aiAPI = {
  // Customer Segmentation
  getCustomerSegmentation: async (timeRange: string = '30days') => {
    const response = await api.get('/ai/customer-segmentation', {
      params: { time_range: timeRange }
    });
    return response.data;
  },

  // Churn Prediction
  getChurnPrediction: async (riskLevel: string = 'all') => {
    const response = await api.get('/ai/churn-prediction', {
      params: { risk_level: riskLevel }
    });
    return response.data;
  },

  // Content Generation
  generateContent: async (contentRequest: any) => {
    const response = await api.post('/ai/content-generation', contentRequest);
    return response.data;
  },

  // Campaign Optimization
  getCampaignOptimization: async () => {
    const response = await api.get('/ai/campaign-optimization');
    return response.data;
  },

  // Sales Prediction
  getSalesPrediction: async (period: string = 'quarterly', confidenceLevel: number = 95) => {
    const response = await api.get('/ai/sales-prediction', {
      params: { period, confidence_level: confidenceLevel }
    });
    return response.data;
  },

  updateSalesFactors: async (factors: Record<string, number>) => {
    const response = await api.post('/ai/sales-prediction', { factors });
    return response.data;
  },

  // Customer Journey Analysis
  getCustomerJourney: async () => {
    const response = await api.get('/ai/customer-journey');
    return response.data;
  },

  // Sentiment Analysis
  getSentimentAnalysis: async (timeRange: string = '30days', platform: string = 'all') => {
    const response = await api.get('/ai/sentiment-analysis', {
      params: { time_range: timeRange, platform }
    });
    return response.data;
  },

  // Health Check
  healthCheck: async () => {
    const response = await api.get('/health');
    return response.data;
  }
};

export default api;
