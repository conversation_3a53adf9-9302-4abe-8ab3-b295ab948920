import { apiClient } from '../api/apiClient';

export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
}

export interface OAuthToken {
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
  tokenType: string;
  scope: string;
}

export interface IntegrationAccount {
  id: string;
  platform: string;
  accountId: string;
  accountName: string;
  email?: string;
  username?: string;
  profilePicture?: string;
  isActive: boolean;
  lastSync: string;
  permissions: string[];
}

class OAuthService {
  private readonly platforms = {
    facebook: {
      authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
      scope: ['pages_read_engagement', 'pages_show_list', 'ads_read', 'business_management']
    },
    instagram: {
      authUrl: 'https://api.instagram.com/oauth/authorize',
      tokenUrl: 'https://api.instagram.com/oauth/access_token',
      scope: ['user_profile', 'user_media', 'instagram_basic', 'instagram_manage_insights']
    },
    google: {
      authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      scope: ['https://www.googleapis.com/auth/adwords', 'https://www.googleapis.com/auth/analytics.readonly']
    },
    linkedin: {
      authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
      scope: ['r_organization_social', 'r_ads', 'r_ads_reporting']
    },
    twitter: {
      authUrl: 'https://twitter.com/i/oauth2/authorize',
      tokenUrl: 'https://api.twitter.com/2/oauth2/token',
      scope: ['tweet.read', 'users.read', 'offline.access']
    },
    youtube: {
      authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      scope: ['https://www.googleapis.com/auth/youtube.readonly', 'https://www.googleapis.com/auth/youtube-analytics.readonly']
    }
  };

  /**
   * بدء عملية OAuth للمنصة المحددة
   */
  async initiateOAuth(platform: string, config: OAuthConfig): Promise<string> {
    try {
      const platformConfig = this.platforms[platform as keyof typeof this.platforms];
      if (!platformConfig) {
        throw new Error(`Unsupported platform: ${platform}`);
      }

      // إنشاء state عشوائي للأمان
      const state = this.generateState();
      
      // حفظ state في localStorage للتحقق لاحقاً
      localStorage.setItem(`oauth_state_${platform}`, state);

      // بناء URL للمصادقة
      const authUrl = new URL(platformConfig.authUrl);
      authUrl.searchParams.append('client_id', config.clientId);
      authUrl.searchParams.append('redirect_uri', config.redirectUri);
      authUrl.searchParams.append('scope', config.scope.join(' '));
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('state', state);

      // إضافة معاملات خاصة بكل منصة
      if (platform === 'facebook' || platform === 'instagram') {
        authUrl.searchParams.append('display', 'popup');
      }

      return authUrl.toString();
    } catch (error) {
      console.error('Failed to initiate OAuth:', error);
      throw error;
    }
  }

  /**
   * تبديل authorization code بـ access token
   */
  async exchangeCodeForToken(
    platform: string, 
    code: string, 
    state: string, 
    config: OAuthConfig
  ): Promise<OAuthToken> {
    try {
      // التحقق من state
      const savedState = localStorage.getItem(`oauth_state_${platform}`);
      if (savedState !== state) {
        throw new Error('Invalid state parameter');
      }

      // إزالة state من localStorage
      localStorage.removeItem(`oauth_state_${platform}`);

      // إرسال طلب للحصول على token
      const response = await apiClient.post('/integrations/oauth/token', {
        platform,
        code,
        clientId: config.clientId,
        clientSecret: config.clientSecret,
        redirectUri: config.redirectUri
      });

      return response.data.token;
    } catch (error) {
      console.error('Failed to exchange code for token:', error);
      throw error;
    }
  }

  /**
   * تحديث access token باستخدام refresh token
   */
  async refreshToken(platform: string, refreshToken: string): Promise<OAuthToken> {
    try {
      const response = await apiClient.post('/integrations/oauth/refresh', {
        platform,
        refreshToken
      });

      return response.data.token;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      throw error;
    }
  }

  /**
   * ربط الحساب بعد الحصول على token
   */
  async connectAccount(platform: string, token: OAuthToken): Promise<IntegrationAccount> {
    try {
      const response = await apiClient.post('/integrations/connect', {
        platform,
        accessToken: token.accessToken,
        refreshToken: token.refreshToken,
        expiresIn: token.expiresIn
      });

      return response.data.account;
    } catch (error) {
      console.error('Failed to connect account:', error);
      throw error;
    }
  }

  /**
   * قطع ربط الحساب
   */
  async disconnectAccount(accountId: string): Promise<void> {
    try {
      await apiClient.delete(`/integrations/accounts/${accountId}`);
    } catch (error) {
      console.error('Failed to disconnect account:', error);
      throw error;
    }
  }

  /**
   * الحصول على قائمة الحسابات المربوطة
   */
  async getConnectedAccounts(): Promise<IntegrationAccount[]> {
    try {
      const response = await apiClient.get('/integrations/accounts');
      return response.data.accounts;
    } catch (error) {
      console.error('Failed to get connected accounts:', error);
      throw error;
    }
  }

  /**
   * مزامنة البيانات من حساب محدد
   */
  async syncAccountData(accountId: string, dataTypes?: string[]): Promise<void> {
    try {
      await apiClient.post(`/integrations/accounts/${accountId}/sync`, {
        dataTypes
      });
    } catch (error) {
      console.error('Failed to sync account data:', error);
      throw error;
    }
  }

  /**
   * الحصول على حالة المزامنة
   */
  async getSyncStatus(accountId: string): Promise<{
    isRunning: boolean;
    lastSync: string;
    nextSync: string;
    progress: number;
    errors: string[];
  }> {
    try {
      const response = await apiClient.get(`/integrations/accounts/${accountId}/sync-status`);
      return response.data;
    } catch (error) {
      console.error('Failed to get sync status:', error);
      throw error;
    }
  }

  /**
   * تحديث إعدادات المزامنة
   */
  async updateSyncSettings(accountId: string, settings: {
    autoSync: boolean;
    syncInterval: number; // بالدقائق
    dataTypes: string[];
  }): Promise<void> {
    try {
      await apiClient.put(`/integrations/accounts/${accountId}/sync-settings`, settings);
    } catch (error) {
      console.error('Failed to update sync settings:', error);
      throw error;
    }
  }

  /**
   * فتح نافذة OAuth في popup
   */
  openOAuthPopup(authUrl: string, platform: string): Promise<{ code: string; state: string }> {
    return new Promise((resolve, reject) => {
      const popup = window.open(
        authUrl,
        `oauth_${platform}`,
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      if (!popup) {
        reject(new Error('Failed to open popup window'));
        return;
      }

      // مراقبة تغيير URL في popup
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new Error('OAuth popup was closed'));
        }

        try {
          const url = popup.location.href;
          if (url.includes('code=')) {
            const urlParams = new URLSearchParams(popup.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            
            if (code && state) {
              popup.close();
              clearInterval(checkClosed);
              resolve({ code, state });
            }
          }
        } catch (error) {
          // تجاهل أخطاء CORS عند محاولة الوصول لـ popup.location
        }
      }, 1000);

      // إغلاق تلقائي بعد 10 دقائق
      setTimeout(() => {
        if (!popup.closed) {
          popup.close();
          clearInterval(checkClosed);
          reject(new Error('OAuth timeout'));
        }
      }, 600000);
    });
  }

  /**
   * إنشاء state عشوائي للأمان
   */
  private generateState(): string {
    const array = new Uint32Array(8);
    crypto.getRandomValues(array);
    return Array.from(array, dec => dec.toString(16)).join('');
  }

  /**
   * التحقق من صحة token
   */
  async validateToken(platform: string, token: string): Promise<boolean> {
    try {
      const response = await apiClient.post('/integrations/validate-token', {
        platform,
        token
      });
      return response.data.valid;
    } catch (error) {
      console.error('Failed to validate token:', error);
      return false;
    }
  }
}

export const oauthService = new OAuthService();
export default oauthService;
