import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Divider,
  Avatar,
  Badge
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Info,
  Warning,
  CheckCircle,
  Error,
  Delete,
  MarkEmailRead,
  MarkEmailUnread,
  Clear
} from '@mui/icons-material';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'success' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

const Notifications: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'success',
      title: 'تم إكمال التحليل',
      message: 'تم إكمال تحليل العملاء بنجاح. يمكنك الآن مراجعة النتائج.',
      timestamp: '2024-01-15T10:30:00Z',
      read: false,
      actionUrl: '/analysis'
    },
    {
      id: '2',
      type: 'warning',
      title: 'انتهاء صلاحية الاشتراك قريباً',
      message: 'سينتهي اشتراكك خلال 7 أيام. قم بتجديد الاشتراك لمواصلة الاستفادة من الخدمات.',
      timestamp: '2024-01-14T15:45:00Z',
      read: false,
      actionUrl: '/subscription'
    },
    {
      id: '3',
      type: 'info',
      title: 'ميزة جديدة متاحة',
      message: 'تم إضافة أدوات ذكاء اصطناعي جديدة لتحليل المشاعر. جربها الآن!',
      timestamp: '2024-01-13T09:15:00Z',
      read: true,
      actionUrl: '/ai-tools'
    },
    {
      id: '4',
      type: 'error',
      title: 'فشل في معالجة البيانات',
      message: 'حدث خطأ أثناء معالجة بيانات الحملة الإعلانية. يرجى المحاولة مرة أخرى.',
      timestamp: '2024-01-12T14:20:00Z',
      read: true
    },
    {
      id: '5',
      type: 'success',
      title: 'تم حفظ التقرير',
      message: 'تم حفظ تقرير الأداء الشهري بنجاح في مكتبة التقارير.',
      timestamp: '2024-01-11T11:00:00Z',
      read: true,
      actionUrl: '/reports'
    }
  ]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <Info color="info" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `منذ ${diffInDays} يوم`;
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAsUnread = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: false } : notif
      )
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Badge badgeContent={unreadCount} color="error">
            <NotificationsIcon fontSize="large" />
          </Badge>
          <Typography variant="h4">
            الإشعارات
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            تحديد الكل كمقروء
          </Button>
          <Button
            variant="outlined"
            size="small"
            color="error"
            onClick={clearAllNotifications}
            disabled={notifications.length === 0}
            startIcon={<Clear />}
          >
            مسح الكل
          </Button>
        </Box>
      </Box>

      {notifications.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <NotificationsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              لا توجد إشعارات
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ستظهر الإشعارات الجديدة هنا
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <List>
            {notifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  sx={{
                    backgroundColor: notification.read ? 'transparent' : 'action.hover',
                    '&:hover': {
                      backgroundColor: 'action.selected'
                    }
                  }}
                >
                  <ListItemIcon>
                    {getNotificationIcon(notification.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" fontWeight={notification.read ? 'normal' : 'bold'}>
                          {notification.title}
                        </Typography>
                        <Chip
                          label={getNotificationColor(notification.type)}
                          size="small"
                          color={getNotificationColor(notification.type) as any}
                          variant="outlined"
                        />
                        {!notification.read && (
                          <Chip
                            label="جديد"
                            size="small"
                            color="primary"
                            variant="filled"
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimestamp(notification.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {notification.read ? (
                        <IconButton
                          size="small"
                          onClick={() => markAsUnread(notification.id)}
                          title="تحديد كغير مقروء"
                        >
                          <MarkEmailUnread />
                        </IconButton>
                      ) : (
                        <IconButton
                          size="small"
                          onClick={() => markAsRead(notification.id)}
                          title="تحديد كمقروء"
                        >
                          <MarkEmailRead />
                        </IconButton>
                      )}
                      <IconButton
                        size="small"
                        onClick={() => deleteNotification(notification.id)}
                        title="حذف الإشعار"
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < notifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Card>
      )}
    </Box>
  );
};

export default Notifications;
