import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/enhanced-homepage.css';

const AboutPage: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  return (
    <div className="about-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
              <li><a href="/support">الدعم</a></li>
            </ul>
            <div className="nav-buttons">
              <button
                className="btn btn-outline"
                onClick={() => navigate('/login')}
              >
                تسجيل الدخول
              </button>
              <button
                className="btn btn-primary"
                onClick={() => navigate('/signup')}
              >
                ابدأ مجاناً
              </button>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">من نحن</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                نحن فريق من الخبراء المتخصصين في الذكاء الاصطناعي والتسويق الرقمي، 
                نعمل على تطوير حلول مبتكرة تساعد الشركات على تحقيق أهدافها التسويقية بكفاءة عالية.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">قصتنا</h2>
            <p className="section-subtitle">
              رحلة تطوير منصة MarketMind من فكرة إلى واقع يخدم آلاف الشركات
            </p>
          </div>
          
          <div className="features-grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-lightbulb"></i>
              </div>
              <h3>البداية</h3>
              <p>
                بدأت فكرة MarketMind في عام 2020 عندما لاحظنا الحاجة الماسة للشركات 
                في المنطقة لحلول ذكية تساعدها في اتخاذ قرارات تسويقية مدروسة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <h3>النمو</h3>
              <p>
                خلال السنوات الماضية، نمت منصتنا لتخدم أكثر من 10,000 شركة 
                وتحلل مليارات نقاط البيانات لتقديم رؤى قيمة لعملائنا.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-trophy"></i>
              </div>
              <h3>الإنجازات</h3>
              <p>
                حققنا العديد من الجوائز في مجال التكنولوجيا والابتكار، 
                وأصبحنا الخيار الأول للشركات الرائدة في المنطقة.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">مهمتنا ورؤيتنا</h2>
            <p className="section-subtitle">
              نسعى لتمكين الشركات من تحقيق أقصى استفادة من بياناتها التسويقية
            </p>
          </div>
          
          <div className="problems-grid">
            <div className="problem-card" data-aos="fade-up" data-aos-delay="100">
              <div className="problem-icon">
                <i className="fas fa-eye"></i>
              </div>
              <h3>رؤيتنا</h3>
              <p>
                أن نكون المنصة الرائدة عالمياً في مجال الذكاء الاصطناعي للتسويق الرقمي، 
                ونساهم في تحويل طريقة عمل الشركات وتفاعلها مع عملائها.
              </p>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="200">
              <div className="problem-icon">
                <i className="fas fa-bullseye"></i>
              </div>
              <h3>مهمتنا</h3>
              <p>
                تطوير حلول ذكية ومبتكرة تساعد الشركات على فهم عملائها بشكل أفضل، 
                وتحسين استراتيجياتها التسويقية، وتحقيق نمو مستدام.
              </p>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="300">
              <div className="problem-icon">
                <i className="fas fa-heart"></i>
              </div>
              <h3>قيمنا</h3>
              <p>
                نؤمن بالابتكار المستمر، والشفافية في التعامل، والتميز في الخدمة، 
                والمسؤولية الاجتماعية تجاه مجتمعنا وبيئتنا.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">فريقنا</h2>
            <p className="section-subtitle">
              مجموعة من الخبراء المتخصصين في التكنولوجيا والتسويق
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-user-tie"></i>
              </div>
              <h3>خبراء التكنولوجيا</h3>
              <p>
                فريق من المطورين والمهندسين المتخصصين في الذكاء الاصطناعي 
                وعلوم البيانات مع خبرة تزيد عن 10 سنوات.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <h3>خبراء التسويق</h3>
              <p>
                متخصصون في التسويق الرقمي وتحليل السلوك الاستهلاكي 
                مع سجل حافل في تطوير استراتيجيات ناجحة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-headset"></i>
              </div>
              <h3>فريق الدعم</h3>
              <p>
                فريق دعم عملاء متاح على مدار الساعة لضمان حصولك 
                على أفضل تجربة استخدام لمنصتنا.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>هل أنت مستعد للانضمام إلينا؟</h2>
            <p>ابدأ رحلتك مع MarketMind اليوم واكتشف قوة الذكاء الاصطناعي في التسويق</p>
            <div className="cta-buttons">
              <a href="/signup" className="btn btn-white">ابدأ مجاناً</a>
              <a href="/contact" className="btn btn-outline-white">تواصل معنا</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/#features">الميزات</a></li>
                <li><a href="/#pricing">الأسعار</a></li>
                <li><a href="/#features">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AboutPage;
