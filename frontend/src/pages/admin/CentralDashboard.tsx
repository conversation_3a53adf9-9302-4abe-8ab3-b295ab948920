import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Container,
  Paper
} from '@mui/material';
import {
  Dashboard,
  People,
  MonetizationOn,
  Computer,
  Analytics,
  Settings
} from '@mui/icons-material';

const CentralDashboard: React.FC = () => {
  const navigate = useNavigate();
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Paper elevation={3} sx={{ p: 3, mb: 4, textAlign: 'center', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Dashboard sx={{ fontSize: 60, mb: 2 }} />
        <Typography variant="h3" component="h1" gutterBottom>
          🎯 لوحة التحكم المركزية
        </Typography>
        <Typography variant="h6">
          مرحباً بك في منصة MarketMind للذكاء الاصطناعي
        </Typography>
      </Paper>

      {/* Quick Stats */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="primary">
              5,226
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي المستخدمين
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <MonetizationOn sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="success.main">
              $1.17M
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الإيرادات
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Computer sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="warning.main">
              99.9%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              وقت التشغيل
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Navigation Cards */}
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        🚀 الأقسام الرئيسية
      </Typography>

      <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', mb: 4 }}>
        <Card sx={{ flex: 1, minWidth: 300 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <People sx={{ mr: 2, color: 'primary.main', fontSize: 30 }} />
              <Typography variant="h6">
                إدارة المستخدمين
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              إدارة شاملة للمستخدمين مع بحث وتصفية متقدمة. عرض تفاصيل المستخدمين وإدارة الحسابات.
            </Typography>
            <Button 
              variant="contained" 
              fullWidth
              startIcon={<People />}
              onClick={() => navigate('/admin/users')}
            >
              فتح إدارة المستخدمين
            </Button>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 300 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <MonetizationOn sx={{ mr: 2, color: 'success.main', fontSize: 30 }} />
              <Typography variant="h6">
                إدارة الاشتراكات
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              إدارة الباقات والإيرادات مع تحليلات مفصلة. متابعة المدفوعات وإحصائيات الاشتراكات.
            </Typography>
            <Button 
              variant="contained" 
              fullWidth
              startIcon={<MonetizationOn />}
              onClick={() => navigate('/admin/subscriptions')}
            >
              فتح إدارة الاشتراكات
            </Button>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 300 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Computer sx={{ mr: 2, color: 'warning.main', fontSize: 30 }} />
              <Typography variant="h6">
                مراقبة النظام
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              مراقبة الأداء والأمان مع تحديث تلقائي. متابعة حالة الخوادم والأمان.
            </Typography>
            <Button 
              variant="contained" 
              fullWidth
              startIcon={<Computer />}
              onClick={() => navigate('/admin/monitoring')}
            >
              فتح مراقبة النظام
            </Button>
          </CardContent>
        </Card>
      </Box>

      {/* Quick Actions */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <Settings sx={{ mr: 1 }} />
          إجراءات سريعة
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="outlined" startIcon={<Analytics />}>
            عرض التقارير
          </Button>
          <Button variant="outlined" startIcon={<Settings />}>
            إعدادات النظام
          </Button>
          <Button variant="outlined" startIcon={<People />}>
            إضافة مستخدم جديد
          </Button>
          <Button variant="outlined" startIcon={<MonetizationOn />}>
            إنشاء باقة جديدة
          </Button>
        </Box>
      </Paper>

      {/* Footer */}
      <Box sx={{ textAlign: 'center', mt: 4, py: 2 }}>
        <Typography variant="body2" color="text.secondary">
          © 2024 MarketMind - منصة الذكاء الاصطناعي للتسويق الرقمي
        </Typography>
      </Box>
    </Container>
  );
};

export default CentralDashboard;
