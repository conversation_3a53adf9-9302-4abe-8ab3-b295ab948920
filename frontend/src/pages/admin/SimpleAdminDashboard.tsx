import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Container
} from '@mui/material';
import {
  Dashboard,
  People,
  MonetizationOn,
  Computer
} from '@mui/icons-material';

const SimpleAdminDashboard: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          🎯 لوحة التحكم المركزية
        </Typography>
        <Typography variant="h6" color="text.secondary">
          مرحباً بك في منصة MarketMind للذكاء الاصطناعي
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Quick Stats */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Card sx={{ flex: 1, minWidth: 250 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                5,226
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي المستخدمين
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 250 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <MonetizationOn sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                $1.17M
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإيرادات
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 250 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Computer sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                99.9%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                وقت التشغيل
              </Typography>
            </CardContent>
          </Card>
        </Box>

        {/* Navigation Cards */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Card sx={{ flex: 1, minWidth: 300 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <People sx={{ mr: 2, color: 'primary.main' }} />
                <Typography variant="h6">
                  إدارة المستخدمين
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                إدارة شاملة للمستخدمين مع بحث وتصفية متقدمة
              </Typography>
              <Button 
                variant="contained" 
                fullWidth
                onClick={() => window.location.href = '/users'}
              >
                فتح إدارة المستخدمين
              </Button>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 300 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MonetizationOn sx={{ mr: 2, color: 'success.main' }} />
                <Typography variant="h6">
                  إدارة الاشتراكات
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                إدارة الباقات والإيرادات مع تحليلات مفصلة
              </Typography>
              <Button 
                variant="contained" 
                fullWidth
                onClick={() => window.location.href = '/subscriptions'}
              >
                فتح إدارة الاشتراكات
              </Button>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 300 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Computer sx={{ mr: 2, color: 'warning.main' }} />
                <Typography variant="h6">
                  مراقبة النظام
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                مراقبة الأداء والأمان مع تحديث تلقائي
              </Typography>
              <Button 
                variant="contained" 
                fullWidth
                onClick={() => window.location.href = '/monitoring'}
              >
                فتح مراقبة النظام
              </Button>
            </CardContent>
          </Card>
        </Box>

        {/* Welcome Message */}
        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <Dashboard sx={{ fontSize: 60, mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              مرحباً بك في لوحة التحكم المركزية
            </Typography>
            <Typography variant="body1">
              هذه هي لوحة التحكم الرئيسية لمنصة MarketMind. من هنا يمكنك إدارة جميع جوانب المنصة بكفاءة عالية.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default SimpleAdminDashboard;
