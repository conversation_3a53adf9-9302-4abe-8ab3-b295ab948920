import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Container,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  People,
  Search,
  Add,
  ArrowBack,
  MonetizationOn
} from '@mui/icons-material';

const SubscriptionAdmin: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  // Mock subscription data
  const subscriptions = [
    {
      id: 1,
      userName: 'أحمد محمد',
      email: '<EMAIL>',
      plan: 'Professional',
      price: 299,
      status: 'active',
      startDate: '2024-01-15',
      endDate: '2024-02-15'
    },
    {
      id: 2,
      userName: 'فاطمة علي',
      email: '<EMAIL>',
      plan: 'Starter',
      price: 49,
      status: 'active',
      startDate: '2024-01-20',
      endDate: '2024-02-20'
    },
    {
      id: 3,
      userName: 'محمد حسن',
      email: '<EMAIL>',
      plan: 'Enterprise',
      price: 999,
      status: 'expired',
      startDate: '2024-01-10',
      endDate: '2024-02-10'
    },
    {
      id: 4,
      userName: 'سارة أحمد',
      email: '<EMAIL>',
      plan: 'Starter',
      price: 49,
      status: 'active',
      startDate: '2024-01-25',
      endDate: '2024-02-25'
    },
    {
      id: 5,
      userName: 'عبدالله محمود',
      email: '<EMAIL>',
      plan: 'Professional',
      price: 299,
      status: 'cancelled',
      startDate: '2024-01-18',
      endDate: '2024-02-18'
    }
  ];

  const filteredSubscriptions = subscriptions.filter(sub =>
    sub.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sub.plan.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'expired': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'expired': return 'منتهي';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const totalRevenue = subscriptions
    .filter(sub => sub.status === 'active')
    .reduce((sum, sub) => sum + sub.price, 0);

  const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active').length;
  const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired').length;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <MonetizationOn sx={{ mr: 2, fontSize: 40 }} />
            💰 إدارة الاشتراكات
          </Typography>
          <Typography variant="body2" color="text.secondary">
            إدارة شاملة لجميع اشتراكات المنصة والإيرادات
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBack />}
            onClick={() => navigate('/admin/dashboard')}
          >
            العودة للوحة التحكم
          </Button>
          <Button variant="contained" startIcon={<Add />}>
            إضافة اشتراك جديد
          </Button>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <MonetizationOn sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="success.main">
              ${totalRevenue.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الإيرادات الشهرية
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUp sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="primary.main">
              {activeSubscriptions}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              الاشتراكات النشطة
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <People sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="info.main">
              {subscriptions.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الاشتراكات
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <AttachMoney sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" color="warning.main">
              {expiredSubscriptions}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              الاشتراكات المنتهية
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="البحث في الاشتراكات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Subscriptions Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 قائمة الاشتراكات ({filteredSubscriptions.length})
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>المستخدم</TableCell>
                  <TableCell>البريد الإلكتروني</TableCell>
                  <TableCell>الباقة</TableCell>
                  <TableCell>السعر</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ البداية</TableCell>
                  <TableCell>تاريخ الانتهاء</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredSubscriptions.map((subscription) => (
                  <TableRow key={subscription.id}>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {subscription.userName}
                      </Typography>
                    </TableCell>
                    <TableCell>{subscription.email}</TableCell>
                    <TableCell>
                      <Chip 
                        label={subscription.plan} 
                        size="small"
                        color={subscription.plan === 'Enterprise' ? 'warning' : subscription.plan === 'Professional' ? 'primary' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold" color="success.main">
                        ${subscription.price}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getStatusLabel(subscription.status)} 
                        size="small"
                        color={getStatusColor(subscription.status) as any}
                      />
                    </TableCell>
                    <TableCell>{subscription.startDate}</TableCell>
                    <TableCell>{subscription.endDate}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button size="small" variant="outlined">
                          عرض
                        </Button>
                        <Button size="small" variant="outlined">
                          تجديد
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Container>
  );
};

export default SubscriptionAdmin;
