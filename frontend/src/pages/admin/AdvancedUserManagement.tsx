import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usersAPI } from '../../services/api';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Container,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  People,
  Search,
  Add,
  Edit,
  Delete,
  ArrowBack
} from '@mui/icons-material';

const AdvancedUserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    business: 0,
    personal: 0
  });

  // Load users from API
  useEffect(() => {
    loadUsers();
    loadStats();
  }, []);

  // Search users when search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadUsers();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getUsers({ search: searchTerm });
      setUsers(response.users || []);
    } catch (error) {
      console.log('API not available, using mock data');
      // Fallback to mock data
      setUsers(mockUsers);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await usersAPI.getUserStats();
      setStats(response);
    } catch (error) {
      console.log('API not available, using mock stats');
      // Fallback to mock stats
      setStats({
        total: mockUsers.length,
        active: mockUsers.filter(u => u.status === 'active').length,
        business: mockUsers.filter(u => u.userType === 'business').length,
        personal: mockUsers.filter(u => u.userType === 'personal').length
      });
    }
  };

  // Mock user data as fallback
  const mockUsers = [
    {
      id: 1,
      name: 'أحمد محمد',
      email: '<EMAIL>',
      userType: 'business',
      subscription: 'Professional',
      status: 'active',
      joinDate: '2024-01-15'
    },
    {
      id: 2,
      name: 'فاطمة علي',
      email: '<EMAIL>',
      userType: 'personal',
      subscription: 'Starter',
      status: 'active',
      joinDate: '2024-01-20'
    },
    {
      id: 3,
      name: 'محمد حسن',
      email: '<EMAIL>',
      userType: 'business',
      subscription: 'Enterprise',
      status: 'inactive',
      joinDate: '2024-01-10'
    },
    {
      id: 4,
      name: 'سارة أحمد',
      email: '<EMAIL>',
      userType: 'personal',
      subscription: 'Starter',
      status: 'active',
      joinDate: '2024-01-25'
    },
    {
      id: 5,
      name: 'عبدالله محمود',
      email: '<EMAIL>',
      userType: 'business',
      subscription: 'Professional',
      status: 'active',
      joinDate: '2024-01-18'
    }
  ];

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'success' : 'error';
  };

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'business': return 'تجاري';
      case 'personal': return 'شخصي';
      case 'admin': return 'مدير';
      default: return userType;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <People sx={{ mr: 2, fontSize: 40 }} />
            👥 إدارة المستخدمين المتقدمة
          </Typography>
          <Typography variant="body2" color="text.secondary">
            إدارة شاملة لجميع مستخدمي المنصة مع أدوات متقدمة
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBack />}
            onClick={() => navigate('/admin/dashboard')}
          >
            العودة للوحة التحكم
          </Button>
          <Button variant="contained" startIcon={<Add />}>
            إضافة مستخدم جديد
          </Button>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              {stats.total}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي المستخدمين
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="success.main">
              {stats.active}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              المستخدمين النشطين
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="info.main">
              {stats.business}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              الحسابات التجارية
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ flex: 1, minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="warning.main">
              {stats.personal}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              الحسابات الشخصية
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="البحث عن المستخدمين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 قائمة المستخدمين ({filteredUsers.length})
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الاسم</TableCell>
                  <TableCell>البريد الإلكتروني</TableCell>
                  <TableCell>نوع الحساب</TableCell>
                  <TableCell>الاشتراك</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ الانضمام</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {user.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip 
                        label={getUserTypeLabel(user.userType)} 
                        size="small"
                        color={user.userType === 'business' ? 'primary' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={user.subscription} 
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={user.status === 'active' ? 'نشط' : 'غير نشط'} 
                        size="small"
                        color={getStatusColor(user.status) as any}
                      />
                    </TableCell>
                    <TableCell>{user.joinDate}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button size="small" startIcon={<Edit />}>
                          تعديل
                        </Button>
                        <Button size="small" color="error" startIcon={<Delete />}>
                          حذف
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Container>
  );
};

export default AdvancedUserManagement;
