import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Container
} from '@mui/material';
import {
  Memory,
  Storage,
  Speed,
  NetworkCheck,
  CheckCircle,
  Warning,
  Error,
  Info,
  Refresh,
  Download,
  ArrowBack,
  Computer
} from '@mui/icons-material';

const SystemMonitoring: React.FC = () => {
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('1hour');

  const systemMetrics = [
    { title: 'استخدام المعالج', value: 68, status: 'normal', icon: <Speed />, color: 'primary' },
    { title: 'استخدام الذاكرة', value: 74, status: 'warning', icon: <Memory />, color: 'warning' },
    { title: 'استخدام القرص', value: 45, status: 'normal', icon: <Storage />, color: 'success' },
    { title: 'الشبكة', value: 32, status: 'normal', icon: <NetworkCheck />, color: 'info' }
  ];

  const serverStatus = [
    { name: 'خادم الويب 01', status: 'online', uptime: '99.9%', load: 45, location: 'الولايات المتحدة - شرق' },
    { name: 'خادم الويب 02', status: 'online', uptime: '99.8%', load: 62, location: 'الولايات المتحدة - غرب' },
    { name: 'خادم قاعدة البيانات', status: 'online', uptime: '99.9%', load: 38, location: 'الولايات المتحدة - وسط' },
    { name: 'معالج الذكاء الاصطناعي', status: 'maintenance', uptime: '98.5%', load: 0, location: 'أوروبا - غرب' },
    { name: 'بوابة API', status: 'online', uptime: '99.7%', load: 55, location: 'عالمي' }
  ];

  const systemLogs = [
    { timestamp: '2024-01-15 14:30:25', level: 'info', service: 'API', message: 'تم تسجيل دخول المستخدم بنجاح' },
    { timestamp: '2024-01-15 14:29:18', level: 'warning', service: 'قاعدة البيانات', message: 'تم رصد عدد اتصالات عالي' },
    { timestamp: '2024-01-15 14:28:45', level: 'error', service: 'خدمة الذكاء الاصطناعي', message: 'انتهت مهلة التنبؤ بالنموذج' },
    { timestamp: '2024-01-15 14:27:32', level: 'info', service: 'التخزين المؤقت', message: 'تم مسح التخزين المؤقت بنجاح' },
    { timestamp: '2024-01-15 14:26:15', level: 'warning', service: 'التخزين', message: 'استخدام القرص أعلى من 70%' }
  ];

  const apiMetrics = [
    { endpoint: '/api/v1/analysis', requests: '1,245', avgResponse: '245ms', errorRate: '0.2%' },
    { endpoint: '/api/v1/campaigns', requests: '892', avgResponse: '180ms', errorRate: '0.1%' },
    { endpoint: '/api/v1/users', requests: '567', avgResponse: '120ms', errorRate: '0.0%' },
    { endpoint: '/api/v1/reports', requests: '234', avgResponse: '890ms', errorRate: '1.2%' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'maintenance': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'online': return 'متصل';
      case 'maintenance': return 'صيانة';
      case 'offline': return 'غير متصل';
      default: return status;
    }
  };

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      case 'info': return <Info color="info" />;
      default: return <CheckCircle color="success" />;
    }
  };

  const getMetricColor = (value: number) => {
    if (value > 80) return 'error';
    if (value > 60) return 'warning';
    return 'success';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Computer sx={{ mr: 2, fontSize: 40 }} />
            🖥️ مراقبة النظام
          </Typography>
          <Typography variant="body2" color="text.secondary">
            مراقبة شاملة لأداء النظام والخوادم
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBack />}
            onClick={() => navigate('/admin/dashboard')}
          >
            العودة للوحة التحكم
          </Button>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الفترة الزمنية</InputLabel>
            <Select
              value={timeRange}
              label="الفترة الزمنية"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1hour">آخر ساعة</MenuItem>
              <MenuItem value="24hours">آخر 24 ساعة</MenuItem>
              <MenuItem value="7days">آخر 7 أيام</MenuItem>
              <MenuItem value="30days">آخر 30 يوم</MenuItem>
            </Select>
          </FormControl>
          <Button variant="contained" startIcon={<Refresh />}>
            تحديث
          </Button>
        </Box>
      </Box>

      {/* System Metrics */}
      <Box sx={{ display: 'flex', gap: 3, mb: 4, flexWrap: 'wrap' }}>
        {systemMetrics.map((metric, index) => (
          <Card key={index} sx={{ flex: 1, minWidth: 250 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box sx={{ color: `${metric.color}.main`, mr: 1 }}>
                  {metric.icon}
                </Box>
                <Typography variant="h6" component="div">
                  {metric.value}%
                </Typography>
                <Chip
                  label={metric.status === 'normal' ? 'طبيعي' : 'تحذير'}
                  color={metric.status === 'normal' ? 'success' : 'warning'}
                  size="small"
                  sx={{ ml: 'auto' }}
                />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {metric.title}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={metric.value}
                color={getMetricColor(metric.value) as any}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        ))}
      </Box>

      <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', mb: 4 }}>
        {/* Server Status */}
        <Card sx={{ flex: 1, minWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🖥️ حالة الخوادم
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>الخادم</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>وقت التشغيل</TableCell>
                    <TableCell>الحمولة</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {serverStatus.map((server, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {server.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {server.location}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(server.status)}
                          color={getStatusColor(server.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{server.uptime}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LinearProgress
                            variant="determinate"
                            value={server.load}
                            sx={{ width: 60, mr: 1 }}
                            color={getMetricColor(server.load) as any}
                          />
                          <Typography variant="body2">
                            {server.load}%
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* API Performance */}
        <Card sx={{ flex: 1, minWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📊 أداء API
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>النقطة النهائية</TableCell>
                    <TableCell>الطلبات</TableCell>
                    <TableCell>متوسط الاستجابة</TableCell>
                    <TableCell>معدل الأخطاء</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {apiMetrics.map((api, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                          {api.endpoint}
                        </Typography>
                      </TableCell>
                      <TableCell>{api.requests}</TableCell>
                      <TableCell>{api.avgResponse}</TableCell>
                      <TableCell>
                        <Chip
                          label={api.errorRate}
                          color={parseFloat(api.errorRate) > 1 ? 'error' : 'success'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>

      {/* System Logs */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              📋 سجلات النظام الحديثة
            </Typography>
            <Button size="small" startIcon={<Download />}>
              تصدير السجلات
            </Button>
          </Box>
          <List>
            {systemLogs.map((log, index) => (
              <ListItem key={index} divider={index < systemLogs.length - 1}>
                <ListItemIcon>
                  {getLogIcon(log.level)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {log.timestamp}
                      </Typography>
                      <Chip label={log.service} size="small" variant="outlined" />
                      <Typography variant="body2">
                        {log.message}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Container>
  );
};

export default SystemMonitoring;
