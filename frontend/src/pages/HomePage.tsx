import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AOS from 'aos';
import 'aos/dist/aos.css';
import '../styles/enhanced-homepage.css';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  // دالة مساعدة للتنقل
  const handleNavigation = (path: string) => {
    console.log(`محاولة الانتقال إلى: ${path}`);
    try {
      navigate(path);
    } catch (error) {
      console.error(`خطأ في التنقل إلى ${path}:`, error);
      window.location.href = path;
    }
  };

  useEffect(() => {
    // Initialize AOS
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });

    // Header scroll effect
    const handleScroll = () => {
      const header = document.querySelector('.header') as HTMLElement;
      if (header) {
        if (window.scrollY > 100) {
          header.style.background = 'rgba(255, 255, 255, 0.95)';
          header.style.backdropFilter = 'blur(10px)';
        } else {
          header.style.background = '#ffffff';
          header.style.backdropFilter = 'none';
        }
      }
    };

    // Smooth scrolling for navigation links
    const handleSmoothScroll = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const targetElement = document.querySelector(target.getAttribute('href')!);
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    };

    // Add hover effects to cards
    const addHoverEffects = () => {
      document.querySelectorAll('.feature-card, .problem-card, .story-card').forEach(card => {
        const cardElement = card as HTMLElement;
        cardElement.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        cardElement.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
    };

    // Animate numbers
    const animateNumbers = () => {
      const numbers = document.querySelectorAll('.stat-number');
      numbers.forEach(number => {
        const numberElement = number as HTMLElement;
        const target = parseInt(numberElement.textContent?.replace(/[^0-9]/g, '') || '0');
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
          current += increment;
          if (current >= target) {
            current = target;
            clearInterval(timer);
          }
          const suffix = numberElement.textContent?.includes('%') ? '%' :
                        numberElement.textContent?.includes('+') ? '+' : '';
          numberElement.textContent = Math.floor(current) + suffix;
        }, 20);
      });
    };

    // Intersection Observer for number animation
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateNumbers();
          observer.unobserve(entry.target);
        }
      });
    });

    const heroSection = document.querySelector('.hero');
    if (heroSection) {
      observer.observe(heroSection);
    }

    // Add event listeners
    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleSmoothScroll);

    // Add hover effects after component mounts
    setTimeout(addHoverEffects, 100);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleSmoothScroll);
      observer.disconnect();
      AOS.refresh();
    };
  }, []);

  return (
    <div>
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <a href="#" className="logo">
              <i className="fas fa-brain"></i>
              MarketMind
            </a>

            <ul className="nav-links">
              <li><a href="#features">الميزات</a></li>
              <li><a href="#pricing">الأسعار</a></li>
              <li><a href="#success">قصص النجاح</a></li>
              <li><a href="#support">الدعم</a></li>
            </ul>

            <div className="nav-buttons">
              <button
                className="btn btn-outline"
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log("تم النقر على زر تسجيل الدخول");
                  handleNavigation('/login');
                }}
              >
                تسجيل الدخول
              </button>
              <button
                className="btn btn-primary"
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log("تم النقر على زر ابدأ مجاناً");
                  handleNavigation('/signup');
                }}
              >
                ابدأ مجاناً
              </button>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text" data-aos="fade-right">
              <h1>MarketMind<br />ثورة الذكاء الاصطناعي في التسويق الرقمي</h1>
              <p>
                حول بياناتك إلى قرارات ذكية، واكتشف فرص نمو لا محدودة مع أقوى منصة ذكاء اصطناعي للتسويق في المنطقة
              </p>

              <div className="hero-buttons">
                <button
                  className="btn btn-hero btn-white"
                  onClick={() => navigate('/signup')}
                >
                  <i className="fas fa-rocket"></i>
                  ابدأ تجربتك المجانية
                </button>
                <button
                  className="btn btn-hero btn-outline-white"
                  onClick={() => navigate('/signup')}
                >
                  <i className="fas fa-play"></i>
                  شاهد العرض التوضيحي
                </button>
              </div>

              <div className="hero-stats">
                <div className="stat">
                  <span className="stat-number">10,000+</span>
                  <span className="stat-label">شركة تثق بنا</span>
                </div>
                <div className="stat">
                  <span className="stat-number">95%</span>
                  <span className="stat-label">دقة التنبؤ</span>
                </div>
                <div className="stat">
                  <span className="stat-number">40%</span>
                  <span className="stat-label">توفير في الوقت</span>
                </div>
              </div>
            </div>

            <div className="hero-visual" data-aos="fade-left" data-aos-delay="200">
              <div className="dashboard-mockup">
                <div className="mockup-header">
                  <div className="mockup-dot dot-red"></div>
                  <div className="mockup-dot dot-yellow"></div>
                  <div className="mockup-dot dot-green"></div>
                </div>
                <div className="mockup-content">
                  <h3>لوحة التحكم الذكية</h3>
                  <div className="chart-bar" style={{width: '85%'}}></div>
                  <div className="chart-bar" style={{width: '70%'}}></div>
                  <div className="chart-bar" style={{width: '90%'}}></div>
                  <div className="chart-bar" style={{width: '65%'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="problems" id="problems">
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">تحديات التسويق الرقمي التي نحلها</h2>
            <p className="section-subtitle">
              نحن نفهم التحديات التي تواجهها في عالم التسويق الرقمي المعقد، ولدينا الحلول الذكية
            </p>
          </div>

          <div className="problems-grid">
            <div className="problem-card" data-aos="fade-up" data-aos-delay="100">
              <div className="problem-icon">
                <i className="fas fa-database"></i>
              </div>
              <h3 className="problem-title">صعوبة تحليل البيانات الضخمة</h3>
              <p className="problem-description">كميات هائلة من البيانات بدون رؤى واضحة أو قابلة للتنفيذ</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                تحليل ذكي فوري مع رؤى عملية
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="200">
              <div className="problem-icon">
                <i className="fas fa-users"></i>
              </div>
              <h3 className="problem-title">عدم فهم سلوك العملاء</h3>
              <p className="problem-description">صعوبة في فهم ما يريده العملاء وكيف يتفاعلون مع علامتك التجارية</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                رؤى عميقة مدعومة بالذكاء الاصطناعي
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="300">
              <div className="problem-icon">
                <i className="fas fa-money-bill-wave"></i>
              </div>
              <h3 className="problem-title">هدر الميزانيات الإعلانية</h3>
              <p className="problem-description">إنفاق كبير على الإعلانات بدون عائد استثمار واضح أو مُحسن</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                تحسين تلقائي للحملات وزيادة العائد
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="400">
              <div className="problem-icon">
                <i className="fas fa-crystal-ball"></i>
              </div>
              <h3 className="problem-title">صعوبة التنبؤ بالاتجاهات</h3>
              <p className="problem-description">عدم القدرة على توقع التغيرات في السوق والاستعداد لها مسبقاً</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                توقعات دقيقة للسوق والاتجاهات
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="500">
              <div className="problem-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <h3 className="problem-title">ضعف معدلات التحويل</h3>
              <p className="problem-description">زوار كثيرون لكن عدد قليل منهم يتحول إلى عملاء فعليين</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                تخصيص ذكي للمحتوى وزيادة التحويل
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="600">
              <div className="problem-icon">
                <i className="fas fa-calculator"></i>
              </div>
              <h3 className="problem-title">عدم قياس عائد الاستثمار</h3>
              <p className="problem-description">صعوبة في تتبع وقياس فعالية الحملات التسويقية المختلفة</p>
              <div className="solution">
                <i className="fas fa-arrow-left"></i>
                تقارير مفصلة وواضحة للعائد
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features" id="features">
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">قوة الذكاء الاصطناعي في خدمة تسويقك</h2>
            <p className="section-subtitle">
              اكتشف مجموعة شاملة من الأدوات الذكية التي ستغير طريقة عملك في التسويق
            </p>
          </div>

          <div className="features-grid">
            <div className="feature-card" data-aos="zoom-in" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-brain"></i>
              </div>
              <h3 className="feature-title">تحليل سلوك العملاء بالذكاء الاصطناعي</h3>
              <p className="feature-description">
                فهم عميق لسلوك عملائك وتفضيلاتهم من خلال تحليل متقدم للبيانات
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-chart-pie"></i>
              </div>
              <h3 className="feature-title">لوحات معلومات تفاعلية</h3>
              <p className="feature-description">
                مراقبة أداء حملاتك في الوقت الفعلي مع رسوم بيانية تفاعلية وسهلة الفهم
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-bullseye"></i>
              </div>
              <h3 className="feature-title">تجزئة العملاء الذكية</h3>
              <p className="feature-description">
                تقسيم عملائك إلى شرائح دقيقة لاستهداف أفضل وحملات أكثر فعالية
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="400">
              <div className="feature-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <h3 className="feature-title">التنبؤ بالمبيعات والاتجاهات</h3>
              <p className="feature-description">
                توقعات دقيقة للمبيعات المستقبلية واتجاهات السوق لتخطيط أفضل
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="500">
              <div className="feature-icon">
                <i className="fas fa-robot"></i>
              </div>
              <h3 className="feature-title">أتمتة الحملات التسويقية</h3>
              <p className="feature-description">
                تشغيل وتحسين حملاتك تلقائياً لتوفير الوقت وزيادة الفعالية
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="600">
              <div className="feature-icon">
                <i className="fas fa-heart"></i>
              </div>
              <h3 className="feature-title">تحليل المشاعر والآراء</h3>
              <p className="feature-description">
                فهم مشاعر عملائك تجاه علامتك التجارية من خلال تحليل التعليقات والمراجعات
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="700">
              <div className="feature-icon">
                <i className="fas fa-search"></i>
              </div>
              <h3 className="feature-title">تحليل المنافسين والسوق</h3>
              <p className="feature-description">
                مراقبة منافسيك وتحليل استراتيجياتهم للبقاء في المقدمة
              </p>
            </div>

            <div className="feature-card" data-aos="zoom-in" data-aos-delay="800">
              <div className="feature-icon">
                <i className="fas fa-mobile-alt"></i>
              </div>
              <h3 className="feature-title">تطبيق محمول متقدم</h3>
              <p className="feature-description">
                إدارة حملاتك ومراقبة أدائها من أي مكان عبر تطبيقنا المحمول
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="pricing" id="pricing">
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">اختر الباقة المناسبة لاحتياجاتك</h2>
            <p className="section-subtitle">
              باقات مرنة تناسب جميع أحجام الأعمال، من الأفراد إلى المؤسسات الكبيرة
            </p>
          </div>

          <div className="pricing-grid">
            <div className="pricing-card" data-aos="fade-up" data-aos-delay="100">
              <h3 className="plan-name">الباقة الشخصية</h3>
              <div className="plan-price">مجانية</div>
              <div className="plan-period">للأفراد والمشاريع الصغيرة</div>
              <ul className="plan-features">
                <li><i className="fas fa-check"></i> 100 تحليل شهرياً</li>
                <li><i className="fas fa-check"></i> لوحة معلومات أساسية</li>
                <li><i className="fas fa-check"></i> تقارير أسبوعية</li>
                <li><i className="fas fa-check"></i> دعم عبر البريد الإلكتروني</li>
                <li><i className="fas fa-check"></i> تحليل أساسي للعملاء</li>
              </ul>
              <button
                className="btn btn-outline"
                onClick={() => navigate('/signup')}
              >
                ابدأ مجاناً
              </button>
            </div>

            <div className="pricing-card featured" data-aos="fade-up" data-aos-delay="200">
              <h3 className="plan-name">الباقة الاحترافية</h3>
              <div className="plan-price">$99</div>
              <div className="plan-period">شهرياً - للشركات الصغيرة والمتوسطة</div>
              <ul className="plan-features">
                <li><i className="fas fa-check"></i> تحليلات غير محدودة</li>
                <li><i className="fas fa-check"></i> جميع ميزات الذكاء الاصطناعي</li>
                <li><i className="fas fa-check"></i> تقارير يومية مخصصة</li>
                <li><i className="fas fa-check"></i> دعم أولوية 24/7</li>
                <li><i className="fas fa-check"></i> تكامل مع 50+ أداة</li>
                <li><i className="fas fa-check"></i> تحليل متقدم للمنافسين</li>
              </ul>
              <button
                className="btn btn-primary"
                onClick={() => navigate('/signup')}
              >
                ابدأ التجربة المجانية
              </button>
            </div>

            <div className="pricing-card" data-aos="fade-up" data-aos-delay="300">
              <h3 className="plan-name">باقة المؤسسات</h3>
              <div className="plan-price">$499</div>
              <div className="plan-period">شهرياً - للشركات الكبيرة والمؤسسات</div>
              <ul className="plan-features">
                <li><i className="fas fa-check"></i> فرق غير محدودة</li>
                <li><i className="fas fa-check"></i> ميزات متقدمة حصرية</li>
                <li><i className="fas fa-check"></i> مدير حساب مخصص</li>
                <li><i className="fas fa-check"></i> تدريب وورش عمل</li>
                <li><i className="fas fa-check"></i> تخصيص كامل للمنصة</li>
                <li><i className="fas fa-check"></i> SLA مضمون 99.9%</li>
              </ul>
              <button
                className="btn btn-outline"
                onClick={() => navigate('/contact')}
              >
                تواصل معنا
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="success-stories" id="success">
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">قصص نجاح عملائنا</h2>
            <p className="section-subtitle">
              اكتشف كيف ساعدت MarketMind الشركات على تحقيق نتائج استثنائية
            </p>
          </div>

          <div className="stories-grid">
            <div className="story-card" data-aos="fade-up" data-aos-delay="100">
              <div className="story-result">+150%</div>
              <h3 className="story-title">زيادة المبيعات</h3>
              <p className="story-description">
                شركة تجارة إلكترونية تمكنت من زيادة مبيعاتها بنسبة 150% خلال 6 أشهر باستخدام تحليلات MarketMind الذكية وتحسين استراتيجيات الاستهداف.
              </p>
              <div className="story-company">متجر الأناقة الرقمي</div>
            </div>

            <div className="story-card" data-aos="fade-up" data-aos-delay="200">
              <div className="story-result">+200%</div>
              <h3 className="story-title">تحسين عائد الاستثمار</h3>
              <p className="story-description">
                وكالة تسويق رقمي حققت تحسناً بنسبة 200% في عائد الاستثمار لعملائها من خلال استخدام أدوات التحليل المتقدمة وأتمتة الحملات.
              </p>
              <div className="story-company">وكالة النجاح الرقمي</div>
            </div>

            <div className="story-card" data-aos="fade-up" data-aos-delay="300">
              <div className="story-result">-60%</div>
              <h3 className="story-title">تقليل تكلفة اكتساب العملاء</h3>
              <p className="story-description">
                شركة تقنية ناشئة تمكنت من تقليل تكلفة اكتساب العملاء بنسبة 60% من خلال تحسين استهداف الحملات وفهم سلوك العملاء بشكل أفضل.
              </p>
              <div className="story-company">تك إنوفيشن</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>ابدأ رحلتك نحو التسويق الذكي اليوم</h2>
            <p>انضم إلى آلاف الشركات التي تثق في MarketMind لتحقيق نتائج استثنائية في التسويق الرقمي</p>

            <form className="cta-form" onSubmit={(e) => {
              e.preventDefault();
              const button = e.currentTarget.querySelector('.cta-button') as HTMLButtonElement;
              const originalText = button.innerHTML;

              button.innerHTML = '<div className="loading"></div> جاري التسجيل...';
              button.disabled = true;

              setTimeout(() => {
                button.innerHTML = '<i className="fas fa-check"></i> تم التسجيل بنجاح!';
                setTimeout(() => {
                  button.innerHTML = originalText;
                  button.disabled = false;
                  e.currentTarget.reset();
                  navigate('/signup');
                }, 2000);
              }, 2000);
            }}>
              <input
                type="email"
                className="cta-input"
                placeholder="أدخل بريدك الإلكتروني"
                required
              />
              <button type="submit" className="cta-button">
                ابدأ مجاناً
                <i className="fas fa-arrow-left"></i>
              </button>
            </form>

            <p style={{ marginTop: '1rem', fontSize: '0.9rem', opacity: 0.8 }}>
              ✓ تجربة مجانية لمدة 14 يوم &nbsp;&nbsp; ✓ لا حاجة لبطاقة ائتمان &nbsp;&nbsp; ✓ إلغاء في أي وقت
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة. نحول بياناتك إلى قرارات ذكية ونتائج استثنائية.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
                <li><a href="/api">API</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/careers">الوظائف</a></li>
                <li><a href="/blog">المدونة</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
                <li><a href="/support">المجتمع</a></li>
                <li><a href="/support">الحالة</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
