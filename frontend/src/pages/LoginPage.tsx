import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import '../styles/enhanced-homepage.css';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading, isAuthenticated } = useAuthStore();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isFormValid, setIsFormValid] = useState(false);
  const [showDemoAccounts, setShowDemoAccounts] = useState(false);

  // Get greeting based on time
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  };

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }

    // Add keyboard shortcuts
    const handleKeyPress = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Enter to submit form
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (isFormValid && !isLoading) {
          document.querySelector('form')?.requestSubmit();
        }
      }

      // Escape to clear form
      if (e.key === 'Escape') {
        setFormData({ email: '', password: '', rememberMe: false });
        setError('');
        setSuccess('');
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isFormValid, isLoading]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Validate password strength
  const validatePassword = (password: string) => {
    return password.length >= 6;
  };

  // Check form validity
  useEffect(() => {
    const isEmailValid = validateEmail(formData.email);
    const isPasswordValid = validatePassword(formData.password);
    setIsFormValid(isEmailValid && isPasswordValid);

    // Clear errors when user starts typing
    if (formData.email && !isEmailValid) {
      setEmailError('يرجى إدخال بريد إلكتروني صحيح');
    } else {
      setEmailError('');
    }

    if (formData.password && !isPasswordValid) {
      setPasswordError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    } else {
      setPasswordError('');
    }
  }, [formData.email, formData.password]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear general error when user starts typing
    if (error) setError('');
  };

  // Quick login with demo accounts
  const quickLogin = async (email: string, password: string) => {
    setFormData({ email, password, rememberMe: false });
    setError('');

    // Show different messages based on account type
    let accountType = 'تجريبي';
    if (email.includes('business')) accountType = 'أعمال';
    else if (email.includes('admin')) accountType = 'مدير';
    else if (email.includes('personal')) accountType = 'شخصي';

    setSuccess(`جاري تسجيل الدخول بحساب ${accountType}...`);

    try {
      const result = await login(email, password);
      setSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');

      // Navigate based on user type with a longer delay to ensure proper loading
      setTimeout(() => {
        console.log('Quick login navigating to:', result.redirectUrl);
        navigate(result.redirectUrl);
      }, 1500);

    } catch (error: any) {
      console.error('Quick login error:', error);
      setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
      setSuccess('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate form
    if (!formData.email || !formData.password) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (!validateEmail(formData.email)) {
      setError('يرجى إدخال بريد إلكتروني صحيح');
      return;
    }

    if (!validatePassword(formData.password)) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    try {
      setSuccess('جاري التحقق من البيانات...');
      const result = await login(formData.email, formData.password);
      setSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');

      // Store remember me preference
      if (formData.rememberMe) {
        localStorage.setItem('remember_me', 'true');
      }

      // Navigate based on user type with a longer delay to ensure proper loading
      setTimeout(() => {
        console.log('Navigating to:', result.redirectUrl);
        navigate(result.redirectUrl);
      }, 1500);

    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
      setSuccess('');
    }
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div className="login-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/pricing">الأسعار</a></li>
              <li><a href="/about">من نحن</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/signup" className="btn btn-outline">إنشاء حساب</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Login Section */}
      <section className="auth-section" style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
        padding: '2rem 0'
      }}>
        <div className="container">
          <div style={{
            display: 'grid',
            gridTemplateColumns: window.innerWidth > 768 ? '1fr 1fr' : '1fr',
            gap: window.innerWidth > 768 ? '4rem' : '2rem',
            alignItems: 'center',
            maxWidth: '1200px',
            margin: '0 auto',
            padding: '0 1rem'
          }}>
            
            {/* Left Side - Welcome Content */}
            <div className="auth-content" data-aos="fade-right">
              <h1 style={{ 
                color: 'white', 
                fontSize: '2.5rem', 
                marginBottom: '1.5rem',
                fontWeight: 'bold'
              }}>
                مرحباً بعودتك!
              </h1>
              <p style={{ 
                color: 'rgba(255,255,255,0.9)', 
                fontSize: '1.1rem',
                lineHeight: '1.6',
                marginBottom: '2rem'
              }}>
                سجل دخولك للوصول إلى لوحة التحكم الخاصة بك واستكمل رحلتك في تحليل البيانات 
                التسويقية وتحسين أداء حملاتك.
              </p>
              
              <div className="auth-features" style={{ color: 'white' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-check-circle" style={{ color: 'var(--accent-color)' }}></i>
                  <span>تحليلات متقدمة في الوقت الفعلي</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-check-circle" style={{ color: 'var(--accent-color)' }}></i>
                  <span>تقارير مخصصة وتفاعلية</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-check-circle" style={{ color: 'var(--accent-color)' }}></i>
                  <span>ذكاء اصطناعي متطور</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <i className="fas fa-check-circle" style={{ color: 'var(--accent-color)' }}></i>
                  <span>دعم فني على مدار الساعة</span>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="auth-form-container" data-aos="fade-left">
              <div style={{
                background: 'white',
                padding: window.innerWidth > 768 ? '3rem' : '2rem',
                borderRadius: '20px',
                boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                backdropFilter: 'blur(10px)',
                position: 'relative',
                overflow: 'hidden'
              }}>
                {/* Decorative background elements */}
                <div style={{
                  position: 'absolute',
                  top: '-50px',
                  right: '-50px',
                  width: '100px',
                  height: '100px',
                  background: 'linear-gradient(45deg, var(--primary-color), var(--secondary-color))',
                  borderRadius: '50%',
                  opacity: 0.1,
                  zIndex: 0
                }}></div>
                <div style={{
                  position: 'absolute',
                  bottom: '-30px',
                  left: '-30px',
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(45deg, var(--accent-color), #fbbf24)',
                  borderRadius: '50%',
                  opacity: 0.1,
                  zIndex: 0
                }}></div>
                <div style={{ textAlign: 'center', marginBottom: '2rem', position: 'relative', zIndex: 1 }}>
                  <div style={{
                    width: '60px',
                    height: '60px',
                    background: 'linear-gradient(135deg, var(--primary-color), var(--secondary-color))',
                    borderRadius: '50%',
                    margin: '0 auto 1rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 8px 20px rgba(30, 58, 138, 0.3)'
                  }}>
                    <i className="fas fa-user" style={{ color: 'white', fontSize: '1.5rem' }}></i>
                  </div>
                  <h2 style={{
                    color: 'var(--primary-color)',
                    marginBottom: '0.5rem',
                    fontSize: '1.75rem',
                    fontWeight: 'bold'
                  }}>
                    {getGreeting()}، مرحباً بعودتك
                  </h2>
                  <p style={{ color: 'var(--text-light)', fontSize: '0.95rem' }}>
                    أدخل بياناتك للوصول إلى حسابك واستكمال رحلتك
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="auth-form" style={{ position: 'relative', zIndex: 1 }}>
                  {/* Success Message */}
                  {success && (
                    <div style={{
                      background: '#d1fae5',
                      border: '1px solid #a7f3d0',
                      color: '#065f46',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      marginBottom: '1.5rem',
                      fontSize: '0.9rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      <i className="fas fa-check-circle"></i>
                      {success}
                    </div>
                  )}

                  {/* Error Message */}
                  {error && (
                    <div style={{
                      background: '#fee2e2',
                      border: '1px solid #fecaca',
                      color: '#dc2626',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      marginBottom: '1.5rem',
                      fontSize: '0.9rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      <i className="fas fa-exclamation-circle"></i>
                      {error}
                    </div>
                  )}

                  <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                    <label htmlFor="email" style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontWeight: '600',
                      color: 'var(--text-dark)'
                    }}>
                      البريد الإلكتروني
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="أدخل بريدك الإلكتروني"
                        style={{
                          width: '100%',
                          padding: '1rem 3rem 1rem 1rem',
                          border: `2px solid ${emailError ? '#ef4444' : formData.email && validateEmail(formData.email) ? '#10b981' : '#e5e7eb'}`,
                          borderRadius: '12px',
                          fontSize: '1rem',
                          transition: 'all 0.3s ease',
                          direction: 'ltr',
                          background: '#fafafa',
                          outline: 'none'
                        }}
                        onFocus={(e) => {
                          e.target.style.background = 'white';
                          e.target.style.transform = 'translateY(-2px)';
                          e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.background = '#fafafa';
                          e.target.style.transform = 'translateY(0)';
                          e.target.style.boxShadow = 'none';
                        }}
                      />
                      <div style={{
                        position: 'absolute',
                        left: '1rem',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: emailError ? '#ef4444' : formData.email && validateEmail(formData.email) ? '#10b981' : '#9ca3af'
                      }}>
                        <i className={`fas ${emailError ? 'fa-exclamation-circle' : formData.email && validateEmail(formData.email) ? 'fa-check-circle' : 'fa-envelope'}`}></i>
                      </div>
                    </div>
                    {emailError && (
                      <div style={{ color: '#ef4444', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        {emailError}
                      </div>
                    )}
                  </div>

                  <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                    <label htmlFor="password" style={{
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontWeight: '600',
                      color: 'var(--text-dark)'
                    }}>
                      كلمة المرور
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        placeholder="أدخل كلمة المرور"
                        style={{
                          width: '100%',
                          padding: '1rem 3rem 1rem 3rem',
                          border: `2px solid ${passwordError ? '#ef4444' : formData.password && validatePassword(formData.password) ? '#10b981' : '#e5e7eb'}`,
                          borderRadius: '12px',
                          fontSize: '1rem',
                          transition: 'all 0.3s ease',
                          direction: 'ltr',
                          background: '#fafafa',
                          outline: 'none'
                        }}
                        onFocus={(e) => {
                          e.target.style.background = 'white';
                          e.target.style.transform = 'translateY(-2px)';
                          e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.background = '#fafafa';
                          e.target.style.transform = 'translateY(0)';
                          e.target.style.boxShadow = 'none';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        style={{
                          position: 'absolute',
                          left: '1rem',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: 'var(--text-light)',
                          cursor: 'pointer',
                          zIndex: 2
                        }}
                      >
                        <i className={showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'}></i>
                      </button>
                      <div style={{
                        position: 'absolute',
                        right: '1rem',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: passwordError ? '#ef4444' : formData.password && validatePassword(formData.password) ? '#10b981' : '#9ca3af'
                      }}>
                        <i className={`fas ${passwordError ? 'fa-exclamation-circle' : formData.password && validatePassword(formData.password) ? 'fa-check-circle' : 'fa-lock'}`}></i>
                      </div>
                    </div>
                    {passwordError && (
                      <div style={{ color: '#ef4444', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        {passwordError}
                      </div>
                    )}
                    {formData.password && !passwordError && (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        marginTop: '0.5rem',
                        fontSize: '0.8rem'
                      }}>
                        <div style={{
                          flex: 1,
                          height: '4px',
                          background: '#e5e7eb',
                          borderRadius: '2px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            height: '100%',
                            width: `${Math.min((formData.password.length / 8) * 100, 100)}%`,
                            background: formData.password.length < 6 ? '#ef4444' :
                                       formData.password.length < 8 ? '#f59e0b' : '#10b981',
                            transition: 'all 0.3s ease'
                          }}></div>
                        </div>
                        <span style={{
                          color: formData.password.length < 6 ? '#ef4444' :
                                 formData.password.length < 8 ? '#f59e0b' : '#10b981'
                        }}>
                          {formData.password.length < 6 ? 'ضعيفة' :
                           formData.password.length < 8 ? 'متوسطة' : 'قوية'}
                        </span>
                      </div>
                    )}
                  </div>

                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '2rem'
                  }}>
                    <label style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '0.5rem',
                      cursor: 'pointer'
                    }}>
                      <input
                        type="checkbox"
                        name="rememberMe"
                        checked={formData.rememberMe}
                        onChange={handleInputChange}
                        style={{ marginLeft: '0.5rem' }}
                      />
                      <span style={{ fontSize: '0.9rem', color: 'var(--text-dark)' }}>
                        تذكرني
                      </span>
                    </label>
                    
                    <a href="/forgot-password" style={{ 
                      color: 'var(--primary-color)',
                      textDecoration: 'none',
                      fontSize: '0.9rem'
                    }}>
                      نسيت كلمة المرور؟
                    </a>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading || !isFormValid}
                    style={{
                      width: '100%',
                      padding: '1rem',
                      background: isLoading || !isFormValid ? '#9ca3af' : 'var(--primary-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      cursor: isLoading || !isFormValid ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      marginBottom: '1.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem',
                      opacity: isLoading || !isFormValid ? 0.7 : 1
                    }}
                    onMouseEnter={(e) => {
                      if (!isLoading && isFormValid) {
                        e.currentTarget.style.background = 'var(--secondary-color)';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isLoading && isFormValid) {
                        e.currentTarget.style.background = 'var(--primary-color)';
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = 'none';
                      }
                    }}
                  >
                    {isLoading && (
                      <div style={{
                        width: '20px',
                        height: '20px',
                        border: '2px solid transparent',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                    )}
                    {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                  </button>

                  <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                    <span style={{ color: 'var(--text-light)' }}>
                      ليس لديك حساب؟{' '}
                    </span>
                    <a href="/signup" style={{
                      color: 'var(--primary-color)',
                      textDecoration: 'none',
                      fontWeight: '600'
                    }}>
                      إنشاء حساب جديد
                    </a>
                  </div>

                  {/* Keyboard Shortcuts Info */}
                  <div style={{
                    background: '#f8fafc',
                    border: '1px solid #e2e8f0',
                    borderRadius: '6px',
                    padding: '0.75rem',
                    fontSize: '0.75rem',
                    color: 'var(--text-light)',
                    marginBottom: '1rem'
                  }}>
                    <div style={{ marginBottom: '0.25rem' }}>
                      <strong>نصائح سريعة:</strong>
                    </div>
                    <div>• Ctrl/Cmd + Enter: تسجيل دخول سريع</div>
                    <div>• Escape: مسح النموذج</div>
                    <div>• استخدم الحسابات التجريبية للاختبار</div>
                  </div>

                  {/* Demo Accounts Info */}
                  <div style={{
                    background: '#f0f9ff',
                    border: '1px solid #0ea5e9',
                    borderRadius: '8px',
                    padding: '1rem',
                    fontSize: '0.85rem'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      <h4 style={{ color: 'var(--primary-color)', fontSize: '0.9rem', margin: 0 }}>
                        حسابات تجريبية للاختبار:
                      </h4>
                      <button
                        type="button"
                        onClick={() => setShowDemoAccounts(!showDemoAccounts)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: 'var(--primary-color)',
                          cursor: 'pointer',
                          fontSize: '0.8rem'
                        }}
                      >
                        {showDemoAccounts ? 'إخفاء' : 'عرض'}
                      </button>
                    </div>

                    {showDemoAccounts && (
                      <div style={{ color: 'var(--text-dark)', lineHeight: '1.4' }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.5rem 0',
                          borderBottom: '1px solid #e0f2fe'
                        }}>
                          <span><strong>مدير:</strong> <EMAIL></span>
                          <button
                            type="button"
                            onClick={() => quickLogin('<EMAIL>', 'password123')}
                            style={{
                              background: 'var(--primary-color)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              padding: '0.25rem 0.5rem',
                              fontSize: '0.7rem',
                              cursor: 'pointer'
                            }}
                          >
                            استخدام
                          </button>
                        </div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.5rem 0',
                          borderBottom: '1px solid #e0f2fe'
                        }}>
                          <span><strong>تجاري:</strong> <EMAIL></span>
                          <button
                            type="button"
                            onClick={() => quickLogin('<EMAIL>', 'password123')}
                            style={{
                              background: 'var(--primary-color)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              padding: '0.25rem 0.5rem',
                              fontSize: '0.7rem',
                              cursor: 'pointer'
                            }}
                          >
                            استخدام
                          </button>
                        </div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.5rem 0'
                        }}>
                          <span><strong>شخصي:</strong> <EMAIL></span>
                          <button
                            type="button"
                            onClick={() => quickLogin('<EMAIL>', 'password123')}
                            style={{
                              background: 'var(--primary-color)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              padding: '0.25rem 0.5rem',
                              fontSize: '0.7rem',
                              cursor: 'pointer'
                            }}
                          >
                            استخدام
                          </button>
                        </div>
                        
                        {/* Help message */}
                        <div style={{
                          marginTop: '1rem',
                          padding: '0.75rem',
                          background: '#f0f9ff',
                          borderRadius: '8px',
                          border: '1px solid #0ea5e9',
                          fontSize: '0.8rem',
                          color: '#0c4a6e'
                        }}>
                          <strong>ملاحظة:</strong> كلمة المرور لجميع الحسابات هي: <code>password123</code>
                          <br />
                          <strong>في حالة وجود مشاكل:</strong> تأكد من تشغيل الخادم الخلفي على المنفذ 8000
                          <br />
                          <strong>إذا ظهرت صفحة سوداء:</strong> انتظر قليلاً أو اضغط F5 لتحديث الصفحة
                        </div>
                      </div>
                    )}
                  </div>
                </form>

                {/* Social Login */}
                <div style={{ marginTop: '2rem' }}>
                  <div style={{ 
                    textAlign: 'center', 
                    marginBottom: '1rem',
                    position: 'relative'
                  }}>
                    <span style={{ 
                      background: 'white',
                      padding: '0 1rem',
                      color: 'var(--text-light)',
                      fontSize: '0.9rem'
                    }}>
                      أو سجل دخولك باستخدام
                    </span>
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: 0,
                      right: 0,
                      height: '1px',
                      background: '#e5e7eb',
                      zIndex: -1
                    }}></div>
                  </div>

                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: '1fr 1fr', 
                    gap: '1rem' 
                  }}>
                    <button style={{
                      padding: '0.75rem',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      background: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}>
                      <i className="fab fa-google" style={{ color: '#db4437' }}></i>
                      Google
                    </button>
                    
                    <button style={{
                      padding: '0.75rem',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      background: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}>
                      <i className="fab fa-microsoft" style={{ color: '#00a1f1' }}></i>
                      Microsoft
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer" style={{ background: 'var(--text-dark)' }}>
        <div className="container">
          <div style={{ 
            textAlign: 'center', 
            padding: '2rem 0',
            color: 'white'
          }}>
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
            <div style={{ marginTop: '1rem' }}>
              <a href="/privacy" style={{ color: 'rgba(255,255,255,0.7)', marginLeft: '2rem' }}>
                سياسة الخصوصية
              </a>
              <a href="/terms" style={{ color: 'rgba(255,255,255,0.7)' }}>
                الشروط والأحكام
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
    </>
  );
};

export default LoginPage;
