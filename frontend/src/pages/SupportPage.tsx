import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const SupportPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const supportCategories = [
    { id: 'all', name: 'جميع الفئات', icon: 'fas fa-th-large' },
    { id: 'getting-started', name: 'البدء', icon: 'fas fa-play-circle' },
    { id: 'features', name: 'الميزات', icon: 'fas fa-star' },
    { id: 'billing', name: 'الفواتير', icon: 'fas fa-credit-card' },
    { id: 'technical', name: 'الدعم الفني', icon: 'fas fa-cog' },
    { id: 'api', name: 'API', icon: 'fas fa-code' }
  ];

  const helpArticles = [
    {
      id: 1,
      title: 'كيفية إنشاء حساب جديد',
      category: 'getting-started',
      description: 'دليل شامل لإنشاء حساب جديد والبدء مع MarketMind',
      readTime: '5 دقائق'
    },
    {
      id: 2,
      title: 'ربط منصات التواصل الاجتماعي',
      category: 'features',
      description: 'تعلم كيفية ربط حساباتك على منصات التواصل الاجتماعي',
      readTime: '8 دقائق'
    },
    {
      id: 3,
      title: 'فهم تقارير التحليلات',
      category: 'features',
      description: 'شرح مفصل لجميع التقارير والمقاييس المتاحة',
      readTime: '12 دقيقة'
    },
    {
      id: 4,
      title: 'إدارة الاشتراكات والفواتير',
      category: 'billing',
      description: 'كيفية إدارة اشتراكك وعرض الفواتير',
      readTime: '6 دقائق'
    },
    {
      id: 5,
      title: 'استخدام API للمطورين',
      category: 'api',
      description: 'دليل المطورين لاستخدام API الخاص بـ MarketMind',
      readTime: '15 دقيقة'
    },
    {
      id: 6,
      title: 'حل مشاكل الاتصال',
      category: 'technical',
      description: 'خطوات حل المشاكل الشائعة في الاتصال',
      readTime: '10 دقائق'
    }
  ];

  const filteredArticles = helpArticles.filter(article => {
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="support-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
              <li><a href="/support">الدعم</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">مركز المساعدة</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                ابحث عن إجابات لأسئلتك أو تواصل مع فريق الدعم للحصول على المساعدة
              </p>
              
              {/* Search Bar */}
              <div className="search-bar" data-aos="fade-up" data-aos-delay="300" style={{ marginTop: '2rem' }}>
                <div style={{ position: 'relative', maxWidth: '500px', margin: '0 auto' }}>
                  <input
                    type="text"
                    placeholder="ابحث في مركز المساعدة..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '1rem 3rem 1rem 1rem',
                      borderRadius: '12px',
                      border: '2px solid rgba(255,255,255,0.3)',
                      background: 'rgba(255,255,255,0.1)',
                      color: 'white',
                      fontSize: '1.1rem',
                      backdropFilter: 'blur(10px)'
                    }}
                  />
                  <i className="fas fa-search" style={{
                    position: 'absolute',
                    right: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(255,255,255,0.7)'
                  }}></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Help Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">مساعدة سريعة</h2>
            <p className="section-subtitle">
              طرق مختلفة للحصول على المساعدة التي تحتاجها
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-comments"></i>
              </div>
              <h3>الدردشة المباشرة</h3>
              <p>تحدث مع فريق الدعم مباشرة عبر الدردشة المباشرة</p>
              <a href="#" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                بدء المحادثة
              </a>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>البريد الإلكتروني</h3>
              <p>أرسل لنا بريداً إلكترونياً وسنرد عليك خلال 24 ساعة</p>
              <a href="mailto:<EMAIL>" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                إرسال بريد
              </a>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>الهاتف</h3>
              <p>اتصل بنا مباشرة للحصول على دعم فوري</p>
              <a href="tel:+966111234567" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                اتصل بنا
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">تصفح حسب الفئة</h2>
          </div>
          
          <div className="category-filters" data-aos="fade-up" data-aos-delay="200" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem'
          }}>
            {supportCategories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: selectedCategory === category.id ? '2px solid var(--primary-color)' : '2px solid transparent',
                  background: selectedCategory === category.id ? 'var(--primary-color)' : 'white',
                  color: selectedCategory === category.id ? 'white' : 'var(--text-dark)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                <i className={category.icon}></i>
                {category.name}
              </button>
            ))}
          </div>

          <div className="help-articles" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: '2rem'
          }}>
            {filteredArticles.map(article => (
              <div key={article.id} className="article-card" data-aos="fade-up" style={{
                background: 'white',
                padding: '2rem',
                borderRadius: '12px',
                boxShadow: 'var(--shadow-soft)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                cursor: 'pointer'
              }}>
                <h3 style={{ marginBottom: '1rem', color: 'var(--primary-color)' }}>
                  {article.title}
                </h3>
                <p style={{ marginBottom: '1rem', color: 'var(--text-light)' }}>
                  {article.description}
                </p>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  fontSize: '0.9rem',
                  color: 'var(--text-light)'
                }}>
                  <span>
                    <i className="fas fa-clock"></i> {article.readTime}
                  </span>
                  <span style={{ color: 'var(--primary-color)' }}>
                    اقرأ المزيد <i className="fas fa-arrow-left"></i>
                  </span>
                </div>
              </div>
            ))}
          </div>

          {filteredArticles.length === 0 && (
            <div style={{ textAlign: 'center', padding: '3rem' }}>
              <i className="fas fa-search" style={{ fontSize: '3rem', color: 'var(--text-light)', marginBottom: '1rem' }}></i>
              <h3>لم نجد نتائج</h3>
              <p>جرب البحث بكلمات مختلفة أو تصفح الفئات المختلفة</p>
            </div>
          )}
        </div>
      </section>

      {/* Status Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">حالة النظام</h2>
            <p className="section-subtitle">
              تحقق من حالة جميع خدمات MarketMind
            </p>
          </div>
          
          <div className="status-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem'
          }}>
            <div className="status-item" data-aos="fade-up" data-aos-delay="100" style={{
              background: '#f0f9ff',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '2px solid #22c55e'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  background: '#22c55e'
                }}></div>
                <h4>منصة التحليلات</h4>
              </div>
              <p style={{ color: '#22c55e', fontWeight: '600' }}>تعمل بشكل طبيعي</p>
            </div>

            <div className="status-item" data-aos="fade-up" data-aos-delay="200" style={{
              background: '#f0f9ff',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '2px solid #22c55e'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  background: '#22c55e'
                }}></div>
                <h4>API</h4>
              </div>
              <p style={{ color: '#22c55e', fontWeight: '600' }}>تعمل بشكل طبيعي</p>
            </div>

            <div className="status-item" data-aos="fade-up" data-aos-delay="300" style={{
              background: '#f0f9ff',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '2px solid #22c55e'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  background: '#22c55e'
                }}></div>
                <h4>التكاملات</h4>
              </div>
              <p style={{ color: '#22c55e', fontWeight: '600' }}>تعمل بشكل طبيعي</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/#features">الميزات</a></li>
                <li><a href="/#pricing">الأسعار</a></li>
                <li><a href="/#features">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SupportPage;
