import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const PricingPage: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const plans = [
    {
      name: 'المبتدئ',
      description: 'مثالي للشركات الناشئة والفرق الصغيرة',
      monthlyPrice: 99,
      yearlyPrice: 990,
      features: [
        'تحليلات أساسية',
        'حتى 10,000 زائر شهرياً',
        'تقارير أساسية',
        'دعم بريد إلكتروني',
        '3 تكاملات',
        'لوحة معلومات واحدة'
      ],
      popular: false,
      color: 'var(--primary-color)'
    },
    {
      name: 'المحترف',
      description: 'الأفضل للشركات المتنامية والفرق المتوسطة',
      monthlyPrice: 299,
      yearlyPrice: 2990,
      features: [
        'جميع ميزات المبتدئ',
        'حتى 100,000 زائر شهرياً',
        'ذكاء اصطناعي متقدم',
        'تحليل تنبؤي',
        'دعم 24/7',
        'تكاملات غير محدودة',
        'تقارير مخصصة',
        'أتمتة التسويق'
      ],
      popular: true,
      color: 'var(--accent-color)'
    },
    {
      name: 'المؤسسي',
      description: 'للشركات الكبيرة والمؤسسات',
      monthlyPrice: 999,
      yearlyPrice: 9990,
      features: [
        'جميع ميزات المحترف',
        'زوار غير محدودين',
        'ذكاء اصطناعي مخصص',
        'تحليل متقدم للشبكات',
        'مدير حساب مخصص',
        'تدريب مخصص',
        'SLA مضمون',
        'أمان متقدم',
        'تكامل مخصص'
      ],
      popular: false,
      color: 'var(--primary-color)'
    }
  ];

  return (
    <div className="pricing-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/pricing">الأسعار</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">خطط أسعار مرنة لكل احتياج</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                اختر الخطة المناسبة لحجم عملك واحتياجاتك. جميع الخطط تشمل تجربة مجانية لمدة 14 يوماً
              </p>
              
              {/* Billing Toggle */}
              <div className="billing-toggle" data-aos="fade-up" data-aos-delay="300" style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '1rem',
                marginTop: '2rem'
              }}>
                <span style={{ color: billingCycle === 'monthly' ? 'white' : 'rgba(255,255,255,0.7)' }}>
                  شهري
                </span>
                <button
                  onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
                  style={{
                    width: '60px',
                    height: '30px',
                    borderRadius: '15px',
                    border: 'none',
                    background: billingCycle === 'yearly' ? 'var(--accent-color)' : 'rgba(255,255,255,0.3)',
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                >
                  <div style={{
                    width: '26px',
                    height: '26px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    right: billingCycle === 'yearly' ? '2px' : '32px',
                    transition: 'all 0.3s ease'
                  }}></div>
                </button>
                <span style={{ color: billingCycle === 'yearly' ? 'white' : 'rgba(255,255,255,0.7)' }}>
                  سنوي <span style={{ color: 'var(--accent-color)', fontWeight: 'bold' }}>(وفر 20%)</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="pricing" style={{ background: 'var(--white)', padding: '4rem 0' }}>
        <div className="container">
          <div className="pricing-grid">
            {plans.map((plan, index) => (
              <div 
                key={plan.name}
                className={`pricing-card ${plan.popular ? 'popular' : ''}`}
                data-aos="fade-up" 
                data-aos-delay={index * 100}
                style={{
                  background: 'white',
                  borderRadius: '20px',
                  padding: '2.5rem',
                  boxShadow: plan.popular ? 'var(--shadow-large)' : 'var(--shadow-medium)',
                  border: plan.popular ? `3px solid ${plan.color}` : '1px solid #e5e7eb',
                  position: 'relative',
                  transform: plan.popular ? 'scale(1.05)' : 'scale(1)',
                  transition: 'transform 0.3s ease'
                }}
              >
                {plan.popular && (
                  <div style={{
                    position: 'absolute',
                    top: '-15px',
                    right: '50%',
                    transform: 'translateX(50%)',
                    background: plan.color,
                    color: 'white',
                    padding: '0.5rem 2rem',
                    borderRadius: '25px',
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>
                    الأكثر شعبية
                  </div>
                )}

                <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                  <h3 style={{ 
                    fontSize: '1.5rem', 
                    fontWeight: 'bold', 
                    color: plan.color,
                    marginBottom: '0.5rem'
                  }}>
                    {plan.name}
                  </h3>
                  <p style={{ color: 'var(--text-light)', marginBottom: '1.5rem' }}>
                    {plan.description}
                  </p>
                  
                  <div style={{ marginBottom: '1rem' }}>
                    <span style={{ 
                      fontSize: '3rem', 
                      fontWeight: 'bold', 
                      color: 'var(--text-dark)' 
                    }}>
                      {billingCycle === 'monthly' ? plan.monthlyPrice : Math.floor(plan.yearlyPrice / 12)}
                    </span>
                    <span style={{ color: 'var(--text-light)' }}>
                      ر.س / شهر
                    </span>
                  </div>
                  
                  {billingCycle === 'yearly' && (
                    <p style={{ 
                      color: 'var(--accent-color)', 
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}>
                      يُدفع سنوياً ({plan.yearlyPrice} ر.س)
                    </p>
                  )}
                </div>

                <ul style={{ 
                  listStyle: 'none', 
                  padding: 0, 
                  marginBottom: '2rem',
                  textAlign: 'right'
                }}>
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} style={{
                      padding: '0.75rem 0',
                      borderBottom: '1px solid #f3f4f6',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem'
                    }}>
                      <i className="fas fa-check" style={{ color: plan.color }}></i>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  style={{
                    width: '100%',
                    padding: '1rem',
                    borderRadius: '12px',
                    border: 'none',
                    background: plan.popular ? plan.color : 'var(--primary-color)',
                    color: 'white',
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  ابدأ التجربة المجانية
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="features" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">الأسئلة الشائعة حول الأسعار</h2>
            <p className="section-subtitle">
              إجابات على أكثر الأسئلة شيوعاً حول خطط الأسعار والفواتير
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <h3>هل يمكنني تغيير خطتي لاحقاً؟</h3>
              <p>
                نعم، يمكنك ترقية أو تخفيض خطتك في أي وقت. التغييرات ستطبق في دورة الفوترة التالية.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <h3>ما هي طرق الدفع المقبولة؟</h3>
              <p>
                نقبل جميع بطاقات الائتمان الرئيسية، التحويل البنكي، وطرق الدفع الرقمية المحلية.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <h3>هل هناك رسوم إعداد؟</h3>
              <p>
                لا، جميع خططنا لا تتضمن أي رسوم إعداد أو رسوم خفية. ما تراه هو ما تدفعه.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <h3>هل يمكنني إلغاء اشتراكي؟</h3>
              <p>
                نعم، يمكنك إلغاء اشتراكك في أي وقت بدون رسوم إلغاء. ستحتفظ بالوصول حتى نهاية دورة الفوترة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="500">
              <h3>هل تقدمون خصومات للمؤسسات؟</h3>
              <p>
                نعم، نقدم خصومات خاصة للمؤسسات الكبيرة والمنظمات غير الربحية. تواصل معنا للتفاصيل.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="600">
              <h3>ماذا يحدث بعد انتهاء التجربة المجانية؟</h3>
              <p>
                بعد انتهاء التجربة المجانية، يمكنك اختيار خطة مدفوعة أو الاستمرار مع الخطة المجانية المحدودة.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Enterprise CTA */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>تحتاج حلول مؤسسية مخصصة؟</h2>
            <p>تواصل مع فريق المبيعات لدينا لمناقشة احتياجاتك الخاصة والحصول على عرض مخصص</p>
            <div className="cta-buttons">
              <a href="/contact" className="btn btn-white">تواصل مع المبيعات</a>
              <a href="/signup" className="btn btn-outline-white">ابدأ التجربة المجانية</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PricingPage;
