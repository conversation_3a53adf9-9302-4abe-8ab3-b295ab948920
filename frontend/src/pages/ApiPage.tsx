import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const ApiPage: React.FC = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState('analytics');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const endpoints = {
    analytics: {
      title: 'Analytics API',
      description: 'الحصول على بيانات التحليلات والمقاييس',
      method: 'GET',
      url: '/api/v1/analytics',
      example: `curl -X GET "https://api.marketmind.ai/v1/analytics" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`,
      response: `{
  "status": "success",
  "data": {
    "visitors": 15420,
    "conversions": 342,
    "revenue": 45600.50,
    "period": "last_30_days"
  }
}`
    },
    campaigns: {
      title: 'Campaigns API',
      description: 'إدارة الحملات التسويقية',
      method: 'POST',
      url: '/api/v1/campaigns',
      example: `curl -X POST "https://api.marketmind.ai/v1/campaigns" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "حملة الصيف 2024",
    "budget": 5000,
    "target_audience": "25-35"
  }'`,
      response: `{
  "status": "success",
  "data": {
    "campaign_id": "camp_123456",
    "name": "حملة الصيف 2024",
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z"
  }
}`
    },
    reports: {
      title: 'Reports API',
      description: 'إنشاء واسترداد التقارير',
      method: 'GET',
      url: '/api/v1/reports',
      example: `curl -X GET "https://api.marketmind.ai/v1/reports?type=performance&period=weekly" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`,
      response: `{
  "status": "success",
  "data": {
    "report_id": "rep_789012",
    "type": "performance",
    "metrics": {
      "impressions": 125000,
      "clicks": 3200,
      "ctr": 2.56
    }
  }
}`
    }
  };

  return (
    <div className="api-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/pricing">الأسعار</a></li>
              <li><a href="/api">API</a></li>
              <li><a href="/docs">الوثائق</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">MarketMind API</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                API قوي ومرن للمطورين لدمج قوة MarketMind في تطبيقاتهم وأنظمتهم. 
                ابني حلول تسويقية مخصصة باستخدام بياناتنا المتقدمة.
              </p>
              
              <div className="api-stats" data-aos="fade-up" data-aos-delay="300" style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '3rem',
                marginTop: '2rem',
                flexWrap: 'wrap'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>99.9%</div>
                  <div style={{ color: 'rgba(255,255,255,0.8)' }}>وقت التشغيل</div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>&lt;100ms</div>
                  <div style={{ color: 'rgba(255,255,255,0.8)' }}>زمن الاستجابة</div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>50+</div>
                  <div style={{ color: 'rgba(255,255,255,0.8)' }}>نقطة نهاية</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Start */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">البدء السريع</h2>
            <p className="section-subtitle">
              ابدأ في استخدام MarketMind API في دقائق معدودة
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-key"></i>
              </div>
              <h3>احصل على مفتاح API</h3>
              <p>
                سجل حساباً مجانياً واحصل على مفتاح API الخاص بك من لوحة التحكم.
              </p>
              <a href="/signup" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                إنشاء حساب
              </a>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-code"></i>
              </div>
              <h3>اقرأ الوثائق</h3>
              <p>
                تصفح وثائقنا الشاملة مع أمثلة الكود والشروحات التفصيلية.
              </p>
              <a href="/docs" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                عرض الوثائق
              </a>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <h3>ابدأ التطوير</h3>
              <p>
                استخدم مكتباتنا الجاهزة أو اتصل مباشرة بـ REST API لبناء تطبيقك.
              </p>
              <a href="#examples" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                أمثلة الكود
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* API Explorer */}
      <section className="problems" style={{ background: 'var(--bg-light)' }} id="examples">
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">مستكشف API</h2>
            <p className="section-subtitle">
              جرب نقاط النهاية المختلفة وشاهد الاستجابات مباشرة
            </p>
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '300px 1fr', gap: '2rem', alignItems: 'start' }}>
            
            {/* Sidebar */}
            <div className="api-sidebar" data-aos="fade-right">
              <h3 style={{ marginBottom: '1.5rem', color: 'var(--primary-color)' }}>نقاط النهاية</h3>
              <nav>
                {Object.entries(endpoints).map(([key, endpoint]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedEndpoint(key)}
                    style={{
                      width: '100%',
                      textAlign: 'right',
                      padding: '1rem',
                      border: 'none',
                      borderRadius: '8px',
                      background: selectedEndpoint === key ? 'var(--primary-color)' : 'white',
                      color: selectedEndpoint === key ? 'white' : 'var(--text-dark)',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      marginBottom: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <span>{endpoint.title}</span>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      background: selectedEndpoint === key ? 'rgba(255,255,255,0.2)' : 'var(--bg-light)',
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}>
                      {endpoint.method}
                    </span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Content */}
            <div className="api-content" data-aos="fade-left">
              <div style={{
                background: 'white',
                padding: '2rem',
                borderRadius: '12px',
                boxShadow: 'var(--shadow-soft)'
              }}>
                <h3 style={{ marginBottom: '1rem', color: 'var(--primary-color)' }}>
                  {endpoints[selectedEndpoint as keyof typeof endpoints].title}
                </h3>
                <p style={{ marginBottom: '2rem', color: 'var(--text-light)' }}>
                  {endpoints[selectedEndpoint as keyof typeof endpoints].description}
                </p>

                <div style={{ marginBottom: '2rem' }}>
                  <h4 style={{ marginBottom: '1rem' }}>مثال على الطلب:</h4>
                  <pre style={{
                    background: '#1a1a1a',
                    color: '#f8f8f2',
                    padding: '1.5rem',
                    borderRadius: '8px',
                    overflow: 'auto',
                    fontSize: '0.9rem',
                    lineHeight: '1.5'
                  }}>
                    {endpoints[selectedEndpoint as keyof typeof endpoints].example}
                  </pre>
                </div>

                <div>
                  <h4 style={{ marginBottom: '1rem' }}>مثال على الاستجابة:</h4>
                  <pre style={{
                    background: '#f8f9fa',
                    color: '#333',
                    padding: '1.5rem',
                    borderRadius: '8px',
                    overflow: 'auto',
                    fontSize: '0.9rem',
                    lineHeight: '1.5',
                    border: '1px solid #e9ecef'
                  }}>
                    {endpoints[selectedEndpoint as keyof typeof endpoints].response}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SDKs and Libraries */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">مكتبات SDK</h2>
            <p className="section-subtitle">
              مكتبات جاهزة للغات البرمجة الشائعة لتسريع عملية التطوير
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon" style={{ background: '#f7df1e' }}>
                <i className="fab fa-js-square" style={{ color: '#000' }}></i>
              </div>
              <h3>JavaScript / Node.js</h3>
              <p>مكتبة JavaScript للمتصفح و Node.js مع دعم TypeScript</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                npm install @marketmind/sdk
              </pre>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon" style={{ background: '#3776ab' }}>
                <i className="fab fa-python" style={{ color: 'white' }}></i>
              </div>
              <h3>Python</h3>
              <p>مكتبة Python مع دعم async/await وتكامل مع pandas</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                pip install marketmind-sdk
              </pre>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon" style={{ background: '#007396' }}>
                <i className="fab fa-java" style={{ color: 'white' }}></i>
              </div>
              <h3>Java</h3>
              <p>مكتبة Java مع دعم Spring Boot وتكامل مع Maven/Gradle</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                implementation 'com.marketmind:sdk:1.0.0'
              </pre>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <div className="feature-icon" style={{ background: '#239120' }}>
                <i className="fab fa-microsoft" style={{ color: 'white' }}></i>
              </div>
              <h3>C# / .NET</h3>
              <p>مكتبة .NET مع دعم .NET Core و .NET Framework</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                Install-Package MarketMind.SDK
              </pre>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="500">
              <div className="feature-icon" style={{ background: '#00add8' }}>
                <i className="fab fa-golang" style={{ color: 'white' }}></i>
              </div>
              <h3>Go</h3>
              <p>مكتبة Go مع دعم goroutines والتعامل مع JSON</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                go get github.com/marketmind/go-sdk
              </pre>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="600">
              <div className="feature-icon" style={{ background: '#cc342d' }}>
                <i className="fab fa-gem" style={{ color: 'white' }}></i>
              </div>
              <h3>Ruby</h3>
              <p>مكتبة Ruby مع دعم Rails وتكامل مع ActiveRecord</p>
              <pre style={{ 
                background: '#f8f9fa', 
                padding: '0.5rem', 
                borderRadius: '4px', 
                fontSize: '0.8rem',
                marginTop: '1rem'
              }}>
                gem install marketmind-sdk
              </pre>
            </div>
          </div>
        </div>
      </section>

      {/* Rate Limits and Pricing */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">حدود الاستخدام والأسعار</h2>
            <p className="section-subtitle">
              خطط مرنة تناسب جميع احتياجات التطوير
            </p>
          </div>
          
          <div className="problems-grid">
            <div className="problem-card" data-aos="fade-up" data-aos-delay="100">
              <div className="problem-icon">
                <i className="fas fa-gift"></i>
              </div>
              <h3>المطور المجاني</h3>
              <p><strong>1,000 طلب/شهر</strong></p>
              <ul style={{ textAlign: 'right', marginTop: '1rem' }}>
                <li>جميع نقاط النهاية الأساسية</li>
                <li>دعم المجتمع</li>
                <li>وثائق كاملة</li>
              </ul>
              <div style={{ marginTop: '1rem', fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>
                مجاني
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="200">
              <div className="problem-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <h3>المطور المحترف</h3>
              <p><strong>100,000 طلب/شهر</strong></p>
              <ul style={{ textAlign: 'right', marginTop: '1rem' }}>
                <li>جميع نقاط النهاية</li>
                <li>دعم فني أولوية</li>
                <li>Webhooks</li>
                <li>تحليلات الاستخدام</li>
              </ul>
              <div style={{ marginTop: '1rem', fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>
                99 ر.س/شهر
              </div>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="300">
              <div className="problem-icon">
                <i className="fas fa-building"></i>
              </div>
              <h3>المؤسسي</h3>
              <p><strong>طلبات غير محدودة</strong></p>
              <ul style={{ textAlign: 'right', marginTop: '1rem' }}>
                <li>جميع الميزات</li>
                <li>SLA مضمون</li>
                <li>دعم مخصص</li>
                <li>نقاط نهاية مخصصة</li>
              </ul>
              <div style={{ marginTop: '1rem', fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>
                تواصل معنا
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>ابدأ البناء مع MarketMind API</h2>
            <p>انضم إلى آلاف المطورين الذين يستخدمون API لبناء تطبيقات تسويقية مبتكرة</p>
            <div className="cta-buttons">
              <a href="/signup" className="btn btn-white">احصل على مفتاح API</a>
              <a href="/docs" className="btn btn-outline-white">عرض الوثائق</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
                <li><a href="/api">API</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ApiPage;
