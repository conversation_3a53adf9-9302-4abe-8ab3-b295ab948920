import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import '../styles/enhanced-homepage.css';

const EnhancedLogin: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuthStore();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // إزالة استخدام AOS لأنه غير مثبت
    // if (typeof window !== 'undefined' && window.AOS) {
    //   window.AOS.init({
    //     duration: 1000,
    //     once: true
    //   });
    // }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.password) {
      setError('يرجى ملء جميع الحقول');
      return;
    }

    try {
      const result = await login(formData.email, formData.password);
      // Navigate to appropriate dashboard based on user type
      navigate(result.redirectUrl);
    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @media (max-width: 768px) {
            .auth-section > .container > div {
              grid-template-columns: 1fr !important;
              gap: 2rem !important;
            }

            .auth-content {
              text-align: center;
              order: 2;
            }

            .auth-form-container {
              order: 1;
            }

            .auth-content h1 {
              font-size: 2rem !important;
            }

            .auth-form-container > div {
              padding: 2rem !important;
            }
          }

          .form-input:focus {
            border-color: var(--primary-color) !important;
            outline: none;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
          }

          .login-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4) !important;
          }
        `}
      </style>
      <div className="login-page">
        {/* Header */}
        <header className="header">
          <div className="container">
            <nav className="nav">
              <div className="logo">
                <h2>MarketMind</h2>
              </div>
              <ul className="nav-links">
                <li><a href="/">الرئيسية</a></li>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/about">من نحن</a></li>
              </ul>
              <div className="nav-buttons">
                <button
                  className="btn btn-outline"
                  onClick={() => navigate('/signup')}
                >
                  إنشاء حساب
                </button>
              </div>
            </nav>
          </div>
        </header>

        {/* Login Section */}
        <section className="auth-section" style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
          padding: '2rem 0'
        }}>
          <div className="container">
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '4rem',
              alignItems: 'center',
              maxWidth: '1200px',
              margin: '0 auto'
            }}>

              {/* Left Side - Welcome Content */}
              <div className="auth-content" data-aos="fade-right">
                <h1 style={{
                  color: 'white',
                  fontSize: '2.5rem',
                  marginBottom: '1.5rem',
                  fontWeight: 'bold'
                }}>
                  مرحباً بعودتك إلى MarketMind
                </h1>
                <p style={{
                  color: 'rgba(255,255,255,0.9)',
                  fontSize: '1.1rem',
                  lineHeight: '1.6',
                  marginBottom: '2rem'
                }}>
                  سجل دخولك للوصول إلى لوحة التحكم الخاصة بك ومتابعة رحلتك في
                  تحليل البيانات التسويقية وتحسين أداء حملاتك.
                </p>

                <div className="login-benefits" style={{ color: 'white' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                    <i className="fas fa-chart-line" style={{ color: 'var(--accent-color)' }}></i>
                    <span>تحليلات متقدمة في الوقت الفعلي</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                    <i className="fas fa-robot" style={{ color: 'var(--accent-color)' }}></i>
                    <span>ذكاء اصطناعي لتحسين الحملات</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                    <i className="fas fa-sync-alt" style={{ color: 'var(--accent-color)' }}></i>
                    <span>تكاملات مع جميع منصاتك</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                    <i className="fas fa-shield-alt" style={{ color: 'var(--accent-color)' }}></i>
                    <span>حماية وأمان متقدم</span>
                  </div>
                </div>

                <div style={{
                  marginTop: '2rem',
                  padding: '1.5rem',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '12px',
                  backdropFilter: 'blur(10px)'
                }}>
                  <h3 style={{ color: 'white', marginBottom: '1rem' }}>حسابات تجريبية للاختبار:</h3>
                  <ul style={{ color: 'rgba(255,255,255,0.9)', listStyle: 'none', padding: 0, fontSize: '0.9rem' }}>
                    <li style={{ marginBottom: '0.5rem' }}>• مدير: <EMAIL></li>
                    <li style={{ marginBottom: '0.5rem' }}>• تجاري: <EMAIL></li>
                    <li style={{ marginBottom: '0.5rem' }}>• شخصي: <EMAIL></li>
                    <li>• كلمة المرور: password123</li>
                  </ul>
                </div>
              </div>

              {/* Right Side - Login Form */}
              <div className="auth-form-container" data-aos="fade-left">
                <div style={{
                  background: 'white',
                  padding: '3rem',
                  borderRadius: '20px',
                  boxShadow: 'var(--shadow-large)'
                }}>
                  <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                    <h2 style={{
                      color: 'var(--primary-color)',
                      marginBottom: '0.5rem',
                      fontSize: '1.75rem'
                    }}>
                      تسجيل الدخول
                    </h2>
                    <p style={{ color: 'var(--text-light)' }}>
                      أدخل بياناتك للوصول إلى حسابك
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="auth-form">
                    {error && (
                      <div style={{
                        background: '#fee2e2',
                        border: '1px solid #fecaca',
                        color: '#dc2626',
                        padding: '0.75rem',
                        borderRadius: '8px',
                        marginBottom: '1.5rem',
                        fontSize: '0.9rem'
                      }}>
                        {error}
                      </div>
                    )}

                    <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                      <label htmlFor="email" style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        البريد الإلكتروني *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="أدخل بريدك الإلكتروني"
                        className="form-input"
                        style={{
                          width: '100%',
                          padding: '1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease',
                          direction: 'ltr'
                        }}
                      />
                    </div>

                    <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                      <label htmlFor="password" style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        كلمة المرور *
                      </label>
                      <div style={{ position: 'relative' }}>
                        <input
                          type={showPassword ? 'text' : 'password'}
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          required
                          placeholder="أدخل كلمة المرور"
                          className="form-input"
                          style={{
                            width: '100%',
                            padding: '1rem',
                            paddingRight: '3rem',
                            border: '2px solid #e5e7eb',
                            borderRadius: '8px',
                            fontSize: '1rem',
                            transition: 'border-color 0.3s ease'
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          style={{
                            position: 'absolute',
                            right: '1rem',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            color: 'var(--text-light)',
                            fontSize: '1.2rem'
                          }}
                        >
                          {showPassword ? '🙈' : '👁️'}
                        </button>
                      </div>
                    </div>

                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '1.5rem'
                    }}>
                      <label style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        cursor: 'pointer'
                      }}>
                        <input
                          type="checkbox"
                          name="rememberMe"
                          checked={formData.rememberMe}
                          onChange={handleInputChange}
                          style={{
                            width: '18px',
                            height: '18px',
                            accentColor: 'var(--primary-color)'
                          }}
                        />
                        <span style={{ color: 'var(--text-dark)', fontSize: '0.9rem' }}>
                          تذكرني
                        </span>
                      </label>

                      <a
                        href="/forgot-password"
                        style={{
                          color: 'var(--primary-color)',
                          textDecoration: 'none',
                          fontSize: '0.9rem',
                          fontWeight: '500'
                        }}
                      >
                        نسيت كلمة المرور؟
                      </a>
                    </div>

                    <button
                      type="submit"
                      disabled={isLoading}
                      className="login-btn"
                      style={{
                        width: '100%',
                        padding: '1rem 2rem',
                        background: isLoading ? '#9ca3af' : 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '12px',
                        fontSize: '1.1rem',
                        fontWeight: '600',
                        cursor: isLoading ? 'not-allowed' : 'pointer',
                        transition: 'all 0.3s ease',
                        marginBottom: '1.5rem',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem',
                        boxShadow: '0 4px 15px rgba(30, 58, 138, 0.3)'
                      }}
                    >
                      {isLoading && (
                        <div style={{
                          width: '20px',
                          height: '20px',
                          border: '2px solid transparent',
                          borderTop: '2px solid white',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite'
                        }}></div>
                      )}
                      {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                    </button>

                    <div style={{ textAlign: 'center' }}>
                      <span style={{ color: 'var(--text-light)', fontSize: '0.9rem' }}>
                        ليس لديك حساب؟{' '}
                      </span>
                      <button
                        type="button"
                        onClick={() => navigate('/signup')}
                        style={{
                          color: 'var(--primary-color)',
                          background: 'none',
                          border: 'none',
                          fontSize: '0.9rem',
                          fontWeight: '600',
                          cursor: 'pointer',
                          textDecoration: 'none'
                        }}
                      >
                        إنشاء حساب جديد
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default EnhancedLogin;
