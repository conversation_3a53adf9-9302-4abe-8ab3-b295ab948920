import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const BlogPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const categories = [
    { id: 'all', name: 'جميع المقالات', icon: 'fas fa-th-large' },
    { id: 'ai', name: 'الذكاء الاصطناعي', icon: 'fas fa-robot' },
    { id: 'marketing', name: 'التسويق الرقمي', icon: 'fas fa-bullhorn' },
    { id: 'analytics', name: 'التحليلات', icon: 'fas fa-chart-bar' },
    { id: 'trends', name: 'الاتجاهات', icon: 'fas fa-trending-up' },
    { id: 'tips', name: 'نصائح وحيل', icon: 'fas fa-lightbulb' }
  ];

  const articles = [
    {
      id: 1,
      title: 'مستقبل التسويق الرقمي مع الذكاء الاصطناعي',
      category: 'ai',
      excerpt: 'كيف يغير الذكاء الاصطناعي قواعد اللعبة في التسويق الرقمي وما هي التوقعات للسنوات القادمة.',
      author: 'أحمد محمد',
      date: '2024-01-15',
      readTime: '8 دقائق',
      image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop',
      featured: true
    },
    {
      id: 2,
      title: '10 استراتيجيات لتحسين معدل التحويل',
      category: 'marketing',
      excerpt: 'اكتشف أهم الاستراتيجيات المثبتة لزيادة معدلات التحويل في متجرك الإلكتروني.',
      author: 'فاطمة أحمد',
      date: '2024-01-12',
      readTime: '6 دقائق',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop',
      featured: false
    },
    {
      id: 3,
      title: 'تحليل البيانات التسويقية: دليل شامل',
      category: 'analytics',
      excerpt: 'تعلم كيفية تحليل بيانات حملاتك التسويقية واستخراج رؤى قيمة لتحسين الأداء.',
      author: 'محمد علي',
      date: '2024-01-10',
      readTime: '12 دقيقة',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop',
      featured: false
    },
    {
      id: 4,
      title: 'اتجاهات التسويق الرقمي لعام 2024',
      category: 'trends',
      excerpt: 'نظرة على أهم الاتجاهات التي ستشكل مستقبل التسويق الرقمي في العام الجديد.',
      author: 'سارة خالد',
      date: '2024-01-08',
      readTime: '7 دقائق',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop',
      featured: true
    },
    {
      id: 5,
      title: 'كيفية بناء استراتيجية محتوى فعالة',
      category: 'tips',
      excerpt: 'خطوات عملية لبناء استراتيجية محتوى تجذب جمهورك وتحقق أهدافك التسويقية.',
      author: 'عمر حسن',
      date: '2024-01-05',
      readTime: '9 دقائق',
      image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=500&h=300&fit=crop',
      featured: false
    },
    {
      id: 6,
      title: 'التسويق عبر وسائل التواصل الاجتماعي: أفضل الممارسات',
      category: 'marketing',
      excerpt: 'دليل شامل لأفضل الممارسات في التسويق عبر منصات التواصل الاجتماعي المختلفة.',
      author: 'نور الدين',
      date: '2024-01-03',
      readTime: '10 دقائق',
      image: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=500&h=300&fit=crop',
      featured: false
    }
  ];

  const filteredArticles = selectedCategory === 'all' 
    ? articles 
    : articles.filter(article => article.category === selectedCategory);

  const featuredArticles = articles.filter(article => article.featured);

  return (
    <div className="blog-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/blog">المدونة</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">مدونة MarketMind</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                اكتشف أحدث الاتجاهات والنصائح في عالم التسويق الرقمي والذكاء الاصطناعي. 
                مقالات متخصصة من خبراء الصناعة لمساعدتك على النجاح.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      <section className="features" style={{ background: 'var(--white)', padding: '3rem 0' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">المقالات المميزة</h2>
          </div>
          
          <div className="featured-articles" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '2rem'
          }}>
            {featuredArticles.map((article, index) => (
              <div 
                key={article.id}
                className="featured-article" 
                data-aos="fade-up" 
                data-aos-delay={index * 100}
                style={{
                  background: 'white',
                  borderRadius: '15px',
                  overflow: 'hidden',
                  boxShadow: 'var(--shadow-medium)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-10px)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                }}
              >
                <div style={{
                  height: '200px',
                  backgroundImage: `url(${article.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  position: 'relative'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: '1rem',
                    right: '1rem',
                    background: 'var(--accent-color)',
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold'
                  }}>
                    مميز
                  </div>
                </div>

                <div style={{ padding: '1.5rem' }}>
                  <h3 style={{ 
                    color: 'var(--primary-color)', 
                    marginBottom: '1rem',
                    fontSize: '1.25rem',
                    lineHeight: '1.4'
                  }}>
                    {article.title}
                  </h3>
                  
                  <p style={{ 
                    color: 'var(--text-light)', 
                    marginBottom: '1rem',
                    lineHeight: '1.6'
                  }}>
                    {article.excerpt}
                  </p>

                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.9rem',
                    color: 'var(--text-light)'
                  }}>
                    <div>
                      <span>{article.author}</span> • <span>{article.date}</span>
                    </div>
                    <span>
                      <i className="fas fa-clock"></i> {article.readTime}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* All Articles */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">جميع المقالات</h2>
          </div>
          
          {/* Category Filter */}
          <div className="category-filters" data-aos="fade-up" data-aos-delay="200" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem'
          }}>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: selectedCategory === category.id ? '2px solid var(--primary-color)' : '2px solid transparent',
                  background: selectedCategory === category.id ? 'var(--primary-color)' : 'white',
                  color: selectedCategory === category.id ? 'white' : 'var(--text-dark)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <i className={category.icon}></i>
                {category.name}
              </button>
            ))}
          </div>

          {/* Articles Grid */}
          <div className="articles-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: '2rem'
          }}>
            {filteredArticles.map((article, index) => (
              <div 
                key={article.id}
                className="article-card" 
                data-aos="fade-up" 
                data-aos-delay={index * 50}
                style={{
                  background: 'white',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  boxShadow: 'var(--shadow-soft)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-soft)';
                }}
              >
                <div style={{
                  height: '180px',
                  backgroundImage: `url(${article.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}></div>

                <div style={{ padding: '1.5rem' }}>
                  <h3 style={{ 
                    color: 'var(--primary-color)', 
                    marginBottom: '1rem',
                    fontSize: '1.1rem',
                    lineHeight: '1.4'
                  }}>
                    {article.title}
                  </h3>
                  
                  <p style={{ 
                    color: 'var(--text-light)', 
                    marginBottom: '1rem',
                    lineHeight: '1.6',
                    fontSize: '0.95rem'
                  }}>
                    {article.excerpt}
                  </p>

                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.85rem',
                    color: 'var(--text-light)'
                  }}>
                    <div>
                      <span>{article.author}</span> • <span>{article.date}</span>
                    </div>
                    <span>
                      <i className="fas fa-clock"></i> {article.readTime}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredArticles.length === 0 && (
            <div style={{ textAlign: 'center', padding: '3rem' }}>
              <i className="fas fa-newspaper" style={{ fontSize: '3rem', color: 'var(--text-light)', marginBottom: '1rem' }}></i>
              <h3>لا توجد مقالات في هذه الفئة</h3>
              <p>جرب فئة أخرى أو تحقق مرة أخرى قريباً</p>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="newsletter-signup" data-aos="fade-up" style={{
            background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
            borderRadius: '20px',
            padding: '3rem',
            textAlign: 'center',
            color: 'white'
          }}>
            <h2 style={{ marginBottom: '1rem', color: 'white' }}>
              اشترك في نشرتنا الإخبارية
            </h2>
            <p style={{ marginBottom: '2rem', opacity: 0.9 }}>
              احصل على أحدث المقالات والنصائح التسويقية مباشرة في بريدك الإلكتروني
            </p>
            
            <div style={{
              display: 'flex',
              gap: '1rem',
              maxWidth: '500px',
              margin: '0 auto',
              flexWrap: 'wrap',
              justifyContent: 'center'
            }}>
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                style={{
                  flex: 1,
                  minWidth: '250px',
                  padding: '1rem',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '1rem'
                }}
              />
              <button style={{
                padding: '1rem 2rem',
                borderRadius: '8px',
                border: 'none',
                background: 'var(--accent-color)',
                color: 'white',
                fontSize: '1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}>
                اشتراك
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>هل لديك فكرة لمقال؟</h2>
            <p>نحن نرحب بمساهمات خبراء الصناعة. شارك معرفتك مع مجتمعنا</p>
            <div className="cta-buttons">
              <a href="/contact" className="btn btn-white">اقترح مقال</a>
              <a href="/about" className="btn btn-outline-white">انضم ككاتب</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/careers">الوظائف</a></li>
                <li><a href="/blog">المدونة</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BlogPage;
