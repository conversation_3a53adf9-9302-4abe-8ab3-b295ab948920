import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper
} from '@mui/material';
import {
  TrendingUp,
  People,
  Campaign,
  Analytics,
  Lightbulb,
  CheckCircle
} from '@mui/icons-material';

const PersonalDashboard: React.FC = () => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetch('http://localhost:8000/api/user/me', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })
      .then(res => res.json())
      .then(data => setUser(data.user));
  }, []);

  const stats = [
    { title: 'Total Campaigns', value: '12', change: '+15%', icon: <Campaign /> },
    { title: 'Audience Reach', value: '45.2K', change: '+8%', icon: <People /> },
    { title: 'Engagement Rate', value: '3.4%', change: '+12%', icon: <TrendingUp /> },
    { title: 'Conversion Rate', value: '2.1%', change: '+5%', icon: <Analytics /> }
  ];

  const recommendations = [
    'Optimize your email campaigns for mobile devices',
    'Consider A/B testing your social media posts',
    'Target audience segment "Young Professionals" shows high engagement',
    'Schedule posts during peak hours (2-4 PM) for better reach'
  ];

  const recentActivities = [
    { action: 'Campaign "Summer Sale" launched', time: '2 hours ago' },
    { action: 'Audience analysis completed', time: '4 hours ago' },
    { action: 'Content personalization updated', time: '1 day ago' },
    { action: 'New customer segment identified', time: '2 days ago' }
  ];

  const handleSave = async () => {
    await fetch('http://localhost:8000/api/user/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({ name, email })
    });
    // أضف رسالة نجاح أو خطأ
  };

  const handleAnalyze = async () => {
    const res = await fetch('http://localhost:8000/api/personal/analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({ data: inputData })
    });
    const result = await res.json();
    setAnalysisResult(result);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Personal Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Welcome back! Here's your marketing performance overview.
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ color: 'primary.main', mr: 1 }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h6" component="div">
                    {stat.value}
                  </Typography>
                  <Chip
                    label={stat.change}
                    color="success"
                    size="small"
                    sx={{ ml: 'auto' }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {stat.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Usage Progress */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Monthly Usage
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  API Calls: 1,250 / 2,000
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={62.5}
                  sx={{ mt: 1, mb: 2 }}
                />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Storage: 3.2 GB / 5 GB
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={64}
                  sx={{ mt: 1, mb: 2 }}
                />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Reports Generated: 8 / 15
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={53.3}
                  sx={{ mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Recommendations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Lightbulb sx={{ mr: 1, verticalAlign: 'middle' }} />
                AI Recommendations
              </Typography>
              <List dense>
                {recommendations.map((recommendation, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CheckCircle color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={recommendation}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activities
            </Typography>
            <List>
              {recentActivities.map((activity, index) => (
                <ListItem key={index} divider={index < recentActivities.length - 1}>
                  <ListItemText
                    primary={activity.action}
                    secondary={activity.time}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PersonalDashboard;
