import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Analytics,
  PieChart,
  TrendingUp,
  Psychology,
  Campaign,
  Download
} from '@mui/icons-material';

const AnalysisTools: React.FC = () => {
  const [selectedTool, setSelectedTool] = useState('');
  const [analysisData, setAnalysisData] = useState('');
  const [results, setResults] = useState<any>(null);

  const tools = [
    {
      id: 'sentiment',
      title: 'Sentiment Analysis',
      description: 'Analyze customer feedback and social media sentiment',
      icon: <Psychology />
    },
    {
      id: 'segmentation',
      title: 'Customer Segmentation',
      description: 'Segment your audience based on behavior and demographics',
      icon: <PieChart />
    },
    {
      id: 'trend',
      title: 'Trend Analysis',
      description: 'Identify trending topics and market opportunities',
      icon: <TrendingUp />
    },
    {
      id: 'campaign',
      title: 'Campaign Performance',
      description: 'Analyze and optimize your marketing campaigns',
      icon: <Campaign />
    }
  ];

  const handleAnalyze = () => {
    // Mock analysis results
    const mockResults = {
      sentiment: {
        positive: 65,
        neutral: 25,
        negative: 10,
        insights: [
          'Overall sentiment is positive',
          'Customers love the new product features',
          'Some concerns about pricing mentioned'
        ]
      },
      segmentation: {
        segments: [
          { name: 'Young Professionals', size: 35, characteristics: ['Tech-savvy', 'High income', 'Urban'] },
          { name: 'Families', size: 40, characteristics: ['Value-conscious', 'Suburban', 'Loyal'] },
          { name: 'Seniors', size: 25, characteristics: ['Traditional', 'Quality-focused', 'Conservative'] }
        ]
      },
      trend: {
        trending: ['Sustainability', 'Remote Work', 'Health & Wellness'],
        declining: ['Fast Fashion', 'Physical Retail'],
        opportunities: ['Eco-friendly products', 'Digital services', 'Home fitness']
      },
      campaign: {
        bestPerforming: 'Email Campaign #3',
        metrics: {
          openRate: '24.5%',
          clickRate: '3.2%',
          conversionRate: '1.8%'
        },
        recommendations: [
          'Increase email frequency',
          'A/B test subject lines',
          'Personalize content'
        ]
      }
    };

    setResults(mockResults[selectedTool as keyof typeof mockResults]);
  };

  const renderResults = () => {
    if (!results) return null;

    switch (selectedTool) {
      case 'sentiment':
        return (
          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sentiment Analysis Results
            </Typography>
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="success.main">
                    {results.positive}%
                  </Typography>
                  <Typography variant="body2">Positive</Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="warning.main">
                    {results.neutral}%
                  </Typography>
                  <Typography variant="body2">Neutral</Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="error.main">
                    {results.negative}%
                  </Typography>
                  <Typography variant="body2">Negative</Typography>
                </Box>
              </Grid>
            </Grid>
            <Typography variant="subtitle1" gutterBottom>
              Key Insights:
            </Typography>
            <List>
              {results.insights.map((insight: string, index: number) => (
                <ListItem key={index}>
                  <ListItemText primary={insight} />
                </ListItem>
              ))}
            </List>
          </Paper>
        );

      case 'segmentation':
        return (
          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Customer Segmentation Results
            </Typography>
            {results.segments.map((segment: any, index: number) => (
              <Card key={index} sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6">
                    {segment.name} ({segment.size}%)
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    {segment.characteristics.map((char: string, i: number) => (
                      <Chip key={i} label={char} sx={{ mr: 1, mb: 1 }} />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Paper>
        );

      default:
        return (
          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Analysis Results
            </Typography>
            <Typography>Results for {selectedTool} analysis would appear here.</Typography>
          </Paper>
        );
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Analysis Tools
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Use AI-powered tools to analyze your marketing data and gain insights.
      </Typography>

      {/* Tool Selection */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {tools.map((tool) => (
          <Grid item xs={12} sm={6} md={3} key={tool.id}>
            <Card
              sx={{
                cursor: 'pointer',
                border: selectedTool === tool.id ? 2 : 1,
                borderColor: selectedTool === tool.id ? 'primary.main' : 'divider'
              }}
              onClick={() => setSelectedTool(tool.id)}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ color: 'primary.main', mr: 1 }}>
                    {tool.icon}
                  </Box>
                  <Typography variant="h6">
                    {tool.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {tool.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Analysis Input */}
      {selectedTool && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Configure Analysis
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Data Source</InputLabel>
                  <Select defaultValue="csv">
                    <MenuItem value="csv">CSV Upload</MenuItem>
                    <MenuItem value="api">API Integration</MenuItem>
                    <MenuItem value="manual">Manual Input</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Time Period</InputLabel>
                  <Select defaultValue="30days">
                    <MenuItem value="7days">Last 7 days</MenuItem>
                    <MenuItem value="30days">Last 30 days</MenuItem>
                    <MenuItem value="90days">Last 90 days</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Data Input"
                  placeholder="Paste your data here or upload a file..."
                  value={analysisData}
                  onChange={(e) => setAnalysisData(e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={handleAnalyze}
                    startIcon={<Analytics />}
                  >
                    Run Analysis
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                  >
                    Export Results
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {renderResults()}
    </Box>
  );
};

export default AnalysisTools;
