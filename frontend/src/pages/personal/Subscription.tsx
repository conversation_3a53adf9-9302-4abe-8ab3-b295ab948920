import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Paper,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Check,
  Star,
  Upgrade,
  Download,
  CreditCard,
  History
} from '@mui/icons-material';
import { useAuthStore } from '../../store/authStore';
import PaymentModal from '../../components/PaymentModal';

const Subscription: React.FC = () => {
  const { user } = useAuthStore();
  const [subscription, setSubscription] = useState<any>(null);
  const [billingHistory, setBillingHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState({ name: '', price: 0 });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError('');
      try {
        const token = localStorage.getItem('auth_token');
        const res = await fetch('http://localhost:8000/api/personal/subscription', {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!res.ok) throw new Error('فشل في جلب بيانات الاشتراك');
        const data = await res.json();
        setSubscription(data.subscription);
        setBillingHistory(data.billingHistory || []);
      } catch (err: any) {
        setError(err.message || 'حدث خطأ');
      }
      setLoading(false);
    };
    fetchData();
  }, []);

  const plans = [
    {
      name: 'Basic',
      price: '$29',
      priceValue: 29,
      period: '/month',
      features: [
        '5 Campaigns per month',
        'Basic analytics',
        'Email support',
        '1 GB storage',
        'Standard templates'
      ],
      current: subscription?.plan === 'basic'
    },
    {
      name: 'Pro',
      price: '$79',
      priceValue: 79,
      period: '/month',
      features: [
        '25 Campaigns per month',
        'Advanced analytics',
        'Priority support',
        '10 GB storage',
        'Premium templates',
        'A/B testing',
        'Custom branding'
      ],
      current: subscription?.plan === 'pro',
      popular: true
    },
    {
      name: 'Enterprise',
      price: '$199',
      priceValue: 199,
      period: '/month',
      features: [
        'Unlimited campaigns',
        'Advanced AI features',
        '24/7 phone support',
        '100 GB storage',
        'Custom templates',
        'Advanced integrations',
        'Team collaboration',
        'White-label solution'
      ],
      current: subscription?.plan === 'enterprise'
    }
  ];

  const usage = subscription?.usage || {
    campaigns: { used: 0, limit: 0, percentage: 0 },
    storage: { used: 0, limit: 0, percentage: 0 },
    apiCalls: { used: 0, limit: 0, percentage: 0 }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!user) {
    return <Alert severity="warning">يجب تسجيل الدخول لعرض صفحة الاشتراك.</Alert>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Subscription & Billing
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage your subscription plan and billing information.
      </Typography>

      {/* Current Plan */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Current Plan</Typography>
            <Chip
              label={subscription?.status || 'Active'}
              color="success"
              variant="outlined"
            />
          </Box>
          <Typography variant="h4" color="primary" gutterBottom>
            {subscription?.plan ? `${subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)} Plan` : 'No Plan'}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            Next billing date: {subscription?.expiresAt || 'N/A'}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Upgrade />}
            onClick={() => setUpgradeDialogOpen(true)}
          >
            Upgrade Plan
          </Button>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Usage This Month
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                Campaigns: {usage.campaigns.used} / {usage.campaigns.limit}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usage.campaigns.percentage}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                Storage: {usage.storage.used} GB / {usage.storage.limit} GB
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usage.storage.percentage}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                API Calls: {usage.apiCalls.used} / {usage.apiCalls.limit}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usage.apiCalls.percentage}
                sx={{ mt: 1 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Billing History</Typography>
            <Button startIcon={<Download />} size="small">
              Download All
            </Button>
          </Box>
          <List>
            {billingHistory.map((bill, index) => (
              <React.Fragment key={index}>
                <ListItem>
                  <ListItemIcon>
                    <CreditCard />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${bill.amount} - ${bill.invoice}`}
                    secondary={bill.date}
                  />
                  <Chip
                    label={bill.status}
                    color="success"
                    size="small"
                  />
                  <Button size="small" sx={{ ml: 2 }}>
                    Download
                  </Button>
                </ListItem>
                {index < billingHistory.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* Upgrade Dialog */}
      <Dialog
        open={upgradeDialogOpen}
        onClose={() => setUpgradeDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>Choose Your Plan</DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            {plans.map((plan, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card
                  sx={{
                    position: 'relative',
                    border: plan.current ? 2 : 1,
                    borderColor: plan.current ? 'primary.main' : 'divider'
                  }}
                >
                  {plan.popular && (
                    <Chip
                      label="Most Popular"
                      color="primary"
                      size="small"
                      icon={<Star />}
                      sx={{
                        position: 'absolute',
                        top: -10,
                        left: '50%',
                        transform: 'translateX(-50%)'
                      }}
                    />
                  )}
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {plan.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 2 }}>
                      <Typography variant="h4" color="primary">
                        {plan.price}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {plan.period}
                      </Typography>
                    </Box>
                    <List dense>
                      {plan.features.map((feature, i) => (
                        <ListItem key={i} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <Check color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={feature}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                    <Button
                      fullWidth
                      variant={plan.current ? 'outlined' : 'contained'}
                      disabled={plan.current}
                      sx={{ mt: 2 }}
                      onClick={() => {
                        if (!plan.current) {
                          setSelectedPlan({ name: plan.name, price: plan.priceValue });
                          setPaymentModalOpen(true);
                        }
                      }}
                    >
                      {plan.current ? 'Current Plan' : 'Upgrade'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpgradeDialogOpen(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Modal */}
      <PaymentModal
        open={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        plan={selectedPlan.name}
        amount={selectedPlan.price}
        onSuccess={() => {
          // Refresh subscription status
          window.location.reload();
        }}
      />
    </Box>
  );
};

export default Subscription;
