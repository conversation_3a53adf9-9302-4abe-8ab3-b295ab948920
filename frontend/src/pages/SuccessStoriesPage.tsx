import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const SuccessStoriesPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const categories = [
    { id: 'all', name: 'جميع القصص', icon: 'fas fa-th-large' },
    { id: 'ecommerce', name: 'التجارة الإلكترونية', icon: 'fas fa-shopping-cart' },
    { id: 'saas', name: 'البرمجيات كخدمة', icon: 'fas fa-cloud' },
    { id: 'retail', name: 'التجارة التقليدية', icon: 'fas fa-store' },
    { id: 'healthcare', name: 'الرعاية الصحية', icon: 'fas fa-heartbeat' },
    { id: 'education', name: 'التعليم', icon: 'fas fa-graduation-cap' }
  ];

  const successStories = [
    {
      id: 1,
      company: 'متجر الأناقة',
      category: 'ecommerce',
      industry: 'التجارة الإلكترونية',
      logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop',
      challenge: 'انخفاض معدل التحويل وارتفاع تكلفة اكتساب العملاء',
      solution: 'استخدام الذكاء الاصطناعي لتحسين تجربة التسوق وتخصيص العروض',
      results: [
        { metric: 'زيادة المبيعات', value: '250%', icon: 'fas fa-chart-line' },
        { metric: 'تحسن معدل التحويل', value: '180%', icon: 'fas fa-percentage' },
        { metric: 'انخفاض تكلفة الاكتساب', value: '45%', icon: 'fas fa-arrow-down' }
      ],
      testimonial: 'MarketMind غيّر طريقة عملنا تماماً. الآن نفهم عملاءنا بشكل أفضل ونقدم لهم تجربة مخصصة تزيد من ولائهم.',
      author: 'أحمد محمد',
      position: 'مدير التسويق',
      featured: true
    },
    {
      id: 2,
      company: 'تطبيق التعلم الذكي',
      category: 'education',
      industry: 'التعليم الرقمي',
      logo: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop',
      challenge: 'صعوبة في فهم سلوك المتعلمين وتحسين معدلات الإكمال',
      solution: 'تحليل بيانات التعلم وتخصيص المحتوى باستخدام الذكاء الاصطناعي',
      results: [
        { metric: 'زيادة معدل الإكمال', value: '320%', icon: 'fas fa-graduation-cap' },
        { metric: 'تحسن رضا المتعلمين', value: '95%', icon: 'fas fa-smile' },
        { metric: 'زيادة الاشتراكات', value: '200%', icon: 'fas fa-users' }
      ],
      testimonial: 'بفضل MarketMind، أصبحنا نفهم كيف يتعلم طلابنا ونقدم لهم محتوى مخصص يناسب احتياجاتهم.',
      author: 'فاطمة أحمد',
      position: 'مديرة المنتج',
      featured: false
    },
    {
      id: 3,
      company: 'عيادة الرعاية المتقدمة',
      category: 'healthcare',
      industry: 'الرعاية الصحية',
      logo: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop',
      challenge: 'تحسين تجربة المرضى وزيادة كفاءة العمليات',
      solution: 'تحليل رحلة المريض وتحسين عمليات الحجز والمتابعة',
      results: [
        { metric: 'تحسن رضا المرضى', value: '85%', icon: 'fas fa-heart' },
        { metric: 'تقليل أوقات الانتظار', value: '60%', icon: 'fas fa-clock' },
        { metric: 'زيادة الحجوزات', value: '150%', icon: 'fas fa-calendar-plus' }
      ],
      testimonial: 'MarketMind ساعدنا في فهم احتياجات مرضانا بشكل أفضل وتحسين خدماتنا لتقديم رعاية أكثر فعالية.',
      author: 'د. سارة خالد',
      position: 'مديرة العمليات',
      featured: true
    },
    {
      id: 4,
      company: 'منصة الخدمات السحابية',
      category: 'saas',
      industry: 'التكنولوجيا',
      logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop',
      challenge: 'ارتفاع معدل إلغاء الاشتراكات وانخفاض التفاعل',
      solution: 'تحليل سلوك المستخدمين وتطوير استراتيجيات الاحتفاظ',
      results: [
        { metric: 'تقليل معدل الإلغاء', value: '70%', icon: 'fas fa-user-check' },
        { metric: 'زيادة التفاعل', value: '300%', icon: 'fas fa-mouse-pointer' },
        { metric: 'نمو الإيرادات', value: '180%', icon: 'fas fa-dollar-sign' }
      ],
      testimonial: 'تحليلات MarketMind كشفت لنا أنماط سلوك لم نكن نعرفها، مما ساعدنا في تحسين منتجنا بشكل كبير.',
      author: 'عمر حسن',
      position: 'مدير النمو',
      featured: false
    },
    {
      id: 5,
      company: 'سلسلة متاجر الأزياء',
      category: 'retail',
      industry: 'التجارة التقليدية',
      logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop',
      challenge: 'دمج التجربة الرقمية مع المتاجر الفعلية',
      solution: 'تحليل بيانات العملاء عبر جميع القنوات وتوحيد التجربة',
      results: [
        { metric: 'زيادة المبيعات', value: '220%', icon: 'fas fa-shopping-bag' },
        { metric: 'تحسن ولاء العملاء', value: '160%', icon: 'fas fa-heart' },
        { metric: 'نمو المبيعات الرقمية', value: '400%', icon: 'fas fa-mobile-alt' }
      ],
      testimonial: 'MarketMind ساعدنا في ربط تجربة العملاء بين متاجرنا الفعلية والرقمية، مما خلق تجربة متكاملة ومميزة.',
      author: 'نور الدين',
      position: 'مدير التسويق الرقمي',
      featured: false
    },
    {
      id: 6,
      company: 'شركة الحلول المالية',
      category: 'saas',
      industry: 'الخدمات المالية',
      logo: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=100&h=100&fit=crop&crop=center',
      image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=600&h=400&fit=crop',
      challenge: 'تحسين عملية اكتساب العملاء وتقليل المخاطر',
      solution: 'استخدام التحليل التنبؤي لتقييم العملاء وتخصيص العروض',
      results: [
        { metric: 'تحسن جودة العملاء', value: '90%', icon: 'fas fa-star' },
        { metric: 'تقليل المخاطر', value: '65%', icon: 'fas fa-shield-alt' },
        { metric: 'زيادة الأرباح', value: '175%', icon: 'fas fa-chart-line' }
      ],
      testimonial: 'التحليل التنبؤي من MarketMind غيّر طريقة تقييمنا للعملاء وساعدنا في اتخاذ قرارات أكثر ذكاءً.',
      author: 'محمد علي',
      position: 'مدير المخاطر',
      featured: false
    }
  ];

  const filteredStories = selectedCategory === 'all' 
    ? successStories 
    : successStories.filter(story => story.category === selectedCategory);

  const featuredStories = successStories.filter(story => story.featured);

  return (
    <div className="success-stories-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/success-stories">قصص النجاح</a></li>
              <li><a href="/about">من نحن</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">قصص نجاح ملهمة</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                اكتشف كيف حققت الشركات نتائج استثنائية باستخدام MarketMind. 
                قصص حقيقية من عملائنا الذين حولوا تحدياتهم إلى فرص نجاح.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="features" style={{ background: 'var(--white)', padding: '3rem 0' }}>
        <div className="container">
          <div className="features-grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' }}>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--primary-color)', marginBottom: '0.5rem' }}>
                500+
              </div>
              <h3>شركة ناجحة</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--accent-color)', marginBottom: '0.5rem' }}>
                250%
              </div>
              <h3>متوسط نمو المبيعات</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--primary-color)', marginBottom: '0.5rem' }}>
                95%
              </div>
              <h3>معدل رضا العملاء</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--accent-color)', marginBottom: '0.5rem' }}>
                6 أشهر
              </div>
              <h3>متوسط تحقيق النتائج</h3>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Stories */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">القصص المميزة</h2>
            <p className="section-subtitle">
              أبرز قصص النجاح التي حققها عملاؤنا
            </p>
          </div>
          
          <div className="featured-stories" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))',
            gap: '3rem'
          }}>
            {featuredStories.map((story, index) => (
              <div 
                key={story.id}
                className="featured-story" 
                data-aos="fade-up" 
                data-aos-delay={index * 100}
                style={{
                  background: 'white',
                  borderRadius: '20px',
                  overflow: 'hidden',
                  boxShadow: 'var(--shadow-large)',
                  transition: 'transform 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-10px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <div style={{
                  height: '250px',
                  backgroundImage: `url(${story.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  position: 'relative'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: '1rem',
                    right: '1rem',
                    background: 'var(--accent-color)',
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '20px',
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>
                    {story.industry}
                  </div>
                </div>

                <div style={{ padding: '2rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1.5rem' }}>
                    <img 
                      src={story.logo} 
                      alt={story.company}
                      style={{
                        width: '60px',
                        height: '60px',
                        borderRadius: '12px',
                        objectFit: 'cover'
                      }}
                    />
                    <div>
                      <h3 style={{ color: 'var(--primary-color)', marginBottom: '0.25rem' }}>
                        {story.company}
                      </h3>
                      <p style={{ color: 'var(--text-light)', fontSize: '0.9rem' }}>
                        {story.industry}
                      </p>
                    </div>
                  </div>

                  <div style={{ marginBottom: '1.5rem' }}>
                    <h4 style={{ color: 'var(--text-dark)', marginBottom: '0.5rem' }}>التحدي:</h4>
                    <p style={{ color: 'var(--text-light)', fontSize: '0.95rem', lineHeight: '1.6' }}>
                      {story.challenge}
                    </p>
                  </div>

                  <div style={{ marginBottom: '2rem' }}>
                    <h4 style={{ color: 'var(--text-dark)', marginBottom: '1rem' }}>النتائج:</h4>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '1rem' }}>
                      {story.results.map((result, resultIndex) => (
                        <div key={resultIndex} style={{ textAlign: 'center' }}>
                          <div style={{
                            width: '50px',
                            height: '50px',
                            borderRadius: '50%',
                            background: 'var(--bg-light)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            margin: '0 auto 0.5rem',
                            color: 'var(--primary-color)'
                          }}>
                            <i className={result.icon}></i>
                          </div>
                          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>
                            {result.value}
                          </div>
                          <div style={{ fontSize: '0.8rem', color: 'var(--text-light)' }}>
                            {result.metric}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <blockquote style={{
                    borderRight: '4px solid var(--primary-color)',
                    paddingRight: '1rem',
                    fontStyle: 'italic',
                    color: 'var(--text-dark)',
                    marginBottom: '1rem',
                    lineHeight: '1.6'
                  }}>
                    "{story.testimonial}"
                  </blockquote>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <strong style={{ color: 'var(--primary-color)' }}>{story.author}</strong>
                    <span style={{ color: 'var(--text-light)' }}>- {story.position}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* All Stories */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">جميع قصص النجاح</h2>
          </div>
          
          {/* Category Filter */}
          <div className="category-filters" data-aos="fade-up" data-aos-delay="200" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem'
          }}>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: selectedCategory === category.id ? '2px solid var(--primary-color)' : '2px solid transparent',
                  background: selectedCategory === category.id ? 'var(--primary-color)' : 'white',
                  color: selectedCategory === category.id ? 'white' : 'var(--text-dark)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontWeight: '500',
                  boxShadow: 'var(--shadow-soft)'
                }}
              >
                <i className={category.icon}></i>
                {category.name}
              </button>
            ))}
          </div>

          {/* Stories Grid */}
          <div className="stories-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '2rem'
          }}>
            {filteredStories.map((story, index) => (
              <div 
                key={story.id}
                className="story-card" 
                data-aos="fade-up" 
                data-aos-delay={index * 50}
                style={{
                  background: 'white',
                  borderRadius: '15px',
                  overflow: 'hidden',
                  boxShadow: 'var(--shadow-medium)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                }}
              >
                <div style={{
                  height: '200px',
                  backgroundImage: `url(${story.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}></div>

                <div style={{ padding: '1.5rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                    <img 
                      src={story.logo} 
                      alt={story.company}
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '8px',
                        objectFit: 'cover'
                      }}
                    />
                    <div>
                      <h3 style={{ color: 'var(--primary-color)', marginBottom: '0.25rem', fontSize: '1.1rem' }}>
                        {story.company}
                      </h3>
                      <p style={{ color: 'var(--text-light)', fontSize: '0.85rem' }}>
                        {story.industry}
                      </p>
                    </div>
                  </div>

                  <p style={{ 
                    color: 'var(--text-light)', 
                    marginBottom: '1rem',
                    fontSize: '0.9rem',
                    lineHeight: '1.5'
                  }}>
                    {story.challenge}
                  </p>

                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', gap: '1rem' }}>
                      {story.results.slice(0, 2).map((result, resultIndex) => (
                        <div key={resultIndex} style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: 'var(--accent-color)' }}>
                            {result.value}
                          </div>
                          <div style={{ fontSize: '0.7rem', color: 'var(--text-light)' }}>
                            {result.metric}
                          </div>
                        </div>
                      ))}
                    </div>
                    <span style={{ color: 'var(--primary-color)', fontSize: '0.9rem' }}>
                      اقرأ المزيد <i className="fas fa-arrow-left"></i>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>هل أنت مستعد لتكون قصة النجاح التالية؟</h2>
            <p>انضم إلى مئات الشركات التي حققت نتائج استثنائية مع MarketMind</p>
            <div className="cta-buttons">
              <a href="/signup" className="btn btn-white">ابدأ تجربتك المجانية</a>
              <a href="/contact" className="btn btn-outline-white">تحدث مع خبير</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/success-stories">قصص النجاح</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SuccessStoriesPage;
