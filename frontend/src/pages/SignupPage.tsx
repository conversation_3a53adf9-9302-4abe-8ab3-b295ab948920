import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/enhanced-homepage.css';

const SignupPage: React.FC = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    company: '',
    phone: '',
    agreeToTerms: false,
    subscribeNewsletter: true
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('كلمات المرور غير متطابقة');
      return;
    }

    if (formData.password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    if (!formData.agreeToTerms) {
      setError('يجب الموافقة على الشروط والأحكام');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare user data
      const userData = {
        name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        password: formData.password,
        user_type: 'personal' // Default to personal account
      };

      // For demo purposes, we'll show success message
      // In production, this would call the real API
      console.log('Signup attempt:', userData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSuccess('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.');

      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate('/login');
      }, 2000);

    } catch (error: any) {
      console.error('Signup error:', error);
      setError(error.message || 'حدث خطأ أثناء إنشاء الحساب');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div className="signup-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/pricing">الأسعار</a></li>
              <li><a href="/about">من نحن</a></li>
            </ul>
            <div className="nav-buttons">
              <button
                className="btn btn-outline"
                onClick={() => navigate('/login')}
              >
                تسجيل الدخول
              </button>
            </div>
          </nav>
        </div>
      </header>

      {/* Signup Section */}
      <section className="auth-section" style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        background: 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
        padding: '2rem 0'
      }}>
        <div className="container">
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '4rem',
            alignItems: 'center',
            maxWidth: '1200px',
            margin: '0 auto'
          }}>
            
            {/* Left Side - Welcome Content */}
            <div className="auth-content" data-aos="fade-right">
              <h1 style={{ 
                color: 'white', 
                fontSize: '2.5rem', 
                marginBottom: '1.5rem',
                fontWeight: 'bold'
              }}>
                ابدأ رحلتك مع MarketMind
              </h1>
              <p style={{ 
                color: 'rgba(255,255,255,0.9)', 
                fontSize: '1.1rem',
                lineHeight: '1.6',
                marginBottom: '2rem'
              }}>
                انضم إلى آلاف الشركات التي تستخدم MarketMind لتحويل بياناتها التسويقية 
                إلى نتائج استثنائية وقرارات ذكية.
              </p>
              
              <div className="signup-benefits" style={{ color: 'white' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-gift" style={{ color: 'var(--accent-color)' }}></i>
                  <span>تجربة مجانية لمدة 14 يوماً</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-credit-card" style={{ color: 'var(--accent-color)' }}></i>
                  <span>لا حاجة لبطاقة ائتمان</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <i className="fas fa-headset" style={{ color: 'var(--accent-color)' }}></i>
                  <span>دعم فني مجاني</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <i className="fas fa-shield-alt" style={{ color: 'var(--accent-color)' }}></i>
                  <span>أمان وخصوصية مضمونة</span>
                </div>
              </div>

              <div style={{ 
                marginTop: '2rem',
                padding: '1.5rem',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '12px',
                backdropFilter: 'blur(10px)'
              }}>
                <h3 style={{ color: 'white', marginBottom: '1rem' }}>ماذا ستحصل عليه:</h3>
                <ul style={{ color: 'rgba(255,255,255,0.9)', listStyle: 'none', padding: 0 }}>
                  <li style={{ marginBottom: '0.5rem' }}>• تحليلات متقدمة لجميع قنواتك التسويقية</li>
                  <li style={{ marginBottom: '0.5rem' }}>• تقارير مخصصة وتفاعلية</li>
                  <li style={{ marginBottom: '0.5rem' }}>• ذكاء اصطناعي لتحسين الحملات</li>
                  <li>• تكاملات مع أكثر من 100 أداة</li>
                </ul>
              </div>
            </div>

            {/* Right Side - Signup Form */}
            <div className="auth-form-container" data-aos="fade-left">
              <div style={{
                background: 'white',
                padding: '3rem',
                borderRadius: '20px',
                boxShadow: 'var(--shadow-large)',
                maxHeight: '90vh',
                overflowY: 'auto'
              }}>
                <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                  <h2 style={{ 
                    color: 'var(--primary-color)', 
                    marginBottom: '0.5rem',
                    fontSize: '1.75rem'
                  }}>
                    إنشاء حساب جديد
                  </h2>
                  <p style={{ color: 'var(--text-light)' }}>
                    املأ البيانات أدناه لإنشاء حسابك
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="auth-form">
                  {error && (
                    <div style={{
                      background: '#fee2e2',
                      border: '1px solid #fecaca',
                      color: '#dc2626',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      marginBottom: '1.5rem',
                      fontSize: '0.9rem'
                    }}>
                      {error}
                    </div>
                  )}

                  {success && (
                    <div style={{
                      background: '#dcfce7',
                      border: '1px solid #bbf7d0',
                      color: '#16a34a',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      marginBottom: '1.5rem',
                      fontSize: '0.9rem'
                    }}>
                      {success}
                    </div>
                  )}

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
                    <div className="form-group">
                      <label htmlFor="firstName" style={{ 
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        الاسم الأول *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                        placeholder="الاسم الأول"
                        style={{
                          width: '100%',
                          padding: '1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease'
                        }}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="lastName" style={{ 
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        الاسم الأخير *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                        placeholder="الاسم الأخير"
                        style={{
                          width: '100%',
                          padding: '1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease'
                        }}
                      />
                    </div>
                  </div>

                  <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                    <label htmlFor="email" style={{ 
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontWeight: '600',
                      color: 'var(--text-dark)'
                    }}>
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      placeholder="أدخل بريدك الإلكتروني"
                      style={{
                        width: '100%',
                        padding: '1rem',
                        border: '2px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        transition: 'border-color 0.3s ease',
                        direction: 'ltr'
                      }}
                    />
                  </div>

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1.5rem' }}>
                    <div className="form-group">
                      <label htmlFor="company" style={{ 
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        اسم الشركة
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        placeholder="اسم شركتك"
                        style={{
                          width: '100%',
                          padding: '1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease'
                        }}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="phone" style={{ 
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: 'var(--text-dark)'
                      }}>
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="رقم الهاتف"
                        style={{
                          width: '100%',
                          padding: '1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease',
                          direction: 'ltr'
                        }}
                      />
                    </div>
                  </div>

                  <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                    <label htmlFor="password" style={{ 
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontWeight: '600',
                      color: 'var(--text-dark)'
                    }}>
                      كلمة المرور *
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        placeholder="أدخل كلمة المرور"
                        style={{
                          width: '100%',
                          padding: '1rem 3rem 1rem 1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease',
                          direction: 'ltr'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        style={{
                          position: 'absolute',
                          left: '1rem',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: 'var(--text-light)',
                          cursor: 'pointer'
                        }}
                      >
                        <i className={showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'}></i>
                      </button>
                    </div>
                  </div>

                  <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                    <label htmlFor="confirmPassword" style={{ 
                      display: 'block',
                      marginBottom: '0.5rem',
                      fontWeight: '600',
                      color: 'var(--text-dark)'
                    }}>
                      تأكيد كلمة المرور *
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required
                        placeholder="أعد إدخال كلمة المرور"
                        style={{
                          width: '100%',
                          padding: '1rem 3rem 1rem 1rem',
                          border: '2px solid #e5e7eb',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          transition: 'border-color 0.3s ease',
                          direction: 'ltr'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        style={{
                          position: 'absolute',
                          left: '1rem',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: 'var(--text-light)',
                          cursor: 'pointer'
                        }}
                      >
                        <i className={showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'}></i>
                      </button>
                    </div>
                  </div>

                  <div style={{ marginBottom: '2rem' }}>
                    <label style={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: '0.75rem',
                      cursor: 'pointer',
                      marginBottom: '1rem'
                    }}>
                      <input
                        type="checkbox"
                        name="agreeToTerms"
                        checked={formData.agreeToTerms}
                        onChange={handleInputChange}
                        required
                        style={{ marginTop: '0.25rem' }}
                      />
                      <span style={{ fontSize: '0.9rem', color: 'var(--text-dark)', lineHeight: '1.5' }}>
                        أوافق على{' '}
                        <a href="/terms" style={{ color: 'var(--primary-color)' }}>الشروط والأحكام</a>
                        {' '}و{' '}
                        <a href="/privacy" style={{ color: 'var(--primary-color)' }}>سياسة الخصوصية</a>
                      </span>
                    </label>
                    
                    <label style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '0.75rem',
                      cursor: 'pointer'
                    }}>
                      <input
                        type="checkbox"
                        name="subscribeNewsletter"
                        checked={formData.subscribeNewsletter}
                        onChange={handleInputChange}
                      />
                      <span style={{ fontSize: '0.9rem', color: 'var(--text-dark)' }}>
                        أريد الحصول على النشرة الإخبارية والتحديثات
                      </span>
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    style={{
                      width: '100%',
                      padding: '1rem',
                      background: isLoading ? '#9ca3af' : 'var(--primary-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      cursor: isLoading ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      marginBottom: '1.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}
                  >
                    {isLoading && (
                      <div style={{
                        width: '20px',
                        height: '20px',
                        border: '2px solid transparent',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}></div>
                    )}
                    {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب مجاني'}
                  </button>

                  <div style={{ textAlign: 'center' }}>
                    <span style={{ color: 'var(--text-light)' }}>
                      لديك حساب بالفعل؟{' '}
                    </span>
                    <button
                      onClick={() => navigate('/login')}
                      style={{
                        color: 'var(--primary-color)',
                        background: 'none',
                        border: 'none',
                        textDecoration: 'underline',
                        fontWeight: '600',
                        cursor: 'pointer'
                      }}
                    >
                      تسجيل الدخول
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer" style={{ background: 'var(--text-dark)' }}>
        <div className="container">
          <div style={{ 
            textAlign: 'center', 
            padding: '2rem 0',
            color: 'white'
          }}>
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
            <div style={{ marginTop: '1rem' }}>
              <a href="/privacy" style={{ color: 'rgba(255,255,255,0.7)', marginLeft: '2rem' }}>
                سياسة الخصوصية
              </a>
              <a href="/terms" style={{ color: 'rgba(255,255,255,0.7)' }}>
                الشروط والأحكام
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
    </>
  );
};

export default SignupPage;
