import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  LinearProgress,
  Chip,
  Avatar,
  AvatarGroup,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  Add,
  MoreVert,
  PlayArrow,
  Pause,
  CheckCircle,
  Schedule,
  TrendingUp,
  Assignment
} from '@mui/icons-material';
import { StatCard, ChartCard, ResponsiveHeader, ResponsiveGrid } from '../../components/business';
import '../../styles/business-theme.css';

const Projects: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [newProjectDialog, setNewProjectDialog] = useState(false);

  const projectStats = [
    {
      title: 'إجمالي المشاريع',
      value: '12',
      change: '+2',
      trend: 'up',
      icon: <Assignment />,
      color: 'primary'
    },
    {
      title: 'المشاريع النشطة',
      value: '8',
      change: '+1',
      trend: 'up',
      icon: <PlayArrow />,
      color: 'success'
    },
    {
      title: 'المشاريع المكتملة',
      value: '4',
      change: '+1',
      trend: 'up',
      icon: <CheckCircle />,
      color: 'info'
    },
    {
      title: 'متوسط التقدم',
      value: '68%',
      change: '+5%',
      trend: 'up',
      icon: <TrendingUp />,
      color: 'warning'
    }
  ];

  const projects = [
    {
      id: 1,
      name: 'حملة الصيف 2024',
      status: 'قيد التنفيذ',
      progress: 75,
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      budget: '200,000 ر.س',
      spent: '150,000 ر.س',
      team: ['سأ', 'مع', 'فح'],
      tasks: { completed: 12, total: 16 },
      roi: '+285%',
      priority: 'عالية',
      description: 'حملة تسويقية شاملة لفصل الصيف تستهدف الشباب'
    },
    {
      id: 2,
      name: 'حملة إطلاق المنتج',
      status: 'التخطيط',
      progress: 25,
      startDate: '2024-02-01',
      endDate: '2024-04-30',
      budget: '300,000 ر.س',
      spent: '75,000 ر.س',
      team: ['مع', 'أم'],
      tasks: { completed: 3, total: 12 },
      roi: 'قيد التحديد',
      priority: 'متوسطة',
      description: 'حملة شاملة لإطلاق منتج جديد في السوق'
    },
    {
      id: 3,
      name: 'عروض العيد الخاصة',
      status: 'مكتمل',
      progress: 100,
      startDate: '2023-11-01',
      endDate: '2023-12-31',
      budget: '120,000 ر.س',
      spent: '114,000 ر.س',
      team: ['سأ', 'فح', 'أم'],
      tasks: { completed: 8, total: 8 },
      roi: '+420%',
      priority: 'عالية',
      description: 'حملة عروض خاصة لموسم الأعياد'
    },
    {
      id: 4,
      name: 'حملة الوعي بالعلامة التجارية',
      status: 'متوقف',
      progress: 40,
      startDate: '2024-01-15',
      endDate: '2024-05-15',
      budget: '160,000 ر.س',
      spent: '64,000 ر.س',
      team: ['مع'],
      tasks: { completed: 4, total: 10 },
      roi: '+150%',
      priority: 'منخفضة',
      description: 'حملة لزيادة الوعي بالعلامة التجارية'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل':
        return 'success';
      case 'قيد التنفيذ':
        return 'primary';
      case 'التخطيط':
        return 'info';
      case 'متوقف':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'مكتمل':
        return <CheckCircle />;
      case 'قيد التنفيذ':
        return <PlayArrow />;
      case 'التخطيط':
        return <Schedule />;
      case 'متوقف':
        return <Pause />;
      default:
        return <Schedule />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية':
        return 'error';
      case 'متوسطة':
        return 'warning';
      case 'منخفضة':
        return 'success';
      default:
        return 'default';
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, projectId: number) => {
    setAnchorEl(event.currentTarget);
    setSelectedProject(projectId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProject(null);
  };

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="متابعة المشاريع"
        subtitle="إدارة ومتابعة تقدم المشاريع والحملات التسويقية"
        primaryAction={{
          label: 'مشروع جديد',
          icon: <Add />,
          onClick: () => setNewProjectDialog(true)
        }}
      />

      {/* Project Stats */}
      <ResponsiveGrid
        columns={{ xs: 1, sm: 2, md: 4 }}
        gap={3}
        className="business-grid-mobile"
      >
        {projectStats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            trend={stat.trend as 'up' | 'down' | 'neutral'}
            icon={stat.icon}
            color={stat.color as any}
          />
        ))}
      </ResponsiveGrid>

      {/* Projects Grid */}
      <ResponsiveGrid
        columns={{ xs: 1, md: 2, lg: 3 }}
        gap={3}
        className="business-grid-mobile"
      >
        {projects.map((project) => (
          <ChartCard
            key={project.id}
            title={project.name}
            subtitle={project.description}
            height="auto"
          >
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label={project.status}
                    color={getStatusColor(project.status)}
                    size="small"
                    icon={getStatusIcon(project.status)}
                  />
                  <Chip
                    label={project.priority}
                    color={getPriorityColor(project.priority)}
                    size="small"
                    variant="outlined"
                  />
                </Box>
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuClick(e, project.id)}
                >
                  <MoreVert />
                </IconButton>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    التقدم
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                    {project.progress}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={project.progress}
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 4,
                      backgroundColor: project.progress >= 75 ? 'success.main' : 
                                     project.progress >= 50 ? 'warning.main' : 'primary.main'
                    }
                  }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  الميزانية: {project.budget} | المُنفق: {project.spent}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  المهام: {project.tasks.completed}/{project.tasks.total} مكتملة
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  العائد على الاستثمار: {project.roi}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 32, height: 32, fontSize: '0.875rem' } }}>
                  {project.team.map((member, index) => (
                    <Avatar key={index} sx={{ bgcolor: 'primary.main' }}>{member}</Avatar>
                  ))}
                </AvatarGroup>
                <Typography variant="caption" color="text.secondary">
                  {project.startDate} - {project.endDate}
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        ))}
      </ResponsiveGrid>

      {/* Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>تعديل المشروع</MenuItem>
        <MenuItem onClick={handleMenuClose}>عرض التفاصيل</MenuItem>
        <MenuItem onClick={handleMenuClose}>حذف المشروع</MenuItem>
      </Menu>

      {/* New Project Dialog */}
      <Dialog
        open={newProjectDialog}
        onClose={() => setNewProjectDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>إضافة مشروع جديد</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="اسم المشروع"
            fullWidth
            variant="outlined"
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="الوصف"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            sx={{ mb: 2 }}
          />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel>الحالة</InputLabel>
              <Select label="الحالة">
                <MenuItem value="planning">التخطيط</MenuItem>
                <MenuItem value="in-progress">قيد التنفيذ</MenuItem>
                <MenuItem value="completed">مكتمل</MenuItem>
                <MenuItem value="on-hold">متوقف</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>الأولوية</InputLabel>
              <Select label="الأولوية">
                <MenuItem value="high">عالية</MenuItem>
                <MenuItem value="medium">متوسطة</MenuItem>
                <MenuItem value="low">منخفضة</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewProjectDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={() => setNewProjectDialog(false)}>
            إضافة المشروع
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Projects;
