import React, { useState } from 'react';
import { 
  Container, 
  Grid, 
  Typography, 
  Box, 
  Divider,
  Alert
} from '@mui/material';
import { 
  Dashboard, 
  People, 
  Star, 
  TrendingUp,
  Save,
  Delete,
  Edit,
  Add
} from '@mui/icons-material';

// استيراد المكونات المحسنة
import EnhancedCard from '../../components/ui/EnhancedCard';
import {
  PrimaryButton,
  SecondaryButton,
  OutlineButton,
  GhostButton,
  GradientButton
} from '../../components/ui/EnhancedButton';
import EnhancedTable from '../../components/ui/EnhancedTable';
import { FormField, SelectField, FormContainer } from '../../components/ui/EnhancedForm';
import { ConfirmationModal, InfoModal } from '../../components/ui/EnhancedModal';
import {
  LoadingSpinner,
  LoadingBar,
  AnimatedBox,
  ProgressIndicator
} from '../../components/ui/LoadingAndAnimations';
import { theme } from '../../theme';

const UITestPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [infoModalOpen, setInfoModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    department: ''
  });

  // بيانات تجريبية للجدول
  const tableData = [
    { id: 1, name: 'أحمد محمد', email: '<EMAIL>', role: 'مطور', department: 'التطوير' },
    { id: 2, name: 'فاطمة علي', email: '<EMAIL>', role: 'مصممة', department: 'التصميم' },
    { id: 3, name: 'محمد السعد', email: '<EMAIL>', role: 'محلل', department: 'التحليلات' }
  ];

  const tableColumns = [
    { id: 'name', label: 'الاسم', minWidth: 150 },
    { id: 'email', label: 'البريد الإلكتروني', minWidth: 200 },
    { id: 'role', label: 'المنصب', minWidth: 120 },
    { id: 'department', label: 'القسم', minWidth: 120 }
  ];

  const handleLoadingTest = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <AnimatedBox animation="fadeIn" duration={0.6}>
        {/* العنوان */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography
            variant="h2"
            sx={{
              fontSize: theme.typography.fontSize['4xl'],
              fontWeight: theme.typography.fontWeight.bold,
              background: theme.effects.gradients.primary,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 2
            }}
          >
            🎨 اختبار نظام التصميم الجديد
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: theme.colors.text.secondary,
              fontWeight: theme.typography.fontWeight.medium
            }}
          >
            عرض شامل لجميع المكونات المحسنة والتأثيرات الجديدة
          </Typography>
        </Box>

        {/* قسم البطاقات */}
        <AnimatedBox animation="slideInRight" delay={0.2} duration={0.5}>
          <Typography variant="h4" sx={{ mb: 3, color: theme.colors.text.primary }}>
            📊 البطاقات المحسنة
          </Typography>
          <Grid container spacing={3} sx={{ mb: 6 }}>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedCard
                title="إجمالي المبيعات"
                value="125,430"
                subtitle="ريال سعودي"
                trend={{ direction: 'up', value: '+12.5%', label: 'هذا الشهر' }}
                icon={<Dashboard />}
                color="primary"
                variant="default"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedCard
                title="العملاء الجدد"
                value={847}
                subtitle="عميل جديد"
                trend={{ direction: 'up', value: '+8.2%', label: 'نمو مستمر' }}
                icon={<People />}
                color="success"
                variant="elevated"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedCard
                title="التقييم"
                value="4.8/5"
                subtitle="رضا العملاء"
                trend={{ direction: 'neutral', value: '0%', label: 'مستقر' }}
                icon={<Star />}
                color="warning"
                variant="glass"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <EnhancedCard
                title="النمو"
                value="15.3%"
                subtitle="معدل النمو"
                trend={{ direction: 'up', value: '+2.1%', label: 'تحسن ممتاز' }}
                icon={<TrendingUp />}
                color="info"
                variant="gradient"
              />
            </Grid>
          </Grid>
        </AnimatedBox>

        {/* قسم الأزرار */}
        <AnimatedBox animation="slideInRight" delay={0.4} duration={0.5}>
          <Typography variant="h4" sx={{ mb: 3, color: theme.colors.text.primary }}>
            🔘 الأزرار المحسنة
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 6 }}>
            <PrimaryButton startIcon={<Save />}>حفظ</PrimaryButton>
            <SecondaryButton startIcon={<Edit />}>تعديل</SecondaryButton>
            <OutlineButton startIcon={<Add />}>إضافة</OutlineButton>
            <GhostButton startIcon={<Delete />}>حذف</GhostButton>
            <GradientButton loading={loading} onClick={handleLoadingTest}>
              {loading ? 'جاري التحميل...' : 'اختبار التحميل'}
            </GradientButton>
          </Box>
        </AnimatedBox>

        {/* قسم مؤشرات التقدم */}
        <AnimatedBox animation="slideInRight" delay={0.6} duration={0.5}>
          <Typography variant="h4" sx={{ mb: 3, color: theme.colors.text.primary }}>
            📈 مؤشرات التقدم
          </Typography>
          <Grid container spacing={3} sx={{ mb: 6 }}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <ProgressIndicator
                  value={75}
                  label="إنجاز المشروع"
                  color="primary"
                  size="large"
                />
              </Box>
              <Box sx={{ mb: 3 }}>
                <ProgressIndicator
                  value={60}
                  label="رضا العملاء"
                  color="success"
                  size="medium"
                />
              </Box>
              <Box>
                <ProgressIndicator
                  value={40}
                  label="الأداء العام"
                  color="warning"
                  size="small"
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, alignItems: 'center' }}>
                <LoadingSpinner size={60} color="primary" />
                <LoadingBar color="success" />
                <Box sx={{ width: '100%' }}>
                  <LoadingBar value={65} variant="determinate" color="info" />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </AnimatedBox>

        {/* قسم النماذج */}
        <AnimatedBox animation="slideInRight" delay={0.8} duration={0.5}>
          <Typography variant="h4" sx={{ mb: 3, color: theme.colors.text.primary }}>
            📝 النماذج المحسنة
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            <Grid item xs={12} md={6}>
              <FormContainer title="نموذج تجريبي" description="اختبار المكونات المحسنة">
                <FormField
                  label="الاسم الكامل"
                  name="name"
                  value={formData.name}
                  onChange={(value) => setFormData(prev => ({ ...prev, name: value as string }))}
                  placeholder="أدخل اسمك الكامل"
                  required
                  startIcon={<People />}
                />
                
                <FormField
                  label="البريد الإلكتروني"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={(value) => setFormData(prev => ({ ...prev, email: value as string }))}
                  placeholder="<EMAIL>"
                  required
                />
                
                <SelectField
                  label="المنصب"
                  name="role"
                  value={formData.role}
                  onChange={(value) => setFormData(prev => ({ ...prev, role: value as string }))}
                  options={[
                    { value: 'developer', label: 'مطور' },
                    { value: 'designer', label: 'مصمم' },
                    { value: 'analyst', label: 'محلل' },
                    { value: 'manager', label: 'مدير' }
                  ]}
                  placeholder="اختر المنصب"
                  required
                />
                
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <PrimaryButton fullWidth>حفظ البيانات</PrimaryButton>
                  <OutlineButton fullWidth>إلغاء</OutlineButton>
                </Box>
              </FormContainer>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Alert severity="success">تم حفظ البيانات بنجاح!</Alert>
                <Alert severity="warning">تحذير: تأكد من صحة البيانات</Alert>
                <Alert severity="error">خطأ: فشل في حفظ البيانات</Alert>
                <Alert severity="info">معلومة: يمكنك تعديل البيانات لاحقاً</Alert>
                
                <Divider sx={{ my: 2 }} />
                
                <PrimaryButton onClick={() => setConfirmModalOpen(true)}>
                  اختبار نافذة التأكيد
                </PrimaryButton>
                <SecondaryButton onClick={() => setInfoModalOpen(true)}>
                  اختبار نافذة المعلومات
                </SecondaryButton>
              </Box>
            </Grid>
          </Grid>
        </AnimatedBox>

        {/* قسم الجدول */}
        <AnimatedBox animation="fadeIn" delay={1.0} duration={0.6}>
          <Typography variant="h4" sx={{ mb: 3, color: theme.colors.text.primary }}>
            📋 الجدول المحسن
          </Typography>
          <EnhancedTable
            title="بيانات الموظفين"
            columns={tableColumns}
            data={tableData}
            searchable={true}
            filterable={true}
            exportable={true}
            selectable={true}
            actions={(row) => (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <GhostButton size="small" startIcon={<Edit />}>تعديل</GhostButton>
                <GhostButton size="small" startIcon={<Delete />}>حذف</GhostButton>
              </Box>
            )}
          />
        </AnimatedBox>
      </AnimatedBox>

      {/* النوافذ المنبثقة */}
      <ConfirmationModal
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        onConfirm={() => {
          console.log('تم التأكيد');
          setConfirmModalOpen(false);
        }}
        title="تأكيد الإجراء"
        message="هل أنت متأكد من تنفيذ هذا الإجراء؟ لا يمكن التراجع عنه."
        confirmText="تأكيد"
        cancelText="إلغاء"
        variant="warning"
      />

      <InfoModal
        open={infoModalOpen}
        onClose={() => setInfoModalOpen(false)}
        title="معلومات النظام"
        icon={<Star />}
      >
        <Typography variant="body1" sx={{ mb: 2 }}>
          مرحباً بك في نظام التصميم الجديد لمنصة MarketMind!
        </Typography>
        <Typography variant="body2" color="textSecondary">
          تم تطوير هذا النظام ليوفر تجربة مستخدم محسنة مع تصميم حديث ومتجاوب.
          يتضمن النظام مكونات محسنة، ألوان متناسقة، وتأثيرات تفاعلية سلسة.
        </Typography>
      </InfoModal>
    </Container>
  );
};

export default UITestPage;
