import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Avatar,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  MoreVert,
  Groups,
  TrendingUp,
  Assignment,
  CheckCircle
} from '@mui/icons-material';
import { StatCard, DataTable, ResponsiveHeader, ResponsiveGrid } from '../../components/business';
import type { Column } from '../../components/business';
import '../../styles/business-theme.css';

const TeamManagement: React.FC = () => {
  const [addMemberDialog, setAddMemberDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newMember, setNewMember] = useState({
    name: '',
    email: '',
    role: '',
    department: ''
  });

  const teamMembers = [
    {
      id: 1,
      name: 'سارة أحمد',
      email: '<EMAIL>',
      role: 'مدير التسويق',
      department: 'التسويق',
      status: 'نشط',
      lastActive: 'منذ ساعتين',
      campaigns: 8,
      avatar: 'سأ',
      performance: 95,
      joinDate: '2023-01-15'
    },
    {
      id: 2,
      name: 'محمد علي',
      email: '<EMAIL>',
      role: 'أخصائي المحتوى',
      department: 'التسويق',
      status: 'نشط',
      lastActive: 'منذ يوم',
      campaigns: 5,
      avatar: 'مع',
      performance: 88,
      joinDate: '2023-03-20'
    },
    {
      id: 3,
      name: 'فاطمة حسن',
      email: '<EMAIL>',
      role: 'محلل البيانات',
      department: 'التحليلات',
      status: 'نشط',
      lastActive: 'منذ 3 ساعات',
      campaigns: 12,
      avatar: 'فح',
      performance: 92,
      joinDate: '2022-11-10'
    },
    {
      id: 4,
      name: 'أحمد محمود',
      email: '<EMAIL>',
      role: 'مدير الحملات',
      department: 'التسويق',
      status: 'غير نشط',
      lastActive: 'منذ أسبوع',
      campaigns: 3,
      avatar: 'أم',
      performance: 75,
      joinDate: '2023-06-05'
    },
    {
      id: 5,
      name: 'نور الدين',
      email: '<EMAIL>',
      role: 'مطور واجهات',
      department: 'التطوير',
      status: 'نشط',
      lastActive: 'منذ ساعة',
      campaigns: 2,
      avatar: 'نا',
      performance: 90,
      joinDate: '2023-08-12'
    }
  ];

  const roles = [
    'مدير التسويق',
    'أخصائي المحتوى',
    'محلل البيانات',
    'مدير الحملات',
    'مدير وسائل التواصل',
    'أخصائي SEO',
    'مطور واجهات',
    'مصمم جرافيك'
  ];

  const departments = [
    'التسويق',
    'التحليلات',
    'المبيعات',
    'المنتج',
    'خدمة العملاء',
    'التطوير',
    'التصميم'
  ];

  // Define table columns
  const columns: Column[] = [
    {
      id: 'member',
      label: 'عضو الفريق',
      minWidth: 200,
      format: (value: any) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            {value.avatar}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              {value.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {value.email}
            </Typography>
          </Box>
        </Box>
      )
    },
    {
      id: 'role',
      label: 'الدور',
      minWidth: 150,
      sortable: true
    },
    {
      id: 'department',
      label: 'القسم',
      minWidth: 120,
      sortable: true
    },
    {
      id: 'status',
      label: 'الحالة',
      minWidth: 100,
      format: (value: string) => (
        <Chip
          label={value}
          color={value === 'نشط' ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      )
    },
    {
      id: 'performance',
      label: 'الأداء',
      minWidth: 100,
      align: 'center',
      format: (value: number) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {value}%
          </Typography>
          {value >= 90 && <CheckCircle color="success" fontSize="small" />}
        </Box>
      )
    },
    {
      id: 'campaigns',
      label: 'الحملات',
      minWidth: 100,
      align: 'center',
      sortable: true
    },
    {
      id: 'lastActive',
      label: 'آخر نشاط',
      minWidth: 120
    },
    {
      id: 'actions',
      label: 'الإجراءات',
      minWidth: 120,
      align: 'center',
      format: () => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <IconButton size="small" color="primary">
            <Edit fontSize="small" />
          </IconButton>
          <IconButton size="small" color="error">
            <Delete fontSize="small" />
          </IconButton>
          <IconButton size="small">
            <MoreVert fontSize="small" />
          </IconButton>
        </Box>
      )
    }
  ];

  // Transform team members data for the table
  const tableData = teamMembers.map(member => ({
    ...member,
    member: { name: member.name, email: member.email, avatar: member.avatar }
  }));

  const handleAddMember = async () => {
    setLoading(true);
    try {
      // TODO: Add member to backend
      console.log('Adding member:', newMember);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setAddMemberDialog(false);
      setNewMember({ name: '', email: '', role: '', department: '' });
    } catch (error) {
      console.error('Error adding member:', error);
    } finally {
      setLoading(false);
    }
  };



  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="إدارة الفريق"
        subtitle="إدارة أعضاء الفريق ومتابعة أدائهم وتنظيم المهام"
        primaryAction={{
          label: 'إضافة عضو جديد',
          icon: <Add />,
          onClick: () => setAddMemberDialog(true)
        }}
      />

      {/* Team Overview Cards */}
      <ResponsiveGrid
        columns={{ xs: 1, sm: 2, md: 4 }}
        gap={3}
        className="business-grid-mobile"
      >
        <StatCard
          title="إجمالي الأعضاء"
          value={teamMembers.length}
          icon={<Groups />}
          color="primary"
          subtitle="عضو في الفريق"
          onClick={() => console.log('View all members')}
        />
        <StatCard
          title="الأعضاء النشطون"
          value={teamMembers.filter(m => m.status === 'نشط').length}
          change={`+${Math.round((teamMembers.filter(m => m.status === 'نشط').length / teamMembers.length) * 100)}%`}
          trend="up"
          icon={<CheckCircle />}
          color="success"
          subtitle="من إجمالي الفريق"
        />
        <StatCard
          title="الأقسام"
          value={departments.length}
          icon={<Assignment />}
          color="info"
          subtitle="قسم مختلف"
        />
        <StatCard
          title="إجمالي الحملات"
          value={teamMembers.reduce((sum, member) => sum + member.campaigns, 0)}
          change="+12%"
          trend="up"
          icon={<TrendingUp />}
          color="warning"
          subtitle="حملة نشطة"
        />
      </ResponsiveGrid>

      {/* Team Members Table */}
      <DataTable
        title="قائمة أعضاء الفريق"
        columns={columns}
        rows={tableData}
        searchable={true}
        downloadable={true}
        onRowClick={(row) => console.log('Selected member:', row)}
        onDownload={() => console.log('Download team data')}
        defaultRowsPerPage={10}
        maxHeight={600}
      />

      {/* Add Member Dialog */}
      <Dialog
        open={addMemberDialog}
        onClose={() => setAddMemberDialog(false)}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: 'var(--business-shadow-xl)'
            }
          }
        }}
      >
        <DialogTitle sx={{
          background: 'var(--business-gradient-primary)',
          color: 'white',
          fontWeight: 600,
          fontSize: '1.25rem'
        }}>
          إضافة عضو جديد للفريق
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 2 }}>
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
            gap: 3
          }}>
            <TextField
              fullWidth
              label="الاسم الكامل"
              value={newMember.name}
              onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
              variant="outlined"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            />
            <TextField
              fullWidth
              label="البريد الإلكتروني"
              type="email"
              value={newMember.email}
              onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
              variant="outlined"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            />
            <FormControl fullWidth required>
              <InputLabel>الدور الوظيفي</InputLabel>
              <Select
                value={newMember.role}
                label="الدور الوظيفي"
                onChange={(e) => setNewMember({ ...newMember, role: e.target.value })}
                sx={{ borderRadius: 2 }}
              >
                {roles.map((role) => (
                  <MenuItem key={role} value={role}>
                    {role}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth required>
              <InputLabel>القسم</InputLabel>
              <Select
                value={newMember.department}
                label="القسم"
                onChange={(e) => setNewMember({ ...newMember, department: e.target.value })}
                sx={{ borderRadius: 2 }}
              >
                {departments.map((dept) => (
                  <MenuItem key={dept} value={dept}>
                    {dept}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setAddMemberDialog(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1
            }}
          >
            إلغاء
          </Button>
          <Button
            onClick={handleAddMember}
            variant="contained"
            disabled={loading || !newMember.name || !newMember.email || !newMember.role || !newMember.department}
            sx={{
              background: 'var(--business-gradient-primary)',
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 600
            }}
          >
            {loading ? 'جاري الإضافة...' : 'إضافة العضو'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamManagement;
