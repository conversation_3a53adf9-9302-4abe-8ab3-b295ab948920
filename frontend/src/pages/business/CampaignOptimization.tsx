import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON>,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
  Tooltip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Campaign,
  TrendingUp,
  TrendingDown,
  Visibility,
  Download,
  Refresh,
  Settings,
  CheckCircle,
  Warning,
  Error,
  Info,
  PlayArrow,
  Pause,
  Stop,
  Edit,
  Analytics,
  AttachMoney,
  People,
  Schedule
} from '@mui/icons-material';
import { <PERSON><PERSON>hart, <PERSON><PERSON>, <PERSON><PERSON>, ResponsiveHeader, ResponsiveGrid, StatCard, DataTable } from '../../components/business';
import type { Column } from '../../components/business';
import '../../styles/business-theme.css';

interface CampaignData {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'completed' | 'draft';
  budget: number;
  spent: number;
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
  roas: number;
  startDate: string;
  endDate: string;
  platform: string;
  audience: string;
  weaknesses: string[];
  recommendations: string[];
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

interface OptimizationRecommendation {
  type: 'budget' | 'targeting' | 'creative' | 'timing' | 'bidding';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  expectedImpact: string;
  effort: string;
}

const CampaignOptimization: React.FC = () => {
  console.log('CampaignOptimization component is rendering');

  const [loading, setLoading] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<CampaignData | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('30days');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock campaign data
  const campaigns: CampaignData[] = [
    {
      id: '1',
      name: 'حملة الصيف 2024',
      status: 'active',
      budget: 50000,
      spent: 42000,
      impressions: 125000,
      clicks: 3200,
      conversions: 156,
      ctr: 2.56,
      cpc: 13.13,
      roas: 4.2,
      startDate: '2024-01-01',
      endDate: '2024-02-01',
      platform: 'Google Ads',
      audience: 'الشباب 18-35',
      weaknesses: ['معدل تحويل منخفض', 'تكلفة نقرة عالية'],
      recommendations: ['تحسين صفحة الهبوط', 'تعديل الاستهداف', 'اختبار إعلانات جديدة'],
      performance: 'good'
    },
    {
      id: '2',
      name: 'إطلاق المنتج الجديد',
      status: 'active',
      budget: 35000,
      spent: 28000,
      impressions: 89000,
      clicks: 2100,
      conversions: 98,
      ctr: 2.36,
      cpc: 13.33,
      roas: 3.1,
      startDate: '2024-01-10',
      endDate: '2024-02-10',
      platform: 'Facebook',
      audience: 'المهنيون',
      weaknesses: ['عائد استثمار منخفض', 'تفاعل ضعيف'],
      recommendations: ['زيادة الميزانية', 'تحسين الإعلانات', 'توسيع الجمهور'],
      performance: 'average'
    },
    {
      id: '3',
      name: 'عروض العيد',
      status: 'completed',
      budget: 75000,
      spent: 73000,
      impressions: 156000,
      clicks: 4500,
      conversions: 234,
      ctr: 2.88,
      cpc: 16.22,
      roas: 5.8,
      startDate: '2023-12-01',
      endDate: '2023-12-31',
      platform: 'Instagram',
      audience: 'العائلات',
      weaknesses: ['تكلفة عالية'],
      recommendations: ['تطبيق نفس الاستراتيجية', 'تحسين التوقيت'],
      performance: 'excellent'
    },
    {
      id: '4',
      name: 'العودة للمدارس',
      status: 'paused',
      budget: 25000,
      spent: 18000,
      impressions: 67000,
      clicks: 1800,
      conversions: 87,
      ctr: 2.69,
      cpc: 10.00,
      roas: 2.1,
      startDate: '2024-01-15',
      endDate: '2024-02-15',
      platform: 'Twitter',
      audience: 'الطلاب',
      weaknesses: ['عائد ضعيف', 'استهداف غير دقيق'],
      recommendations: ['إعادة تقييم الجمهور', 'تحسين الرسالة', 'تقليل الميزانية'],
      performance: 'poor'
    }
  ];

  // Mock optimization recommendations
  const optimizationRecommendations: OptimizationRecommendation[] = [
    {
      type: 'targeting',
      priority: 'high',
      title: 'تحسين استهداف الجمهور',
      description: 'توسيع الجمهور المستهدف ليشمل شرائح جديدة بناءً على البيانات',
      expectedImpact: '+25% في التحويلات',
      effort: 'متوسط'
    },
    {
      type: 'budget',
      priority: 'high',
      title: 'إعادة توزيع الميزانية',
      description: 'تحويل الميزانية من الحملات ضعيفة الأداء إلى الحملات الناجحة',
      expectedImpact: '+15% في العائد',
      effort: 'منخفض'
    },
    {
      type: 'creative',
      priority: 'medium',
      title: 'اختبار إعلانات جديدة',
      description: 'إنشاء نسخ إعلانية جديدة واختبارها مقابل الحالية',
      expectedImpact: '+10% في معدل النقر',
      effort: 'عالي'
    },
    {
      type: 'timing',
      priority: 'medium',
      title: 'تحسين توقيت العرض',
      description: 'تعديل أوقات عرض الإعلانات بناءً على أداء الساعات المختلفة',
      expectedImpact: '+8% في التفاعل',
      effort: 'منخفض'
    },
    {
      type: 'bidding',
      priority: 'low',
      title: 'تحسين استراتيجية المزايدة',
      description: 'تغيير استراتيجية المزايدة لتحسين التكلفة',
      expectedImpact: '-12% في التكلفة',
      effort: 'متوسط'
    }
  ];

  const filteredCampaigns = campaigns.filter(campaign => 
    statusFilter === 'all' || campaign.status === statusFilter
  );

  const totalBudget = campaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
  const totalSpent = campaigns.reduce((sum, campaign) => sum + campaign.spent, 0);
  const totalConversions = campaigns.reduce((sum, campaign) => sum + campaign.conversions, 0);
  const avgROAS = campaigns.reduce((sum, campaign) => sum + campaign.roas, 0) / campaigns.length;

  const performanceData = [
    { label: 'ممتاز', value: campaigns.filter(c => c.performance === 'excellent').length },
    { label: 'جيد', value: campaigns.filter(c => c.performance === 'good').length },
    { label: 'متوسط', value: campaigns.filter(c => c.performance === 'average').length },
    { label: 'ضعيف', value: campaigns.filter(c => c.performance === 'poor').length }
  ];

  const roasData = campaigns.map(campaign => ({
    label: campaign.name,
    value: campaign.roas
  }));

  const columns: Column[] = [
    {
      id: 'name',
      label: 'اسم الحملة',
      minWidth: 200,
      sortable: true
    },
    {
      id: 'status',
      label: 'الحالة',
      minWidth: 100,
      align: 'center',
      format: (value: string) => (
        <Chip
          label={value === 'active' ? 'نشطة' : value === 'paused' ? 'متوقفة' : value === 'completed' ? 'مكتملة' : 'مسودة'}
          color={value === 'active' ? 'success' : value === 'paused' ? 'warning' : value === 'completed' ? 'info' : 'default'}
          size="small"
          icon={value === 'active' ? <PlayArrow /> : value === 'paused' ? <Pause /> : value === 'completed' ? <CheckCircle /> : <Edit />}
        />
      )
    },
    {
      id: 'performance',
      label: 'الأداء',
      minWidth: 120,
      align: 'center',
      format: (value: string) => (
        <Chip
          label={value === 'excellent' ? 'ممتاز' : value === 'good' ? 'جيد' : value === 'average' ? 'متوسط' : 'ضعيف'}
          color={value === 'excellent' ? 'success' : value === 'good' ? 'info' : value === 'average' ? 'warning' : 'error'}
          size="small"
          icon={value === 'excellent' ? <CheckCircle /> : value === 'good' ? <Info /> : value === 'average' ? <Warning /> : <Error />}
        />
      )
    },
    {
      id: 'budget',
      label: 'الميزانية',
      minWidth: 120,
      align: 'center',
      format: (value: number, row: CampaignData) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {value.toLocaleString()} ر.س
          </Typography>
          <LinearProgress
            variant="determinate"
            value={(row.spent / value) * 100}
            sx={{
              mt: 0.5,
              height: 4,
              borderRadius: 2,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 2,
                backgroundColor: (row.spent / value) > 0.9 ? '#ef4444' : (row.spent / value) > 0.7 ? '#f59e0b' : '#10b981'
              }
            }}
          />
          <Typography variant="caption" color="text.secondary">
            {row.spent.toLocaleString()} مُنفق
          </Typography>
        </Box>
      )
    },
    {
      id: 'roas',
      label: 'عائد الاستثمار',
      minWidth: 120,
      align: 'center',
      format: (value: number) => (
        <Chip
          label={`${value.toFixed(1)}x`}
          color={value > 4 ? 'success' : value > 2 ? 'info' : value > 1 ? 'warning' : 'error'}
          size="small"
          sx={{ fontWeight: 600 }}
        />
      )
    },
    {
      id: 'conversions',
      label: 'التحويلات',
      minWidth: 100,
      align: 'center',
      sortable: true
    },
    {
      id: 'actions',
      label: 'الإجراءات',
      minWidth: 120,
      align: 'center',
      format: (value: any, row: CampaignData) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="عرض التفاصيل">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedCampaign(row);
                setDialogOpen(true);
              }}
            >
              <Visibility />
            </IconButton>
          </Tooltip>
          <Tooltip title="تحسين">
            <IconButton size="small" color="primary">
              <Settings />
            </IconButton>
          </Tooltip>
          <Tooltip title="تحليل">
            <IconButton size="small" color="secondary">
              <Analytics />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  // Calculate summary statistics for filtered campaigns
  const filteredTotalBudget = filteredCampaigns.reduce((sum, campaign) => sum + campaign.budget, 0);
  const filteredAvgCTR = filteredCampaigns.reduce((sum, campaign) => sum + campaign.ctr, 0) / filteredCampaigns.length;
  const filteredAvgROAS = filteredCampaigns.reduce((sum, campaign) => sum + campaign.roas, 0) / filteredCampaigns.length;

  const handleRefreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportReport = () => {
    // Create CSV data for campaigns
    const csvData = filteredCampaigns.map(campaign => ({
      'اسم الحملة': campaign.name,
      'المنصة': campaign.platform,
      'الحالة': campaign.status === 'active' ? 'نشطة' : campaign.status === 'paused' ? 'متوقفة' : 'مكتملة',
      'الميزانية': `${campaign.budget} ر.س`,
      'المُنفق': `${campaign.spent} ر.س`,
      'المشاهدات': campaign.impressions,
      'النقرات': campaign.clicks,
      'التحويلات': campaign.conversions,
      'معدل النقر': `${campaign.ctr}%`,
      'تكلفة النقرة': `${campaign.cpc} ر.س`,
      'عائد الاستثمار': `${campaign.roas}x`,
      'الأداء': campaign.performance === 'excellent' ? 'ممتاز' : campaign.performance === 'good' ? 'جيد' : 'متوسط'
    }));

    // Convert to CSV
    const headers = Object.keys(csvData[0]);
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `campaign-optimization-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleEditCampaign = (campaign: CampaignData) => {
    alert(`تعديل الحملة: ${campaign.name}\nسيتم توجيهك لصفحة التعديل...`);
    setDialogOpen(false);
  };

  const handleOptimizeCampaign = (campaign: CampaignData) => {
    alert(`تحسين الحملة: ${campaign.name}\nسيتم تطبيق التوصيات الذكية...`);
    setDialogOpen(false);
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'budget': return <AttachMoney />;
      case 'targeting': return <People />;
      case 'creative': return <Edit />;
      case 'timing': return <Schedule />;
      case 'bidding': return <TrendingUp />;
      default: return <Info />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  try {
    return (
      <Box className="business-dashboard" sx={{
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        minHeight: '100vh',
        p: 3
      }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            تحسين الحملات التسويقية
          </Typography>
          <Typography variant="subtitle1" sx={{ color: 'text.secondary' }}>
            تحليل وتحسين أداء الحملات التسويقية لتحقيق أفضل النتائج
          </Typography>
        </Box>

        {/* Summary Stats */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="primary">
                      {filteredCampaigns.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي الحملات
                    </Typography>
                  </Box>
                  <Campaign color="primary" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="success.main">
                      {filteredTotalBudget.toLocaleString()} ر.س
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الميزانية الإجمالية
                    </Typography>
                  </Box>
                  <AttachMoney color="success" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="warning.main">
                      {filteredAvgCTR.toFixed(2)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط معدل النقر
                    </Typography>
                  </Box>
                  <TrendingUp color="warning" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="info.main">
                      {filteredAvgROAS.toFixed(1)}x
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط عائد الاستثمار
                    </Typography>
                  </Box>
                  <Analytics color="info" />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Campaigns Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              قائمة الحملات التسويقية
            </Typography>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>اسم الحملة</TableCell>
                    <TableCell>المنصة</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>الميزانية</TableCell>
                    <TableCell>المُنفق</TableCell>
                    <TableCell>معدل النقر</TableCell>
                    <TableCell>عائد الاستثمار</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredCampaigns.map((campaign) => (
                    <TableRow key={campaign.id}>
                      <TableCell>{campaign.name}</TableCell>
                      <TableCell>{campaign.platform}</TableCell>
                      <TableCell>
                        <Chip
                          label={campaign.status === 'active' ? 'نشطة' : campaign.status === 'paused' ? 'متوقفة' : 'مكتملة'}
                          color={campaign.status === 'active' ? 'success' : campaign.status === 'paused' ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{campaign.budget.toLocaleString()} ر.س</TableCell>
                      <TableCell>{campaign.spent.toLocaleString()} ر.س</TableCell>
                      <TableCell>{campaign.ctr}%</TableCell>
                      <TableCell>{campaign.roas}x</TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          onClick={() => {
                            setSelectedCampaign(campaign);
                            setDialogOpen(true);
                          }}
                        >
                          عرض
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Campaign Details Dialog */}
        <Dialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          {selectedCampaign && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Campaign color="primary" />
                  <Box>
                    <Typography variant="h6">{selectedCampaign.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {selectedCampaign.platform} • {selectedCampaign.status === 'active' ? 'نشطة' : selectedCampaign.status === 'paused' ? 'متوقفة' : 'مكتملة'}
                    </Typography>
                  </Box>
                </Box>
              </DialogTitle>

              <DialogContent>
                <Typography variant="body1" sx={{ mb: 3 }}>
                  تفاصيل الحملة: {selectedCampaign.name}
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          الأداء المالي
                        </Typography>
                        <Typography>الميزانية: {selectedCampaign.budget.toLocaleString()} ر.س</Typography>
                        <Typography>المُنفق: {selectedCampaign.spent.toLocaleString()} ر.س</Typography>
                        <Typography>عائد الاستثمار: {selectedCampaign.roas}x</Typography>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          مقاييس التفاعل
                        </Typography>
                        <Typography>المشاهدات: {selectedCampaign.impressions.toLocaleString()}</Typography>
                        <Typography>النقرات: {selectedCampaign.clicks.toLocaleString()}</Typography>
                        <Typography>معدل النقر: {selectedCampaign.ctr}%</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </DialogContent>

              <DialogActions>
                <Button onClick={() => setDialogOpen(false)}>إغلاق</Button>
                <Button
                  variant="outlined"
                  startIcon={<Edit />}
                  onClick={() => handleEditCampaign(selectedCampaign!)}
                >
                  تعديل الحملة
                </Button>
                <Button
                  variant="contained"
                  startIcon={<TrendingUp />}
                  onClick={() => handleOptimizeCampaign(selectedCampaign!)}
                >
                  تحسين الحملة
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Box>
    );
  } catch (error) {
    console.error('Error in CampaignOptimization component:', error);
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" color="error">
          خطأ في تحميل صفحة تحسين الحملات
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          {error instanceof Error ? error.message : 'خطأ غير معروف'}
        </Typography>
      </Box>
    );
  }
};

export default CampaignOptimization;
