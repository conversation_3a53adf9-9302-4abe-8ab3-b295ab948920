import React, { useState } from 'react';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import { Business, TrendingUp, Analytics, Warning } from '@mui/icons-material';

const CompetitorAnalysisPage: React.FC = () => {
  const [competitorName, setCompetitorName] = useState('');
  const [competitorData, setCompetitorData] = useState('');
  const [competitorResults, setCompetitorResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const handleCompetitorAnalysis = async () => {
    if (!competitorName.trim() || !competitorData.trim()) {
      setError('يرجى إدخال اسم المنافس وبياناته');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      setCompetitorResults({
        name: competitorName,
        sentiment: 'neutral',
        marketPosition: 'challenger',
        marketShare: '15%',
        strengths: [
          'خدمة عملاء ممتازة',
          'أسعار تنافسية',
          'شبكة توزيع واسعة',
          'علامة تجارية قوية'
        ],
        weaknesses: [
          'تقنية قديمة',
          'نطاق محدود من المنتجات',
          'بطء في التطوير',
          'خدمة ما بعد البيع ضعيفة'
        ],
        opportunities: [
          'توسيع النطاق الجغرافي',
          'تحسين التقنية',
          'شراكات استراتيجية',
          'تطوير منتجات جديدة'
        ],
        threats: [
          'منافسين جدد',
          'تغيرات في السوق',
          'ارتفاع التكاليف',
          'تغيير تفضيلات العملاء'
        ],
        recommendations: [
          'تحسين التقنية والابتكار',
          'توسيع نطاق المنتجات',
          'تحسين خدمة ما بعد البيع',
          'استراتيجية تسويق أكثر فعالية'
        ]
      });
      
      setSuccess('تم تحليل المنافس بنجاح!');
    } catch (err) {
      setError('فشل في تحليل المنافس');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <Business sx={{ fontSize: 40, color: '#667eea' }} />
          تحليل المنافسين
        </Typography>

        <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary' }}>
          فهم نقاط القوة والضعف لدى المنافسين وتطوير استراتيجيات تفوقهم
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', lg: 'row' }, gap: 4 }}>
          {/* Input Section */}
          <Box sx={{ flex: 1 }}>
            <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Analytics color="primary" />
                بيانات المنافس
              </Typography>
              
              <TextField
                fullWidth
                label="اسم المنافس"
                value={competitorName}
                onChange={(e) => setCompetitorName(e.target.value)}
                placeholder="أدخل اسم المنافس..."
                sx={{ mb: 3 }}
              />

              <TextField
                fullWidth
                multiline
                rows={6}
                label="معلومات المنافس"
                value={competitorData}
                onChange={(e) => setCompetitorData(e.target.value)}
                placeholder="أدخل معلومات عن المنافس مثل:
• المنتجات والخدمات
• الأسعار والاستراتيجية التسويقية
• نقاط البيع والخدمة
• التقنية المستخدمة
• العملاء المستهدفين
• المزايا التنافسية"
                sx={{ mb: 3 }}
              />

              <Button
                variant="contained"
                onClick={handleCompetitorAnalysis}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
                sx={{ 
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  '&:hover': { background: 'linear-gradient(45deg, #5a6fd8, #6a4190)' }
                }}
              >
                {loading ? 'جاري التحليل...' : 'تحليل المنافس'}
              </Button>
            </Card>
          </Box>

          {/* Results Section */}
          <Box sx={{ flex: 1 }}>
            {competitorResults && (
              <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)' }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp color="primary" />
                  نتائج تحليل {competitorResults.name}
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>الموقع السوقي:</Typography>
                  <Chip
                    label={competitorResults.marketPosition === 'challenger' ? 'منافس قوي' : 'منافس عادي'}
                    color="primary"
                    sx={{ fontSize: '1rem', p: 1 }}
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>الحصة السوقية:</Typography>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 'bold' }}>
                    {competitorResults.marketShare}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom color="success.main">
                    نقاط القوة:
                  </Typography>
                  <List dense>
                    {competitorResults.strengths.map((strength: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={strength}
                          primaryTypographyProps={{ variant: 'body2', color: 'success.main' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom color="error.main">
                    نقاط الضعف:
                  </Typography>
                  <List dense>
                    {competitorResults.weaknesses.map((weakness: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={weakness}
                          primaryTypographyProps={{ variant: 'body2', color: 'error.main' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom color="info.main">
                    الفرص:
                  </Typography>
                  <List dense>
                    {competitorResults.opportunities.map((opportunity: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={opportunity}
                          primaryTypographyProps={{ variant: 'body2', color: 'info.main' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom color="warning.main">
                    التهديدات:
                  </Typography>
                  <List dense>
                    {competitorResults.threats.map((threat: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={threat}
                          primaryTypographyProps={{ variant: 'body2', color: 'warning.main' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Warning color="primary" />
                    التوصيات الاستراتيجية:
                  </Typography>
                  <List dense>
                    {competitorResults.recommendations.map((rec: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={rec}
                          primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Card>
            )}
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 3 }}>
            {success}
          </Alert>
        )}
      </Box>
    </Container>
  );
};

export default CompetitorAnalysisPage; 