import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  People,
  TrendingUp,
  TrendingDown,
  Visibility,
  Download,
  Refresh,
  FilterList,
  PersonAdd,
  Campaign
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, ResponsiveHeader, ResponsiveGrid, StatCard } from '../../components/business';
import '../../styles/business-theme.css';

interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  customerCount: number;
  percentage: number;
  avgValue: number;
  growth: number;
  color: string;
  characteristics: string[];
  recommendations: string[];
}

interface Customer {
  id: string;
  name: string;
  email: string;
  segment: string;
  value: number;
  lastActivity: string;
  riskScore: number;
}

const CustomerSegmentation: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<CustomerSegment | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');

  // Mock data for customer segments
  const segments: CustomerSegment[] = [
    {
      id: 'high-value',
      name: 'العملاء ذوو القيمة العالية',
      description: 'عملاء يحققون أعلى إيرادات ولديهم ولاء عالي',
      customerCount: 1250,
      percentage: 15,
      avgValue: 5200,
      growth: 12.5,
      color: '#10b981',
      characteristics: ['إنفاق عالي', 'تفاعل منتظم', 'ولاء عالي', 'مشتريات متكررة'],
      recommendations: ['برامج VIP خاصة', 'خدمة عملاء مميزة', 'عروض حصرية', 'تواصل شخصي']
    },
    {
      id: 'new-customers',
      name: 'العملاء الجدد',
      description: 'عملاء انضموا خلال الـ 90 يوم الماضية',
      customerCount: 2100,
      percentage: 25,
      avgValue: 850,
      growth: 8.3,
      color: '#3b82f6',
      characteristics: ['حديثو الانضمام', 'استكشاف المنتجات', 'حاجة للتوجيه'],
      recommendations: ['برامج ترحيب', 'دعم إضافي', 'عروض تجريبية', 'محتوى تعليمي']
    },
    {
      id: 'at-risk',
      name: 'العملاء المعرضون للخطر',
      description: 'عملاء قل تفاعلهم وقد يتوقفوا عن الشراء',
      customerCount: 890,
      percentage: 10.5,
      avgValue: 1200,
      growth: -5.2,
      color: '#ef4444',
      characteristics: ['تفاعل منخفض', 'عدم شراء حديث', 'شكاوى سابقة'],
      recommendations: ['حملات استرداد', 'عروض خاصة', 'تواصل مباشر', 'حل المشاكل']
    },
    {
      id: 'regular',
      name: 'العملاء المنتظمون',
      description: 'عملاء يشترون بانتظام بقيم متوسطة',
      customerCount: 3200,
      percentage: 38,
      avgValue: 1800,
      growth: 3.1,
      color: '#f59e0b',
      characteristics: ['شراء منتظم', 'قيمة متوسطة', 'رضا جيد'],
      recommendations: ['برامج ولاء', 'تحفيز للترقية', 'منتجات مكملة', 'تجربة محسنة']
    },
    {
      id: 'dormant',
      name: 'العملاء الخاملون',
      description: 'عملاء لم يتفاعلوا لفترة طويلة',
      customerCount: 950,
      percentage: 11.5,
      avgValue: 450,
      growth: -12.8,
      color: '#6b7280',
      characteristics: ['عدم تفاعل', 'آخر شراء قديم', 'عدم فتح الإيميلات'],
      recommendations: ['حملات إعادة تفعيل', 'استطلاعات رأي', 'عروض مغرية', 'تحديث البيانات']
    }
  ];

  // Mock customers data
  const customers: Customer[] = [
    { id: '1', name: 'أحمد محمد', email: '<EMAIL>', segment: 'high-value', value: 5200, lastActivity: '2024-01-15', riskScore: 15 },
    { id: '2', name: 'فاطمة علي', email: '<EMAIL>', segment: 'new-customers', value: 850, lastActivity: '2024-01-20', riskScore: 25 },
    { id: '3', name: 'محمد سالم', email: '<EMAIL>', segment: 'at-risk', value: 1200, lastActivity: '2023-12-10', riskScore: 85 },
  ];

  const segmentData = segments.map(segment => ({
    label: segment.name,
    value: segment.percentage,
    color: segment.color
  }));

  const valueData = segments.map(segment => ({
    label: segment.name,
    value: segment.avgValue
  }));

  const handleSegmentClick = (segment: CustomerSegment) => {
    setSelectedSegment(segment);
    setDialogOpen(true);
  };

  const handleRefreshData = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportReport = () => {
    // Create CSV data
    const csvData = segments.map(segment => ({
      'اسم الشريحة': segment.name,
      'عدد العملاء': segment.customerCount,
      'النسبة المئوية': `${segment.percentage}%`,
      'متوسط القيمة': `${segment.avgValue} ر.س`,
      'معدل النمو': `${segment.growth}%`
    }));

    // Convert to CSV
    const headers = Object.keys(csvData[0]);
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `customer-segmentation-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCreateTargetedCampaign = (segment: CustomerSegment) => {
    // Navigate to campaign creation with pre-filled segment data
    alert(`إنشاء حملة مستهدفة لشريحة: ${segment.name}\nعدد العملاء: ${segment.customerCount}\nسيتم توجيهك لصفحة إنشاء الحملة...`);
    // In a real app, you would navigate to campaign creation page
    // navigate('/business/campaigns/create', { state: { targetSegment: segment } });
  };

  const totalCustomers = segments.reduce((sum, segment) => sum + segment.customerCount, 0);
  const avgGrowth = segments.reduce((sum, segment) => sum + segment.growth, 0) / segments.length;
  const totalValue = segments.reduce((sum, segment) => sum + (segment.customerCount * segment.avgValue), 0);

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="تجزئة العملاء"
        subtitle="تحليل شرائح العملاء وخصائصهم لتحسين الاستراتيجيات التسويقية"
        actions={[
          <Button
            key="refresh"
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefreshData}
            disabled={loading}
          >
            تحديث البيانات
          </Button>,
          <Button
            key="export"
            variant="contained"
            startIcon={<Download />}
            onClick={handleExportReport}
            sx={{ ml: 1 }}
          >
            تصدير التقرير
          </Button>
        ]}
        filters={[
          {
            label: 'الفترة الزمنية',
            value: timeRange,
            options: [
              { value: '7days', label: 'آخر 7 أيام' },
              { value: '30days', label: 'آخر 30 يوم' },
              { value: '90days', label: 'آخر 90 يوم' },
              { value: '1year', label: 'السنة الماضية' }
            ],
            onChange: setTimeRange
          }
        ]}
      />

      {/* Summary Stats */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, md: 4 }} gap={3}>
        <StatCard
          title="إجمالي العملاء"
          value={totalCustomers.toLocaleString()}
          change={`+${avgGrowth.toFixed(1)}%`}
          trend="up"
          icon={<People />}
          color="primary"
          subtitle="عميل نشط"
          loading={loading}
        />
        <StatCard
          title="القيمة الإجمالية"
          value={`${(totalValue / 1000000).toFixed(1)}M ر.س`}
          change="+8.2%"
          trend="up"
          icon={<TrendingUp />}
          color="success"
          subtitle="قيمة العملاء"
          loading={loading}
        />
        <StatCard
          title="متوسط القيمة"
          value={`${(totalValue / totalCustomers).toFixed(0)} ر.س`}
          change="+3.5%"
          trend="up"
          icon={<TrendingUp />}
          color="info"
          subtitle="لكل عميل"
          loading={loading}
        />
        <StatCard
          title="الشرائح النشطة"
          value={segments.length.toString()}
          change="0"
          trend="neutral"
          icon={<FilterList />}
          color="warning"
          subtitle="شريحة محددة"
          loading={loading}
        />
      </ResponsiveGrid>

      {/* Charts Section */}
      <ResponsiveGrid columns={{ xs: 1, lg: 2 }} gap={3}>
        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              توزيع شرائح العملاء
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              النسبة المئوية لكل شريحة من إجمالي العملاء
            </Typography>
            <PieChart
              data={segmentData}
              height={300}
              showLegend={true}
            />
          </CardContent>
        </Card>

        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              متوسط القيمة لكل شريحة
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              متوسط الإنفاق لكل عميل في كل شريحة
            </Typography>
            <BarChart
              data={valueData}
              height={300}
              showValues={true}
            />
          </CardContent>
        </Card>
      </ResponsiveGrid>

      {/* Segments Grid */}
      <Card className="business-card">
        <CardContent>
          <Typography variant="h6" gutterBottom>
            تفاصيل الشرائح
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            انقر على أي شريحة لعرض التفاصيل والتوصيات
          </Typography>
          
          <Grid container spacing={3}>
            {segments.map((segment) => (
              <Grid item xs={12} md={6} lg={4} key={segment.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    },
                    border: `2px solid ${segment.color}20`,
                    borderLeft: `4px solid ${segment.color}`
                  }}
                  onClick={() => handleSegmentClick(segment)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: segment.color, mr: 2 }}>
                        <People />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontSize: '1rem' }}>
                          {segment.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {segment.customerCount.toLocaleString()} عميل
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Typography variant="body2" sx={{ mb: 2, minHeight: 40 }}>
                      {segment.description}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body2">
                        <strong>{segment.percentage}%</strong> من العملاء
                      </Typography>
                      <Chip
                        label={`${segment.growth > 0 ? '+' : ''}${segment.growth}%`}
                        color={segment.growth > 0 ? 'success' : segment.growth < 0 ? 'error' : 'default'}
                        size="small"
                      />
                    </Box>
                    
                    <LinearProgress
                      variant="determinate"
                      value={segment.percentage}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          backgroundColor: segment.color
                        }
                      }}
                    />
                    
                    <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                      متوسط القيمة: <strong>{segment.avgValue.toLocaleString()} ر.س</strong>
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Segment Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedSegment && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: selectedSegment.color, mr: 2 }}>
                  <People />
                </Avatar>
                <Box>
                  <Typography variant="h6">{selectedSegment.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedSegment.customerCount.toLocaleString()} عميل ({selectedSegment.percentage}%)
                  </Typography>
                </Box>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    الخصائص الرئيسية
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                    {selectedSegment.characteristics.map((char, index) => (
                      <Chip key={index} label={char} variant="outlined" size="small" />
                    ))}
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    التوصيات التسويقية
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                    {selectedSegment.recommendations.map((rec, index) => (
                      <Chip 
                        key={index} 
                        label={rec} 
                        color="primary" 
                        variant="outlined" 
                        size="small"
                        icon={<Campaign />}
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
              
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                عينة من العملاء
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>اسم العميل</TableCell>
                      <TableCell>البريد الإلكتروني</TableCell>
                      <TableCell align="center">القيمة</TableCell>
                      <TableCell align="center">آخر نشاط</TableCell>
                      <TableCell align="center">درجة المخاطرة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {customers
                      .filter(customer => customer.segment === selectedSegment.id)
                      .map((customer) => (
                        <TableRow key={customer.id}>
                          <TableCell>{customer.name}</TableCell>
                          <TableCell>{customer.email}</TableCell>
                          <TableCell align="center">{customer.value.toLocaleString()} ر.س</TableCell>
                          <TableCell align="center">{customer.lastActivity}</TableCell>
                          <TableCell align="center">
                            <Chip
                              label={`${customer.riskScore}%`}
                              color={customer.riskScore > 70 ? 'error' : customer.riskScore > 40 ? 'warning' : 'success'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>إغلاق</Button>
              <Button
                variant="contained"
                startIcon={<Campaign />}
                onClick={() => handleCreateTargetedCampaign(selectedSegment!)}
              >
                إنشاء حملة مستهدفة
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default CustomerSegmentation;
