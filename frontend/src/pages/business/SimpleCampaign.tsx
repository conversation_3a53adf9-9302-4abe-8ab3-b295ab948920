import React from 'react';
import { Box, Typography, Card, CardContent, Button } from '@mui/material';
import { Download, Refresh } from '@mui/icons-material';

const SimpleCampaign: React.FC = () => {
  console.log('SimpleCampaign component is rendering');
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        تحسين الحملات التسويقية
      </Typography>
      
      <Typography variant="subtitle1" sx={{ mb: 3, color: 'text.secondary' }}>
        تحليل وتحسين أداء الحملات التسويقية لتحقيق أفضل النتائج
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button variant="outlined" startIcon={<Refresh />}>
          تحديث البيانات
        </Button>
        <Button variant="contained" startIcon={<Download />}>
          تصدير التقرير
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ملخص الحملات
          </Typography>
          <Typography variant="body1">
            هذه نسخة مبسطة من صفحة تحسين الحملات للاختبار.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SimpleCampaign;
