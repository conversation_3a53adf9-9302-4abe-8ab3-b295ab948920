import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  Analytics,
  Download,
  Refresh,
  Timeline,
  AttachMoney,
  CalendarToday,
  ShowChart,
  Assessment,
  Tune,
  Info,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveHeader, ResponsiveGrid, StatCard } from '../../components/business';
import '../../styles/business-theme.css';

interface PredictionData {
  period: string;
  predicted: number;
  confidence: number;
  lower_bound: number;
  upper_bound: number;
}

interface InfluencingFactor {
  name: string;
  impact: number;
  description: string;
  adjustable: boolean;
  currentValue: number;
  unit: string;
}

const SalesPrediction: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [predictionPeriod, setPredictionPeriod] = useState('quarterly');
  const [confidenceLevel, setConfidenceLevel] = useState(95);
  const [factors, setFactors] = useState<InfluencingFactor[]>([
    {
      name: 'الميزانية التسويقية',
      impact: 35,
      description: 'تأثير الإنفاق التسويقي على المبيعات',
      adjustable: true,
      currentValue: 50000,
      unit: 'ر.س'
    },
    {
      name: 'الموسمية',
      impact: 25,
      description: 'تأثير المواسم والأعياد على المبيعات',
      adjustable: false,
      currentValue: 1.2,
      unit: 'معامل'
    },
    {
      name: 'المؤشرات الاقتصادية',
      impact: 20,
      description: 'تأثير الوضع الاقتصادي العام',
      adjustable: false,
      currentValue: 0.95,
      unit: 'مؤشر'
    },
    {
      name: 'المنافسة',
      impact: 15,
      description: 'تأثير النشاط التنافسي في السوق',
      adjustable: true,
      currentValue: 0.8,
      unit: 'مؤشر'
    },
    {
      name: 'حملات ترويجية',
      impact: 5,
      description: 'تأثير العروض والخصومات',
      adjustable: true,
      currentValue: 3,
      unit: 'حملة/شهر'
    }
  ]);

  // Mock prediction data
  const predictionData: PredictionData[] = [
    { period: 'Q1 2024', predicted: 850000, confidence: 92, lower_bound: 780000, upper_bound: 920000 },
    { period: 'Q2 2024', predicted: 920000, confidence: 88, lower_bound: 830000, upper_bound: 1010000 },
    { period: 'Q3 2024', predicted: 1100000, confidence: 85, lower_bound: 980000, upper_bound: 1220000 },
    { period: 'Q4 2024', predicted: 1350000, confidence: 82, lower_bound: 1180000, upper_bound: 1520000 }
  ];

  const monthlyData = [
    { label: 'يناير', value: 280000, predicted: 295000 },
    { label: 'فبراير', value: 265000, predicted: 285000 },
    { label: 'مارس', value: 305000, predicted: 320000 },
    { label: 'أبريل', value: 0, predicted: 340000 },
    { label: 'مايو', value: 0, predicted: 365000 },
    { label: 'يونيو', value: 0, predicted: 385000 }
  ];

  const scenarioData = [
    { label: 'متفائل', value: 1520000, color: '#10b981' },
    { label: 'متوقع', value: 1350000, color: '#3b82f6' },
    { label: 'متحفظ', value: 1180000, color: '#f59e0b' }
  ];

  const handleFactorChange = (index: number, newValue: number) => {
    const updatedFactors = [...factors];
    updatedFactors[index].currentValue = newValue;
    setFactors(updatedFactors);
    // Here you would recalculate predictions based on new factors
  };

  const handleRefreshPredictions = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportReport = () => {
    // Create CSV data for sales predictions
    const csvData = predictionData.map(prediction => ({
      'الفترة': prediction.period,
      'المبيعات المتوقعة': `${prediction.predicted.toLocaleString()} ر.س`,
      'مستوى الثقة': `${prediction.confidence}%`,
      'الحد الأدنى': `${prediction.lower_bound.toLocaleString()} ر.س`,
      'الحد الأعلى': `${prediction.upper_bound.toLocaleString()} ر.س`
    }));

    // Add factors data
    const factorsData = factors.map(factor => ({
      'العامل': factor.name,
      'التأثير': `${factor.impact}%`,
      'الوصف': factor.description,
      'القيمة الحالية': `${factor.currentValue} ${factor.unit}`,
      'قابل للتعديل': factor.adjustable ? 'نعم' : 'لا'
    }));

    // Combine data
    const allData = [
      'التوقعات:',
      ...csvData.map(row => Object.values(row).join(',')),
      '',
      'العوامل المؤثرة:',
      ...factorsData.map(row => Object.values(row).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([allData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `sales-prediction-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const totalPredicted = predictionData.reduce((sum, item) => sum + item.predicted, 0);
  const avgConfidence = predictionData.reduce((sum, item) => sum + item.confidence, 0) / predictionData.length;
  const growthRate = ((predictionData[predictionData.length - 1].predicted - predictionData[0].predicted) / predictionData[0].predicted) * 100;

  const chartData = predictionData.map(item => ({
    label: item.period,
    value: item.predicted,
    confidence: item.confidence
  }));

  const confidenceData = predictionData.map(item => ({
    label: item.period,
    predicted: item.predicted,
    lower: item.lower_bound,
    upper: item.upper_bound
  }));

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="التنبؤ بالمبيعات"
        subtitle="توقعات المبيعات المستقبلية مع تحليل العوامل المؤثرة وسيناريوهات مختلفة"
        actions={[
          <Button
            key="refresh"
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefreshPredictions}
            disabled={loading}
          >
            تحديث التوقعات
          </Button>,
          <Button
            key="export"
            variant="contained"
            startIcon={<Download />}
            onClick={handleExportReport}
            sx={{ ml: 1 }}
          >
            تصدير التقرير
          </Button>
        ]}
        filters={[
          {
            label: 'فترة التنبؤ',
            value: predictionPeriod,
            options: [
              { value: 'monthly', label: 'شهري' },
              { value: 'quarterly', label: 'ربع سنوي' },
              { value: 'yearly', label: 'سنوي' }
            ],
            onChange: setPredictionPeriod
          }
        ]}
      />

      {/* Summary Stats */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, md: 4 }} gap={3}>
        <StatCard
          title="إجمالي المبيعات المتوقعة"
          value={`${(totalPredicted / 1000000).toFixed(1)}M ر.س`}
          change={`+${growthRate.toFixed(1)}%`}
          trend="up"
          icon={<AttachMoney />}
          color="primary"
          subtitle="للعام القادم"
          loading={loading}
        />
        <StatCard
          title="متوسط الثقة في التوقعات"
          value={`${avgConfidence.toFixed(1)}%`}
          change="+2.3%"
          trend="up"
          icon={<Assessment />}
          color="success"
          subtitle="دقة النموذج"
          loading={loading}
        />
        <StatCard
          title="معدل النمو المتوقع"
          value={`${growthRate.toFixed(1)}%`}
          change="+1.2%"
          trend="up"
          icon={<TrendingUp />}
          color="info"
          subtitle="مقارنة بالعام الحالي"
          loading={loading}
        />
        <StatCard
          title="أفضل ربع متوقع"
          value="Q4 2024"
          change={`${(predictionData[3].predicted / 1000000).toFixed(1)}M ر.س`}
          trend="up"
          icon={<ShowChart />}
          color="warning"
          subtitle="أعلى مبيعات متوقعة"
          loading={loading}
        />
      </ResponsiveGrid>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="التوقعات الأساسية" />
        <Tab label="العوامل المؤثرة" />
        <Tab label="السيناريوهات" />
      </Tabs>

      {activeTab === 0 && (
        <>
          {/* Main Prediction Charts */}
          <ResponsiveGrid columns={{ xs: 1, lg: 2 }} gap={3}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توقعات المبيعات الربع سنوية
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  المبيعات المتوقعة لكل ربع من العام القادم
                </Typography>
                <BarChart
                  data={chartData}
                  height={350}
                  showValues={true}
                />
              </CardContent>
            </Card>

            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  اتجاه المبيعات الشهرية
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  مقارنة المبيعات الفعلية مع المتوقعة
                </Typography>
                <LineChart
                  data={monthlyData.map(item => ({ label: item.label, value: item.value || item.predicted }))}
                  color="#3b82f6"
                  height={350}
                />
              </CardContent>
            </Card>
          </ResponsiveGrid>

          {/* Confidence Intervals */}
          <Card className="business-card">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                نطاقات الثقة في التوقعات
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                الحد الأدنى والأعلى للمبيعات المتوقعة مع مستوى الثقة {confidenceLevel}%
              </Typography>
              
              <Grid container spacing={2}>
                {predictionData.map((item, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Paper sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                      <Typography variant="h6" color="primary" gutterBottom>
                        {item.period}
                      </Typography>
                      <Typography variant="h4" sx={{ mb: 1 }}>
                        {(item.predicted / 1000).toFixed(0)}K
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        ر.س متوقع
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption">
                          أدنى: {(item.lower_bound / 1000).toFixed(0)}K
                        </Typography>
                        <Typography variant="caption">
                          أعلى: {(item.upper_bound / 1000).toFixed(0)}K
                        </Typography>
                      </Box>
                      <Chip
                        label={`ثقة ${item.confidence}%`}
                        color={item.confidence > 90 ? 'success' : item.confidence > 80 ? 'info' : 'warning'}
                        size="small"
                      />
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </>
      )}

      {activeTab === 1 && (
        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              العوامل المؤثرة في التوقعات
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              اضبط العوامل القابلة للتعديل لرؤية تأثيرها على توقعات المبيعات
            </Typography>
            
            <Grid container spacing={3}>
              {factors.map((factor, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" sx={{ fontSize: '1rem' }}>
                          {factor.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {factor.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={`${factor.impact}% تأثير`}
                        color={factor.impact > 30 ? 'error' : factor.impact > 20 ? 'warning' : 'info'}
                        size="small"
                      />
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        القيمة الحالية: <strong>{factor.currentValue.toLocaleString()} {factor.unit}</strong>
                      </Typography>
                      
                      {factor.adjustable ? (
                        <Box sx={{ mt: 2 }}>
                          <Slider
                            value={factor.currentValue}
                            onChange={(e, newValue) => handleFactorChange(index, newValue as number)}
                            min={factor.currentValue * 0.5}
                            max={factor.currentValue * 2}
                            step={factor.currentValue * 0.1}
                            valueLabelDisplay="auto"
                            valueLabelFormat={(value) => `${value.toLocaleString()} ${factor.unit}`}
                          />
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                            <Typography variant="caption">
                              {(factor.currentValue * 0.5).toLocaleString()}
                            </Typography>
                            <Typography variant="caption">
                              {(factor.currentValue * 2).toLocaleString()}
                            </Typography>
                          </Box>
                        </Box>
                      ) : (
                        <Alert severity="info" sx={{ mt: 2 }}>
                          هذا العامل غير قابل للتعديل ويعتمد على ظروف خارجية
                        </Alert>
                      )}
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
            
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="contained"
                startIcon={<Analytics />}
                onClick={handleRefreshPredictions}
                disabled={loading}
                size="large"
              >
                {loading ? 'جاري إعادة الحساب...' : 'إعادة حساب التوقعات'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} lg={6}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  سيناريوهات المبيعات
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  مقارنة السيناريوهات المختلفة للعام القادم
                </Typography>
                <BarChart
                  data={scenarioData}
                  height={300}
                  showValues={true}
                />
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} lg={6}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تفاصيل السيناريوهات
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="السيناريو المتفائل"
                      secondary="نمو قوي في السوق، زيادة الميزانية التسويقية بـ 50%، عدم وجود منافسة قوية"
                    />
                    <Typography variant="h6" color="success.main">
                      1.52M ر.س
                    </Typography>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Info color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="السيناريو المتوقع"
                      secondary="نمو طبيعي، استمرار الاستراتيجيات الحالية، ظروف السوق مستقرة"
                    />
                    <Typography variant="h6" color="primary.main">
                      1.35M ر.س
                    </Typography>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="السيناريو المتحفظ"
                      secondary="تحديات اقتصادية، زيادة المنافسة، تقليل الميزانية التسويقية"
                    />
                    <Typography variant="h6" color="warning.main">
                      1.18M ر.س
                    </Typography>
                  </ListItem>
                </List>
                
                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    <strong>توصية:</strong> التركيز على السيناريو المتوقع مع الاستعداد للسيناريو المتحفظ
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default SalesPrediction;
