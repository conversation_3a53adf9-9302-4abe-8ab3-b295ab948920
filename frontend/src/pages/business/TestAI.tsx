import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Button,
  TextField,
  Alert,
  Chip,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import { AutoAwesome, Psychology, Analytics } from '@mui/icons-material';
import apiClient from '../../api/apiClient';

const TestAI: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState('');

  const testSentimentAnalysis = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.post('/ai/analyze-sentiment', {
        text: 'هذا المنتج رائع جداً وأنصح الجميع بتجربته! أحببت كل شيء فيه',
        use_ai: true
      });
      setResult({ type: 'sentiment', data: response.data.data });
    } catch (err: any) {
      setError(err.response?.data?.detail || 'فشل في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  const testContentGeneration = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.post('/ai/generate-content', {
        content_type: 'social',
        prompt: 'منشور تحفيزي عن النجاح في ريادة الأعمال',
        target_audience: 'رواد الأعمال الشباب',
        tone: 'motivational'
      });
      setResult({ type: 'content', data: response.data.data });
    } catch (err: any) {
      setError(err.response?.data?.detail || 'فشل في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  const testCompetitorAnalysis = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await apiClient.post('/ai/analyze-competitor', {
        name: 'شركة التسويق الذكي',
        website: 'https://smartmarketing.com',
        description: 'شركة رائدة في مجال التسويق الرقمي تقدم خدمات متميزة للشركات'
      });
      setResult({ type: 'competitor', data: response.data.data });
    } catch (err: any) {
      setError(err.response?.data?.detail || 'فشل في الاختبار');
    } finally {
      setLoading(false);
    }
  };

  const renderResult = () => {
    if (!result) return null;

    switch (result.type) {
      case 'sentiment':
        return (
          <Paper sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              نتيجة تحليل المشاعر
              {result.data.enhanced && (
                <Chip 
                  label="MarketMind AI" 
                  color="primary" 
                  size="small" 
                  icon={<AutoAwesome />}
                />
              )}
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Chip 
                label={`المشاعر: ${result.data.sentiment === 'positive' ? 'إيجابي' : 
                                  result.data.sentiment === 'negative' ? 'سلبي' : 'محايد'}`}
                color={result.data.sentiment === 'positive' ? 'success' : 
                       result.data.sentiment === 'negative' ? 'error' : 'info'}
                sx={{ mr: 1 }}
              />
              <Chip 
                label={`الثقة: ${(result.data.confidence * 100).toFixed(1)}%`}
                variant="outlined"
              />
              {result.data.source && (
                <Chip 
                  label={result.data.source}
                  variant="outlined"
                  size="small"
                  sx={{ ml: 1 }}
                />
              )}
            </Box>

            {result.data.emotions && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  المشاعر المكتشفة:
                </Typography>
                {result.data.emotions.map((emotion: string, index: number) => (
                  <Chip key={index} label={emotion} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                ))}
              </Box>
            )}

            <Typography variant="body2" sx={{ mt: 1 }}>
              عدد الكلمات: {result.data.word_count}
            </Typography>
          </Paper>
        );

      case 'content':
        return (
          <Paper sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              المحتوى المُنشأ
              {result.data.enhanced && (
                <Chip 
                  label="محسن بـ MarketMind" 
                  color="primary" 
                  size="small" 
                  icon={<AutoAwesome />}
                />
              )}
            </Typography>
            <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap', mb: 2 }}>
              {result.data.content}
            </Typography>
            
            {result.data.sentiment_analysis && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  تحليل المشاعر للمحتوى:
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={`${result.data.sentiment_analysis.sentiment === 'positive' ? 'إيجابي' : 
                             result.data.sentiment_analysis.sentiment === 'negative' ? 'سلبي' : 'محايد'} 
                           (${(result.data.sentiment_analysis.confidence * 100).toFixed(1)}%)`}
                    color={result.data.sentiment_analysis.sentiment === 'positive' ? 'success' : 
                           result.data.sentiment_analysis.sentiment === 'negative' ? 'error' : 'info'}
                    size="small"
                  />
                  <Chip 
                    label={result.data.sentiment_analysis.source}
                    variant="outlined"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Box>
              </>
            )}
            
            {result.data.suggestions && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  اقتراحات للتحسين:
                </Typography>
                {result.data.suggestions.map((suggestion: string, index: number) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    • {suggestion}
                  </Typography>
                ))}
              </>
            )}

            {result.data.target_insights && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  رؤى الجمهور المستهدف:
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>النوع:</strong> {result.data.target_insights.audience_type}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>التوصيات:</strong>
                </Typography>
                {result.data.target_insights.recommendations.map((rec: string, index: number) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5, ml: 2 }}>
                    • {rec}
                  </Typography>
                ))}
              </>
            )}
          </Paper>
        );

      case 'competitor':
        return (
          <Paper sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              تحليل المنافس: {result.data.competitor}
              {result.data.enhanced && (
                <Chip 
                  label="محسن بـ MarketMind" 
                  color="primary" 
                  size="small" 
                  icon={<AutoAwesome />}
                />
              )}
            </Typography>
            <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap', mb: 2 }}>
              {result.data.analysis}
            </Typography>
            
            {result.data.description_sentiment && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  تحليل مشاعر الوصف:
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={`${result.data.description_sentiment.sentiment === 'positive' ? 'إيجابي' : 
                             result.data.description_sentiment.sentiment === 'negative' ? 'سلبي' : 'محايد'} 
                           (${(result.data.description_sentiment.confidence * 100).toFixed(1)}%)`}
                    color={result.data.description_sentiment.sentiment === 'positive' ? 'success' : 
                           result.data.description_sentiment.sentiment === 'negative' ? 'error' : 'info'}
                    size="small"
                  />
                  <Chip 
                    label={result.data.description_sentiment.source}
                    variant="outlined"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Box>
              </>
            )}

            {result.data.competitive_insights && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  رؤى تنافسية:
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>استراتيجيات بناءً على المشاعر:</strong>
                </Typography>
                {result.data.competitive_insights.sentiment_based_strategy.map((strategy: string, index: number) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5, ml: 2 }}>
                    • {strategy}
                  </Typography>
                ))}
              </>
            )}
          </Paper>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AutoAwesome color="primary" />
          اختبار التحسينات المتقدمة
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          هذه صفحة اختبار مباشرة للتأكد من عمل التحسينات. انقر على أي زر لاختبار الميزات المحسنة.
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Psychology color="primary" />
                  تحليل المشاعر المحسن
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  اختبار تحليل المشاعر باستخدام MarketMind AI مع استخراج المشاعر الذكي
                </Typography>
                <Button
                  variant="contained"
                  onClick={testSentimentAnalysis}
                  disabled={loading}
                  fullWidth
                  startIcon={<Psychology />}
                >
                  اختبار تحليل المشاعر
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AutoAwesome color="primary" />
                  إنشاء المحتوى المحسن
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  اختبار إنشاء المحتوى مع تحليل المشاعر التلقائي والاقتراحات الذكية
                </Typography>
                <Button
                  variant="contained"
                  onClick={testContentGeneration}
                  disabled={loading}
                  fullWidth
                  startIcon={<AutoAwesome />}
                >
                  اختبار إنشاء المحتوى
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Analytics color="primary" />
                  تحليل المنافسين المحسن
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  اختبار تحليل المنافسين مع تحليل مشاعر الوصف والرؤى التنافسية
                </Typography>
                <Button
                  variant="contained"
                  onClick={testCompetitorAnalysis}
                  disabled={loading}
                  fullWidth
                  startIcon={<Analytics />}
                >
                  اختبار تحليل المنافسين
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}

        {renderResult()}
      </Box>
    </Container>
  );
};

export default TestAI;
