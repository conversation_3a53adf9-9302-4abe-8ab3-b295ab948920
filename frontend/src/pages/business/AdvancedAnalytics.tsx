import React, { useState } from 'react';
import {
  Box
} from '@mui/material';
import {
  Download,
  Analytics,
  TrendingUp,
  Schedule
} from '@mui/icons-material';
import { StatCard, ChartCard, LineChart, BarChart, PieChart, ResponsiveHeader, ResponsiveGrid } from '../../components/business';
import '../../styles/business-theme.css';

const AdvancedAnalytics: React.FC = () => {
  const [reportType, setReportType] = useState('performance');
  const [timeRange, setTimeRange] = useState('30days');

  const performanceMetrics = [
    {
      title: 'معدل النقر',
      value: '3.2%',
      change: '+0.5%',
      trend: 'up',
      icon: <TrendingUp />,
      color: 'success'
    },
    {
      title: 'معدل التحويل',
      value: '2.1%',
      change: '+0.3%',
      trend: 'up',
      icon: <Analytics />,
      color: 'primary'
    },
    {
      title: 'تكلفة الاكتساب',
      value: '185 ر.س',
      change: '-12 ر.س',
      trend: 'down',
      icon: <Download />,
      color: 'success'
    },
    {
      title: 'عائد الإعلان',
      value: '4.2x',
      change: '+0.8x',
      trend: 'up',
      icon: <Schedule />,
      color: 'warning'
    }
  ];



  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="التحليلات المتقدمة"
        subtitle="تحليل شامل لأداء الحملات والجمهور والقنوات التسويقية"
        filters={[
          {
            label: 'نوع التقرير',
            value: reportType,
            options: [
              { value: 'performance', label: 'الأداء' },
              { value: 'audience', label: 'الجمهور' },
              { value: 'channels', label: 'القنوات' }
            ],
            onChange: setReportType
          },
          {
            label: 'الفترة الزمنية',
            value: timeRange,
            options: [
              { value: '7days', label: 'آخر 7 أيام' },
              { value: '30days', label: 'آخر 30 يوم' },
              { value: '90days', label: 'آخر 90 يوم' }
            ],
            onChange: setTimeRange
          }
        ]}
        primaryAction={{
          label: 'تحميل التقرير',
          icon: <Download />,
          onClick: () => console.log('Download report')
        }}
      />

      {/* Performance Metrics */}
      <ResponsiveGrid
        columns={{ xs: 1, sm: 2, md: 4 }}
        gap={3}
        className="business-grid-mobile"
      >
        {performanceMetrics.map((metric, index) => (
          <StatCard
            key={index}
            title={metric.title}
            value={metric.value}
            change={metric.change}
            trend={metric.trend as 'up' | 'down' | 'neutral'}
            icon={metric.icon}
            color={metric.color as any}
          />
        ))}
      </ResponsiveGrid>

      {/* Report Content */}
      {reportType === 'performance' && (
        <ResponsiveGrid
          columns={{ xs: 1, lg: 2 }}
          gap={3}
          className="business-grid-mobile"
        >
          <ChartCard
            title="اتجاه معدل النقر"
            subtitle="تطور معدل النقر خلال الفترة المحددة"
            height={350}
          >
            <LineChart
              data={[
                { label: 'الأسبوع 1', value: 2.8 },
                { label: 'الأسبوع 2', value: 3.1 },
                { label: 'الأسبوع 3', value: 3.2 },
                { label: 'الأسبوع 4', value: 3.0 }
              ]}
              color="#3b82f6"
              height={300}
            />
          </ChartCard>

          <ChartCard
            title="معدل التحويل حسب المصدر"
            subtitle="مقارنة أداء مصادر الزيارات المختلفة"
            height={350}
          >
            <BarChart
              data={[
                { label: 'البحث المباشر', value: 4.2 },
                { label: 'وسائل التواصل', value: 3.1 },
                { label: 'الإعلانات المدفوعة', value: 2.8 },
                { label: 'البريد الإلكتروني', value: 5.1 }
              ]}
              height={300}
              showValues={true}
            />
          </ChartCard>
        </ResponsiveGrid>
      )}

      {reportType === 'audience' && (
        <ResponsiveGrid
          columns={{ xs: 1, lg: 2 }}
          gap={3}
          className="business-grid-mobile"
        >
          <ChartCard
            title="توزيع شرائح الجمهور"
            subtitle="نسبة كل شريحة من إجمالي الجمهور"
            height={350}
          >
            <PieChart
              data={[
                { label: 'الشباب المهنيون', value: 35, color: '#3b82f6' },
                { label: 'العائلات', value: 40, color: '#10b981' },
                { label: 'كبار السن', value: 25, color: '#f59e0b' }
              ]}
              height={300}
              showLegend={true}
            />
          </ChartCard>

          <ChartCard
            title="معدل التفاعل حسب الشريحة"
            subtitle="مقارنة مستوى التفاعل بين الشرائح"
            height={350}
          >
            <BarChart
              data={[
                { label: 'الشباب المهنيون', value: 4.2 },
                { label: 'العائلات', value: 3.1 },
                { label: 'كبار السن', value: 2.8 }
              ]}
              height={300}
              showValues={true}
            />
          </ChartCard>
        </ResponsiveGrid>
      )}

      {reportType === 'channels' && (
        <ResponsiveGrid
          columns={{ xs: 1, lg: 2 }}
          gap={3}
          className="business-grid-mobile"
        >
          <ChartCard
            title="مقارنة أداء القنوات"
            subtitle="عدد التحويلات لكل قناة تسويقية"
            height={350}
          >
            <BarChart
              data={[
                { label: 'إعلانات جوجل', value: 234 },
                { label: 'التسويق عبر الإيميل', value: 156 },
                { label: 'وسائل التواصل', value: 98 },
                { label: 'إعلانات فيسبوك', value: 89 }
              ]}
              height={300}
              showValues={true}
            />
          </ChartCard>

          <ChartCard
            title="توزيع النقرات"
            subtitle="نسبة النقرات حسب القناة"
            height={350}
          >
            <PieChart
              data={[
                { label: 'جوجل', value: 5100, color: '#3b82f6' },
                { label: 'إيميل', value: 4200, color: '#10b981' },
                { label: 'تواصل اجتماعي', value: 2800, color: '#f59e0b' },
                { label: 'فيسبوك', value: 2300, color: '#ef4444' }
              ]}
              height={300}
              showLegend={true}
            />
          </ChartCard>
        </ResponsiveGrid>
      )}
    </Box>
  );
};

export default AdvancedAnalytics;
