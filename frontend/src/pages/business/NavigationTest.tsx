import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Button, 
  Grid, 
  Card, 
  CardContent,
  Chip,
  Alert
} from '@mui/material';
import { 
  Dashboard,
  People,
  Analytics,
  Assignment,
  AutoAwesome,
  TrendingDown,
  Campaign,
  Timeline,
  SentimentSatisfied,
  Link
} from '@mui/icons-material';

const NavigationTest: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const testRoutes = [
    { 
      name: 'مؤشرات الأداء', 
      path: '/business/dashboard', 
      icon: <Dashboard />,
      status: 'active'
    },
    { 
      name: 'إدارة الفريق', 
      path: '/business/team', 
      icon: <People />,
      status: 'active'
    },
    { 
      name: 'التحليلات المتقدمة', 
      path: '/business/analytics', 
      icon: <Analytics />,
      status: 'active'
    },
    { 
      name: 'تجزئة العملاء', 
      path: '/business/customer-segmentation', 
      icon: <People />,
      status: 'active'
    },
    { 
      name: 'التنبؤ بتوقف العملاء', 
      path: '/business/churn-prediction', 
      icon: <TrendingDown />,
      status: 'active'
    },
    { 
      name: 'توليد المحتوى', 
      path: '/business/content-generation', 
      icon: <AutoAwesome />,
      status: 'active'
    },
    { 
      name: 'تحسين الحملات', 
      path: '/business/campaign-optimization', 
      icon: <Campaign />,
      status: 'active'
    },
    { 
      name: 'التنبؤ بالمبيعات', 
      path: '/business/sales-prediction', 
      icon: <Analytics />,
      status: 'active'
    },
    { 
      name: 'رحلة العميل', 
      path: '/business/customer-journey', 
      icon: <Timeline />,
      status: 'active'
    },
    { 
      name: 'تحليل المشاعر', 
      path: '/business/sentiment-analysis', 
      icon: <SentimentSatisfied />,
      status: 'active'
    },
    { 
      name: 'متابعة المشاريع', 
      path: '/business/projects', 
      icon: <Assignment />,
      status: 'active'
    },
    { 
      name: 'أدوات الذكاء الاصطناعي', 
      path: '/business/ai-tools', 
      icon: <AutoAwesome />,
      status: 'active'
    },
    { 
      name: 'سلوك العملاء', 
      path: '/business/customer-behavior', 
      icon: <People />,
      status: 'active'
    },
    { 
      name: 'تحليل المنافسين', 
      path: '/business/competitor-analysis', 
      icon: <Analytics />,
      status: 'active'
    },
    { 
      name: 'توصية المنتجات', 
      path: '/business/product-recommendation', 
      icon: <AutoAwesome />,
      status: 'active'
    },
    { 
      name: 'التكاملات والربط', 
      path: '/business/integrations', 
      icon: <Link />,
      status: 'active'
    },
    { 
      name: 'اختبار التحسينات', 
      path: '/business/test-ai', 
      icon: <AutoAwesome />,
      status: 'active'
    },
    { 
      name: 'اختبار النظام الجديد', 
      path: '/business/ui-test', 
      icon: <AutoAwesome />,
      status: 'active'
    }
  ];

  const handleNavigate = (path: string, name: string) => {
    console.log(`🔄 محاولة الانتقال إلى: ${path} (${name})`);
    try {
      navigate(path);
      console.log(`✅ تم الانتقال بنجاح إلى: ${path}`);
    } catch (error) {
      console.error(`❌ فشل الانتقال إلى: ${path}`, error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ 
        fontWeight: 'bold',
        color: 'primary.main',
        mb: 3
      }}>
        🔧 اختبار التنقل - Business Dashboard
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>المسار الحالي:</strong> {location.pathname}
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          هذه الصفحة لاختبار جميع روابط التنقل في Business Dashboard. انقر على أي زر للانتقال إلى الصفحة المطلوبة.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {testRoutes.map((route, index) => (
          <Grid item xs={12} sm={6} md={4} key={route.path}>
            <Card 
              sx={{ 
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                },
                border: location.pathname === route.path ? 2 : 0,
                borderColor: location.pathname === route.path ? 'primary.main' : 'transparent'
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ mr: 2, color: 'primary.main' }}>
                    {route.icon}
                  </Box>
                  <Chip 
                    label={route.status} 
                    color={getStatusColor(route.status)} 
                    size="small" 
                  />
                </Box>
                
                <Typography variant="h6" gutterBottom sx={{ 
                  fontSize: '1rem',
                  fontWeight: 'medium'
                }}>
                  {route.name}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {route.path}
                </Typography>
                
                <Button
                  variant={location.pathname === route.path ? 'contained' : 'outlined'}
                  fullWidth
                  onClick={() => handleNavigate(route.path, route.name)}
                  startIcon={route.icon}
                  sx={{ 
                    justifyContent: 'flex-start',
                    textAlign: 'right'
                  }}
                >
                  {location.pathname === route.path ? 'الصفحة الحالية' : 'انتقل إلى الصفحة'}
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          📊 إحصائيات التنقل
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • إجمالي الصفحات: {testRoutes.length}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • الصفحات النشطة: {testRoutes.filter(r => r.status === 'active').length}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • الصفحة الحالية: {location.pathname}
        </Typography>
      </Box>
    </Box>
  );
};

export default NavigationTest;
