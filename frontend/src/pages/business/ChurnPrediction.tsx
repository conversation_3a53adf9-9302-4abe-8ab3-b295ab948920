import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  LinearProgress,
  Alert,
  Tooltip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Warning,
  TrendingDown,
  Person,
  Email,
  Phone,
  Campaign,
  Download,
  Refresh,
  PriorityHigh,
  CheckCircle,
  Cancel,
  Schedule,
  AttachMoney,
  Visibility
} from '@mui/icons-material';
import { <PERSON><PERSON>hart, Bar<PERSON>hart, ResponsiveHeader, ResponsiveGrid, StatCard, DataTable } from '../../components/business';
import type { Column } from '../../components/business';
import '../../styles/business-theme.css';

interface ChurnCustomer {
  id: string;
  name: string;
  email: string;
  phone: string;
  churnProbability: number;
  riskLevel: 'high' | 'medium' | 'low';
  customerValue: number;
  lastActivity: string;
  daysSinceLastPurchase: number;
  totalPurchases: number;
  avgOrderValue: number;
  supportTickets: number;
  emailEngagement: number;
  factors: string[];
  recommendations: string[];
}

interface ChurnFactor {
  factor: string;
  impact: number;
  description: string;
  color: string;
}

const ChurnPrediction: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<ChurnCustomer | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [timeRange, setTimeRange] = useState('30days');
  const [riskFilter, setRiskFilter] = useState('all');

  // Mock data for at-risk customers
  const churnCustomers: ChurnCustomer[] = [
    {
      id: '1',
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      phone: '+966501234567',
      churnProbability: 85,
      riskLevel: 'high',
      customerValue: 12500,
      lastActivity: '2024-01-05',
      daysSinceLastPurchase: 45,
      totalPurchases: 15,
      avgOrderValue: 850,
      supportTickets: 3,
      emailEngagement: 15,
      factors: ['عدم شراء حديث', 'انخفاض التفاعل', 'شكاوى متعددة'],
      recommendations: ['اتصال شخصي', 'عرض خصم 20%', 'حل مشاكل الدعم']
    },
    {
      id: '2',
      name: 'فاطمة سالم',
      email: '<EMAIL>',
      phone: '+966507654321',
      churnProbability: 72,
      riskLevel: 'high',
      customerValue: 8900,
      lastActivity: '2024-01-10',
      daysSinceLastPurchase: 30,
      totalPurchases: 8,
      avgOrderValue: 1100,
      supportTickets: 1,
      emailEngagement: 25,
      factors: ['تراجع في الطلبات', 'عدم فتح الإيميلات'],
      recommendations: ['حملة إيميل مخصصة', 'عرض منتجات جديدة']
    },
    {
      id: '3',
      name: 'محمد عبدالله',
      email: '<EMAIL>',
      phone: '+966502345678',
      churnProbability: 68,
      riskLevel: 'medium',
      customerValue: 5600,
      lastActivity: '2024-01-15',
      daysSinceLastPurchase: 25,
      totalPurchases: 12,
      avgOrderValue: 470,
      supportTickets: 0,
      emailEngagement: 40,
      factors: ['انخفاض قيمة الطلبات', 'تفاعل متوسط'],
      recommendations: ['برنامج ولاء', 'عروض ترقية']
    },
    {
      id: '4',
      name: 'نورا أحمد',
      email: '<EMAIL>',
      phone: '+966509876543',
      churnProbability: 45,
      riskLevel: 'medium',
      customerValue: 3200,
      lastActivity: '2024-01-18',
      daysSinceLastPurchase: 20,
      totalPurchases: 6,
      avgOrderValue: 530,
      supportTickets: 0,
      emailEngagement: 60,
      factors: ['عميل جديد نسبياً', 'نشاط متقطع'],
      recommendations: ['برنامج ترحيب', 'محتوى تعليمي']
    },
    {
      id: '5',
      name: 'خالد سعد',
      email: '<EMAIL>',
      phone: '+966503456789',
      churnProbability: 35,
      riskLevel: 'low',
      customerValue: 2100,
      lastActivity: '2024-01-20',
      daysSinceLastPurchase: 15,
      totalPurchases: 4,
      avgOrderValue: 525,
      supportTickets: 0,
      emailEngagement: 75,
      factors: ['تفاعل جيد', 'شراء منتظم'],
      recommendations: ['متابعة دورية', 'عروض تكميلية']
    }
  ];

  // Churn factors data
  const churnFactors: ChurnFactor[] = [
    { factor: 'عدم الشراء لفترة طويلة', impact: 35, description: 'أكثر من 30 يوم بدون شراء', color: '#ef4444' },
    { factor: 'انخفاض التفاعل', impact: 25, description: 'قلة فتح الإيميلات والتفاعل', color: '#f59e0b' },
    { factor: 'شكاوى الدعم', impact: 20, description: 'تذاكر دعم متعددة غير محلولة', color: '#ef4444' },
    { factor: 'تراجع قيمة الطلبات', impact: 15, description: 'انخفاض في متوسط قيمة الطلب', color: '#f59e0b' },
    { factor: 'عدم استخدام الميزات', impact: 5, description: 'عدم استخدام ميزات المنتج الرئيسية', color: '#6b7280' }
  ];

  const filteredCustomers = churnCustomers.filter(customer => 
    riskFilter === 'all' || customer.riskLevel === riskFilter
  );

  const highRiskCount = churnCustomers.filter(c => c.riskLevel === 'high').length;
  const mediumRiskCount = churnCustomers.filter(c => c.riskLevel === 'medium').length;
  const totalAtRisk = churnCustomers.length;
  const avgChurnProbability = churnCustomers.reduce((sum, c) => sum + c.churnProbability, 0) / churnCustomers.length;

  const riskDistributionData = [
    { label: 'مخاطرة عالية', value: highRiskCount },
    { label: 'مخاطرة متوسطة', value: mediumRiskCount },
    { label: 'مخاطرة منخفضة', value: churnCustomers.filter(c => c.riskLevel === 'low').length }
  ];

  const factorsData = churnFactors.map(factor => ({
    label: factor.factor,
    value: factor.impact
  }));

  const columns: Column[] = [
    {
      id: 'name',
      label: 'اسم العميل',
      minWidth: 180,
      sortable: true
    },
    {
      id: 'churnProbability',
      label: 'احتمالية التوقف',
      minWidth: 150,
      align: 'center',
      format: (value: number) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LinearProgress
            variant="determinate"
            value={value}
            sx={{
              width: 60,
              height: 8,
              borderRadius: 4,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: value > 70 ? '#ef4444' : value > 40 ? '#f59e0b' : '#10b981'
              }
            }}
          />
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {value}%
          </Typography>
        </Box>
      )
    },
    {
      id: 'riskLevel',
      label: 'مستوى المخاطرة',
      minWidth: 120,
      align: 'center',
      format: (value: string) => (
        <Chip
          label={value === 'high' ? 'عالية' : value === 'medium' ? 'متوسطة' : 'منخفضة'}
          color={value === 'high' ? 'error' : value === 'medium' ? 'warning' : 'success'}
          size="small"
          icon={value === 'high' ? <PriorityHigh /> : value === 'medium' ? <Warning /> : <CheckCircle />}
        />
      )
    },
    {
      id: 'customerValue',
      label: 'قيمة العميل',
      minWidth: 120,
      align: 'center',
      format: (value: number) => `${value.toLocaleString()} ر.س`
    },
    {
      id: 'daysSinceLastPurchase',
      label: 'أيام منذ آخر شراء',
      minWidth: 150,
      align: 'center',
      format: (value: number) => (
        <Chip
          label={`${value} يوم`}
          color={value > 30 ? 'error' : value > 15 ? 'warning' : 'success'}
          size="small"
        />
      )
    },
    {
      id: 'actions',
      label: 'الإجراءات',
      minWidth: 120,
      align: 'center',
      format: (value: any, row: ChurnCustomer) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="عرض التفاصيل">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedCustomer(row);
                setDialogOpen(true);
              }}
            >
              <Visibility />
            </IconButton>
          </Tooltip>
          <Tooltip title="اتصال">
            <IconButton size="small" color="primary">
              <Phone />
            </IconButton>
          </Tooltip>
          <Tooltip title="إرسال إيميل">
            <IconButton size="small" color="secondary">
              <Email />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  const handleRefreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportList = () => {
    // Create CSV data for churn predictions
    const csvData = filteredCustomers.map(customer => ({
      'اسم العميل': customer.name,
      'البريد الإلكتروني': customer.email,
      'احتمالية التوقف': `${customer.churnProbability}%`,
      'مستوى المخاطرة': customer.riskLevel === 'high' ? 'عالية' : customer.riskLevel === 'medium' ? 'متوسطة' : 'منخفضة',
      'قيمة العميل': `${customer.customerValue} ر.س`,
      'أيام منذ آخر شراء': customer.daysSinceLastPurchase,
      'العوامل': customer.factors.join(', '),
      'التوصيات': customer.recommendations.join(', ')
    }));

    // Convert to CSV
    const headers = Object.keys(csvData[0]);
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `churn-prediction-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCreateRetentionCampaign = (customer?: ChurnCustomer) => {
    if (customer) {
      alert(`إنشاء حملة استرداد للعميل: ${customer.name}\nاحتمالية التوقف: ${customer.churnProbability}%\nسيتم توجيهك لصفحة إنشاء الحملة...`);
    } else {
      alert(`إنشاء حملة استرداد للعملاء عالي المخاطرة\nعدد العملاء: ${highRiskCount}\nسيتم توجيهك لصفحة إنشاء الحملة...`);
    }
  };

  const handleSendEmail = (customer: ChurnCustomer) => {
    alert(`إرسال إيميل للعميل: ${customer.name}\nالبريد الإلكتروني: ${customer.email}\nسيتم فتح نافذة إنشاء الإيميل...`);
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="التنبؤ بتوقف العملاء"
        subtitle="تحديد العملاء المعرضين لخطر التوقف واتخاذ إجراءات استباقية للاحتفاظ بهم"
        actions={[
          <Button
            key="refresh"
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefreshData}
            disabled={loading}
          >
            تحديث التنبؤات
          </Button>,
          <Button
            key="export"
            variant="contained"
            startIcon={<Download />}
            onClick={handleExportList}
            sx={{ ml: 1 }}
          >
            تصدير القائمة
          </Button>
        ]}
        filters={[
          {
            label: 'الفترة الزمنية',
            value: timeRange,
            options: [
              { value: '7days', label: 'آخر 7 أيام' },
              { value: '30days', label: 'آخر 30 يوم' },
              { value: '90days', label: 'آخر 90 يوم' }
            ],
            onChange: setTimeRange
          },
          {
            label: 'مستوى المخاطرة',
            value: riskFilter,
            options: [
              { value: 'all', label: 'جميع المستويات' },
              { value: 'high', label: 'مخاطرة عالية' },
              { value: 'medium', label: 'مخاطرة متوسطة' },
              { value: 'low', label: 'مخاطرة منخفضة' }
            ],
            onChange: setRiskFilter
          }
        ]}
      />

      {/* Alert for high-risk customers */}
      {highRiskCount > 0 && (
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              startIcon={<Campaign />}
              onClick={() => handleCreateRetentionCampaign()}
            >
              إنشاء حملة استرداد
            </Button>
          }
        >
          <strong>تنبيه:</strong> لديك {highRiskCount} عميل معرض لخطر عالي للتوقف. يُنصح باتخاذ إجراءات فورية.
        </Alert>
      )}

      {/* Summary Stats */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, md: 4 }} gap={3}>
        <StatCard
          title="العملاء المعرضون للخطر"
          value={totalAtRisk.toString()}
          change={`${highRiskCount} عالي المخاطرة`}
          trend="down"
          icon={<Warning />}
          color="error"
          subtitle="يحتاجون متابعة"
          loading={loading}
        />
        <StatCard
          title="متوسط احتمالية التوقف"
          value={`${avgChurnProbability.toFixed(1)}%`}
          change="-2.3%"
          trend="down"
          icon={<TrendingDown />}
          color="warning"
          subtitle="مقارنة بالشهر الماضي"
          loading={loading}
        />
        <StatCard
          title="القيمة المعرضة للخطر"
          value={`${(churnCustomers.reduce((sum, c) => sum + c.customerValue, 0) / 1000).toFixed(0)}K ر.س`}
          change="+5.2%"
          trend="up"
          icon={<AttachMoney />}
          color="info"
          subtitle="إجمالي قيمة العملاء"
          loading={loading}
        />
        <StatCard
          title="معدل النجاح في الاستبقاء"
          value="78%"
          change="+12%"
          trend="up"
          icon={<CheckCircle />}
          color="success"
          subtitle="من الحملات السابقة"
          loading={loading}
        />
      </ResponsiveGrid>

      {/* Charts Section */}
      <ResponsiveGrid columns={{ xs: 1, lg: 2 }} gap={3}>
        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              توزيع مستويات المخاطرة
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              عدد العملاء في كل مستوى مخاطرة
            </Typography>
            <BarChart
              data={riskDistributionData}
              height={300}
              showValues={true}
            />
          </CardContent>
        </Card>

        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              العوامل المؤثرة في التوقف
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              تأثير كل عامل على احتمالية توقف العملاء
            </Typography>
            <BarChart
              data={factorsData}
              height={300}
              showValues={true}
            />
          </CardContent>
        </Card>
      </ResponsiveGrid>

      {/* Customers Table */}
      <DataTable
        title="قائمة العملاء المعرضين للخطر"
        columns={columns}
        rows={filteredCustomers}
        searchable={true}
        downloadable={true}
        onRowClick={(row) => {
          setSelectedCustomer(row as ChurnCustomer);
          setDialogOpen(true);
        }}
        defaultRowsPerPage={10}
        maxHeight={600}
      />

      {/* Customer Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedCustomer && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: getRiskColor(selectedCustomer.riskLevel), mr: 2 }}>
                  <Person />
                </Avatar>
                <Box>
                  <Typography variant="h6">{selectedCustomer.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    احتمالية التوقف: {selectedCustomer.churnProbability}%
                  </Typography>
                </Box>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    معلومات العميل
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Email /></ListItemIcon>
                      <ListItemText primary={selectedCustomer.email} />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Phone /></ListItemIcon>
                      <ListItemText primary={selectedCustomer.phone} />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><AttachMoney /></ListItemIcon>
                      <ListItemText primary={`قيمة العميل: ${selectedCustomer.customerValue.toLocaleString()} ر.س`} />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Schedule /></ListItemIcon>
                      <ListItemText primary={`آخر نشاط: ${selectedCustomer.lastActivity}`} />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    إحصائيات الشراء
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary={`إجمالي المشتريات: ${selectedCustomer.totalPurchases}`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={`متوسط قيمة الطلب: ${selectedCustomer.avgOrderValue} ر.س`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={`أيام منذ آخر شراء: ${selectedCustomer.daysSinceLastPurchase}`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={`تذاكر الدعم: ${selectedCustomer.supportTickets}`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={`تفاعل الإيميل: ${selectedCustomer.emailEngagement}%`} />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    عوامل المخاطرة
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedCustomer.factors.map((factor, index) => (
                      <Chip 
                        key={index} 
                        label={factor} 
                        color="error" 
                        variant="outlined" 
                        size="small"
                        icon={<Warning />}
                      />
                    ))}
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    الإجراءات المقترحة
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedCustomer.recommendations.map((rec, index) => (
                      <Chip 
                        key={index} 
                        label={rec} 
                        color="success" 
                        variant="outlined" 
                        size="small"
                        icon={<CheckCircle />}
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>إغلاق</Button>
              <Button
                variant="outlined"
                startIcon={<Email />}
                onClick={() => handleSendEmail(selectedCustomer!)}
              >
                إرسال إيميل
              </Button>
              <Button
                variant="contained"
                startIcon={<Campaign />}
                onClick={() => handleCreateRetentionCampaign(selectedCustomer!)}
              >
                إنشاء حملة استرداد
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default ChurnPrediction;
