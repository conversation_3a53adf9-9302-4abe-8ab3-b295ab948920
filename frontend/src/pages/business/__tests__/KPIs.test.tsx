import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import KPIs from '../KPIs';

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Mock the chart components since they use SVG and complex rendering
jest.mock('../../components/business', () => ({
  StatCard: ({ title, value, change, trend }: any) => (
    <div data-testid="stat-card">
      <div>{title}</div>
      <div>{value}</div>
      {change && <div>{change}</div>}
      {trend && <div>{trend}</div>}
    </div>
  ),
  DataTable: ({ title, columns, rows }: any) => (
    <div data-testid="data-table">
      <div>{title}</div>
      <div>{columns?.length} columns</div>
      <div>{rows?.length} rows</div>
    </div>
  ),
  ChartCard: ({ title, subtitle, children }: any) => (
    <div data-testid="chart-card">
      <div>{title}</div>
      <div>{subtitle}</div>
      <div>{children}</div>
    </div>
  ),
  LineChart: ({ data, title }: any) => (
    <div data-testid="line-chart">
      <div>{title}</div>
      <div>{data?.length} data points</div>
    </div>
  ),
  BarChart: ({ data, title }: any) => (
    <div data-testid="bar-chart">
      <div>{title}</div>
      <div>{data?.length} data points</div>
    </div>
  ),
  PieChart: ({ data, title }: any) => (
    <div data-testid="pie-chart">
      <div>{title}</div>
      <div>{data?.length} data points</div>
    </div>
  ),
  ResponsiveHeader: ({ title, subtitle, filters, primaryAction }: any) => (
    <div data-testid="responsive-header">
      <div>{title}</div>
      <div>{subtitle}</div>
      {filters?.map((filter: any, index: number) => (
        <select key={index} value={filter.value} onChange={(e) => filter.onChange(e.target.value)}>
          {filter.options.map((option: any) => (
            <option key={option.value} value={option.value}>{option.label}</option>
          ))}
        </select>
      ))}
      {primaryAction && <button onClick={primaryAction.onClick}>{primaryAction.label}</button>}
    </div>
  ),
  ResponsiveGrid: ({ children }: any) => (
    <div data-testid="responsive-grid">{children}</div>
  )
}));

describe('KPIs Page', () => {
  it('renders page title and subtitle correctly', () => {
    renderWithProviders(<KPIs />);
    
    expect(screen.getByText('مؤشرات الأداء الرئيسية')).toBeInTheDocument();
    expect(screen.getByText('متابعة وتحليل أداء الأعمال والحملات التسويقية')).toBeInTheDocument();
  });

  it('displays all KPI stat cards', () => {
    renderWithProviders(<KPIs />);
    
    expect(screen.getByText('إجمالي الإيرادات')).toBeInTheDocument();
    expect(screen.getByText('تكلفة اكتساب العميل')).toBeInTheDocument();
    expect(screen.getByText('معدل التحويل')).toBeInTheDocument();
    expect(screen.getByText('الحملات النشطة')).toBeInTheDocument();
    expect(screen.getByText('زوار الموقع')).toBeInTheDocument();
    expect(screen.getByText('معدل فتح الإيميل')).toBeInTheDocument();
  });

  it('shows filter controls', () => {
    renderWithProviders(<KPIs />);
    
    // Check for time range filter
    const timeRangeSelect = screen.getByDisplayValue('آخر 30 يوم');
    expect(timeRangeSelect).toBeInTheDocument();
    
    // Check for department filter
    const departmentSelect = screen.getByDisplayValue('جميع الأقسام');
    expect(departmentSelect).toBeInTheDocument();
  });

  it('updates filters when changed', async () => {
    renderWithProviders(<KPIs />);
    
    const timeRangeSelect = screen.getByDisplayValue('آخر 30 يوم');
    fireEvent.change(timeRangeSelect, { target: { value: '7days' } });
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('آخر 7 أيام')).toBeInTheDocument();
    });
  });

  it('displays charts section', () => {
    renderWithProviders(<KPIs />);
    
    expect(screen.getByText('اتجاه الإيرادات')).toBeInTheDocument();
    expect(screen.getByText('توزيع القنوات التسويقية')).toBeInTheDocument();
    expect(screen.getByText('معدل التحويل الأسبوعي')).toBeInTheDocument();
  });

  it('renders campaign performance table', () => {
    renderWithProviders(<KPIs />);
    
    expect(screen.getByText('أداء الحملات التسويقية')).toBeInTheDocument();
    
    // Check for data table
    const dataTables = screen.getAllByTestId('data-table');
    expect(dataTables.length).toBeGreaterThan(0);
  });

  it('displays department budget overview', () => {
    renderWithProviders(<KPIs />);
    
    expect(screen.getByText('نظرة عامة على ميزانيات الأقسام')).toBeInTheDocument();
  });

  it('shows responsive grid components', () => {
    renderWithProviders(<KPIs />);
    
    const responsiveGrids = screen.getAllByTestId('responsive-grid');
    expect(responsiveGrids.length).toBeGreaterThan(0);
  });

  it('displays chart cards with correct data', () => {
    renderWithProviders(<KPIs />);
    
    const chartCards = screen.getAllByTestId('chart-card');
    expect(chartCards.length).toBeGreaterThan(0);
    
    // Check for specific chart titles
    expect(screen.getByText('نمو الإيرادات الشهرية')).toBeInTheDocument();
    expect(screen.getByText('نسبة المبيعات حسب القناة')).toBeInTheDocument();
  });

  it('handles loading state correctly', () => {
    renderWithProviders(<KPIs />);
    
    // The page should render without loading state by default
    const statCards = screen.getAllByTestId('stat-card');
    expect(statCards.length).toBe(6); // 6 KPI cards
  });

  it('applies business dashboard styling', () => {
    const { container } = renderWithProviders(<KPIs />);
    
    const dashboardElement = container.querySelector('.business-dashboard');
    expect(dashboardElement).toBeInTheDocument();
  });
});
