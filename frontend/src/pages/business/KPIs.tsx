import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Chip,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  Campaign,
  AttachMoney,
  Visibility
} from '@mui/icons-material';
import { StatCard, DataTable, ChartCard, LineChart, BarChart, Pie<PERSON>hart, ResponsiveHeader, ResponsiveGrid } from '../../components/business';
import type { Column } from '../../components/business';
import '../../styles/business-theme.css';

const KPIs: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30days');
  const [department, setDepartment] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column"
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        gap={2}
        sx={{
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
        }}
      >
        <LinearProgress style={{ width: '50%' }} />
        <Typography variant="h6" color="text.secondary">
          جاري تحميل لوحة التحكم...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Chart data
  const revenueData = [
    { label: 'يناير', value: 45000 },
    { label: 'فبراير', value: 52000 },
    { label: 'مارس', value: 48000 },
    { label: 'أبريل', value: 61000 },
    { label: 'مايو', value: 55000 },
    { label: 'يونيو', value: 67000 }
  ];

  const channelData = [
    { label: 'إعلانات جوجل', value: 35, color: '#3b82f6' },
    { label: 'فيسبوك', value: 25, color: '#10b981' },
    { label: 'إنستجرام', value: 20, color: '#f59e0b' },
    { label: 'تويتر', value: 12, color: '#ef4444' },
    { label: 'أخرى', value: 8, color: '#8b5cf6' }
  ];

  const conversionData = [
    { label: 'الأسبوع 1', value: 2.1 },
    { label: 'الأسبوع 2', value: 2.8 },
    { label: 'الأسبوع 3', value: 3.2 },
    { label: 'الأسبوع 4', value: 2.9 }
  ];

  const kpiData = [
    {
      title: 'إجمالي الإيرادات',
      value: '524,500 ر.س',
      change: '+12.5%',
      trend: 'up',
      icon: <AttachMoney />,
      color: 'success',
      subtitle: 'مقارنة بالشهر الماضي'
    },
    {
      title: 'تكلفة اكتساب العميل',
      value: '185 ر.س',
      change: '-8.3%',
      trend: 'down',
      icon: <People />,
      color: 'success',
      subtitle: 'انخفاض في التكلفة'
    },
    {
      title: 'معدل التحويل',
      value: '3.2%',
      change: '+0.8%',
      trend: 'up',
      icon: <TrendingUp />,
      color: 'primary',
      subtitle: 'من إجمالي الزوار'
    },
    {
      title: 'الحملات النشطة',
      value: '24',
      change: '+4',
      trend: 'up',
      icon: <Campaign />,
      color: 'info',
      subtitle: 'حملة قيد التشغيل'
    },
    {
      title: 'زوار الموقع',
      value: '45.2K',
      change: '+15.2%',
      trend: 'up',
      icon: <Visibility />,
      color: 'warning',
      subtitle: 'زائر هذا الشهر'
    },
    {
      title: 'معدل فتح الإيميل',
      value: '24.8%',
      change: '-2.1%',
      trend: 'down',
      icon: <TrendingDown />,
      color: 'error',
      subtitle: 'من إجمالي المرسل'
    }
  ];

  const campaignPerformance = [
    {
      id: 1,
      name: 'حملة الصيف 2024',
      impressions: '125K',
      clicks: '3.2K',
      conversions: '156',
      roi: '320%',
      status: 'نشطة',
      budget: '50,000 ر.س'
    },
    {
      id: 2,
      name: 'إطلاق المنتج الجديد',
      impressions: '89K',
      clicks: '2.1K',
      conversions: '98',
      roi: '245%',
      status: 'مكتملة',
      budget: '35,000 ر.س'
    },
    {
      id: 3,
      name: 'عروض العيد',
      impressions: '156K',
      clicks: '4.5K',
      conversions: '234',
      roi: '410%',
      status: 'نشطة',
      budget: '75,000 ر.س'
    },
    {
      id: 4,
      name: 'العودة للمدارس',
      impressions: '67K',
      clicks: '1.8K',
      conversions: '87',
      roi: '180%',
      status: 'متوقفة',
      budget: '25,000 ر.س'
    }
  ];

  const departmentKPIs = [
    {
      id: 1,
      department: 'التسويق',
      budget: '200K ر.س',
      spent: '168K ر.س',
      roi: '285%',
      campaigns: 12,
      performance: 92
    },
    {
      id: 2,
      department: 'المبيعات',
      budget: '120K ر.س',
      spent: '112K ر.س',
      roi: '340%',
      campaigns: 8,
      performance: 95
    },
    {
      id: 3,
      department: 'المنتج',
      budget: '100K ر.س',
      spent: '88K ر.س',
      roi: '220%',
      campaigns: 6,
      performance: 88
    },
    {
      id: 4,
      department: 'خدمة العملاء',
      budget: '60K ر.س',
      spent: '52K ر.س',
      roi: '195%',
      campaigns: 4,
      performance: 85
    }
  ];

  // Define columns for campaign performance table
  const campaignColumns: Column[] = [
    {
      id: 'name',
      label: 'اسم الحملة',
      minWidth: 200,
      sortable: true
    },
    {
      id: 'status',
      label: 'الحالة',
      minWidth: 100,
      format: (value: string) => (
        <Chip
          label={value}
          color={value === 'نشطة' ? 'success' : value === 'مكتملة' ? 'info' : 'default'}
          size="small"
          variant="outlined"
        />
      )
    },
    {
      id: 'impressions',
      label: 'المشاهدات',
      minWidth: 100,
      align: 'center',
      sortable: true
    },
    {
      id: 'clicks',
      label: 'النقرات',
      minWidth: 100,
      align: 'center',
      sortable: true
    },
    {
      id: 'conversions',
      label: 'التحويلات',
      minWidth: 100,
      align: 'center',
      sortable: true
    },
    {
      id: 'roi',
      label: 'العائد على الاستثمار',
      minWidth: 150,
      align: 'center',
      format: (value: string) => (
        <Chip
          label={value}
          color="success"
          size="small"
          sx={{ fontWeight: 600 }}
        />
      )
    }
  ];

  const getProgressValue = (spent: string, budget: string) => {
    const spentNum = parseFloat(spent.replace('K ر.س', '').replace(',', ''));
    const budgetNum = parseFloat(budget.replace('K ر.س', '').replace(',', ''));
    return (spentNum / budgetNum) * 100;
  };

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="مؤشرات الأداء الرئيسية"
        subtitle="متابعة وتحليل أداء الأعمال والحملات التسويقية"
        filters={[
          {
            label: 'الفترة الزمنية',
            value: timeRange,
            options: [
              { value: '7days', label: 'آخر 7 أيام' },
              { value: '30days', label: 'آخر 30 يوم' },
              { value: '90days', label: 'آخر 90 يوم' },
              { value: '1year', label: 'السنة الماضية' }
            ],
            onChange: setTimeRange
          },
          {
            label: 'القسم',
            value: department,
            options: [
              { value: 'all', label: 'جميع الأقسام' },
              { value: 'marketing', label: 'التسويق' },
              { value: 'sales', label: 'المبيعات' },
              { value: 'product', label: 'المنتج' }
            ],
            onChange: setDepartment
          }
        ]}
      />

      {/* Main KPI Cards */}
      <ResponsiveGrid
        columns={{ xs: 1, sm: 2, md: 3, lg: 6 }}
        gap={3}
        className="business-grid-mobile"
      >
        {kpiData.map((kpi, index) => (
          <StatCard
            key={index}
            title={kpi.title}
            value={kpi.value}
            change={kpi.change}
            trend={kpi.trend as 'up' | 'down' | 'neutral'}
            icon={kpi.icon}
            color={kpi.color as any}
            subtitle={kpi.subtitle}
            loading={loading}
          />
        ))}
      </ResponsiveGrid>

      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        {/* Campaign Performance */}
        <DataTable
          title="أداء الحملات التسويقية"
          columns={campaignColumns}
          rows={campaignPerformance}
          searchable={true}
          downloadable={true}
          onRowClick={(row) => console.log('Selected campaign:', row)}
          onDownload={() => console.log('Download campaign data')}
          defaultRowsPerPage={5}
          maxHeight={400}
        />

        {/* Department Budget Overview */}
        <ChartCard
          title="نظرة عامة على ميزانيات الأقسام"
          subtitle="متابعة الإنفاق والأداء لكل قسم"
          height={400}
        >
          <Box sx={{ p: 2 }}>
            {departmentKPIs.map((dept, index) => (
              <Box key={index} sx={{
                mb: 3,
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
                background: 'white'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {dept.department}
                  </Typography>
                  <Chip
                    label={dept.roi}
                    color="success"
                    size="small"
                    sx={{ fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {dept.spent} / {dept.budget} مُنفق
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={getProgressValue(dept.spent, dept.budget)}
                  sx={{
                    mb: 1,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 4,
                      backgroundColor: dept.performance >= 90 ? 'success.main' :
                                     dept.performance >= 80 ? 'warning.main' : 'error.main'
                    }
                  }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    {dept.campaigns} حملة نشطة
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    أداء: {dept.performance}%
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </ChartCard>
      </Box>

      {/* Charts Section */}
      <ResponsiveGrid
        columns={{ xs: 1, lg: 2 }}
        gap={3}
        className="business-grid-mobile"
      >
        {/* Revenue Trend */}
        <ChartCard
          title="اتجاه الإيرادات"
          subtitle="نمو الإيرادات الشهرية"
          height={350}
        >
          <LineChart
            data={revenueData}
            color="#3b82f6"
            height={300}
          />
        </ChartCard>

        {/* Channel Distribution */}
        <ChartCard
          title="توزيع القنوات التسويقية"
          subtitle="نسبة المبيعات حسب القناة"
          height={350}
        >
          <PieChart
            data={channelData}
            height={300}
            showLegend={true}
          />
        </ChartCard>
      </ResponsiveGrid>

      {/* Conversion Rate Chart */}
      <ChartCard
        title="معدل التحويل الأسبوعي"
        subtitle="تطور معدل التحويل خلال الشهر الماضي"
        height={350}
      >
        <BarChart
          data={conversionData}
          height={300}
          showValues={true}
        />
      </ChartCard>
    </Box>
  );
};

export default KPIs;
