import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  StepContent,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Tabs,
  Tab,
  LinearProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Timeline,
  Visibility,
  ShoppingCart,
  Payment,
  Support,
  Download,
  Refresh,
  TrendingUp,
  TrendingDown,
  Warning,
  CheckCircle,
  Error,
  Info,
  PersonAdd,
  Email,
  Phone,
  Web,
  Store,
  Analytics
} from '@mui/icons-material';
import { <PERSON><PERSON>hart, Bar<PERSON>hart, ResponsiveHeader, ResponsiveGrid, StatCard } from '../../components/business';
import '../../styles/business-theme.css';

interface JourneyStage {
  id: string;
  name: string;
  description: string;
  customers: number;
  conversionRate: number;
  avgTime: string;
  dropoffRate: number;
  touchpoints: string[];
  painPoints: string[];
  opportunities: string[];
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

interface Touchpoint {
  name: string;
  channel: string;
  interactions: number;
  satisfaction: number;
  conversionRate: number;
  issues: string[];
}

const CustomerJourney: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedStage, setSelectedStage] = useState<JourneyStage | null>(null);
  const [timeRange, setTimeRange] = useState('30days');

  // Mock journey stages data
  const journeyStages: JourneyStage[] = [
    {
      id: 'awareness',
      name: 'الوعي',
      description: 'اكتشاف العلامة التجارية والمنتجات',
      customers: 10000,
      conversionRate: 25,
      avgTime: '2-3 أيام',
      dropoffRate: 75,
      touchpoints: ['إعلانات جوجل', 'وسائل التواصل', 'المحتوى التسويقي'],
      painPoints: ['صعوبة العثور على المعلومات', 'رسائل غير واضحة'],
      opportunities: ['تحسين SEO', 'محتوى أكثر جاذبية', 'استهداف أفضل'],
      performance: 'good'
    },
    {
      id: 'consideration',
      name: 'الاهتمام',
      description: 'مقارنة المنتجات والبحث عن المعلومات',
      customers: 2500,
      conversionRate: 40,
      avgTime: '5-7 أيام',
      dropoffRate: 60,
      touchpoints: ['موقع الويب', 'مراجعات العملاء', 'دعم العملاء'],
      painPoints: ['معلومات غير كافية', 'صعوبة المقارنة', 'أسعار غير واضحة'],
      opportunities: ['دليل مقارنة', 'مراجعات أكثر', 'أسعار شفافة'],
      performance: 'average'
    },
    {
      id: 'purchase',
      name: 'الشراء',
      description: 'اتخاذ قرار الشراء وإتمام العملية',
      customers: 1000,
      conversionRate: 70,
      avgTime: '1-2 أيام',
      dropoffRate: 30,
      touchpoints: ['صفحة الدفع', 'خدمة العملاء', 'تأكيد الطلب'],
      painPoints: ['عملية دفع معقدة', 'خيارات دفع محدودة', 'رسوم إضافية'],
      opportunities: ['تبسيط الدفع', 'خيارات دفع أكثر', 'شفافية الأسعار'],
      performance: 'poor'
    },
    {
      id: 'delivery',
      name: 'التسليم',
      description: 'استلام المنتج وتجربة ما بعد الشراء',
      customers: 700,
      conversionRate: 85,
      avgTime: '3-5 أيام',
      dropoffRate: 15,
      touchpoints: ['تتبع الشحن', 'التسليم', 'خدمة ما بعد البيع'],
      painPoints: ['تأخير في التسليم', 'تتبع غير دقيق', 'تغليف ضعيف'],
      opportunities: ['تحسين الشحن', 'تتبع أفضل', 'تغليف مميز'],
      performance: 'good'
    },
    {
      id: 'retention',
      name: 'الاحتفاظ',
      description: 'بناء الولاء وتشجيع الشراء المتكرر',
      customers: 595,
      conversionRate: 45,
      avgTime: '30+ أيام',
      dropoffRate: 55,
      touchpoints: ['برامج الولاء', 'إيميلات تسويقية', 'عروض خاصة'],
      painPoints: ['قلة التواصل', 'عروض غير مناسبة', 'خدمة عملاء ضعيفة'],
      opportunities: ['برنامج ولاء أفضل', 'تخصيص العروض', 'تحسين الخدمة'],
      performance: 'average'
    }
  ];

  // Mock touchpoints data
  const touchpoints: Touchpoint[] = [
    {
      name: 'موقع الويب',
      channel: 'رقمي',
      interactions: 15000,
      satisfaction: 78,
      conversionRate: 12,
      issues: ['بطء التحميل', 'صعوبة التنقل']
    },
    {
      name: 'تطبيق الجوال',
      channel: 'رقمي',
      interactions: 8500,
      satisfaction: 85,
      conversionRate: 18,
      issues: ['أخطاء تقنية', 'واجهة معقدة']
    },
    {
      name: 'خدمة العملاء',
      channel: 'مباشر',
      interactions: 3200,
      satisfaction: 72,
      conversionRate: 35,
      issues: ['أوقات انتظار طويلة', 'عدم حل المشاكل']
    },
    {
      name: 'وسائل التواصل',
      channel: 'رقمي',
      interactions: 12000,
      satisfaction: 68,
      conversionRate: 8,
      issues: ['ردود بطيئة', 'محتوى غير مناسب']
    }
  ];

  const totalCustomers = journeyStages[0].customers;
  const overallConversion = (journeyStages[journeyStages.length - 1].customers / totalCustomers) * 100;
  const avgSatisfaction = touchpoints.reduce((sum, tp) => sum + tp.satisfaction, 0) / touchpoints.length;
  const criticalStages = journeyStages.filter(stage => stage.performance === 'poor').length;

  const conversionData = journeyStages.map(stage => ({
    label: stage.name,
    value: stage.conversionRate
  }));

  const customerFlowData = journeyStages.map(stage => ({
    label: stage.name,
    value: stage.customers
  }));

  const touchpointData = touchpoints.map(tp => ({
    label: tp.name,
    value: tp.satisfaction
  }));

  const handleRefreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportAnalysis = () => {
    // Create CSV data for journey analysis
    const journeyData = journeyStages.map(stage => ({
      'المرحلة': stage.name,
      'الوصف': stage.description,
      'عدد العملاء': stage.customers,
      'معدل التحويل': `${stage.conversionRate}%`,
      'متوسط الوقت': stage.avgTime,
      'معدل التسرب': `${stage.dropoffRate}%`,
      'نقاط التفاعل': stage.touchpoints.join(', '),
      'نقاط الألم': stage.painPoints.join(', '),
      'فرص التحسين': stage.opportunities.join(', ')
    }));

    // Convert to CSV
    const headers = Object.keys(journeyData[0]);
    const csvContent = [
      headers.join(','),
      ...journeyData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `customer-journey-analysis-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'average': return 'warning';
      case 'poor': return 'error';
      default: return 'default';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <CheckCircle />;
      case 'good': return <Info />;
      case 'average': return <Warning />;
      case 'poor': return <Error />;
      default: return <Info />;
    }
  };

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="تحليل رحلة العميل"
        subtitle="فهم تجربة العميل عبر جميع نقاط التفاعل وتحديد الفرص للتحسين"
        actions={[
          <Button
            key="refresh"
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefreshData}
            disabled={loading}
          >
            تحديث البيانات
          </Button>,
          <Button
            key="export"
            variant="contained"
            startIcon={<Download />}
            onClick={handleExportAnalysis}
            sx={{ ml: 1 }}
          >
            تصدير التحليل
          </Button>
        ]}
        filters={[
          {
            label: 'الفترة الزمنية',
            value: timeRange,
            options: [
              { value: '7days', label: 'آخر 7 أيام' },
              { value: '30days', label: 'آخر 30 يوم' },
              { value: '90days', label: 'آخر 90 يوم' }
            ],
            onChange: setTimeRange
          }
        ]}
      />

      {/* Summary Stats */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, md: 4 }} gap={3}>
        <StatCard
          title="إجمالي العملاء"
          value={totalCustomers.toLocaleString()}
          change="+8.5%"
          trend="up"
          icon={<PersonAdd />}
          color="primary"
          subtitle="دخلوا الرحلة"
          loading={loading}
        />
        <StatCard
          title="معدل التحويل الإجمالي"
          value={`${overallConversion.toFixed(1)}%`}
          change="-2.1%"
          trend="down"
          icon={<TrendingDown />}
          color="warning"
          subtitle="من البداية للنهاية"
          loading={loading}
        />
        <StatCard
          title="متوسط الرضا"
          value={`${avgSatisfaction.toFixed(1)}%`}
          change="+3.2%"
          trend="up"
          icon={<CheckCircle />}
          color="success"
          subtitle="عبر نقاط التفاعل"
          loading={loading}
        />
        <StatCard
          title="المراحل الحرجة"
          value={criticalStages.toString()}
          change={criticalStages > 0 ? "تحتاج تحسين" : "جيدة"}
          trend={criticalStages > 0 ? "down" : "up"}
          icon={<Warning />}
          color="error"
          subtitle="مرحلة ضعيفة الأداء"
          loading={loading}
        />
      </ResponsiveGrid>

      {criticalStages > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>تنبيه:</strong> يوجد {criticalStages} مرحلة تحتاج إلى تحسين فوري في رحلة العميل.
          </Typography>
        </Alert>
      )}

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="خريطة الرحلة" />
        <Tab label="نقاط التفاعل" />
        <Tab label="التحليل المتقدم" />
      </Tabs>

      {activeTab === 0 && (
        <>
          {/* Journey Map */}
          <Card className="business-card" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                خريطة رحلة العميل
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                تدفق العملاء عبر مراحل الرحلة المختلفة
              </Typography>
              
              <Stepper orientation="vertical">
                {journeyStages.map((stage, index) => (
                  <Step key={stage.id} active={true}>
                    <StepLabel
                      icon={
                        <Box sx={{ 
                          width: 40, 
                          height: 40, 
                          borderRadius: '50%', 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center',
                          bgcolor: `${getPerformanceColor(stage.performance)}.main`,
                          color: 'white'
                        }}>
                          {getPerformanceIcon(stage.performance)}
                        </Box>
                      }
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Typography variant="h6">{stage.name}</Typography>
                        <Chip
                          label={`${stage.customers.toLocaleString()} عميل`}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`${stage.conversionRate}% تحويل`}
                          size="small"
                          color={getPerformanceColor(stage.performance) as any}
                        />
                      </Box>
                    </StepLabel>
                    <StepContent>
                      <Paper sx={{ p: 2, mt: 1, border: '1px solid', borderColor: 'divider' }}>
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {stage.description}
                        </Typography>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={4}>
                            <Typography variant="subtitle2" gutterBottom>
                              نقاط التفاعل الرئيسية
                            </Typography>
                            <List dense>
                              {stage.touchpoints.map((touchpoint, idx) => (
                                <ListItem key={idx} sx={{ py: 0.5 }}>
                                  <ListItemIcon sx={{ minWidth: 30 }}>
                                    <Web fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText primary={touchpoint} />
                                </ListItem>
                              ))}
                            </List>
                          </Grid>
                          
                          <Grid item xs={12} md={4}>
                            <Typography variant="subtitle2" gutterBottom color="error">
                              نقاط الألم
                            </Typography>
                            <List dense>
                              {stage.painPoints.map((pain, idx) => (
                                <ListItem key={idx} sx={{ py: 0.5 }}>
                                  <ListItemIcon sx={{ minWidth: 30 }}>
                                    <Warning fontSize="small" color="error" />
                                  </ListItemIcon>
                                  <ListItemText primary={pain} />
                                </ListItem>
                              ))}
                            </List>
                          </Grid>
                          
                          <Grid item xs={12} md={4}>
                            <Typography variant="subtitle2" gutterBottom color="success.main">
                              فرص التحسين
                            </Typography>
                            <List dense>
                              {stage.opportunities.map((opportunity, idx) => (
                                <ListItem key={idx} sx={{ py: 0.5 }}>
                                  <ListItemIcon sx={{ minWidth: 30 }}>
                                    <TrendingUp fontSize="small" color="success" />
                                  </ListItemIcon>
                                  <ListItemText primary={opportunity} />
                                </ListItem>
                              ))}
                            </List>
                          </Grid>
                        </Grid>
                        
                        <Box sx={{ mt: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
                          <Typography variant="caption">
                            متوسط الوقت: <strong>{stage.avgTime}</strong>
                          </Typography>
                          <Typography variant="caption">
                            معدل التسرب: <strong>{stage.dropoffRate}%</strong>
                          </Typography>
                        </Box>
                      </Paper>
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </CardContent>
          </Card>

          {/* Charts */}
          <ResponsiveGrid columns={{ xs: 1, lg: 2 }} gap={3}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  معدلات التحويل بين المراحل
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  نسبة العملاء الذين ينتقلون لكل مرحلة
                </Typography>
                <BarChart
                  data={conversionData}
                  height={300}
                  showValues={true}
                />
              </CardContent>
            </Card>

            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تدفق العملاء
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  عدد العملاء في كل مرحلة من الرحلة
                </Typography>
                <LineChart
                  data={customerFlowData}
                  color="#3b82f6"
                  height={300}
                />
              </CardContent>
            </Card>
          </ResponsiveGrid>
        </>
      )}

      {activeTab === 1 && (
        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              تحليل نقاط التفاعل
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              أداء ورضا العملاء عبر نقاط التفاعل المختلفة
            </Typography>
            
            <Grid container spacing={3}>
              {touchpoints.map((touchpoint, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" sx={{ flexGrow: 1 }}>
                        {touchpoint.name}
                      </Typography>
                      <Chip
                        label={touchpoint.channel}
                        size="small"
                        color={touchpoint.channel === 'رقمي' ? 'primary' : 'secondary'}
                      />
                    </Box>
                    
                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          التفاعلات
                        </Typography>
                        <Typography variant="h6">
                          {touchpoint.interactions.toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          معدل التحويل
                        </Typography>
                        <Typography variant="h6">
                          {touchpoint.conversionRate}%
                        </Typography>
                      </Grid>
                    </Grid>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        مستوى الرضا: {touchpoint.satisfaction}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={touchpoint.satisfaction}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            backgroundColor: touchpoint.satisfaction > 80 ? '#10b981' : 
                                           touchpoint.satisfaction > 60 ? '#f59e0b' : '#ef4444'
                          }
                        }}
                      />
                    </Box>
                    
                    <Typography variant="subtitle2" gutterBottom color="error">
                      المشاكل الرئيسية:
                    </Typography>
                    <List dense>
                      {touchpoint.issues.map((issue, idx) => (
                        <ListItem key={idx} sx={{ py: 0.5, pl: 0 }}>
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            <Error fontSize="small" color="error" />
                          </ListItemIcon>
                          <ListItemText primary={issue} />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                </Grid>
              ))}
            </Grid>
            
            <Box sx={{ mt: 3 }}>
              <Card sx={{ bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    رضا العملاء عبر نقاط التفاعل
                  </Typography>
                  <BarChart
                    data={touchpointData}
                    height={250}
                    showValues={true}
                  />
                </CardContent>
              </Card>
            </Box>
          </CardContent>
        </Card>
      )}

      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} lg={8}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تحليل الاختناقات
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  المراحل التي تحتاج إلى تحسين فوري
                </Typography>
                
                {journeyStages
                  .filter(stage => stage.dropoffRate > 50)
                  .map((stage, index) => (
                    <Alert 
                      key={stage.id} 
                      severity="warning" 
                      sx={{ mb: 2 }}
                      action={
                        <Button size="small" variant="outlined">
                          تحسين
                        </Button>
                      }
                    >
                      <Typography variant="body2">
                        <strong>مرحلة {stage.name}:</strong> معدل تسرب عالي ({stage.dropoffRate}%) - 
                        يفقد {Math.round(stage.customers * stage.dropoffRate / 100).toLocaleString()} عميل
                      </Typography>
                    </Alert>
                  ))}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} lg={4}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توصيات سريعة
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <TrendingUp color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="تحسين عملية الدفع"
                      secondary="تقليل خطوات الشراء بـ 50%"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <Analytics color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="تحسين المحتوى"
                      secondary="إضافة مقاطع فيديو توضيحية"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <Support color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="تطوير الدعم"
                      secondary="إضافة دردشة مباشرة"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default CustomerJourney;
