import React, { useState } from 'react';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Rating,
  Avatar,
  Divider
} from '@mui/material';
import { Recommend, ShoppingCart, TrendingUp, Star } from '@mui/icons-material';

const ProductRecommendationPage: React.FC = () => {
  const [userId, setUserId] = useState('');
  const [recommendationResults, setRecommendationResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const handleProductRecommendation = async () => {
    if (!userId.trim()) {
      setError('يرجى إدخال معرف المستخدم');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1800));
      
      setRecommendationResults({
        userId,
        userSegment: 'premium',
        totalRecommendations: 5,
        recommendations: [
          {
            id: 1,
            name: 'منتج التكنولوجيا المتقدم',
            category: 'إلكترونيات',
            price: '$299',
            score: 0.95,
            reason: 'مشابه للمشتريات السابقة في نفس الفئة',
            popularity: 'شائع جداً',
            rating: 4.8,
            reviews: 1250,
            image: '🔧'
          },
          {
            id: 2,
            name: 'خدمة الاشتراك المميزة',
            category: 'خدمات',
            price: '$49/شهر',
            score: 0.87,
            reason: 'شائع بين المستخدمين المشابهين',
            popularity: 'شائع',
            rating: 4.6,
            reviews: 890,
            image: '⭐'
          },
          {
            id: 3,
            name: 'دورة تدريبية متخصصة',
            category: 'تعليم',
            price: '$199',
            score: 0.82,
            reason: 'يتناسب مع اهتمامات المستخدم',
            popularity: 'متوسط',
            rating: 4.7,
            reviews: 456,
            image: '📚'
          },
          {
            id: 4,
            name: 'منتج العناية الشخصية',
            category: 'صحة وجمال',
            price: '$79',
            score: 0.78,
            reason: 'منتجات مشابهة حصلت على تقييمات إيجابية',
            popularity: 'جديد',
            rating: 4.5,
            reviews: 234,
            image: '💄'
          },
          {
            id: 5,
            name: 'كتاب إلكتروني متخصص',
            category: 'كتب',
            price: '$29',
            score: 0.75,
            reason: 'محتوى يتناسب مع مستوى المستخدم',
            popularity: 'محدود',
            rating: 4.4,
            reviews: 123,
            image: '📖'
          }
        ]
      });
      
      setSuccess('تم توليد التوصيات بنجاح!');
    } catch (err) {
      setError('فشل في توليد التوصيات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <Recommend sx={{ fontSize: 40, color: '#667eea' }} />
          توصية المنتجات
        </Typography>

        <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary' }}>
          توليد توصيات مخصصة للمنتجات بناءً على سلوك وتفضيلات المستخدمين
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', lg: 'row' }, gap: 4 }}>
          {/* Input Section */}
          <Box sx={{ flex: 1 }}>
            <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ShoppingCart color="primary" />
                بيانات المستخدم
              </Typography>
              
              <TextField
                fullWidth
                label="معرف المستخدم"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="أدخل معرف المستخدم أو البريد الإلكتروني..."
                sx={{ mb: 3 }}
              />

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                سيتم تحليل سلوك المستخدم وتفضيلاته لتوليد توصيات مخصصة للمنتجات والخدمات.
              </Typography>

              <Button
                variant="contained"
                onClick={handleProductRecommendation}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
                sx={{ 
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  '&:hover': { background: 'linear-gradient(45deg, #5a6fd8, #6a4190)' }
                }}
              >
                {loading ? 'جاري التوصية...' : 'توليد التوصيات'}
              </Button>
            </Card>
          </Box>

          {/* Results Section */}
          <Box sx={{ flex: 1 }}>
            {recommendationResults && (
              <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)' }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp color="primary" />
                  توصيات للمستخدم {recommendationResults.userId}
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>شريحة المستخدم:</Typography>
                  <Chip
                    label={recommendationResults.userSegment === 'premium' ? 'عملاء مميزون' : 'عملاء عاديون'}
                    color="primary"
                    sx={{ fontSize: '1rem', p: 1 }}
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>عدد التوصيات:</Typography>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 'bold' }}>
                    {recommendationResults.totalRecommendations} توصية
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  المنتجات الموصى بها:
                </Typography>

                <List>
                  {recommendationResults.recommendations.map((rec: any, index: number) => (
                    <ListItem 
                      key={index} 
                      sx={{ 
                        border: 1, 
                        borderColor: 'divider', 
                        borderRadius: 2, 
                        mb: 2,
                        background: 'rgba(255, 255, 255, 0.7)'
                      }}
                    >
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main', fontSize: '1.5rem' }}>
                        {rec.image}
                      </Avatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                              {rec.name}
                            </Typography>
                            <Chip 
                              label={`${Math.round(rec.score * 100)}% تطابق`} 
                              size="small" 
                              color="success"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                الفئة: {rec.category}
                              </Typography>
                              <Typography variant="body2" color="primary" sx={{ fontWeight: 'bold' }}>
                                السعر: {rec.price}
                              </Typography>
                            </Box>
                            
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              السبب: {rec.reason}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Rating value={rec.rating} readOnly size="small" />
                              <Typography variant="caption" color="text.secondary">
                                ({rec.reviews} تقييم)
                              </Typography>
                              <Chip 
                                label={rec.popularity} 
                                size="small" 
                                variant="outlined"
                                color={rec.popularity === 'شائع جداً' ? 'success' : 'default'}
                              />
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Card>
            )}
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 3 }}>
            {success}
          </Alert>
        )}
      </Box>
    </Container>
  );
};

export default ProductRecommendationPage; 