import React, { useState, useEffect } from 'react';
import { Box, Typography, Container } from '@mui/material';
import { 
  TrendingUp, 
  People, 
  AttachMoney, 
  Visibility,
  ShoppingCart,
  Campaign,
  Analytics,
  Star
} from '@mui/icons-material';
import EnhancedCard from '../../components/ui/EnhancedCard';
import { LoadingOverlay, AnimatedBox } from '../../components/ui/LoadingAndAnimations';
import { theme } from '../../theme';

interface KPIData {
  id: string;
  title: string;
  value: string | number;
  subtitle: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    label: string;
  };
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

const EnhancedKPIs: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [kpiData, setKpiData] = useState<KPIData[]>([]);

  useEffect(() => {
    // محاكاة تحميل البيانات
    const fetchKPIData = async () => {
      setLoading(true);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockData: KPIData[] = [
        {
          id: 'revenue',
          title: 'إجمالي الإيرادات',
          value: '245,680',
          subtitle: 'ريال سعودي',
          trend: {
            direction: 'up',
            value: '+12.5%',
            label: 'مقارنة بالشهر الماضي'
          },
          icon: <AttachMoney />,
          color: 'success'
        },
        {
          id: 'customers',
          title: 'العملاء الجدد',
          value: 1247,
          subtitle: 'عميل هذا الشهر',
          trend: {
            direction: 'up',
            value: '+8.2%',
            label: 'نمو مستمر'
          },
          icon: <People />,
          color: 'primary'
        },
        {
          id: 'conversion',
          title: 'معدل التحويل',
          value: '3.8%',
          subtitle: 'من الزوار إلى عملاء',
          trend: {
            direction: 'up',
            value: '+0.5%',
            label: 'تحسن ملحوظ'
          },
          icon: <TrendingUp />,
          color: 'info'
        },
        {
          id: 'pageviews',
          title: 'مشاهدات الصفحة',
          value: '89,432',
          subtitle: 'مشاهدة هذا الأسبوع',
          trend: {
            direction: 'down',
            value: '-2.1%',
            label: 'انخفاض طفيف'
          },
          icon: <Visibility />,
          color: 'warning'
        },
        {
          id: 'orders',
          title: 'الطلبات المكتملة',
          value: 892,
          subtitle: 'طلب هذا الشهر',
          trend: {
            direction: 'up',
            value: '+15.3%',
            label: 'أداء ممتاز'
          },
          icon: <ShoppingCart />,
          color: 'success'
        },
        {
          id: 'campaigns',
          title: 'الحملات النشطة',
          value: 12,
          subtitle: 'حملة تسويقية',
          trend: {
            direction: 'neutral',
            value: '0%',
            label: 'مستقر'
          },
          icon: <Campaign />,
          color: 'secondary'
        },
        {
          id: 'engagement',
          title: 'معدل التفاعل',
          value: '4.7%',
          subtitle: 'على وسائل التواصل',
          trend: {
            direction: 'up',
            value: '+1.2%',
            label: 'تفاعل متزايد'
          },
          icon: <Analytics />,
          color: 'info'
        },
        {
          id: 'satisfaction',
          title: 'رضا العملاء',
          value: '4.8/5',
          subtitle: 'تقييم متوسط',
          trend: {
            direction: 'up',
            value: '+0.3',
            label: 'تحسن في الخدمة'
          },
          icon: <Star />,
          color: 'warning'
        }
      ];
      
      setKpiData(mockData);
      setLoading(false);
    };

    fetchKPIData();
  }, []);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <LoadingOverlay loading={loading}>
        <AnimatedBox animation="fadeIn" duration={0.6}>
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h3"
              sx={{
                fontSize: theme.typography.fontSize['3xl'],
                fontWeight: theme.typography.fontWeight.bold,
                color: theme.colors.text.primary,
                mb: 1,
                background: theme.effects.gradients.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              📊 مؤشرات الأداء الرئيسية
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: theme.colors.text.secondary,
                fontWeight: theme.typography.fontWeight.medium
              }}
            >
              نظرة شاملة على أداء أعمالك في الوقت الفعلي
            </Typography>
          </Box>

          <Box 
            sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 3,
              '& > *': {
                flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)', lg: '1 1 calc(25% - 18px)' }
              }
            }}
          >
            {kpiData.map((kpi, index) => (
              <Box key={kpi.id}>
                <AnimatedBox 
                  animation="slideInRight" 
                  delay={index * 0.1} 
                  duration={0.5}
                >
                  <EnhancedCard
                    title={kpi.title}
                    value={kpi.value}
                    subtitle={kpi.subtitle}
                    trend={kpi.trend}
                    icon={kpi.icon}
                    color={kpi.color}
                    variant="elevated"
                    onClick={() => {
                      console.log(`Clicked on ${kpi.title}`);
                      // يمكن إضافة navigation أو modal هنا
                    }}
                  />
                </AnimatedBox>
              </Box>
            ))}
          </Box>

          {/* قسم إضافي للرؤى السريعة */}
          <AnimatedBox animation="fadeIn" delay={0.8} duration={0.6}>
            <Box sx={{ mt: 6 }}>
              <Typography
                variant="h4"
                sx={{
                  fontSize: theme.typography.fontSize['2xl'],
                  fontWeight: theme.typography.fontWeight.semibold,
                  color: theme.colors.text.primary,
                  mb: 3,
                  textAlign: 'center'
                }}
              >
                🎯 رؤى سريعة
              </Typography>

              <Box 
                sx={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: 3,
                  '& > *': {
                    flex: { xs: '1 1 100%', md: '1 1 calc(33.333% - 16px)' }
                  }
                }}
              >
                <Box>
                  <EnhancedCard
                    title="أفضل منتج"
                    value="جهاز لابتوب"
                    subtitle="892 مبيعة هذا الشهر"
                    icon={<Star />}
                    color="warning"
                    variant="gradient"
                    badge={{ label: "الأكثر مبيعاً", color: "warning" }}
                  />
                </Box>

                <Box>
                  <EnhancedCard
                    title="أفضل حملة"
                    value="حملة الصيف"
                    subtitle="معدل تحويل 5.2%"
                    icon={<Campaign />}
                    color="success"
                    variant="gradient"
                    badge={{ label: "الأكثر فعالية", color: "success" }}
                  />
                </Box>

                <Box>
                  <EnhancedCard
                    title="أفضل قناة"
                    value="وسائل التواصل"
                    subtitle="45% من الزيارات"
                    icon={<Analytics />}
                    color="info"
                    variant="gradient"
                    badge={{ label: "المصدر الأول", color: "info" }}
                  />
                </Box>
              </Box>
            </Box>
          </AnimatedBox>

          {/* قسم الملخص */}
          <AnimatedBox animation="fadeIn" delay={1.0} duration={0.6}>
            <Box 
              sx={{ 
                mt: 6,
                p: 4,
                background: theme.effects.gradients.card,
                borderRadius: theme.spacing.border.radius['2xl'],
                border: `1px solid ${theme.colors.border.light}`,
                textAlign: 'center'
              }}
            >
              <Typography
                variant="h5"
                sx={{
                  fontSize: theme.typography.fontSize.xl,
                  fontWeight: theme.typography.fontWeight.semibold,
                  color: theme.colors.text.primary,
                  mb: 2
                }}
              >
                📈 ملخص الأداء العام
              </Typography>
              
              <Typography
                variant="body1"
                sx={{
                  color: theme.colors.text.secondary,
                  lineHeight: theme.typography.lineHeight.relaxed,
                  maxWidth: '600px',
                  mx: 'auto'
                }}
              >
                أعمالك تسير في الاتجاه الصحيح! مع نمو في الإيرادات بنسبة 12.5% وزيادة في عدد العملاء الجدد، 
                تظهر البيانات تحسناً مستمراً في الأداء. ننصح بالتركيز على تحسين معدل التحويل لتحقيق نتائج أفضل.
              </Typography>
            </Box>
          </AnimatedBox>
        </AnimatedBox>
      </LoadingOverlay>
    </Container>
  );
};

export default EnhancedKPIs;
