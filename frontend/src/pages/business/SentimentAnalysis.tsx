import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Alert,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  Divider,
  LinearProgress,
  Container,
  CircularProgress
} from '@mui/material';
import {
  SentimentSatisfied,
  SentimentNeutral,
  SentimentDissatisfied,
  Download,
  Refresh,
  Search,
  TrendingUp,
  TrendingDown,
  Facebook,
  Twitter,
  Instagram,
  Star,
  Comment,
  Share,
  ThumbUp,
  ThumbDown,
  Warning,
  Info,
  CheckCircle,
  Psychology,
  Analytics
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Bar<PERSON>hart, ResponsiveHeader, ResponsiveGrid, StatCard, DataTable } from '../../components/business';
import type { Column } from '../../components/business';
import '../../styles/business-theme.css';

interface SentimentData {
  source: string;
  positive: number;
  neutral: number;
  negative: number;
  total: number;
  trend: 'up' | 'down' | 'neutral';
  change: number;
}

interface SentimentPost {
  id: string;
  platform: string;
  author: string;
  content: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  confidence: number;
  engagement: number;
  date: string;
  keywords: string[];
  category: string;
}

interface KeywordSentiment {
  keyword: string;
  mentions: number;
  sentiment: number; // -100 to 100
  trend: 'up' | 'down' | 'neutral';
  change: number;
}

const SentimentAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [timeRange, setTimeRange] = useState('30days');
  const [sentimentFilter, setSentimentFilter] = useState('all');
  const [sentimentText, setSentimentText] = useState('');
  const [sentimentResults, setSentimentResults] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Mock sentiment data by source
  const sentimentBySource: SentimentData[] = [
    {
      source: 'Twitter',
      positive: 45,
      neutral: 35,
      negative: 20,
      total: 1250,
      trend: 'up',
      change: 5.2
    },
    {
      source: 'Facebook',
      positive: 52,
      neutral: 28,
      negative: 20,
      total: 890,
      trend: 'up',
      change: 3.1
    },
    {
      source: 'Instagram',
      positive: 65,
      neutral: 25,
      negative: 10,
      total: 650,
      trend: 'up',
      change: 8.5
    },
    {
      source: 'مراجعات Google',
      positive: 38,
      neutral: 32,
      negative: 30,
      total: 420,
      trend: 'down',
      change: -2.8
    },
    {
      source: 'مراجعات المنتجات',
      positive: 42,
      neutral: 28,
      negative: 30,
      total: 380,
      trend: 'down',
      change: -1.5
    }
  ];

  // Mock posts data
  const sentimentPosts: SentimentPost[] = [
    {
      id: '1',
      platform: 'Twitter',
      author: '@ahmed_salem',
      content: 'تجربة رائعة مع المنتج الجديد! جودة عالية وخدمة ممتازة. أنصح الجميع بالتجربة 👍',
      sentiment: 'positive',
      confidence: 92,
      engagement: 45,
      date: '2024-01-20',
      keywords: ['جودة عالية', 'خدمة ممتازة', 'تجربة رائعة'],
      category: 'منتج'
    },
    {
      id: '2',
      platform: 'Facebook',
      author: 'فاطمة محمد',
      content: 'للأسف التسليم كان متأخر جداً وخدمة العملاء لم تكن مفيدة. أتمنى تحسين الخدمة.',
      sentiment: 'negative',
      confidence: 88,
      engagement: 23,
      date: '2024-01-19',
      keywords: ['تسليم متأخر', 'خدمة العملاء', 'تحسين الخدمة'],
      category: 'خدمة'
    },
    {
      id: '3',
      platform: 'Instagram',
      author: '@sara_style',
      content: 'المنتج جيد بشكل عام، لكن السعر مرتفع قليلاً مقارنة بالمنافسين.',
      sentiment: 'neutral',
      confidence: 75,
      engagement: 18,
      date: '2024-01-18',
      keywords: ['منتج جيد', 'سعر مرتفع', 'منافسين'],
      category: 'سعر'
    },
    {
      id: '4',
      platform: 'Google Reviews',
      author: 'محمد عبدالله',
      content: 'خدمة سريعة ومنتج ممتاز! سأعود للشراء مرة أخرى بالتأكيد.',
      sentiment: 'positive',
      confidence: 95,
      engagement: 12,
      date: '2024-01-17',
      keywords: ['خدمة سريعة', 'منتج ممتاز', 'سأعود للشراء'],
      category: 'خدمة'
    }
  ];

  // Mock keyword sentiment data
  const keywordSentiments: KeywordSentiment[] = [
    { keyword: 'جودة المنتج', mentions: 245, sentiment: 75, trend: 'up', change: 8.2 },
    { keyword: 'خدمة العملاء', mentions: 189, sentiment: -15, trend: 'down', change: -12.5 },
    { keyword: 'التسليم', mentions: 156, sentiment: -25, trend: 'down', change: -8.9 },
    { keyword: 'السعر', mentions: 134, sentiment: 10, trend: 'up', change: 3.4 },
    { keyword: 'التطبيق', mentions: 98, sentiment: 45, trend: 'up', change: 15.2 }
  ];

  const filteredPosts = sentimentPosts.filter(post => {
    const matchesSearch = searchTerm === '' || 
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSentiment = sentimentFilter === 'all' || post.sentiment === sentimentFilter;
    return matchesSearch && matchesSentiment;
  });

  const totalMentions = sentimentBySource.reduce((sum, source) => sum + source.total, 0);
  const overallPositive = sentimentBySource.reduce((sum, source) => sum + (source.positive * source.total), 0) / totalMentions;
  const overallNegative = sentimentBySource.reduce((sum, source) => sum + (source.negative * source.total), 0) / totalMentions;
  const sentimentScore = overallPositive - overallNegative;

  const sentimentDistribution = [
    { label: 'إيجابي', value: overallPositive, color: '#10b981' },
    { label: 'محايد', value: 100 - overallPositive - overallNegative, color: '#6b7280' },
    { label: 'سلبي', value: overallNegative, color: '#ef4444' }
  ];

  const trendData = [
    { label: 'الأسبوع 1', value: 65 },
    { label: 'الأسبوع 2', value: 72 },
    { label: 'الأسبوع 3', value: 68 },
    { label: 'الأسبوع 4', value: 75 }
  ];

  const platformData = sentimentBySource.map(source => ({
    label: source.source,
    value: source.positive
  }));

  const columns: Column[] = [
    {
      id: 'platform',
      label: 'المنصة',
      minWidth: 100,
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          icon={
            value === 'Twitter' ? <Twitter /> :
            value === 'Facebook' ? <Facebook /> :
            value === 'Instagram' ? <Instagram /> : <Star />
          }
        />
      )
    },
    {
      id: 'author',
      label: 'المؤلف',
      minWidth: 150
    },
    {
      id: 'content',
      label: 'المحتوى',
      minWidth: 300,
      format: (value: string) => (
        <Typography variant="body2" sx={{ 
          maxWidth: 300, 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {value}
        </Typography>
      )
    },
    {
      id: 'sentiment',
      label: 'المشاعر',
      minWidth: 120,
      align: 'center',
      format: (value: string, row: SentimentPost) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={value === 'positive' ? 'إيجابي' : value === 'negative' ? 'سلبي' : 'محايد'}
            color={value === 'positive' ? 'success' : value === 'negative' ? 'error' : 'default'}
            size="small"
            icon={
              value === 'positive' ? <SentimentSatisfied /> :
              value === 'negative' ? <SentimentDissatisfied /> : <SentimentNeutral />
            }
          />
          <Typography variant="caption">
            {row.confidence}%
          </Typography>
        </Box>
      )
    },
    {
      id: 'engagement',
      label: 'التفاعل',
      minWidth: 100,
      align: 'center'
    },
    {
      id: 'date',
      label: 'التاريخ',
      minWidth: 120,
      align: 'center'
    }
  ];

  const handleRefreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 2000);
  };

  const handleExportReport = () => {
    // Create CSV data for sentiment analysis
    const postsData = filteredPosts.map(post => ({
      'المنصة': post.platform,
      'المؤلف': post.author,
      'المحتوى': post.content,
      'المشاعر': post.sentiment === 'positive' ? 'إيجابي' : post.sentiment === 'negative' ? 'سلبي' : 'محايد',
      'مستوى الثقة': `${post.confidence}%`,
      'التفاعل': post.engagement,
      'التاريخ': post.date,
      'الفئة': post.category
    }));

    // Add summary data
    const summaryData = [
      'ملخص التحليل:',
      `إجمالي الإشارات: ${totalMentions}`,
      `المشاعر الإيجابية: ${overallPositive.toFixed(1)}%`,
      `المشاعر السلبية: ${overallNegative.toFixed(1)}%`,
      `نقاط المشاعر: ${sentimentScore.toFixed(1)}`,
      '',
      'تفاصيل المنشورات:',
      ...postsData.map(row => Object.values(row).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([summaryData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `sentiment-analysis-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 20) return <SentimentSatisfied color="success" />;
    if (sentiment < -20) return <SentimentDissatisfied color="error" />;
    return <SentimentNeutral color="action" />;
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 20) return 'success.main';
    if (sentiment < -20) return 'error.main';
    return 'text.secondary';
  };

  const handleSentimentAnalysis = async () => {
    if (!sentimentText.trim()) {
      setError('يرجى إدخال نص للتحليل');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const sentiment = Math.random() > 0.5 ? 'positive' : 'negative';
      const score = Math.random() * 100;
      
      setSentimentResults({
        sentiment,
        score: score.toFixed(1),
        confidence: (Math.random() * 20 + 80).toFixed(1),
        emotions: {
          joy: Math.random() * 100,
          sadness: Math.random() * 100,
          anger: Math.random() * 100,
          fear: Math.random() * 100,
          surprise: Math.random() * 100
        },
        keywords: [
          { word: 'ممتاز', sentiment: 'positive', weight: 0.8 },
          { word: 'رائع', sentiment: 'positive', weight: 0.7 },
          { word: 'مشكلة', sentiment: 'negative', weight: 0.6 },
          { word: 'تحسن', sentiment: 'positive', weight: 0.5 }
        ],
        suggestions: [
          'تحسين خدمة العملاء',
          'تطوير المنتجات',
          'تحسين التواصل',
          'تطوير الاستراتيجية'
        ]
      });
      
      setSuccess('تم تحليل المشاعر بنجاح!');
    } catch (err) {
      setError('فشل في تحليل المشاعر');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <Psychology sx={{ fontSize: 40, color: '#667eea' }} />
          تحليل مشاعر العملاء
        </Typography>

        <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary' }}>
          فهم مشاعر العملاء وتحليل آرائهم لتحسين الخدمات والمنتجات
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', lg: 'row' }, gap: 4 }}>
          {/* Input Section */}
          <Box sx={{ flex: 1 }}>
            <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Analytics color="primary" />
                النص المراد تحليله
              </Typography>
              
              <TextField
                fullWidth
                multiline
                rows={6}
                label="النص المراد تحليله"
                value={sentimentText}
                onChange={(e) => setSentimentText(e.target.value)}
                placeholder="أدخل النص الذي تريد تحليل مشاعره مثل:
• تعليقات العملاء
• مراجعات المنتجات
• ردود الفعل على الخدمات
• آراء المستخدمين
• تقييمات الموقع"
                sx={{ mb: 3 }}
              />

              <Button
                variant="contained"
                onClick={handleSentimentAnalysis}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
                sx={{ 
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  '&:hover': { background: 'linear-gradient(45deg, #5a6fd8, #6a4190)' }
                }}
              >
                {loading ? 'جاري التحليل...' : 'تحليل المشاعر'}
              </Button>
            </Card>
          </Box>

          {/* Results Section */}
          <Box sx={{ flex: 1 }}>
            {sentimentResults && (
              <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)' }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp color="primary" />
                  نتائج تحليل المشاعر
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>التقييم العام:</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    {getSentimentIcon(sentimentResults.sentiment)}
                    <Chip
                      label={sentimentResults.sentiment === 'positive' ? 'إيجابي' : 'سلبي'}
                      color={getSentimentColor(sentimentResults.sentiment) as any}
                      sx={{ fontSize: '1rem', p: 1 }}
                    />
                  </Box>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 'bold' }}>
                    النتيجة: {sentimentResults.score}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    مستوى الثقة: {sentimentResults.confidence}%
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>تحليل المشاعر التفصيلي:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '200px' }}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(76, 175, 80, 0.1)', borderRadius: 2 }}>
                        <Typography variant="h6" color="success.main">{sentimentResults.emotions.joy.toFixed(1)}%</Typography>
                        <Typography variant="body2">الفرح</Typography>
                      </Box>
                    </Box>
                    <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '200px' }}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(244, 67, 54, 0.1)', borderRadius: 2 }}>
                        <Typography variant="h6" color="error.main">{sentimentResults.emotions.sadness.toFixed(1)}%</Typography>
                        <Typography variant="body2">الحزن</Typography>
                      </Box>
                    </Box>
                    <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '200px' }}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
                        <Typography variant="h6" color="warning.main">{sentimentResults.emotions.anger.toFixed(1)}%</Typography>
                        <Typography variant="body2">الغضب</Typography>
                      </Box>
                    </Box>
                    <Box sx={{ flex: '1 1 calc(50% - 8px)', minWidth: '200px' }}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(156, 39, 176, 0.1)', borderRadius: 2 }}>
                        <Typography variant="h6" color="secondary.main">{sentimentResults.emotions.fear.toFixed(1)}%</Typography>
                        <Typography variant="body2">الخوف</Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>الكلمات المفتاحية:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {sentimentResults.keywords.map((keyword: any, index: number) => (
                      <Chip
                        key={index}
                        label={`${keyword.word} (${Math.round(keyword.weight * 100)}%)`}
                        color={keyword.sentiment === 'positive' ? 'success' : 'error'}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>

                <Box>
                  <Typography variant="subtitle1" gutterBottom>التوصيات:</Typography>
                  <List dense>
                    {sentimentResults.suggestions.map((suggestion: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={suggestion}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Card>
            )}
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 3 }}>
            {success}
          </Alert>
        )}
      </Box>
    </Container>
  );
};

export default SentimentAnalysis;
