import React, { useState } from 'react';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  Alert,
  CircularProgress,
  Card,
  CardContent
} from '@mui/material';
import { People, TrendingUp, Psychology } from '@mui/icons-material';

const CustomerBehaviorPage: React.FC = () => {
  const [customerData, setCustomerData] = useState('');
  const [behaviorResults, setBehaviorResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const handleCustomerBehavior = async () => {
    if (!customerData.trim()) {
      setError('يرجى إدخال بيانات العميل');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setBehaviorResults({
        purchaseIntent: 'high',
        probability: '85%',
        customerSegment: 'premium',
        lifetimeValue: '$2,500',
        recommendations: [
          'عرض خاص على المنتجات المميزة',
          'خدمة مخصصة مع مدير حساب',
          'متابعة مباشرة خلال 24 ساعة',
          'عرض تجريبي مجاني'
        ],
        riskFactors: [
          'عدم وجود تفاعل في الشهر الماضي',
          'انخفاض في قيمة المشتريات'
        ]
      });
      
      setSuccess('تم تحليل سلوك العميل بنجاح!');
    } catch (err) {
      setError('فشل في تحليل سلوك العميل');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <People sx={{ fontSize: 40, color: '#667eea' }} />
          تحليل سلوك العملاء
        </Typography>

        <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary' }}>
          توقع سلوك العملاء وتحسين استراتيجيات الاحتفاظ بهم
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', lg: 'row' }, gap: 4 }}>
          {/* Input Section */}
          <Box sx={{ flex: 1 }}>
            <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Psychology color="primary" />
                بيانات العميل
              </Typography>
              
              <TextField
                fullWidth
                multiline
                rows={6}
                label="بيانات العميل"
                value={customerData}
                onChange={(e) => setCustomerData(e.target.value)}
                placeholder="أدخل بيانات العميل مثل:
• العمر والجنس
• المشتريات السابقة
• تاريخ التفاعل
• تفضيلات المنتجات
• سلوك التصفح
• تقييمات سابقة"
                sx={{ mb: 3 }}
              />

              <Button
                variant="contained"
                onClick={handleCustomerBehavior}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
                sx={{ 
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  '&:hover': { background: 'linear-gradient(45deg, #5a6fd8, #6a4190)' }
                }}
              >
                {loading ? 'جاري التحليل...' : 'تحليل سلوك العميل'}
              </Button>
            </Card>
          </Box>

          {/* Results Section */}
          <Box sx={{ flex: 1 }}>
            {behaviorResults && (
              <Card sx={{ p: 3, background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)' }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TrendingUp color="primary" />
                  نتائج التحليل
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>نية الشراء:</Typography>
                  <Chip
                    label={behaviorResults.purchaseIntent === 'high' ? 'عالية جداً' : 'منخفضة'}
                    color={behaviorResults.purchaseIntent === 'high' ? 'success' : 'warning'}
                    sx={{ fontSize: '1rem', p: 1 }}
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>احتمالية الشراء:</Typography>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                    {behaviorResults.probability}
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>شريحة العميل:</Typography>
                  <Chip
                    label={behaviorResults.customerSegment === 'premium' ? 'عملاء مميزون' : 'عملاء عاديون'}
                    color="primary"
                    variant="outlined"
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>القيمة مدى الحياة:</Typography>
                  <Typography variant="h5" color="success.main" sx={{ fontWeight: 'bold' }}>
                    {behaviorResults.lifetimeValue}
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>التوصيات:</Typography>
                  <List dense>
                    {behaviorResults.recommendations.map((rec: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={rec}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box>
                  <Typography variant="subtitle1" gutterBottom color="warning.main">
                    عوامل الخطر:
                  </Typography>
                  <List dense>
                    {behaviorResults.riskFactors.map((risk: string, index: number) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText 
                          primary={risk}
                          primaryTypographyProps={{ variant: 'body2', color: 'warning.main' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Card>
            )}
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 3 }}>
            {success}
          </Alert>
        )}
      </Box>
    </Container>
  );
};

export default CustomerBehaviorPage; 