import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Container, 
  Typography, 
  Grid, 
  Avatar, 
  Chip,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import { 
  Add, 
  MoreVert, 
  Edit, 
  Delete, 
  Email, 
  Phone,
  Person,
  Group,
  Star,
  Work
} from '@mui/icons-material';
import EnhancedCard from '../../components/ui/EnhancedCard';
import EnhancedTable from '../../components/ui/EnhancedTable';
import { PrimaryButton, OutlineButton } from '../../components/ui/EnhancedButton';
import { LoadingOverlay, AnimatedBox } from '../../components/ui/LoadingAndAnimations';
import { ConfirmationModal } from '../../components/ui/EnhancedModal';
import { theme } from '../../theme';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  department: string;
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  joinDate: string;
  lastActive: string;
  projects: number;
  performance: number;
}

const EnhancedTeamManagement: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  useEffect(() => {
    // محاكاة تحميل البيانات
    const fetchTeamData = async () => {
      setLoading(true);
      
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      const mockTeamData: TeamMember[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '+966501234567',
          role: 'مدير التسويق',
          department: 'التسويق',
          status: 'active',
          joinDate: '2023-01-15',
          lastActive: '2024-06-25 10:30',
          projects: 8,
          performance: 95
        },
        {
          id: '2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          phone: '+966507654321',
          role: 'مطورة واجهات',
          department: 'التطوير',
          status: 'active',
          joinDate: '2023-03-20',
          lastActive: '2024-06-25 09:15',
          projects: 12,
          performance: 88
        },
        {
          id: '3',
          name: 'محمد السعد',
          email: '<EMAIL>',
          phone: '+966509876543',
          role: 'محلل بيانات',
          department: 'التحليلات',
          status: 'active',
          joinDate: '2023-05-10',
          lastActive: '2024-06-24 16:45',
          projects: 6,
          performance: 92
        },
        {
          id: '4',
          name: 'نورا خالد',
          email: '<EMAIL>',
          phone: '+966502468135',
          role: 'مصممة UX/UI',
          department: 'التصميم',
          status: 'pending',
          joinDate: '2024-06-01',
          lastActive: '2024-06-25 08:00',
          projects: 2,
          performance: 85
        },
        {
          id: '5',
          name: 'عبدالله أحمد',
          email: '<EMAIL>',
          phone: '+966503691472',
          role: 'مطور خلفي',
          department: 'التطوير',
          status: 'inactive',
          joinDate: '2022-11-30',
          lastActive: '2024-06-20 14:20',
          projects: 15,
          performance: 78
        }
      ];
      
      setTeamMembers(mockTeamData);
      setLoading(false);
    };

    fetchTeamData();
  }, []);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, member: TeamMember) => {
    setAnchorEl(event.currentTarget);
    setSelectedMember(member);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMember(null);
  };

  const handleDeleteClick = () => {
    setDeleteModalOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = () => {
    if (selectedMember) {
      setTeamMembers(prev => prev.filter(member => member.id !== selectedMember.id));
      setDeleteModalOpen(false);
      setSelectedMember(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'pending': return 'في الانتظار';
      default: return status;
    }
  };

  const getPerformanceColor = (performance: number) => {
    if (performance >= 90) return 'success';
    if (performance >= 80) return 'info';
    if (performance >= 70) return 'warning';
    return 'error';
  };

  // إعداد أعمدة الجدول
  const columns = [
    {
      id: 'member',
      label: 'العضو',
      minWidth: 200,
      format: (value: any, row: TeamMember) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ 
            bgcolor: theme.colors.primary[500],
            width: 40,
            height: 40
          }}>
            {row.name.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              {row.name}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {row.email}
            </Typography>
          </Box>
        </Box>
      )
    },
    {
      id: 'role',
      label: 'المنصب',
      minWidth: 150,
      format: (value: string, row: TeamMember) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {value}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {row.department}
          </Typography>
        </Box>
      )
    },
    {
      id: 'status',
      label: 'الحالة',
      minWidth: 100,
      format: (value: string) => (
        <Chip
          label={getStatusText(value)}
          color={getStatusColor(value) as any}
          size="small"
        />
      )
    },
    {
      id: 'projects',
      label: 'المشاريع',
      minWidth: 100,
      align: 'center' as const
    },
    {
      id: 'performance',
      label: 'الأداء',
      minWidth: 120,
      format: (value: number) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {value}%
          </Typography>
          <Chip
            label={value >= 90 ? 'ممتاز' : value >= 80 ? 'جيد' : value >= 70 ? 'مقبول' : 'ضعيف'}
            color={getPerformanceColor(value) as any}
            size="small"
            variant="outlined"
          />
        </Box>
      )
    },
    {
      id: 'lastActive',
      label: 'آخر نشاط',
      minWidth: 150,
      format: (value: string) => (
        <Typography variant="caption" color="textSecondary">
          {new Date(value).toLocaleDateString('ar-SA')}
        </Typography>
      )
    }
  ];

  // إحصائيات الفريق
  const teamStats = [
    {
      title: 'إجمالي الأعضاء',
      value: teamMembers.length,
      subtitle: 'عضو في الفريق',
      icon: <Group />,
      color: 'primary' as const
    },
    {
      title: 'الأعضاء النشطون',
      value: teamMembers.filter(m => m.status === 'active').length,
      subtitle: 'عضو نشط',
      icon: <Person />,
      color: 'success' as const
    },
    {
      title: 'متوسط الأداء',
      value: `${Math.round(teamMembers.reduce((acc, m) => acc + m.performance, 0) / teamMembers.length)}%`,
      subtitle: 'أداء الفريق',
      icon: <Star />,
      color: 'info' as const
    },
    {
      title: 'المشاريع النشطة',
      value: teamMembers.reduce((acc, m) => acc + m.projects, 0),
      subtitle: 'مشروع قيد التنفيذ',
      icon: <Work />,
      color: 'warning' as const
    }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <LoadingOverlay loading={loading}>
        <AnimatedBox animation="fadeIn" duration={0.6}>
          {/* العنوان والأزرار */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 4 
          }}>
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontSize: theme.typography.fontSize['3xl'],
                  fontWeight: theme.typography.fontWeight.bold,
                  color: theme.colors.text.primary,
                  mb: 1,
                  background: theme.effects.gradients.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                👥 إدارة الفريق
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: theme.colors.text.secondary,
                  fontWeight: theme.typography.fontWeight.medium
                }}
              >
                إدارة أعضاء الفريق ومتابعة أدائهم
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <OutlineButton startIcon={<Email />}>
                دعوة عضو
              </OutlineButton>
              <PrimaryButton startIcon={<Add />}>
                إضافة عضو جديد
              </PrimaryButton>
            </Box>
          </Box>

          {/* إحصائيات الفريق */}
          <AnimatedBox animation="slideInRight" delay={0.2} duration={0.5}>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {teamStats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={stat.title}>
                  <EnhancedCard
                    title={stat.title}
                    value={stat.value}
                    subtitle={stat.subtitle}
                    icon={stat.icon}
                    color={stat.color}
                    variant="elevated"
                  />
                </Grid>
              ))}
            </Grid>
          </AnimatedBox>

          {/* جدول الأعضاء */}
          <AnimatedBox animation="fadeIn" delay={0.4} duration={0.6}>
            <EnhancedTable
              title="أعضاء الفريق"
              columns={columns}
              data={teamMembers}
              searchable={true}
              filterable={true}
              exportable={true}
              actions={(row: TeamMember) => (
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuClick(e, row)}
                >
                  <MoreVert />
                </IconButton>
              )}
              onRowClick={(row) => {
                console.log('Selected member:', row);
              }}
            />
          </AnimatedBox>
        </AnimatedBox>
      </LoadingOverlay>

      {/* قائمة الإجراءات */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Edit sx={{ mr: 1 }} />
          تعديل
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Phone sx={{ mr: 1 }} />
          اتصال
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Email sx={{ mr: 1 }} />
          إرسال بريد
        </MenuItem>
        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* نافذة تأكيد الحذف */}
      <ConfirmationModal
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="تأكيد الحذف"
        message={`هل أنت متأكد من حذف ${selectedMember?.name}؟ لا يمكن التراجع عن هذا الإجراء.`}
        confirmText="حذف"
        cancelText="إلغاء"
        variant="error"
      />
    </Container>
  );
};

export default EnhancedTeamManagement;
