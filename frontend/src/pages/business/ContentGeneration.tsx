import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  AutoAwesome,
  ContentCopy,
  Download,
  Edit,
  Save,
  Delete,
  Refresh,
  Campaign,
  Email,
  Facebook,
  Twitter,
  Instagram,
  Language,
  Psychology,
  TrendingUp
} from '@mui/icons-material';
import { ResponsiveHeader, ResponsiveGrid, StatCard } from '../../components/business';
import '../../styles/business-theme.css';

interface ContentRequest {
  type: string;
  audience: string;
  tone: string;
  keywords: string[];
  objective: string;
  length: string;
  language: string;
}

interface GeneratedContent {
  id: string;
  type: string;
  title: string;
  content: string;
  audience: string;
  tone: string;
  keywords: string[];
  createdAt: string;
  performance?: {
    engagement: number;
    clicks: number;
    conversions: number;
  };
}

const ContentGeneration: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [contentRequest, setContentRequest] = useState<ContentRequest>({
    type: 'social-post',
    audience: 'general',
    tone: 'professional',
    keywords: [],
    objective: 'engagement',
    length: 'medium',
    language: 'ar'
  });
  const [keywordInput, setKeywordInput] = useState('');
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [selectedContent, setSelectedContent] = useState<GeneratedContent | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editedContent, setEditedContent] = useState('');

  // Mock saved content library
  const savedContent: GeneratedContent[] = [
    {
      id: '1',
      type: 'social-post',
      title: 'منشور ترويجي للمنتج الجديد',
      content: '🌟 اكتشف منتجنا الجديد الذي سيغير حياتك! \n\n✨ مميزات رائعة\n🚀 أداء متفوق\n💎 جودة عالية\n\n#منتج_جديد #ابتكار #جودة',
      audience: 'general',
      tone: 'enthusiastic',
      keywords: ['منتج جديد', 'ابتكار', 'جودة'],
      createdAt: '2024-01-20',
      performance: { engagement: 85, clicks: 120, conversions: 15 }
    },
    {
      id: '2',
      type: 'email-subject',
      title: 'عنوان إيميل تسويقي',
      content: 'عرض حصري لك فقط - خصم 50% لفترة محدودة! 🎉',
      audience: 'customers',
      tone: 'urgent',
      keywords: ['عرض حصري', 'خصم', 'فترة محدودة'],
      createdAt: '2024-01-19',
      performance: { engagement: 92, clicks: 340, conversions: 45 }
    },
    {
      id: '3',
      type: 'ad-copy',
      title: 'نص إعلاني للحملة الصيفية',
      content: 'استمتع بصيف رائع مع منتجاتنا المميزة. خصومات تصل إلى 40% على جميع المنتجات. اطلب الآن واحصل على توصيل مجاني!',
      audience: 'young-adults',
      tone: 'friendly',
      keywords: ['صيف', 'خصومات', 'توصيل مجاني'],
      createdAt: '2024-01-18',
      performance: { engagement: 78, clicks: 280, conversions: 32 }
    }
  ];

  const contentTypes = [
    { value: 'social-post', label: 'منشور وسائل التواصل', icon: <Facebook /> },
    { value: 'email-subject', label: 'عنوان إيميل', icon: <Email /> },
    { value: 'ad-copy', label: 'نص إعلاني', icon: <Campaign /> },
    { value: 'blog-title', label: 'عنوان مقال', icon: <Edit /> },
    { value: 'product-description', label: 'وصف منتج', icon: <Language /> }
  ];

  const audiences = [
    { value: 'general', label: 'الجمهور العام' },
    { value: 'customers', label: 'العملاء الحاليون' },
    { value: 'prospects', label: 'العملاء المحتملون' },
    { value: 'young-adults', label: 'الشباب (18-35)' },
    { value: 'professionals', label: 'المهنيون' },
    { value: 'families', label: 'العائلات' }
  ];

  const tones = [
    { value: 'professional', label: 'مهني' },
    { value: 'friendly', label: 'ودود' },
    { value: 'enthusiastic', label: 'متحمس' },
    { value: 'urgent', label: 'عاجل' },
    { value: 'informative', label: 'إعلامي' },
    { value: 'humorous', label: 'فكاهي' }
  ];

  const objectives = [
    { value: 'engagement', label: 'زيادة التفاعل' },
    { value: 'sales', label: 'زيادة المبيعات' },
    { value: 'awareness', label: 'زيادة الوعي' },
    { value: 'traffic', label: 'زيادة الزيارات' },
    { value: 'leads', label: 'جذب عملاء محتملين' }
  ];

  const handleAddKeyword = () => {
    if (keywordInput.trim() && !contentRequest.keywords.includes(keywordInput.trim())) {
      setContentRequest(prev => ({
        ...prev,
        keywords: [...prev.keywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const handleRemoveKeyword = (keyword: string) => {
    setContentRequest(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }));
  };

  const handleGenerateContent = async () => {
    setLoading(true);
    
    // Simulate AI content generation
    setTimeout(() => {
      const mockContent: GeneratedContent = {
        id: Date.now().toString(),
        type: contentRequest.type,
        title: `محتوى ${contentTypes.find(t => t.value === contentRequest.type)?.label}`,
        content: generateMockContent(contentRequest),
        audience: contentRequest.audience,
        tone: contentRequest.tone,
        keywords: contentRequest.keywords,
        createdAt: new Date().toISOString().split('T')[0]
      };
      
      setGeneratedContent(prev => [mockContent, ...prev]);
      setLoading(false);
    }, 2000);
  };

  const generateMockContent = (request: ContentRequest): string => {
    const templates = {
      'social-post': `🌟 ${request.keywords.join(' و')} - تجربة استثنائية تنتظرك!\n\n✨ اكتشف الجديد\n🚀 جودة مضمونة\n💎 أسعار مناسبة\n\n#${request.keywords.join(' #')}`,
      'email-subject': `${request.keywords[0] || 'عرض خاص'} - لا تفوت الفرصة! 🎉`,
      'ad-copy': `استمتع بأفضل ${request.keywords.join(' و')} مع منتجاتنا المميزة. عروض حصرية وجودة عالية. اطلب الآن!`,
      'blog-title': `دليلك الشامل لـ ${request.keywords.join(' و')} - كل ما تحتاج معرفته`,
      'product-description': `منتج عالي الجودة يتميز بـ ${request.keywords.join(' و')}. مصمم خصيصاً لتلبية احتياجاتك بأفضل الأسعار.`
    };
    
    return templates[request.type as keyof typeof templates] || 'محتوى تم إنشاؤه بواسطة الذكاء الاصطناعي';
  };

  const handleEditContent = (content: GeneratedContent) => {
    setSelectedContent(content);
    setEditedContent(content.content);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (selectedContent) {
      setGeneratedContent(prev => 
        prev.map(item => 
          item.id === selectedContent.id 
            ? { ...item, content: editedContent }
            : item
        )
      );
      setEditDialogOpen(false);
    }
  };

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    // Show success message
  };

  const totalGenerated = generatedContent.length + savedContent.length;
  const avgEngagement = savedContent.reduce((sum, item) => sum + (item.performance?.engagement || 0), 0) / savedContent.length;
  const totalClicks = savedContent.reduce((sum, item) => sum + (item.performance?.clicks || 0), 0);

  return (
    <Box className="business-dashboard">
      {/* Header */}
      <ResponsiveHeader
        title="توليد المحتوى المخصص"
        subtitle="إنشاء محتوى تسويقي مخصص باستخدام الذكاء الاصطناعي"
        actions={[
          <Button
            key="refresh"
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => {
              setLoading(true);
              setTimeout(() => setLoading(false), 2000);
            }}
            disabled={loading}
          >
            تحديث النماذج
          </Button>
        ]}
      />

      {/* Summary Stats */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, md: 4 }} gap={3}>
        <StatCard
          title="المحتوى المُنشأ"
          value={totalGenerated.toString()}
          change="+12"
          trend="up"
          icon={<AutoAwesome />}
          color="primary"
          subtitle="هذا الشهر"
          loading={loading}
        />
        <StatCard
          title="متوسط التفاعل"
          value={`${avgEngagement.toFixed(1)}%`}
          change="+5.2%"
          trend="up"
          icon={<TrendingUp />}
          color="success"
          subtitle="للمحتوى المنشور"
          loading={loading}
        />
        <StatCard
          title="إجمالي النقرات"
          value={totalClicks.toLocaleString()}
          change="+18%"
          trend="up"
          icon={<Psychology />}
          color="info"
          subtitle="من المحتوى المُنشأ"
          loading={loading}
        />
        <StatCard
          title="معدل التحويل"
          value="3.2%"
          change="+0.8%"
          trend="up"
          icon={<Campaign />}
          color="warning"
          subtitle="من النقرات"
          loading={loading}
        />
      </ResponsiveGrid>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="إنشاء محتوى جديد" />
        <Tab label="مكتبة المحتوى" />
      </Tabs>

      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Content Generation Form */}
          <Grid item xs={12} lg={6}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  إعدادات المحتوى
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>نوع المحتوى</InputLabel>
                      <Select
                        value={contentRequest.type}
                        onChange={(e) => setContentRequest(prev => ({ ...prev, type: e.target.value }))}
                      >
                        {contentTypes.map(type => (
                          <MenuItem key={type.value} value={type.value}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {type.icon}
                              {type.label}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>الجمهور المستهدف</InputLabel>
                      <Select
                        value={contentRequest.audience}
                        onChange={(e) => setContentRequest(prev => ({ ...prev, audience: e.target.value }))}
                      >
                        {audiences.map(audience => (
                          <MenuItem key={audience.value} value={audience.value}>
                            {audience.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>نبرة المحتوى</InputLabel>
                      <Select
                        value={contentRequest.tone}
                        onChange={(e) => setContentRequest(prev => ({ ...prev, tone: e.target.value }))}
                      >
                        {tones.map(tone => (
                          <MenuItem key={tone.value} value={tone.value}>
                            {tone.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>الهدف من المحتوى</InputLabel>
                      <Select
                        value={contentRequest.objective}
                        onChange={(e) => setContentRequest(prev => ({ ...prev, objective: e.target.value }))}
                      >
                        {objectives.map(objective => (
                          <MenuItem key={objective.value} value={objective.value}>
                            {objective.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                      <TextField
                        fullWidth
                        label="الكلمات المفتاحية"
                        value={keywordInput}
                        onChange={(e) => setKeywordInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                        placeholder="أدخل كلمة مفتاحية واضغط Enter"
                      />
                      <Button variant="outlined" onClick={handleAddKeyword}>
                        إضافة
                      </Button>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {contentRequest.keywords.map((keyword, index) => (
                        <Chip
                          key={index}
                          label={keyword}
                          onDelete={() => handleRemoveKeyword(keyword)}
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      size="large"
                      startIcon={<AutoAwesome />}
                      onClick={handleGenerateContent}
                      disabled={loading || contentRequest.keywords.length === 0}
                      sx={{ mt: 2 }}
                    >
                      {loading ? 'جاري الإنشاء...' : 'إنشاء المحتوى'}
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Generated Content Display */}
          <Grid item xs={12} lg={6}>
            <Card className="business-card">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  المحتوى المُنشأ
                </Typography>
                
                {generatedContent.length === 0 ? (
                  <Alert severity="info">
                    لم يتم إنشاء أي محتوى بعد. استخدم النموذج لإنشاء محتوى جديد.
                  </Alert>
                ) : (
                  <Box sx={{ maxHeight: 500, overflow: 'auto' }}>
                    {generatedContent.map((content, index) => (
                      <Paper key={content.id} sx={{ p: 2, mb: 2, border: '1px solid', borderColor: 'divider' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="subtitle2" color="primary">
                            {content.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="نسخ">
                              <IconButton size="small" onClick={() => handleCopyContent(content.content)}>
                                <ContentCopy />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="تعديل">
                              <IconButton size="small" onClick={() => handleEditContent(content)}>
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="حفظ">
                              <IconButton size="small" color="success">
                                <Save />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                        
                        <Typography variant="body2" sx={{ mb: 2, whiteSpace: 'pre-line' }}>
                          {content.content}
                        </Typography>
                        
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                          {content.keywords.map((keyword, idx) => (
                            <Chip key={idx} label={keyword} size="small" variant="outlined" />
                          ))}
                        </Box>
                        
                        <Typography variant="caption" color="text.secondary">
                          تم الإنشاء في: {content.createdAt}
                        </Typography>
                      </Paper>
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeTab === 1 && (
        <Card className="business-card">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              مكتبة المحتوى المحفوظ
            </Typography>
            
            <Grid container spacing={2}>
              {savedContent.map((content) => (
                <Grid item xs={12} md={6} lg={4} key={content.id}>
                  <Paper sx={{ p: 2, border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="subtitle2" color="primary">
                        {content.title}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="نسخ">
                          <IconButton size="small" onClick={() => handleCopyContent(content.content)}>
                            <ContentCopy />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="تعديل">
                          <IconButton size="small" onClick={() => handleEditContent(content)}>
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="حذف">
                          <IconButton size="small" color="error">
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    
                    <Typography variant="body2" sx={{ mb: 2, whiteSpace: 'pre-line' }}>
                      {content.content.length > 100 ? `${content.content.substring(0, 100)}...` : content.content}
                    </Typography>
                    
                    {content.performance && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          الأداء: {content.performance.engagement}% تفاعل، {content.performance.clicks} نقرة، {content.performance.conversions} تحويل
                        </Typography>
                      </Box>
                    )}
                    
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                      {content.keywords.slice(0, 3).map((keyword, idx) => (
                        <Chip key={idx} label={keyword} size="small" variant="outlined" />
                      ))}
                    </Box>
                    
                    <Typography variant="caption" color="text.secondary">
                      {content.createdAt}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Edit Content Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تعديل المحتوى</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={6}
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleSaveEdit}>حفظ التغييرات</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContentGeneration;
