import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

const TestCampaign: React.FC = () => {
  console.log('TestCampaign component is rendering');
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        اختبار صفحة تحسين الحملات
      </Typography>
      
      <Card sx={{ mt: 2 }}>
        <CardContent>
          <Typography variant="h6">
            هذه صفحة اختبار للتأكد من أن المكونات تعمل بشكل صحيح
          </Typography>
          <Typography variant="body1" sx={{ mt: 2 }}>
            إذا كنت ترى هذا النص، فهذا يعني أن المكون يُحمّل بشكل صحيح.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TestCampaign;
