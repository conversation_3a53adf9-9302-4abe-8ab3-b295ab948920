import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp,
  People,
  AttachMoney,
  ShoppingCart,
  Analytics,
  Campaign
} from '@mui/icons-material';

const SimpleKPIs: React.FC = () => {
  const kpiData = [
    {
      title: 'إجمالي المبيعات',
      value: '2,450,000',
      unit: 'ريال',
      change: '+12.5%',
      changeType: 'positive',
      icon: <AttachMoney />,
      color: '#10b981'
    },
    {
      title: 'عدد العملاء',
      value: '1,234',
      unit: 'عميل',
      change: '+8.2%',
      changeType: 'positive',
      icon: <People />,
      color: '#3b82f6'
    },
    {
      title: 'معدل التحويل',
      value: '3.8',
      unit: '%',
      change: '+0.5%',
      changeType: 'positive',
      icon: <TrendingUp />,
      color: '#8b5cf6'
    },
    {
      title: 'متوسط قيمة الطلب',
      value: '485',
      unit: 'ريال',
      change: '-2.1%',
      changeType: 'negative',
      icon: <ShoppingCart />,
      color: '#f59e0b'
    },
    {
      title: 'عائد الاستثمار الإعلاني',
      value: '4.2',
      unit: 'x',
      change: '+15.3%',
      changeType: 'positive',
      icon: <Campaign />,
      color: '#ef4444'
    },
    {
      title: 'نقاط المشاركة',
      value: '89.5',
      unit: '%',
      change: '+3.7%',
      changeType: 'positive',
      icon: <Analytics />,
      color: '#06b6d4'
    }
  ];

  const progressData = [
    { label: 'هدف المبيعات الشهرية', current: 75, target: 100, color: '#10b981' },
    { label: 'اكتساب عملاء جدد', current: 60, target: 100, color: '#3b82f6' },
    { label: 'رضا العملاء', current: 92, target: 100, color: '#8b5cf6' },
    { label: 'كفاءة الحملات', current: 85, target: 100, color: '#f59e0b' }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" gutterBottom fontWeight="bold" color="primary">
          مؤشرات الأداء الرئيسية
        </Typography>
        <Typography variant="h6" color="text.secondary">
          نظرة شاملة على أداء أعمالك
        </Typography>
      </Box>

      {/* KPI Cards */}
      <Grid container spacing={3} mb={4}>
        {kpiData.map((kpi, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card 
              elevation={2}
              sx={{ 
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box 
                    sx={{ 
                      p: 1.5, 
                      borderRadius: 2, 
                      backgroundColor: `${kpi.color}20`,
                      color: kpi.color
                    }}
                  >
                    {kpi.icon}
                  </Box>
                  <Chip 
                    label={kpi.change}
                    size="small"
                    sx={{
                      backgroundColor: kpi.changeType === 'positive' ? '#dcfce7' : '#fee2e2',
                      color: kpi.changeType === 'positive' ? '#16a34a' : '#dc2626',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
                
                <Typography variant="h4" fontWeight="bold" color="text.primary" gutterBottom>
                  {kpi.value}
                  <Typography component="span" variant="h6" color="text.secondary" sx={{ ml: 1 }}>
                    {kpi.unit}
                  </Typography>
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  {kpi.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Progress Section */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              تقدم الأهداف الشهرية
            </Typography>
            <Box mt={3}>
              {progressData.map((item, index) => (
                <Box key={index} mb={3}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body1" fontWeight="medium">
                      {item.label}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {item.current}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={item.current}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#f3f4f6',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: item.color,
                        borderRadius: 4
                      }
                    }}
                  />
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              ملخص سريع
            </Typography>
            <Box mt={3}>
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  الأداء العام هذا الشهر
                </Typography>
                <Typography variant="h5" fontWeight="bold" color="success.main">
                  ممتاز
                </Typography>
              </Box>
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  نمو مقارنة بالشهر الماضي
                </Typography>
                <Typography variant="h5" fontWeight="bold" color="primary">
                  +18.5%
                </Typography>
              </Box>
              
              <Box mb={3}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  التوقعات للشهر القادم
                </Typography>
                <Typography variant="h5" fontWeight="bold" color="info.main">
                  إيجابية
                </Typography>
              </Box>

              <Box 
                sx={{ 
                  p: 2, 
                  backgroundColor: '#f0f9ff', 
                  borderRadius: 2,
                  border: '1px solid #bfdbfe'
                }}
              >
                <Typography variant="body2" color="primary" fontWeight="medium">
                  💡 نصيحة: ركز على تحسين معدل التحويل لزيادة المبيعات بنسبة 25%
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default SimpleKPIs;
