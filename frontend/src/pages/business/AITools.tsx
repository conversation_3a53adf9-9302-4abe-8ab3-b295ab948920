import React from 'react';
import { Container, Box, Button, Typography } from '@mui/material';
import { AutoAwesome, Psychology, Business, People, Recommend } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import AIToolsSimple from '../../components/AIToolsSimple';

const AIToolsPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <AutoAwesome sx={{ fontSize: 40, color: '#667eea' }} />
          أدوات الذكاء الاصطناعي المتقدمة
        </Typography>
        
        <Typography variant="h6" sx={{ mb: 4, color: 'text.secondary' }}>
          استكشف مجموعة شاملة من أدوات الذكاء الاصطناعي لتحسين استراتيجياتك التسويقية
        </Typography>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 4 }}>
          <Button
            variant="outlined"
            startIcon={<AutoAwesome />}
            onClick={() => navigate('/business/ai-tools')}
            sx={{ 
              borderColor: '#667eea', 
              color: '#667eea',
              '&:hover': { borderColor: '#764ba2', backgroundColor: 'rgba(102, 126, 234, 0.1)' }
            }}
          >
            أدوات الذكاء الاصطناعي الأساسية
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<Psychology />}
            onClick={() => navigate('/business/sentiment-analysis')}
            sx={{ 
              borderColor: '#667eea', 
              color: '#667eea',
              '&:hover': { borderColor: '#764ba2', backgroundColor: 'rgba(102, 126, 234, 0.1)' }
            }}
          >
            تحليل المشاعر
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<Business />}
            onClick={() => navigate('/business/competitor-analysis')}
            sx={{ 
              borderColor: '#667eea', 
              color: '#667eea',
              '&:hover': { borderColor: '#764ba2', backgroundColor: 'rgba(102, 126, 234, 0.1)' }
            }}
          >
            تحليل المنافسين
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<People />}
            onClick={() => navigate('/business/customer-behavior')}
            sx={{ 
              borderColor: '#667eea', 
              color: '#667eea',
              '&:hover': { borderColor: '#764ba2', backgroundColor: 'rgba(102, 126, 234, 0.1)' }
            }}
          >
            سلوك العملاء
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<Recommend />}
            onClick={() => navigate('/business/product-recommendation')}
            sx={{ 
              borderColor: '#667eea', 
              color: '#667eea',
              '&:hover': { borderColor: '#764ba2', backgroundColor: 'rgba(102, 126, 234, 0.1)' }
            }}
          >
            توصية المنتجات
          </Button>
        </Box>

        <AIToolsSimple />
      </Box>
    </Container>
  );
};

export default AIToolsPage;
