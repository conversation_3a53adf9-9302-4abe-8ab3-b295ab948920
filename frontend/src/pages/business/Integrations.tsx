import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  <PERSON>ert,
  Chip,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Facebook,
  Instagram,
  Twitter,
  LinkedIn,
  YouTube,
  Google,
  Settings,
  Sync,
  CheckCircle,
  Error,
  Warning,
  Add,
  Delete,
  Refresh,
  Analytics,
  Campaign,
  TrendingUp
} from '@mui/icons-material';
import IntegratedAnalytics from '../../components/IntegratedAnalytics';

interface Integration {
  id: string;
  name: string;
  platform: string;
  icon: React.ReactNode;
  connected: boolean;
  status: 'active' | 'error' | 'warning' | 'disconnected';
  lastSync: string;
  dataTypes: string[];
  accountInfo?: {
    username?: string;
    email?: string;
    accountId?: string;
  };
}

const Integrations: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: 'facebook',
      name: 'Facebook Business',
      platform: 'facebook',
      icon: <Facebook sx={{ color: '#1877F2' }} />,
      connected: false,
      status: 'disconnected',
      lastSync: '',
      dataTypes: ['إعلانات', 'صفحات', 'إحصائيات الجمهور', 'المنشورات']
    },
    {
      id: 'instagram',
      name: 'Instagram Business',
      platform: 'instagram',
      icon: <Instagram sx={{ color: '#E4405F' }} />,
      connected: true,
      status: 'active',
      lastSync: '2024-06-24 10:30',
      dataTypes: ['المنشورات', 'القصص', 'الإحصائيات', 'التفاعل'],
      accountInfo: {
        username: '@mybusiness',
        accountId: 'inst_123456'
      }
    },
    {
      id: 'google-ads',
      name: 'Google Ads',
      platform: 'google',
      icon: <Google sx={{ color: '#4285F4' }} />,
      connected: true,
      status: 'warning',
      lastSync: '2024-06-23 15:45',
      dataTypes: ['الحملات الإعلانية', 'الكلمات المفتاحية', 'التحويلات', 'التكاليف'],
      accountInfo: {
        email: '<EMAIL>',
        accountId: 'gads_789012'
      }
    },
    {
      id: 'linkedin',
      name: 'LinkedIn Company',
      platform: 'linkedin',
      icon: <LinkedIn sx={{ color: '#0A66C2' }} />,
      connected: false,
      status: 'disconnected',
      lastSync: '',
      dataTypes: ['صفحة الشركة', 'المنشورات', 'الإحصائيات', 'الموظفين']
    },
    {
      id: 'youtube',
      name: 'YouTube Channel',
      platform: 'youtube',
      icon: <YouTube sx={{ color: '#FF0000' }} />,
      connected: false,
      status: 'disconnected',
      lastSync: '',
      dataTypes: ['الفيديوهات', 'المشاهدات', 'التعليقات', 'المشتركين']
    },
    {
      id: 'twitter',
      name: 'Twitter/X Business',
      platform: 'twitter',
      icon: <Twitter sx={{ color: '#1DA1F2' }} />,
      connected: false,
      status: 'disconnected',
      lastSync: '',
      dataTypes: ['التغريدات', 'التفاعل', 'المتابعين', 'الإعلانات']
    }
  ]);

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [loading, setLoading] = useState(false);
  const [credentials, setCredentials] = useState({
    apiKey: '',
    secretKey: '',
    accessToken: '',
    refreshToken: ''
  });
  const [showAnalytics, setShowAnalytics] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return null;
    }
  };

  const handleConnect = (integration: Integration) => {
    setSelectedIntegration(integration);
    setOpenDialog(true);
  };

  const handleDisconnect = async (integrationId: string) => {
    setLoading(true);
    try {
      // API call to disconnect integration
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      setIntegrations(prev => prev.map(int => 
        int.id === integrationId 
          ? { ...int, connected: false, status: 'disconnected', lastSync: '', accountInfo: undefined }
          : int
      ));
    } catch (error) {
      console.error('Failed to disconnect:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (integrationId: string) => {
    setLoading(true);
    try {
      // API call to sync data
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      const now = new Date().toLocaleString('ar-SA');
      setIntegrations(prev => prev.map(int => 
        int.id === integrationId 
          ? { ...int, lastSync: now, status: 'active' }
          : int
      ));
    } catch (error) {
      console.error('Failed to sync:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConnection = async () => {
    if (!selectedIntegration) return;
    
    setLoading(true);
    try {
      // API call to save connection
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      
      const now = new Date().toLocaleString('ar-SA');
      setIntegrations(prev => prev.map(int => 
        int.id === selectedIntegration.id 
          ? { 
              ...int, 
              connected: true, 
              status: 'active', 
              lastSync: now,
              accountInfo: {
                username: credentials.apiKey ? `@${credentials.apiKey}` : undefined,
                accountId: `${selectedIntegration.platform}_${Date.now()}`
              }
            }
          : int
      ));
      
      setOpenDialog(false);
      setCredentials({ apiKey: '', secretKey: '', accessToken: '', refreshToken: '' });
    } catch (error) {
      console.error('Failed to connect:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2,
        mb: 3 
      }}>
        <Settings sx={{ fontSize: 40, color: 'primary.main' }} />
        إدارة التكاملات والربط
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              🔗 ربط حساباتك لسحب البيانات وتحليلها تلقائياً
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              قم بربط حساباتك على منصات التواصل الاجتماعي والتسويق الرقمي لتحصل على تحليلات شاملة ودقيقة
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<Analytics />}
            onClick={() => setShowAnalytics(true)}
            sx={{ ml: 2 }}
          >
            عرض التحليلات المدمجة
          </Button>
        </Box>
      </Alert>

      <Grid container spacing={3}>
        {integrations.map((integration) => (
          <Grid item xs={12} md={6} lg={4} key={integration.id}>
            <Card sx={{ 
              height: '100%',
              border: integration.connected ? '2px solid' : '1px solid',
              borderColor: integration.connected ? 'success.main' : 'divider',
              position: 'relative'
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {integration.icon}
                  <Typography variant="h6" sx={{ ml: 1, flex: 1 }}>
                    {integration.name}
                  </Typography>
                  {getStatusIcon(integration.status)}
                </Box>

                <Chip 
                  label={integration.connected ? 'متصل' : 'غير متصل'}
                  color={getStatusColor(integration.status)}
                  size="small"
                  sx={{ mb: 2 }}
                />

                {integration.connected && integration.accountInfo && (
                  <Box sx={{ mb: 2 }}>
                    {integration.accountInfo.username && (
                      <Typography variant="body2" color="text.secondary">
                        الحساب: {integration.accountInfo.username}
                      </Typography>
                    )}
                    {integration.accountInfo.email && (
                      <Typography variant="body2" color="text.secondary">
                        البريد: {integration.accountInfo.email}
                      </Typography>
                    )}
                    {integration.lastSync && (
                      <Typography variant="body2" color="text.secondary">
                        آخر مزامنة: {integration.lastSync}
                      </Typography>
                    )}
                  </Box>
                )}

                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  أنواع البيانات:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                  {integration.dataTypes.map((type, index) => (
                    <Chip 
                      key={index}
                      label={type}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  {integration.connected ? (
                    <>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<Sync />}
                        onClick={() => handleSync(integration.id)}
                        disabled={loading}
                      >
                        مزامنة
                      </Button>
                      <Button
                        variant="outlined"
                        color="error"
                        size="small"
                        startIcon={<Delete />}
                        onClick={() => handleDisconnect(integration.id)}
                        disabled={loading}
                      >
                        قطع الاتصال
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Add />}
                      onClick={() => handleConnect(integration)}
                      fullWidth
                    >
                      ربط الحساب
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Connection Dialog */}
      <Dialog 
        open={openDialog} 
        onClose={() => setOpenDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {selectedIntegration?.icon}
            ربط {selectedIntegration?.name}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            تأكد من أن لديك الصلاحيات اللازمة للوصول إلى بيانات الحساب
          </Alert>
          
          <TextField
            fullWidth
            label="API Key / App ID"
            value={credentials.apiKey}
            onChange={(e) => setCredentials(prev => ({ ...prev, apiKey: e.target.value }))}
            margin="normal"
            placeholder="أدخل API Key أو App ID"
          />
          
          <TextField
            fullWidth
            label="Secret Key / App Secret"
            type="password"
            value={credentials.secretKey}
            onChange={(e) => setCredentials(prev => ({ ...prev, secretKey: e.target.value }))}
            margin="normal"
            placeholder="أدخل Secret Key أو App Secret"
          />
          
          <TextField
            fullWidth
            label="Access Token (اختياري)"
            value={credentials.accessToken}
            onChange={(e) => setCredentials(prev => ({ ...prev, accessToken: e.target.value }))}
            margin="normal"
            placeholder="أدخل Access Token إذا كان متوفراً"
          />

          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            💡 يمكنك الحصول على هذه المعلومات من لوحة تحكم المطور في {selectedIntegration?.name}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            إلغاء
          </Button>
          <Button 
            onClick={handleSaveConnection}
            variant="contained"
            disabled={loading || !credentials.apiKey || !credentials.secretKey}
            startIcon={loading ? <CircularProgress size={20} /> : <Add />}
          >
            {loading ? 'جاري الربط...' : 'ربط الحساب'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Integrated Analytics Component */}
      <IntegratedAnalytics
        open={showAnalytics}
        onClose={() => setShowAnalytics(false)}
      />
    </Box>
  );
};

export default Integrations;
