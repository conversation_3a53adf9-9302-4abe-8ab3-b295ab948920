import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const IntegrationsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const categories = [
    { id: 'all', name: 'جميع التكاملات', icon: 'fas fa-th-large' },
    { id: 'social', name: 'وسائل التواصل', icon: 'fab fa-facebook' },
    { id: 'analytics', name: 'التحليلات', icon: 'fas fa-chart-bar' },
    { id: 'email', name: 'التسويق الإلكتروني', icon: 'fas fa-envelope' },
    { id: 'crm', name: 'إدارة العملاء', icon: 'fas fa-users' },
    { id: 'ecommerce', name: 'التجارة الإلكترونية', icon: 'fas fa-shopping-cart' }
  ];

  const integrations = [
    {
      name: 'Facebook',
      category: 'social',
      description: 'تحليل صفحات Facebook والإعلانات والتفاعل',
      icon: 'fab fa-facebook',
      color: '#1877f2',
      popular: true
    },
    {
      name: 'Instagram',
      category: 'social',
      description: 'مراقبة المحتوى والقصص والتفاعل على Instagram',
      icon: 'fab fa-instagram',
      color: '#E4405F',
      popular: true
    },
    {
      name: 'Google Analytics',
      category: 'analytics',
      description: 'ربط بيانات موقعك الإلكتروني وتحليل الزوار',
      icon: 'fab fa-google',
      color: '#4285f4',
      popular: true
    },
    {
      name: 'Twitter',
      category: 'social',
      description: 'تتبع التغريدات والهاشتاغات والمنشورات',
      icon: 'fab fa-twitter',
      color: '#1da1f2',
      popular: false
    },
    {
      name: 'LinkedIn',
      category: 'social',
      description: 'تحليل الشبكة المهنية والمحتوى التجاري',
      icon: 'fab fa-linkedin',
      color: '#0077b5',
      popular: false
    },
    {
      name: 'Mailchimp',
      category: 'email',
      description: 'إدارة حملات البريد الإلكتروني والقوائم البريدية',
      icon: 'fab fa-mailchimp',
      color: '#ffe01b',
      popular: true
    },
    {
      name: 'HubSpot',
      category: 'crm',
      description: 'إدارة العملاء المحتملين ومسار المبيعات',
      icon: 'fas fa-hubspot',
      color: '#ff7a59',
      popular: true
    },
    {
      name: 'Shopify',
      category: 'ecommerce',
      description: 'تحليل متجرك الإلكتروني والمبيعات',
      icon: 'fab fa-shopify',
      color: '#96bf48',
      popular: false
    },
    {
      name: 'Google Ads',
      category: 'analytics',
      description: 'تتبع أداء إعلانات Google وتحسينها',
      icon: 'fab fa-google',
      color: '#4285f4',
      popular: true
    },
    {
      name: 'Salesforce',
      category: 'crm',
      description: 'ربط بيانات العملاء ومسار المبيعات',
      icon: 'fab fa-salesforce',
      color: '#00a1e0',
      popular: false
    },
    {
      name: 'YouTube',
      category: 'social',
      description: 'تحليل قناتك ومقاطع الفيديو والتفاعل',
      icon: 'fab fa-youtube',
      color: '#ff0000',
      popular: false
    },
    {
      name: 'WooCommerce',
      category: 'ecommerce',
      description: 'تحليل متجر WooCommerce والمنتجات',
      icon: 'fab fa-wordpress',
      color: '#96588a',
      popular: false
    }
  ];

  const filteredIntegrations = selectedCategory === 'all' 
    ? integrations 
    : integrations.filter(integration => integration.category === selectedCategory);

  return (
    <div className="integrations-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/features">الميزات</a></li>
              <li><a href="/pricing">الأسعار</a></li>
              <li><a href="/integrations">التكاملات</a></li>
              <li><a href="/about">من نحن</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">اربط جميع أدواتك في مكان واحد</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                أكثر من 100 تكامل مع المنصات والأدوات الرائدة لتوحيد بياناتك التسويقية 
                والحصول على رؤية شاملة لأداء حملاتك
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="features" style={{ background: 'var(--white)', padding: '3rem 0' }}>
        <div className="container">
          <div className="features-grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' }}>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--primary-color)', marginBottom: '0.5rem' }}>
                100+
              </div>
              <h3>تكامل متاح</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--accent-color)', marginBottom: '0.5rem' }}>
                5 دقائق
              </div>
              <h3>متوسط وقت الإعداد</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--primary-color)', marginBottom: '0.5rem' }}>
                99.9%
              </div>
              <h3>وقت التشغيل</h3>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--accent-color)', marginBottom: '0.5rem' }}>
                24/7
              </div>
              <h3>مزامنة البيانات</h3>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">تصفح التكاملات حسب الفئة</h2>
          </div>
          
          <div className="category-filters" data-aos="fade-up" data-aos-delay="200" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem'
          }}>
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: selectedCategory === category.id ? '2px solid var(--primary-color)' : '2px solid transparent',
                  background: selectedCategory === category.id ? 'var(--primary-color)' : 'white',
                  color: selectedCategory === category.id ? 'white' : 'var(--text-dark)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <i className={category.icon}></i>
                {category.name}
              </button>
            ))}
          </div>

          {/* Integrations Grid */}
          <div className="integrations-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem'
          }}>
            {filteredIntegrations.map((integration, index) => (
              <div 
                key={integration.name}
                className="integration-card" 
                data-aos="fade-up" 
                data-aos-delay={index * 50}
                style={{
                  background: 'white',
                  padding: '2rem',
                  borderRadius: '12px',
                  boxShadow: 'var(--shadow-soft)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  cursor: 'pointer',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-soft)';
                }}
              >
                {integration.popular && (
                  <div style={{
                    position: 'absolute',
                    top: '1rem',
                    left: '1rem',
                    background: 'var(--accent-color)',
                    color: 'white',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold'
                  }}>
                    شائع
                  </div>
                )}

                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                  <div style={{
                    width: '60px',
                    height: '60px',
                    borderRadius: '12px',
                    background: integration.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem'
                  }}>
                    <i className={integration.icon}></i>
                  </div>
                  <div>
                    <h3 style={{ margin: 0, color: 'var(--text-dark)' }}>{integration.name}</h3>
                  </div>
                </div>

                <p style={{ color: 'var(--text-light)', marginBottom: '1.5rem', lineHeight: '1.6' }}>
                  {integration.description}
                </p>

                <button style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: `2px solid ${integration.color}`,
                  background: 'transparent',
                  color: integration.color,
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = integration.color;
                  e.currentTarget.style.color = 'white';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = integration.color;
                }}
                >
                  ربط الآن
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">كيف تعمل التكاملات؟</h2>
            <p className="section-subtitle">
              عملية بسيطة وآمنة لربط جميع أدواتك في دقائق معدودة
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon" style={{ background: 'var(--primary-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>1</span>
              </div>
              <h3>اختر التكامل</h3>
              <p>
                اختر الأداة أو المنصة التي تريد ربطها من قائمة التكاملات المتاحة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon" style={{ background: 'var(--accent-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>2</span>
              </div>
              <h3>أذن الوصول</h3>
              <p>
                امنح MarketMind الإذن الآمن للوصول إلى بياناتك من خلال OAuth أو API.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon" style={{ background: 'var(--primary-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>3</span>
              </div>
              <h3>ابدأ التحليل</h3>
              <p>
                ابدأ في الحصول على رؤى قيمة من بياناتك المدمجة فوراً.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">فوائد التكاملات</h2>
            <p className="section-subtitle">
              لماذا تحتاج إلى دمج جميع أدواتك التسويقية؟
            </p>
          </div>
          
          <div className="problems-grid">
            <div className="problem-card" data-aos="fade-up" data-aos-delay="100">
              <div className="problem-icon">
                <i className="fas fa-eye"></i>
              </div>
              <h3>رؤية شاملة</h3>
              <p>
                احصل على نظرة شاملة لجميع أنشطتك التسويقية من لوحة معلومات واحدة.
              </p>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="200">
              <div className="problem-icon">
                <i className="fas fa-clock"></i>
              </div>
              <h3>توفير الوقت</h3>
              <p>
                لا حاجة للتنقل بين عدة منصات. جميع بياناتك في مكان واحد.
              </p>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="300">
              <div className="problem-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <h3>تحليل أعمق</h3>
              <p>
                ربط البيانات من مصادر متعددة يوفر رؤى أعمق وأكثر دقة.
              </p>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="400">
              <div className="problem-icon">
                <i className="fas fa-sync-alt"></i>
              </div>
              <h3>مزامنة تلقائية</h3>
              <p>
                البيانات تتحدث تلقائياً في الوقت الفعلي بدون تدخل يدوي.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>ابدأ في ربط أدواتك اليوم</h2>
            <p>انضم إلى آلاف الشركات التي تستخدم MarketMind لتوحيد بياناتها التسويقية</p>
            <div className="cta-buttons">
              <a href="/signup" className="btn btn-white">ابدأ مجاناً</a>
              <a href="/contact" className="btn btn-outline-white">تحدث مع خبير</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default IntegrationsPage;
