import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    subject: '',
    message: ''
  });

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
  };

  return (
    <div className="contact-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
              <li><a href="/support">الدعم</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">تواصل معنا</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                نحن هنا لمساعدتك. تواصل معنا وسنكون سعداء للإجابة على جميع استفساراتك 
                ومساعدتك في بدء رحلتك مع MarketMind.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="features-grid" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))' }}>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>البريد الإلكتروني</h3>
              <p><EMAIL></p>
              <p><EMAIL></p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>الهاتف</h3>
              <p>+966 11 123 4567</p>
              <p>متاح من 9 صباحاً إلى 6 مساءً</p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-map-marker-alt"></i>
              </div>
              <h3>العنوان</h3>
              <p>الرياض، المملكة العربية السعودية</p>
              <p>حي الملك فهد، طريق الملك فهد</p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <div className="feature-icon">
                <i className="fas fa-clock"></i>
              </div>
              <h3>ساعات العمل</h3>
              <p>الأحد - الخميس: 9:00 - 18:00</p>
              <p>الجمعة - السبت: مغلق</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">أرسل لنا رسالة</h2>
            <p className="section-subtitle">
              املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن
            </p>
          </div>
          
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <form onSubmit={handleSubmit} className="contact-form" data-aos="fade-up" data-aos-delay="200">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">الاسم الكامل *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل اسمك الكامل"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="email">البريد الإلكتروني *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل بريدك الإلكتروني"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="company">اسم الشركة</label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="أدخل اسم شركتك"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="phone">رقم الهاتف</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="أدخل رقم هاتفك"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="subject">الموضوع *</label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">اختر الموضوع</option>
                  <option value="general">استفسار عام</option>
                  <option value="sales">المبيعات</option>
                  <option value="support">الدعم الفني</option>
                  <option value="partnership">الشراكات</option>
                  <option value="media">الإعلام</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="message">الرسالة *</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  placeholder="اكتب رسالتك هنا..."
                ></textarea>
              </div>

              <div className="form-submit">
                <button type="submit" className="btn btn-primary btn-large">
                  إرسال الرسالة
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">الأسئلة الشائعة</h2>
            <p className="section-subtitle">
              إجابات على أكثر الأسئلة شيوعاً حول منصة MarketMind
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <h3>كيف يمكنني البدء؟</h3>
              <p>
                يمكنك البدء بإنشاء حساب مجاني والاستفادة من الفترة التجريبية 
                لاستكشاف جميع ميزات المنصة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <h3>هل تقدمون دعماً فنياً؟</h3>
              <p>
                نعم، نقدم دعماً فنياً على مدار الساعة عبر البريد الإلكتروني 
                والدردشة المباشرة والهاتف.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <h3>هل يمكن تخصيص المنصة؟</h3>
              <p>
                نعم، نقدم حلولاً مخصصة للشركات الكبيرة مع إمكانية التكامل 
                مع أنظمتها الحالية.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/#features">الميزات</a></li>
                <li><a href="/#pricing">الأسعار</a></li>
                <li><a href="/#features">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ContactPage;
