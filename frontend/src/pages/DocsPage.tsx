import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const DocsPage: React.FC = () => {
  const [selectedSection, setSelectedSection] = useState('getting-started');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const docSections = [
    {
      id: 'getting-started',
      title: 'البدء السريع',
      icon: 'fas fa-rocket',
      items: [
        'إنشاء حساب جديد',
        'إعداد المشروع الأول',
        'ربط مصادر البيانات',
        'إنشاء أول تقرير'
      ]
    },
    {
      id: 'analytics',
      title: 'التحليلات',
      icon: 'fas fa-chart-bar',
      items: [
        'لوحة المعلومات',
        'التقارير المخصصة',
        'المقاييس والمؤشرات',
        'التصدير والمشاركة'
      ]
    },
    {
      id: 'integrations',
      title: 'التكاملات',
      icon: 'fas fa-plug',
      items: [
        'منصات التواصل الاجتماعي',
        'أدوات التسويق',
        'أنظمة CRM',
        'قواعد البيانات'
      ]
    },
    {
      id: 'api',
      title: 'API',
      icon: 'fas fa-code',
      items: [
        'مقدمة API',
        'المصادقة',
        'نقاط النهاية',
        'أمثلة الكود'
      ]
    },
    {
      id: 'advanced',
      title: 'الميزات المتقدمة',
      icon: 'fas fa-cogs',
      items: [
        'الذكاء الاصطناعي',
        'التعلم الآلي',
        'التنبؤات',
        'الأتمتة'
      ]
    }
  ];

  const getContentForSection = (sectionId: string) => {
    switch (sectionId) {
      case 'getting-started':
        return {
          title: 'البدء السريع مع MarketMind',
          content: `
            <h3>مرحباً بك في MarketMind!</h3>
            <p>هذا الدليل سيساعدك على البدء مع منصة MarketMind في خطوات بسيطة.</p>
            
            <h4>1. إنشاء حساب جديد</h4>
            <p>ابدأ بإنشاء حساب جديد من خلال النقر على زر "ابدأ مجاناً" في الصفحة الرئيسية.</p>
            <ul>
              <li>أدخل بياناتك الأساسية</li>
              <li>اختر نوع الاشتراك المناسب</li>
              <li>تأكيد البريد الإلكتروني</li>
            </ul>

            <h4>2. إعداد المشروع الأول</h4>
            <p>بعد تسجيل الدخول، ستحتاج إلى إنشاء مشروعك الأول:</p>
            <ul>
              <li>اختر اسماً لمشروعك</li>
              <li>حدد نوع الصناعة</li>
              <li>اختر الأهداف التسويقية</li>
            </ul>

            <h4>3. ربط مصادر البيانات</h4>
            <p>اربط حساباتك على منصات التواصل الاجتماعي وأدوات التسويق:</p>
            <ul>
              <li>Facebook & Instagram</li>
              <li>Google Analytics</li>
              <li>Twitter</li>
              <li>LinkedIn</li>
            </ul>
          `
        };
      case 'analytics':
        return {
          title: 'التحليلات والتقارير',
          content: `
            <h3>فهم التحليلات في MarketMind</h3>
            <p>تعلم كيفية استخدام أدوات التحليل المتقدمة لفهم أداء حملاتك التسويقية.</p>
            
            <h4>لوحة المعلومات الرئيسية</h4>
            <p>تعرض لوحة المعلومات نظرة شاملة على أداء جميع حملاتك:</p>
            <ul>
              <li>إجمالي الزيارات والتحويلات</li>
              <li>معدل التفاعل عبر المنصات</li>
              <li>تحليل الجمهور المستهدف</li>
              <li>عائد الاستثمار (ROI)</li>
            </ul>

            <h4>التقارير المخصصة</h4>
            <p>أنشئ تقارير مخصصة حسب احتياجاتك:</p>
            <ul>
              <li>اختر المقاييس المهمة</li>
              <li>حدد الفترة الزمنية</li>
              <li>قارن بين الحملات المختلفة</li>
              <li>جدولة التقارير التلقائية</li>
            </ul>
          `
        };
      case 'integrations':
        return {
          title: 'التكاملات مع الأدوات الأخرى',
          content: `
            <h3>ربط MarketMind مع أدواتك المفضلة</h3>
            <p>يدعم MarketMind التكامل مع أكثر من 50 أداة ومنصة مختلفة.</p>
            
            <h4>منصات التواصل الاجتماعي</h4>
            <ul>
              <li><strong>Facebook:</strong> تحليل الصفحات والإعلانات</li>
              <li><strong>Instagram:</strong> مراقبة المحتوى والتفاعل</li>
              <li><strong>Twitter:</strong> تتبع المنشورات والهاشتاغات</li>
              <li><strong>LinkedIn:</strong> تحليل الشبكة المهنية</li>
            </ul>

            <h4>أدوات التسويق</h4>
            <ul>
              <li><strong>Google Analytics:</strong> بيانات الموقع الإلكتروني</li>
              <li><strong>Google Ads:</strong> أداء الإعلانات</li>
              <li><strong>Mailchimp:</strong> حملات البريد الإلكتروني</li>
              <li><strong>HubSpot:</strong> إدارة العملاء المحتملين</li>
            </ul>
          `
        };
      case 'api':
        return {
          title: 'دليل API للمطورين',
          content: `
            <h3>استخدام MarketMind API</h3>
            <p>يوفر MarketMind API قوي ومرن للمطورين لدمج البيانات والوظائف في تطبيقاتهم.</p>
            
            <h4>المصادقة</h4>
            <p>جميع طلبات API تتطلب مفتاح API صالح:</p>
            <pre><code>
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
            </code></pre>

            <h4>نقاط النهاية الرئيسية</h4>
            <ul>
              <li><code>GET /api/v1/analytics</code> - الحصول على بيانات التحليلات</li>
              <li><code>POST /api/v1/campaigns</code> - إنشاء حملة جديدة</li>
              <li><code>GET /api/v1/reports</code> - استرداد التقارير</li>
              <li><code>PUT /api/v1/settings</code> - تحديث الإعدادات</li>
            </ul>

            <h4>مثال على الاستخدام</h4>
            <pre><code>
curl -X GET "https://api.marketmind.ai/v1/analytics" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"
            </code></pre>
          `
        };
      case 'advanced':
        return {
          title: 'الميزات المتقدمة والذكاء الاصطناعي',
          content: `
            <h3>استخدام الذكاء الاصطناعي في التسويق</h3>
            <p>اكتشف كيف يمكن للذكاء الاصطناعي في MarketMind تحسين استراتيجياتك التسويقية.</p>
            
            <h4>التنبؤ بسلوك العملاء</h4>
            <p>استخدم نماذج التعلم الآلي للتنبؤ بسلوك العملاء:</p>
            <ul>
              <li>احتمالية الشراء</li>
              <li>قيمة العميل المتوقعة</li>
              <li>أفضل وقت للتواصل</li>
              <li>المنتجات المفضلة</li>
            </ul>

            <h4>تحسين الحملات التلقائي</h4>
            <p>دع الذكاء الاصطناعي يحسن حملاتك تلقائياً:</p>
            <ul>
              <li>تحسين الميزانية</li>
              <li>اختيار الجمهور المناسب</li>
              <li>تحديد أفضل أوقات النشر</li>
              <li>تخصيص المحتوى</li>
            </ul>

            <h4>التحليل التنبؤي</h4>
            <p>احصل على رؤى مستقبلية لاتخاذ قرارات أفضل:</p>
            <ul>
              <li>توقعات المبيعات</li>
              <li>اتجاهات السوق</li>
              <li>أداء الحملات المستقبلي</li>
              <li>فرص النمو</li>
            </ul>
          `
        };
      default:
        return { title: '', content: '' };
    }
  };

  const currentContent = getContentForSection(selectedSection);

  return (
    <div className="docs-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
              <li><a href="/support">الدعم</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">الوثائق والأدلة</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                دليل شامل لاستخدام جميع ميزات MarketMind والاستفادة القصوى من المنصة
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Documentation Content */}
      <section className="features" style={{ background: 'var(--white)', padding: '4rem 0' }}>
        <div className="container">
          <div style={{ display: 'grid', gridTemplateColumns: '300px 1fr', gap: '3rem', alignItems: 'start' }}>
            
            {/* Sidebar */}
            <div className="docs-sidebar" data-aos="fade-right">
              <h3 style={{ marginBottom: '1.5rem', color: 'var(--primary-color)' }}>المحتويات</h3>
              <nav>
                {docSections.map(section => (
                  <div key={section.id} style={{ marginBottom: '1rem' }}>
                    <button
                      onClick={() => setSelectedSection(section.id)}
                      style={{
                        width: '100%',
                        textAlign: 'right',
                        padding: '1rem',
                        border: 'none',
                        borderRadius: '8px',
                        background: selectedSection === section.id ? 'var(--primary-color)' : 'var(--bg-light)',
                        color: selectedSection === section.id ? 'white' : 'var(--text-dark)',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem'
                      }}
                    >
                      <i className={section.icon}></i>
                      <span style={{ fontWeight: '500' }}>{section.title}</span>
                    </button>
                    
                    {selectedSection === section.id && (
                      <ul style={{
                        listStyle: 'none',
                        padding: '0.5rem 0 0 2rem',
                        margin: 0
                      }}>
                        {section.items.map((item, index) => (
                          <li key={index} style={{
                            padding: '0.25rem 0',
                            color: 'var(--text-light)',
                            fontSize: '0.9rem'
                          }}>
                            • {item}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </nav>
            </div>

            {/* Content */}
            <div className="docs-content" data-aos="fade-left">
              <div style={{
                background: 'white',
                padding: '2.5rem',
                borderRadius: '12px',
                boxShadow: 'var(--shadow-soft)',
                minHeight: '600px'
              }}>
                <h2 style={{ marginBottom: '2rem', color: 'var(--primary-color)' }}>
                  {currentContent.title}
                </h2>
                <div 
                  style={{ 
                    lineHeight: '1.8',
                    color: 'var(--text-dark)'
                  }}
                  dangerouslySetInnerHTML={{ __html: currentContent.content }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">روابط سريعة</h2>
            <p className="section-subtitle">
              الوصول السريع للموارد الأكثر استخداماً
            </p>
          </div>
          
          <div className="problems-grid">
            <div className="problem-card" data-aos="fade-up" data-aos-delay="100">
              <div className="problem-icon">
                <i className="fas fa-download"></i>
              </div>
              <h3>تحميل SDK</h3>
              <p>حمل مكتبات التطوير للغات البرمجة المختلفة</p>
              <a href="#" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                تحميل
              </a>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="200">
              <div className="problem-icon">
                <i className="fas fa-video"></i>
              </div>
              <h3>فيديوهات تعليمية</h3>
              <p>شاهد فيديوهات تعليمية مفصلة لجميع الميزات</p>
              <a href="#" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                مشاهدة
              </a>
            </div>

            <div className="problem-card" data-aos="fade-up" data-aos-delay="300">
              <div className="problem-icon">
                <i className="fas fa-users"></i>
              </div>
              <h3>مجتمع المطورين</h3>
              <p>انضم إلى مجتمع المطورين وشارك خبراتك</p>
              <a href="#" className="btn btn-outline" style={{ marginTop: '1rem' }}>
                انضمام
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/#features">الميزات</a></li>
                <li><a href="/#pricing">الأسعار</a></li>
                <li><a href="/#features">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DocsPage;
