import React, { useEffect, useState } from 'react';
import '../styles/enhanced-homepage.css';

const CareersPage: React.FC = () => {
  const [selectedDepartment, setSelectedDepartment] = useState('all');

  useEffect(() => {
    // Initialize AOS animations
    if (typeof window !== 'undefined' && (window as any).AOS) {
      (window as any).AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
      });
    }
  }, []);

  const departments = [
    { id: 'all', name: 'جميع الأقسام', icon: 'fas fa-th-large' },
    { id: 'engineering', name: 'الهندسة', icon: 'fas fa-code' },
    { id: 'product', name: 'المنتج', icon: 'fas fa-lightbulb' },
    { id: 'design', name: 'التصميم', icon: 'fas fa-palette' },
    { id: 'marketing', name: 'التسويق', icon: 'fas fa-bullhorn' },
    { id: 'sales', name: 'المبيعات', icon: 'fas fa-handshake' },
    { id: 'support', name: 'الدعم', icon: 'fas fa-headset' }
  ];

  const jobs = [
    {
      id: 1,
      title: 'مطور Full Stack Senior',
      department: 'engineering',
      location: 'الرياض، السعودية',
      type: 'دوام كامل',
      experience: '5+ سنوات',
      description: 'نبحث عن مطور Full Stack متمرس للانضمام إلى فريقنا التقني المتنامي.',
      requirements: [
        'خبرة 5+ سنوات في React و Node.js',
        'معرفة قوية بقواعد البيانات',
        'خبرة في AWS أو Azure',
        'مهارات تواصل ممتازة'
      ]
    },
    {
      id: 2,
      title: 'مصمم UX/UI',
      department: 'design',
      location: 'الرياض، السعودية',
      type: 'دوام كامل',
      experience: '3+ سنوات',
      description: 'انضم إلى فريق التصميم لدينا وساعد في تشكيل مستقبل تجربة المستخدم.',
      requirements: [
        'خبرة 3+ سنوات في UX/UI',
        'إتقان Figma و Adobe Creative Suite',
        'فهم قوي لمبادئ التصميم',
        'خبرة في تصميم المنتجات الرقمية'
      ]
    },
    {
      id: 3,
      title: 'مدير منتج',
      department: 'product',
      location: 'الرياض، السعودية',
      type: 'دوام كامل',
      experience: '4+ سنوات',
      description: 'قد استراتيجية المنتج وتطويره لتحقيق أهداف الشركة.',
      requirements: [
        'خبرة 4+ سنوات في إدارة المنتجات',
        'فهم عميق لمنهجيات Agile',
        'مهارات تحليلية قوية',
        'خبرة في منتجات SaaS'
      ]
    },
    {
      id: 4,
      title: 'مهندس ذكاء اصطناعي',
      department: 'engineering',
      location: 'الرياض، السعودية',
      type: 'دوام كامل',
      experience: '3+ سنوات',
      description: 'طور وحسن خوارزميات الذكاء الاصطناعي والتعلم الآلي.',
      requirements: [
        'خبرة 3+ سنوات في ML/AI',
        'إتقان Python و TensorFlow/PyTorch',
        'خبرة في معالجة البيانات الكبيرة',
        'درجة في علوم الحاسوب أو مجال ذي صلة'
      ]
    },
    {
      id: 5,
      title: 'أخصائي تسويق رقمي',
      department: 'marketing',
      location: 'الرياض، السعودية',
      type: 'دوام كامل',
      experience: '2+ سنوات',
      description: 'قد حملاتنا التسويقية الرقمية وزد من وعي العلامة التجارية.',
      requirements: [
        'خبرة 2+ سنوات في التسويق الرقمي',
        'معرفة بـ Google Ads و Facebook Ads',
        'مهارات كتابة محتوى ممتازة',
        'خبرة في تحليل البيانات التسويقية'
      ]
    },
    {
      id: 6,
      title: 'مطور DevOps',
      department: 'engineering',
      location: 'عن بُعد',
      type: 'دوام كامل',
      experience: '4+ سنوات',
      description: 'أدر البنية التحتية السحابية وعمليات النشر المستمر.',
      requirements: [
        'خبرة 4+ سنوات في DevOps',
        'إتقان Docker و Kubernetes',
        'خبرة في AWS/Azure/GCP',
        'معرفة بأدوات CI/CD'
      ]
    }
  ];

  const filteredJobs = selectedDepartment === 'all' 
    ? jobs 
    : jobs.filter(job => job.department === selectedDepartment);

  return (
    <div className="careers-page">
      {/* Header */}
      <header className="header">
        <div className="container">
          <nav className="nav">
            <div className="logo">
              <h2>MarketMind</h2>
            </div>
            <ul className="nav-links">
              <li><a href="/">الرئيسية</a></li>
              <li><a href="/about">من نحن</a></li>
              <li><a href="/careers">الوظائف</a></li>
              <li><a href="/contact">تواصل معنا</a></li>
            </ul>
            <div className="nav-buttons">
              <a href="/login" className="btn btn-outline">تسجيل الدخول</a>
              <a href="/signup" className="btn btn-primary">ابدأ مجاناً</a>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero" style={{ padding: '6rem 0 4rem' }}>
        <div className="container">
          <div className="hero-content" style={{ textAlign: 'center', gridTemplateColumns: '1fr' }}>
            <div className="hero-text">
              <h1 data-aos="fade-up">انضم إلى فريق MarketMind</h1>
              <p data-aos="fade-up" data-aos-delay="200">
                كن جزءاً من رحلتنا لتغيير مستقبل التسويق الرقمي بالذكاء الاصطناعي. 
                نبحث عن المواهب المتميزة للانضمام إلى فريقنا المتنامي.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">لماذا MarketMind؟</h2>
            <p className="section-subtitle">
              اكتشف ما يجعل MarketMind مكان عمل استثنائي
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <h3>نمو سريع</h3>
              <p>
                انضم إلى شركة سريعة النمو وكن جزءاً من تشكيل مستقبل التكنولوجيا 
                في المنطقة مع فرص تطوير مهني لا محدودة.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-users"></i>
              </div>
              <h3>فريق متميز</h3>
              <p>
                اعمل مع أفضل المواهب في الصناعة في بيئة تعاونية تشجع على 
                الابتكار والتعلم المستمر.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-balance-scale"></i>
              </div>
              <h3>توازن الحياة</h3>
              <p>
                نؤمن بأهمية التوازن بين العمل والحياة مع مرونة في أوقات العمل 
                وإمكانية العمل عن بُعد.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <div className="feature-icon">
                <i className="fas fa-graduation-cap"></i>
              </div>
              <h3>التطوير المستمر</h3>
              <p>
                استثمر في مستقبلك مع برامج التدريب المستمر والمؤتمرات 
                وميزانية مخصصة للتطوير المهني.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="500">
              <div className="feature-icon">
                <i className="fas fa-heart"></i>
              </div>
              <h3>مزايا شاملة</h3>
              <p>
                تأمين صحي شامل، إجازات مرنة، مكافآت الأداء، وبيئة عمل 
                محفزة مع أحدث التقنيات.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="600">
              <div className="feature-icon">
                <i className="fas fa-globe"></i>
              </div>
              <h3>تأثير عالمي</h3>
              <p>
                ساهم في بناء منتج يستخدمه آلاف الشركات حول العالم 
                ويؤثر على مستقبل التسويق الرقمي.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="problems" style={{ background: 'var(--bg-light)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">الوظائف المتاحة</h2>
            <p className="section-subtitle">
              اكتشف الفرص المتاحة وابدأ رحلتك معنا
            </p>
          </div>
          
          {/* Department Filter */}
          <div className="category-filters" data-aos="fade-up" data-aos-delay="200" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '3rem'
          }}>
            {departments.map(department => (
              <button
                key={department.id}
                onClick={() => setSelectedDepartment(department.id)}
                className={`category-btn ${selectedDepartment === department.id ? 'active' : ''}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: selectedDepartment === department.id ? '2px solid var(--primary-color)' : '2px solid transparent',
                  background: selectedDepartment === department.id ? 'var(--primary-color)' : 'white',
                  color: selectedDepartment === department.id ? 'white' : 'var(--text-dark)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontWeight: '500'
                }}
              >
                <i className={department.icon}></i>
                {department.name}
              </button>
            ))}
          </div>

          {/* Jobs Grid */}
          <div className="jobs-grid" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '2rem'
          }}>
            {filteredJobs.map((job, index) => (
              <div 
                key={job.id}
                className="job-card" 
                data-aos="fade-up" 
                data-aos-delay={index * 100}
                style={{
                  background: 'white',
                  padding: '2rem',
                  borderRadius: '12px',
                  boxShadow: 'var(--shadow-soft)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'var(--shadow-soft)';
                }}
              >
                <div style={{ marginBottom: '1.5rem' }}>
                  <h3 style={{ 
                    color: 'var(--primary-color)', 
                    marginBottom: '0.5rem',
                    fontSize: '1.25rem'
                  }}>
                    {job.title}
                  </h3>
                  
                  <div style={{ 
                    display: 'flex', 
                    flexWrap: 'wrap',
                    gap: '1rem', 
                    fontSize: '0.9rem', 
                    color: 'var(--text-light)',
                    marginBottom: '1rem'
                  }}>
                    <span><i className="fas fa-map-marker-alt"></i> {job.location}</span>
                    <span><i className="fas fa-clock"></i> {job.type}</span>
                    <span><i className="fas fa-user-tie"></i> {job.experience}</span>
                  </div>
                </div>

                <p style={{ 
                  color: 'var(--text-dark)', 
                  marginBottom: '1.5rem',
                  lineHeight: '1.6'
                }}>
                  {job.description}
                </p>

                <div style={{ marginBottom: '2rem' }}>
                  <h4 style={{ 
                    color: 'var(--text-dark)', 
                    marginBottom: '1rem',
                    fontSize: '1rem'
                  }}>
                    المتطلبات:
                  </h4>
                  <ul style={{ 
                    listStyle: 'none', 
                    padding: 0,
                    textAlign: 'right'
                  }}>
                    {job.requirements.map((req, reqIndex) => (
                      <li key={reqIndex} style={{
                        padding: '0.25rem 0',
                        color: 'var(--text-light)',
                        fontSize: '0.9rem',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem'
                      }}>
                        <i className="fas fa-check" style={{ color: 'var(--accent-color)' }}></i>
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>

                <button style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: 'none',
                  background: 'var(--primary-color)',
                  color: 'white',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'var(--accent-color)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'var(--primary-color)';
                }}
                >
                  تقدم للوظيفة
                </button>
              </div>
            ))}
          </div>

          {filteredJobs.length === 0 && (
            <div style={{ textAlign: 'center', padding: '3rem' }}>
              <i className="fas fa-briefcase" style={{ fontSize: '3rem', color: 'var(--text-light)', marginBottom: '1rem' }}></i>
              <h3>لا توجد وظائف متاحة حالياً</h3>
              <p>تحقق مرة أخرى قريباً أو تواصل معنا لمعرفة الفرص القادمة</p>
            </div>
          )}
        </div>
      </section>

      {/* Application Process */}
      <section className="features" style={{ background: 'var(--white)' }}>
        <div className="container">
          <div className="section-header" data-aos="fade-up">
            <h2 className="section-title">عملية التوظيف</h2>
            <p className="section-subtitle">
              خطوات بسيطة وشفافة للانضمام إلى فريقنا
            </p>
          </div>
          
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon" style={{ background: 'var(--primary-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>1</span>
              </div>
              <h3>تقديم الطلب</h3>
              <p>
                أرسل سيرتك الذاتية وخطاب التغطية عبر موقعنا أو البريد الإلكتروني.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon" style={{ background: 'var(--accent-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>2</span>
              </div>
              <h3>المراجعة الأولية</h3>
              <p>
                فريق الموارد البشرية سيراجع طلبك ويتواصل معك خلال 3-5 أيام عمل.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon" style={{ background: 'var(--primary-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>3</span>
              </div>
              <h3>المقابلات</h3>
              <p>
                مقابلة تقنية مع الفريق ومقابلة ثقافية مع الإدارة لضمان التوافق.
              </p>
            </div>

            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <div className="feature-icon" style={{ background: 'var(--accent-color)' }}>
                <span style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold' }}>4</span>
              </div>
              <h3>العرض والانضمام</h3>
              <p>
                إذا تم اختيارك، ستحصل على عرض عمل مفصل وبرنامج تأهيل شامل.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content" data-aos="fade-up">
            <h2>لم تجد الوظيفة المناسبة؟</h2>
            <p>أرسل لنا سيرتك الذاتية وسنتواصل معك عند توفر فرصة مناسبة لمهاراتك</p>
            <div className="cta-buttons">
              <a href="/contact" className="btn btn-white">أرسل سيرتك الذاتية</a>
              <a href="/about" className="btn btn-outline-white">تعرف علينا أكثر</a>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>MarketMind</h3>
              <p>منصة الذكاء الاصطناعي الرائدة للتسويق الرقمي في المنطقة.</p>
            </div>

            <div className="footer-section">
              <h3>المنتج</h3>
              <ul>
                <li><a href="/features">الميزات</a></li>
                <li><a href="/pricing">الأسعار</a></li>
                <li><a href="/integrations">التكاملات</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الشركة</h3>
              <ul>
                <li><a href="/about">من نحن</a></li>
                <li><a href="/careers">الوظائف</a></li>
                <li><a href="/contact">تواصل معنا</a></li>
              </ul>
            </div>

            <div className="footer-section">
              <h3>الدعم</h3>
              <ul>
                <li><a href="/support">مركز المساعدة</a></li>
                <li><a href="/docs">الوثائق</a></li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2024 MarketMind. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CareersPage;
