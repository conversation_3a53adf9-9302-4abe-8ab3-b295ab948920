// نظام الطباعة والخطوط المحدث لمنصة MarketMind
// يدعم اللغة العربية والإنجليزية مع تحسينات للقراءة

export const typography = {
  // عائلات الخطوط - Font Families
  fontFamily: {
    // الخط الأساسي (يدعم العربية والإنجليزية)
    primary: [
      'Cairo',
      'Tajawal', 
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"'
    ].join(','),

    // خط ثانوي للنصوص الطويلة
    secondary: [
      'Tajawal',
      'Cairo',
      'system-ui',
      '-apple-system',
      'sans-serif'
    ].join(','),

    // خط أحادي المسافة للكود
    mono: [
      '"Fira Code"',
      '"JetBrains Mono"',
      'Consolas',
      '"Liberation Mono"',
      'Menlo',
      'Courier',
      'monospace'
    ].join(','),

    // خط للعناوين
    heading: [
      'Cairo',
      'Tajawal',
      '-apple-system',
      'BlinkMacSystemFont',
      'sans-serif'
    ].join(',')
  },

  // أحجام الخطوط - Font Sizes
  fontSize: {
    // أحجام أساسية
    xs: '0.75rem',      // 12px
    sm: '0.875rem',     // 14px
    base: '1rem',       // 16px
    lg: '1.125rem',     // 18px
    xl: '1.25rem',      // 20px
    '2xl': '1.5rem',    // 24px
    '3xl': '1.875rem',  // 30px
    '4xl': '2.25rem',   // 36px
    '5xl': '3rem',      // 48px
    '6xl': '3.75rem',   // 60px
    '7xl': '4.5rem',    // 72px
    '8xl': '6rem',      // 96px
    '9xl': '8rem',      // 128px

    // أحجام للمكونات المحددة
    button: {
      small: '0.875rem',    // 14px
      medium: '1rem',       // 16px
      large: '1.125rem'     // 18px
    },

    input: {
      small: '0.875rem',    // 14px
      medium: '1rem',       // 16px
      large: '1.125rem'     // 18px
    },

    caption: '0.75rem',     // 12px
    body: '1rem',           // 16px
    subtitle: '1.125rem',   // 18px
    title: '1.5rem',        // 24px
    heading: '2.25rem',     // 36px
    display: '3rem'         // 48px
  },

  // أوزان الخطوط - Font Weights
  fontWeight: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
  },

  // ارتفاع الأسطر - Line Heights
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,

    // ارتفاعات محددة للمكونات
    heading: 1.2,
    body: 1.6,
    caption: 1.4,
    button: 1.5
  },

  // تباعد الأحرف - Letter Spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },

  // أنماط النصوص - Text Styles
  textStyles: {
    // العناوين
    h1: {
      fontSize: '3rem',        // 48px
      fontWeight: 700,
      lineHeight: 1.2,
      letterSpacing: '-0.025em',
      fontFamily: 'Cairo, sans-serif'
    },

    h2: {
      fontSize: '2.25rem',     // 36px
      fontWeight: 600,
      lineHeight: 1.25,
      letterSpacing: '-0.025em',
      fontFamily: 'Cairo, sans-serif'
    },

    h3: {
      fontSize: '1.875rem',    // 30px
      fontWeight: 600,
      lineHeight: 1.3,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    h4: {
      fontSize: '1.5rem',      // 24px
      fontWeight: 600,
      lineHeight: 1.35,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    h5: {
      fontSize: '1.25rem',     // 20px
      fontWeight: 500,
      lineHeight: 1.4,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    h6: {
      fontSize: '1.125rem',    // 18px
      fontWeight: 500,
      lineHeight: 1.45,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    // النصوص الأساسية
    body1: {
      fontSize: '1rem',        // 16px
      fontWeight: 400,
      lineHeight: 1.6,
      letterSpacing: '0em',
      fontFamily: 'Tajawal, sans-serif'
    },

    body2: {
      fontSize: '0.875rem',    // 14px
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0em',
      fontFamily: 'Tajawal, sans-serif'
    },

    // النصوص الفرعية
    subtitle1: {
      fontSize: '1.125rem',    // 18px
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    subtitle2: {
      fontSize: '1rem',        // 16px
      fontWeight: 500,
      lineHeight: 1.45,
      letterSpacing: '0em',
      fontFamily: 'Cairo, sans-serif'
    },

    // النصوص التوضيحية
    caption: {
      fontSize: '0.75rem',     // 12px
      fontWeight: 400,
      lineHeight: 1.4,
      letterSpacing: '0.025em',
      fontFamily: 'Tajawal, sans-serif'
    },

    overline: {
      fontSize: '0.75rem',     // 12px
      fontWeight: 600,
      lineHeight: 1.4,
      letterSpacing: '0.1em',
      textTransform: 'uppercase' as const,
      fontFamily: 'Cairo, sans-serif'
    },

    // الأزرار
    button: {
      fontSize: '1rem',        // 16px
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.025em',
      fontFamily: 'Cairo, sans-serif'
    },

    // الروابط
    link: {
      fontSize: '1rem',        // 16px
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0em',
      textDecoration: 'underline',
      fontFamily: 'Tajawal, sans-serif'
    }
  },

  // أنماط خاصة للمكونات - Component Text Styles
  components: {
    // بطاقات البيانات
    card: {
      title: {
        fontSize: '1.25rem',    // 20px
        fontWeight: 600,
        lineHeight: 1.4,
        fontFamily: 'Cairo, sans-serif'
      },
      subtitle: {
        fontSize: '0.875rem',   // 14px
        fontWeight: 400,
        lineHeight: 1.5,
        fontFamily: 'Tajawal, sans-serif'
      },
      value: {
        fontSize: '2rem',       // 32px
        fontWeight: 700,
        lineHeight: 1.2,
        fontFamily: 'Cairo, sans-serif'
      }
    },

    // الجداول
    table: {
      header: {
        fontSize: '0.875rem',   // 14px
        fontWeight: 600,
        lineHeight: 1.4,
        fontFamily: 'Cairo, sans-serif'
      },
      cell: {
        fontSize: '0.875rem',   // 14px
        fontWeight: 400,
        lineHeight: 1.5,
        fontFamily: 'Tajawal, sans-serif'
      }
    },

    // النماذج
    form: {
      label: {
        fontSize: '0.875rem',   // 14px
        fontWeight: 500,
        lineHeight: 1.4,
        fontFamily: 'Cairo, sans-serif'
      },
      input: {
        fontSize: '1rem',       // 16px
        fontWeight: 400,
        lineHeight: 1.5,
        fontFamily: 'Tajawal, sans-serif'
      },
      helper: {
        fontSize: '0.75rem',    // 12px
        fontWeight: 400,
        lineHeight: 1.4,
        fontFamily: 'Tajawal, sans-serif'
      }
    },

    // التنقل
    navigation: {
      item: {
        fontSize: '0.95rem',    // 15.2px
        fontWeight: 500,
        lineHeight: 1.4,
        fontFamily: 'Cairo, sans-serif'
      },
      title: {
        fontSize: '1.125rem',   // 18px
        fontWeight: 600,
        lineHeight: 1.3,
        fontFamily: 'Cairo, sans-serif'
      }
    }
  },

  // أنماط متجاوبة - Responsive Typography
  responsive: {
    // العناوين المتجاوبة
    heading: {
      h1: {
        fontSize: {
          xs: '2rem',      // 32px
          sm: '2.5rem',    // 40px
          md: '3rem',      // 48px
          lg: '3.5rem',    // 56px
          xl: '4rem'       // 64px
        }
      },
      h2: {
        fontSize: {
          xs: '1.75rem',   // 28px
          sm: '2rem',      // 32px
          md: '2.25rem',   // 36px
          lg: '2.5rem',    // 40px
          xl: '3rem'       // 48px
        }
      },
      h3: {
        fontSize: {
          xs: '1.5rem',    // 24px
          sm: '1.625rem',  // 26px
          md: '1.875rem',  // 30px
          lg: '2rem',      // 32px
          xl: '2.25rem'    // 36px
        }
      }
    },

    // النصوص المتجاوبة
    body: {
      fontSize: {
        xs: '0.875rem',    // 14px
        sm: '1rem',        // 16px
        md: '1rem',        // 16px
        lg: '1.125rem',    // 18px
        xl: '1.125rem'     // 18px
      }
    }
  }
};

// دوال مساعدة للطباعة
export const typographyUtils = {
  // تحويل px إلى rem
  pxToRem: (px: number): string => {
    return `${px / 16}rem`;
  },

  // إنشاء نمط نص متجاوب
  responsiveText: (sizes: { xs?: string; sm?: string; md?: string; lg?: string; xl?: string }) => ({
    fontSize: sizes.xs || typography.fontSize.base,
    '@media (min-width: 600px)': {
      fontSize: sizes.sm || sizes.xs || typography.fontSize.base
    },
    '@media (min-width: 900px)': {
      fontSize: sizes.md || sizes.sm || sizes.xs || typography.fontSize.base
    },
    '@media (min-width: 1200px)': {
      fontSize: sizes.lg || sizes.md || sizes.sm || sizes.xs || typography.fontSize.base
    },
    '@media (min-width: 1536px)': {
      fontSize: sizes.xl || sizes.lg || sizes.md || sizes.sm || sizes.xs || typography.fontSize.base
    }
  }),

  // تطبيق نمط نص
  applyTextStyle: (style: keyof typeof typography.textStyles) => {
    return typography.textStyles[style];
  },

  // حساب ارتفاع السطر المثالي
  calculateLineHeight: (fontSize: string): number => {
    const size = parseFloat(fontSize);
    if (size <= 14) return 1.5;
    if (size <= 18) return 1.4;
    if (size <= 24) return 1.3;
    return 1.2;
  }
};

// تصدير للاستخدام السريع
export const {
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  textStyles,
  components,
  responsive
} = typography;

export default typography;
