// نظام الألوان المحدث لمنصة MarketMind
// متوافق مع WCAG 2.1 AA للتباين والوصولية

export const colors = {
  // الألوان الأساسية - Primary Colors
  primary: {
    50: '#eff6ff',   // أفتح درجة
    100: '#dbeafe',  // فاتح جداً
    200: '#bfdbfe',  // فاتح
    300: '#93c5fd',  // متوسط فاتح
    400: '#60a5fa',  // متوسط
    500: '#3b82f6',  // الأساسي - Primary
    600: '#2563eb',  // متوسط غامق
    700: '#1d4ed8',  // غامق
    800: '#1e40af',  // غامق جداً
    900: '#1e3a8a',  // الأغمق
    950: '#172554'   // الأغمق على الإطلاق
  },

  // الألوان الثانوية - Secondary Colors
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',  // الأساسي
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617'
  },

  // ألوان التمييز - Accent Colors
  accent: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',  // الأساسي - Accent
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03'
  },

  // ألوان النجاح - Success Colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',  // الأساسي
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16'
  },

  // ألوان التحذير - Warning Colors
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',  // الأساسي
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03'
  },

  // ألوان الخطر - Error Colors
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',  // الأساسي
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a'
  },

  // ألوان المعلومات - Info Colors
  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // الأساسي
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554'
  },

  // الألوان الرمادية - Gray Scale
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',  // الأساسي
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712'
  },

  // ألوان خاصة بالمنصة - Brand Colors
  brand: {
    // الأزرق الأساسي للمنصة
    blue: {
      light: '#3b82f6',
      main: '#1e3a8a',
      dark: '#1e40af'
    },
    // الذهبي للتمييز
    gold: {
      light: '#fbbf24',
      main: '#f59e0b',
      dark: '#d97706'
    },
    // الأبيض والأسود
    neutral: {
      white: '#ffffff',
      black: '#000000',
      light: '#f8fafc',
      dark: '#0f172a'
    }
  },

  // ألوان الخلفيات - Background Colors
  background: {
    primary: '#ffffff',      // خلفية أساسية
    secondary: '#f8fafc',    // خلفية ثانوية
    tertiary: '#f1f5f9',     // خلفية ثالثية
    dark: '#0f172a',         // خلفية داكنة
    gradient: {
      primary: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
      secondary: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
      success: 'linear-gradient(135deg, #22c55e 0%, #4ade80 100%)',
      error: 'linear-gradient(135deg, #ef4444 0%, #f87171 100%)',
      info: 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)',
      warning: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)'
    }
  },

  // ألوان النصوص - Text Colors
  text: {
    primary: '#111827',      // نص أساسي
    secondary: '#4b5563',    // نص ثانوي
    tertiary: '#9ca3af',     // نص ثالثي
    inverse: '#ffffff',      // نص معكوس
    disabled: '#d1d5db',     // نص معطل
    link: '#2563eb',         // روابط
    linkHover: '#1d4ed8'     // روابط عند التمرير
  },

  // ألوان الحدود - Border Colors
  border: {
    light: '#e5e7eb',        // حدود فاتحة
    medium: '#d1d5db',       // حدود متوسطة
    dark: '#9ca3af',         // حدود داكنة
    focus: '#3b82f6',        // حدود عند التركيز
    error: '#ef4444',        // حدود خطأ
    success: '#22c55e'       // حدود نجاح
  },

  // ألوان الظلال - Shadow Colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.05)',
    medium: 'rgba(0, 0, 0, 0.1)',
    dark: 'rgba(0, 0, 0, 0.15)',
    colored: {
      primary: 'rgba(59, 130, 246, 0.25)',
      success: 'rgba(34, 197, 94, 0.25)',
      warning: 'rgba(245, 158, 11, 0.25)',
      error: 'rgba(239, 68, 68, 0.25)'
    }
  },

  // ألوان التفاعل - Interactive Colors
  interactive: {
    hover: {
      primary: '#2563eb',
      secondary: '#475569',
      accent: '#d97706',
      success: '#16a34a',
      warning: '#d97706',
      error: '#dc2626'
    },
    active: {
      primary: '#1d4ed8',
      secondary: '#334155',
      accent: '#b45309',
      success: '#15803d',
      warning: '#b45309',
      error: '#b91c1c'
    },
    focus: {
      primary: 'rgba(59, 130, 246, 0.5)',
      secondary: 'rgba(100, 116, 139, 0.5)',
      accent: 'rgba(245, 158, 11, 0.5)',
      success: 'rgba(34, 197, 94, 0.5)',
      warning: 'rgba(245, 158, 11, 0.5)',
      error: 'rgba(239, 68, 68, 0.5)'
    }
  }
};

// دوال مساعدة للألوان
export const colorUtils = {
  // إضافة شفافية للون
  alpha: (color: string, opacity: number): string => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  // الحصول على لون متباين
  getContrastColor: (backgroundColor: string): string => {
    // حساب مبسط للتباين
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? colors.text.primary : colors.text.inverse;
  },

  // تدرج لوني
  gradient: (color1: string, color2: string, direction: string = '135deg'): string => {
    return `linear-gradient(${direction}, ${color1} 0%, ${color2} 100%)`;
  }
};

// تصدير الألوان للاستخدام السريع
export const {
  primary,
  secondary,
  accent,
  success,
  warning,
  error,
  info,
  gray,
  brand,
  background,
  text,
  border,
  shadow,
  interactive
} = colors;

export default colors;
