// نظام التصميم الشامل لمنصة MarketMind
// يجمع جميع عناصر التصميم في نظام موحد ومتسق

import { createTheme } from '@mui/material/styles';
import type { ThemeOptions } from '@mui/material/styles';
import { colors } from './colors';
import { spacing } from './spacing';
import { typography } from './typography';
import { effects } from './effects';

// إعدادات Material-UI المخصصة
const muiThemeOptions: ThemeOptions = {
  // نظام الألوان
  palette: {
    mode: 'light',
    primary: {
      main: colors.primary[500],
      light: colors.primary[400],
      dark: colors.primary[700],
      contrastText: colors.text.inverse
    },
    secondary: {
      main: colors.secondary[500],
      light: colors.secondary[400],
      dark: colors.secondary[700],
      contrastText: colors.text.inverse
    },
    error: {
      main: colors.error[500],
      light: colors.error[400],
      dark: colors.error[700],
      contrastText: colors.text.inverse
    },
    warning: {
      main: colors.warning[500],
      light: colors.warning[400],
      dark: colors.warning[700],
      contrastText: colors.text.primary
    },
    info: {
      main: colors.info[500],
      light: colors.info[400],
      dark: colors.info[700],
      contrastText: colors.text.inverse
    },
    success: {
      main: colors.success[500],
      light: colors.success[400],
      dark: colors.success[700],
      contrastText: colors.text.inverse
    },
    grey: colors.gray,
    background: {
      default: colors.background.primary,
      paper: colors.background.secondary
    },
    text: {
      primary: colors.text.primary,
      secondary: colors.text.secondary,
      disabled: colors.text.disabled
    },
    divider: colors.border.light
  },

  // نظام الطباعة
  typography: {
    fontFamily: typography.fontFamily.primary,
    h1: {
      ...typography.textStyles.h1,
      '@media (max-width:600px)': {
        fontSize: typography.responsive.heading.h1.fontSize.xs
      },
      '@media (min-width:600px)': {
        fontSize: typography.responsive.heading.h1.fontSize.sm
      },
      '@media (min-width:900px)': {
        fontSize: typography.responsive.heading.h1.fontSize.md
      }
    },
    h2: {
      ...typography.textStyles.h2,
      '@media (max-width:600px)': {
        fontSize: typography.responsive.heading.h2.fontSize.xs
      },
      '@media (min-width:600px)': {
        fontSize: typography.responsive.heading.h2.fontSize.sm
      },
      '@media (min-width:900px)': {
        fontSize: typography.responsive.heading.h2.fontSize.md
      }
    },
    h3: typography.textStyles.h3,
    h4: typography.textStyles.h4,
    h5: typography.textStyles.h5,
    h6: typography.textStyles.h6,
    body1: typography.textStyles.body1,
    body2: typography.textStyles.body2,
    subtitle1: typography.textStyles.subtitle1,
    subtitle2: typography.textStyles.subtitle2,
    caption: typography.textStyles.caption,
    overline: typography.textStyles.overline,
    button: typography.textStyles.button
  },

  // نظام المسافات
  spacing: (factor: number) => `${0.25 * factor}rem`,

  // نقاط التوقف
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536
    }
  },

  // الظلال
  shadows: [
    'none',
    effects.shadows.xs,
    effects.shadows.sm,
    effects.shadows.md,
    effects.shadows.lg,
    effects.shadows.xl,
    effects.shadows['2xl'],
    effects.shadows.inner,
    effects.shadows.colored.primary,
    effects.shadows.colored.secondary,
    effects.shadows.colored.success,
    effects.shadows.colored.warning,
    effects.shadows.colored.error,
    effects.shadows.colored.info,
    effects.shadows.card,
    effects.shadows.cardHover,
    effects.shadows.button,
    effects.shadows.buttonHover,
    effects.shadows.modal,
    effects.shadows.dropdown,
    effects.shadows.tooltip,
    // إضافة المزيد من الظلال حسب الحاجة
    ...Array(4).fill(effects.shadows.md)
  ] as any,

  // الشكل والحدود
  shape: {
    borderRadius: parseFloat(spacing.border.radius.lg) * 16 // تحويل rem إلى px
  },

  // الانتقالات
  transitions: {
    duration: {
      shortest: parseInt(effects.transitions.duration.fastest),
      shorter: parseInt(effects.transitions.duration.fast),
      short: parseInt(effects.transitions.duration.normal),
      standard: parseInt(effects.transitions.duration.slow),
      complex: parseInt(effects.transitions.duration.slower),
      enteringScreen: parseInt(effects.transitions.duration.normal),
      leavingScreen: parseInt(effects.transitions.duration.fast)
    },
    easing: {
      easeInOut: effects.transitions.easing.easeInOut,
      easeOut: effects.transitions.easing.easeOut,
      easeIn: effects.transitions.easing.easeIn,
      sharp: effects.transitions.easing.linear
    }
  }
};

// إنشاء Theme Material-UI
export const muiTheme = createTheme(muiThemeOptions);

// نظام التصميم الشامل
export const theme = {
  // الأنظمة الأساسية
  colors,
  spacing,
  typography,
  effects,

  // إعدادات Material-UI
  mui: muiTheme,

  // مكونات مخصصة
  components: {
    // البطاقات
    Card: {
      default: {
        backgroundColor: colors.background.primary,
        borderRadius: spacing.border.radius.lg,
        boxShadow: effects.shadows.card,
        padding: spacing.component.card.padding,
        transition: effects.transitions.component.card,
        border: `1px solid ${colors.border.light}`,
        '&:hover': {
          boxShadow: effects.shadows.cardHover,
          transform: effects.transforms.translate.y.sm
        }
      },
      elevated: {
        backgroundColor: colors.background.primary,
        borderRadius: spacing.border.radius.xl,
        boxShadow: effects.shadows.lg,
        padding: spacing.component.card.paddingLarge,
        transition: effects.transitions.component.card
      },
      glass: {
        background: effects.glass.background,
        backdropFilter: effects.glass.backdropFilter,
        border: effects.glass.border,
        borderRadius: spacing.border.radius.xl,
        padding: spacing.component.card.padding
      }
    },

    // الأزرار
    Button: {
      primary: {
        backgroundColor: colors.primary[500],
        color: colors.text.inverse,
        padding: `${spacing.component.button.paddingY} ${spacing.component.button.paddingX}`,
        borderRadius: spacing.border.radius.lg,
        fontWeight: typography.fontWeight.medium,
        fontSize: typography.fontSize.button.medium,
        transition: effects.transitions.component.button,
        border: 'none',
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: colors.interactive.hover.primary,
          transform: effects.transforms.translate.y.sm,
          boxShadow: effects.shadows.buttonHover
        },
        '&:active': {
          backgroundColor: colors.interactive.active.primary,
          transform: effects.transforms.scale.active
        },
        '&:focus': {
          outline: 'none',
          boxShadow: `0 0 0 3px ${colors.interactive.focus.primary}`
        }
      },
      secondary: {
        backgroundColor: colors.secondary[500],
        color: colors.text.inverse,
        padding: `${spacing.component.button.paddingY} ${spacing.component.button.paddingX}`,
        borderRadius: spacing.border.radius.lg,
        fontWeight: typography.fontWeight.medium,
        fontSize: typography.fontSize.button.medium,
        transition: effects.transitions.component.button,
        border: 'none',
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: colors.interactive.hover.secondary,
          transform: effects.transforms.translate.y.sm
        }
      },
      outline: {
        backgroundColor: 'transparent',
        color: colors.primary[500],
        padding: `${spacing.component.button.paddingY} ${spacing.component.button.paddingX}`,
        borderRadius: spacing.border.radius.lg,
        fontWeight: typography.fontWeight.medium,
        fontSize: typography.fontSize.button.medium,
        transition: effects.transitions.component.button,
        border: `2px solid ${colors.primary[500]}`,
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: colors.primary[500],
          color: colors.text.inverse,
          transform: effects.transforms.translate.y.sm
        }
      }
    },

    // حقول الإدخال
    Input: {
      default: {
        backgroundColor: colors.background.primary,
        border: `1px solid ${colors.border.light}`,
        borderRadius: spacing.border.radius.lg,
        padding: `${spacing.component.form.labelGap} ${spacing.component.form.fieldGap}`,
        fontSize: typography.fontSize.input.medium,
        fontFamily: typography.fontFamily.primary,
        color: colors.text.primary,
        transition: effects.transitions.common.colors,
        '&:focus': {
          outline: 'none',
          borderColor: colors.border.focus,
          boxShadow: `0 0 0 3px ${colors.interactive.focus.primary}`
        },
        '&::placeholder': {
          color: colors.text.tertiary
        }
      }
    },

    // الجداول
    Table: {
      default: {
        backgroundColor: colors.background.primary,
        borderRadius: spacing.border.radius.lg,
        overflow: 'hidden',
        boxShadow: effects.shadows.card,
        '& th': {
          backgroundColor: colors.background.secondary,
          padding: spacing.component.table.headerPadding,
          fontSize: typography.components.table.header.fontSize,
          fontWeight: typography.components.table.header.fontWeight,
          color: colors.text.primary,
          borderBottom: `1px solid ${colors.border.light}`
        },
        '& td': {
          padding: spacing.component.table.cellPadding,
          fontSize: typography.components.table.cell.fontSize,
          color: colors.text.secondary,
          borderBottom: `1px solid ${colors.border.light}`
        },
        '& tr:hover': {
          backgroundColor: colors.background.tertiary
        }
      }
    }
  },

  // أدوات مساعدة
  utils: {
    // إنشاء متغيرات CSS
    createCSSVariables: () => {
      const cssVars: Record<string, string> = {};
      
      // ألوان
      Object.entries(colors.primary).forEach(([key, value]) => {
        cssVars[`--color-primary-${key}`] = value;
      });
      
      // مسافات
      Object.entries(spacing.base).forEach(([key, value]) => {
        cssVars[`--spacing-${key}`] = value;
      });
      
      return cssVars;
    },

    // تطبيق نمط متجاوب
    responsive: (styles: Record<string, any>) => {
      const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl'];
      const responsiveStyles: Record<string, any> = {};

      breakpoints.forEach(bp => {
        if (styles[bp]) {
          const mediaQuery = bp === 'xs' ? '' : `@media (min-width: ${spacing.responsive.breakpoints[bp as keyof typeof spacing.responsive.breakpoints]})`;
          if (mediaQuery) {
            responsiveStyles[mediaQuery] = styles[bp];
          } else {
            Object.assign(responsiveStyles, styles[bp]);
          }
        }
      });

      return responsiveStyles;
    }
  }
};

// تصدير الأنظمة الفردية
export { colors, spacing, typography, effects };

// تصدير النظام الكامل كافتراضي
export default theme;
