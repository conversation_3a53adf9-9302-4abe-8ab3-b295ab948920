// نظام التأثيرات والظلال والانتقالات المحدث لمنصة MarketMind
// يوفر تأثيرات بصرية حديثة ومتسقة

export const effects = {
  // الظلال - Shadows
  shadows: {
    // ظلال أساسية
    none: 'none',
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',

    // ظلال ملونة
    colored: {
      primary: '0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05)',
      secondary: '0 10px 15px -3px rgba(100, 116, 139, 0.1), 0 4px 6px -2px rgba(100, 116, 139, 0.05)',
      success: '0 10px 15px -3px rgba(34, 197, 94, 0.1), 0 4px 6px -2px rgba(34, 197, 94, 0.05)',
      warning: '0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
      error: '0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05)',
      info: '0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05)'
    },

    // ظلال للمكونات المحددة
    card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    cardHover: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    button: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    buttonHover: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    modal: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    dropdown: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    tooltip: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  },

  // الانتقالات - Transitions
  transitions: {
    // مدد الانتقال
    duration: {
      fastest: '75ms',
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms',
      slowest: '1000ms'
    },

    // منحنيات التسارع
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    },

    // انتقالات شائعة
    common: {
      all: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
      colors: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
      transform: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
      opacity: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
      shadow: 'box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1)',
      size: 'width 200ms cubic-bezier(0.4, 0, 0.2, 1), height 200ms cubic-bezier(0.4, 0, 0.2, 1)'
    },

    // انتقالات للمكونات
    component: {
      button: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
      card: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1), box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1)',
      modal: 'opacity 200ms cubic-bezier(0.4, 0, 0.2, 1), transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
      dropdown: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
      tooltip: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
      sidebar: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)'
    }
  },

  // التحويلات - Transforms
  transforms: {
    // التحريك
    translate: {
      none: 'translate(0, 0)',
      x: {
        sm: 'translateX(0.25rem)',
        md: 'translateX(0.5rem)',
        lg: 'translateX(1rem)'
      },
      y: {
        sm: 'translateY(0.25rem)',
        md: 'translateY(0.5rem)',
        lg: 'translateY(1rem)'
      }
    },

    // التكبير والتصغير
    scale: {
      none: 'scale(1)',
      sm: 'scale(0.95)',
      md: 'scale(1.05)',
      lg: 'scale(1.1)',
      hover: 'scale(1.02)',
      active: 'scale(0.98)'
    },

    // الدوران
    rotate: {
      none: 'rotate(0deg)',
      sm: 'rotate(1deg)',
      md: 'rotate(3deg)',
      lg: 'rotate(6deg)',
      '45': 'rotate(45deg)',
      '90': 'rotate(90deg)',
      '180': 'rotate(180deg)'
    }
  },

  // التدرجات - Gradients
  gradients: {
    // تدرجات أساسية
    primary: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
    secondary: 'linear-gradient(135deg, #64748b 0%, #94a3b8 100%)',
    accent: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
    success: 'linear-gradient(135deg, #22c55e 0%, #4ade80 100%)',
    warning: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
    error: 'linear-gradient(135deg, #ef4444 0%, #f87171 100%)',
    info: 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)',

    // تدرجات خاصة
    hero: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%)',
    card: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
    glass: 'linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
    
    // تدرجات متحركة
    animated: {
      rainbow: 'linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)',
      sunset: 'linear-gradient(-45deg, #ff9a9e, #fecfef, #fecfef, #ff9a9e)',
      ocean: 'linear-gradient(-45deg, #667eea, #764ba2, #667eea, #764ba2)'
    }
  },

  // التأثيرات الزجاجية - Glass Effects
  glass: {
    // خلفية زجاجية
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    
    // متغيرات مختلفة
    light: {
      background: 'rgba(255, 255, 255, 0.15)',
      backdropFilter: 'blur(15px)',
      border: '1px solid rgba(255, 255, 255, 0.25)'
    },
    
    dark: {
      background: 'rgba(0, 0, 0, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)'
    }
  },

  // تأثيرات التفاعل - Interactive Effects
  interactive: {
    // تأثيرات التمرير
    hover: {
      lift: {
        transform: 'translateY(-2px)',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
      },
      scale: {
        transform: 'scale(1.02)'
      },
      glow: {
        boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)'
      },
      slide: {
        transform: 'translateX(4px)'
      }
    },

    // تأثيرات النقر
    active: {
      press: {
        transform: 'scale(0.98)'
      },
      sink: {
        transform: 'translateY(1px)',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
      }
    },

    // تأثيرات التركيز
    focus: {
      ring: {
        outline: 'none',
        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.5)'
      },
      glow: {
        outline: 'none',
        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.3), 0 0 20px rgba(59, 130, 246, 0.2)'
      }
    }
  },

  // الرسوم المتحركة - Animations
  animations: {
    // رسوم متحركة أساسية
    keyframes: {
      fadeIn: {
        '0%': { opacity: 0 },
        '100%': { opacity: 1 }
      },
      fadeOut: {
        '0%': { opacity: 1 },
        '100%': { opacity: 0 }
      },
      slideInUp: {
        '0%': { transform: 'translateY(100%)', opacity: 0 },
        '100%': { transform: 'translateY(0)', opacity: 1 }
      },
      slideInDown: {
        '0%': { transform: 'translateY(-100%)', opacity: 0 },
        '100%': { transform: 'translateY(0)', opacity: 1 }
      },
      slideInLeft: {
        '0%': { transform: 'translateX(-100%)', opacity: 0 },
        '100%': { transform: 'translateX(0)', opacity: 1 }
      },
      slideInRight: {
        '0%': { transform: 'translateX(100%)', opacity: 0 },
        '100%': { transform: 'translateX(0)', opacity: 1 }
      },
      scaleIn: {
        '0%': { transform: 'scale(0)', opacity: 0 },
        '100%': { transform: 'scale(1)', opacity: 1 }
      },
      bounce: {
        '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
        '40%, 43%': { transform: 'translate3d(0, -30px, 0)' },
        '70%': { transform: 'translate3d(0, -15px, 0)' },
        '90%': { transform: 'translate3d(0, -4px, 0)' }
      },
      pulse: {
        '0%': { transform: 'scale(1)' },
        '50%': { transform: 'scale(1.05)' },
        '100%': { transform: 'scale(1)' }
      },
      spin: {
        '0%': { transform: 'rotate(0deg)' },
        '100%': { transform: 'rotate(360deg)' }
      }
    },

    // مدد الرسوم المتحركة
    duration: {
      fast: '200ms',
      normal: '300ms',
      slow: '500ms'
    }
  },

  // تأثيرات خاصة - Special Effects
  special: {
    // تأثير النيون
    neon: {
      textShadow: '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
      filter: 'drop-shadow(0 0 10px currentColor)'
    },

    // تأثير ثلاثي الأبعاد
    '3d': {
      transform: 'perspective(1000px) rotateX(15deg)',
      transformStyle: 'preserve-3d' as const
    },

    // تأثير الضوء
    spotlight: {
      background: 'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)'
    }
  }
};

// دوال مساعدة للتأثيرات
export const effectsUtils = {
  // إنشاء انتقال مخصص
  createTransition: (
    properties: string[],
    duration: string = effects.transitions.duration.normal,
    easing: string = effects.transitions.easing.easeInOut
  ): string => {
    return properties.map(prop => `${prop} ${duration} ${easing}`).join(', ');
  },

  // إنشاء ظل ملون
  createColoredShadow: (color: string, opacity: number = 0.1): string => {
    const rgb = color.replace('#', '').match(/.{2}/g)?.map(x => parseInt(x, 16)).join(', ');
    return `0 10px 15px -3px rgba(${rgb}, ${opacity}), 0 4px 6px -2px rgba(${rgb}, ${opacity * 0.5})`;
  },

  // إنشاء تدرج مخصص
  createGradient: (colors: string[], direction: string = '135deg'): string => {
    const colorStops = colors.map((color, index) => 
      `${color} ${(index / (colors.length - 1)) * 100}%`
    ).join(', ');
    return `linear-gradient(${direction}, ${colorStops})`;
  },

  // تطبيق تأثير زجاجي
  applyGlassEffect: (variant: 'light' | 'dark' | 'default' = 'default') => {
    const glass = variant === 'default' ? effects.glass : effects.glass[variant];
    return {
      background: glass.background,
      backdropFilter: glass.backdropFilter,
      border: glass.border
    };
  }
};

// تصدير للاستخدام السريع
export const {
  shadows,
  transitions,
  transforms,
  gradients,
  glass,
  interactive,
  animations,
  special
} = effects;

export default effects;
