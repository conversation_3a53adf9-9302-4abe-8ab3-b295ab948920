// نظام المسافات والتخطيط المحدث لمنصة MarketMind
// يوفر مسافات متسقة ومتجاوبة عبر جميع المكونات

export const spacing = {
  // المسافات الأساسية - Base Spacing (rem units)
  base: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '2.5rem',  // 40px
    '3xl': '3rem',    // 48px
    '4xl': '4rem',    // 64px
    '5xl': '5rem',    // 80px
    '6xl': '6rem'     // 96px
  },

  // المسافات الدقيقة - Fine Spacing
  fine: {
    '0': '0',
    '0.5': '0.125rem',  // 2px
    '1': '0.25rem',     // 4px
    '1.5': '0.375rem',  // 6px
    '2': '0.5rem',      // 8px
    '2.5': '0.625rem',  // 10px
    '3': '0.75rem',     // 12px
    '3.5': '0.875rem',  // 14px
    '4': '1rem',        // 16px
    '5': '1.25rem',     // 20px
    '6': '1.5rem',      // 24px
    '7': '1.75rem',     // 28px
    '8': '2rem',        // 32px
    '9': '2.25rem',     // 36px
    '10': '2.5rem',     // 40px
    '11': '2.75rem',    // 44px
    '12': '3rem',       // 48px
    '14': '3.5rem',     // 56px
    '16': '4rem',       // 64px
    '20': '5rem',       // 80px
    '24': '6rem',       // 96px
    '28': '7rem',       // 112px
    '32': '8rem',       // 128px
    '36': '9rem',       // 144px
    '40': '10rem',      // 160px
    '44': '11rem',      // 176px
    '48': '12rem',      // 192px
    '52': '13rem',      // 208px
    '56': '14rem',      // 224px
    '60': '15rem',      // 240px
    '64': '16rem',      // 256px
    '72': '18rem',      // 288px
    '80': '20rem',      // 320px
    '96': '24rem'       // 384px
  },

  // مسافات المكونات - Component Spacing
  component: {
    // البطاقات
    card: {
      padding: '1.5rem',        // 24px
      paddingSmall: '1rem',     // 16px
      paddingLarge: '2rem',     // 32px
      gap: '1rem',              // 16px
      margin: '1rem'            // 16px
    },

    // الأزرار
    button: {
      paddingX: '1rem',         // 16px
      paddingY: '0.5rem',       // 8px
      paddingXSmall: '0.75rem', // 12px
      paddingYSmall: '0.375rem', // 6px
      paddingXLarge: '1.5rem',  // 24px
      paddingYLarge: '0.75rem', // 12px
      gap: '0.5rem'             // 8px
    },

    // النماذج
    form: {
      fieldGap: '1rem',         // 16px
      labelGap: '0.5rem',       // 8px
      sectionGap: '2rem',       // 32px
      padding: '1.5rem'         // 24px
    },

    // الجداول
    table: {
      cellPadding: '0.75rem',   // 12px
      headerPadding: '1rem',    // 16px
      rowGap: '0.5rem'          // 8px
    },

    // القوائم
    list: {
      itemPadding: '0.75rem',   // 12px
      itemGap: '0.5rem',        // 8px
      sectionGap: '1.5rem'      // 24px
    },

    // النوافذ المنبثقة
    modal: {
      padding: '2rem',          // 32px
      headerPadding: '1.5rem',  // 24px
      footerPadding: '1.5rem',  // 24px
      gap: '1.5rem'             // 24px
    }
  },

  // مسافات التخطيط - Layout Spacing
  layout: {
    // الحاويات
    container: {
      paddingX: '1rem',         // 16px
      paddingXMd: '1.5rem',     // 24px
      paddingXLg: '2rem',       // 32px
      maxWidth: '1200px'        // العرض الأقصى
    },

    // الشبكات
    grid: {
      gap: '1rem',              // 16px
      gapSmall: '0.5rem',       // 8px
      gapLarge: '1.5rem',       // 24px
      gapXLarge: '2rem'         // 32px
    },

    // الأقسام
    section: {
      paddingY: '3rem',         // 48px
      paddingYSmall: '2rem',    // 32px
      paddingYLarge: '4rem',    // 64px
      gap: '2rem'               // 32px
    },

    // الرأس والتذييل
    header: {
      height: '4rem',           // 64px
      padding: '1rem'           // 16px
    },

    footer: {
      padding: '2rem'           // 32px
    },

    // الشريط الجانبي
    sidebar: {
      width: '240px',           // عرض الشريط الجانبي
      padding: '1rem',          // 16px
      itemPadding: '0.75rem'    // 12px
    }
  },

  // مسافات متجاوبة - Responsive Spacing
  responsive: {
    // نقاط التوقف
    breakpoints: {
      xs: '0px',
      sm: '600px',
      md: '900px',
      lg: '1200px',
      xl: '1536px'
    },

    // مسافات متجاوبة
    padding: {
      xs: '0.5rem',   // 8px
      sm: '1rem',     // 16px
      md: '1.5rem',   // 24px
      lg: '2rem',     // 32px
      xl: '2.5rem'    // 40px
    },

    margin: {
      xs: '0.5rem',   // 8px
      sm: '1rem',     // 16px
      md: '1.5rem',   // 24px
      lg: '2rem',     // 32px
      xl: '2.5rem'    // 40px
    },

    gap: {
      xs: '0.5rem',   // 8px
      sm: '1rem',     // 16px
      md: '1.5rem',   // 24px
      lg: '2rem',     // 32px
      xl: '2.5rem'    // 40px
    }
  },

  // الارتفاعات والعروض - Dimensions
  dimensions: {
    // ارتفاعات المكونات
    height: {
      button: '2.5rem',         // 40px
      buttonSmall: '2rem',      // 32px
      buttonLarge: '3rem',      // 48px
      input: '2.5rem',          // 40px
      inputSmall: '2rem',       // 32px
      inputLarge: '3rem',       // 48px
      header: '4rem',           // 64px
      navbar: '3.5rem',        // 56px
      card: 'auto',
      modal: 'auto'
    },

    // عروض المكونات
    width: {
      sidebar: '240px',
      sidebarCollapsed: '60px',
      modal: '500px',
      modalLarge: '800px',
      modalSmall: '400px',
      container: '1200px',
      containerSmall: '800px',
      containerLarge: '1400px'
    },

    // الحد الأدنى والأقصى
    min: {
      height: '2rem',           // 32px
      width: '2rem'             // 32px
    },

    max: {
      height: '100vh',
      width: '100vw'
    }
  },

  // الحدود والانحناءات - Borders & Radius
  border: {
    width: {
      none: '0',
      thin: '1px',
      medium: '2px',
      thick: '4px'
    },

    radius: {
      none: '0',
      sm: '0.25rem',    // 4px
      md: '0.375rem',   // 6px
      lg: '0.5rem',     // 8px
      xl: '0.75rem',    // 12px
      '2xl': '1rem',    // 16px
      '3xl': '1.5rem',  // 24px
      full: '9999px'    // دائري كامل
    }
  },

  // الظلال - Shadows
  shadow: {
    elevation: {
      none: 'none',
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
    }
  }
};

// دوال مساعدة للمسافات
export const spacingUtils = {
  // تحويل rem إلى px
  remToPx: (rem: string): number => {
    return parseFloat(rem) * 16;
  },

  // تحويل px إلى rem
  pxToRem: (px: number): string => {
    return `${px / 16}rem`;
  },

  // إنشاء مسافة متجاوبة
  responsive: (xs: string, sm?: string, md?: string, lg?: string, xl?: string) => ({
    xs,
    sm: sm || xs,
    md: md || sm || xs,
    lg: lg || md || sm || xs,
    xl: xl || lg || md || sm || xs
  }),

  // إنشاء padding متجاوب
  responsivePadding: (values: { xs?: string; sm?: string; md?: string; lg?: string; xl?: string }) => ({
    padding: values.xs || spacing.responsive.padding.xs,
    '@media (min-width: 600px)': {
      padding: values.sm || spacing.responsive.padding.sm
    },
    '@media (min-width: 900px)': {
      padding: values.md || spacing.responsive.padding.md
    },
    '@media (min-width: 1200px)': {
      padding: values.lg || spacing.responsive.padding.lg
    },
    '@media (min-width: 1536px)': {
      padding: values.xl || spacing.responsive.padding.xl
    }
  })
};

// تصدير للاستخدام السريع
export const {
  base,
  fine,
  component,
  layout,
  responsive,
  dimensions,
  border,
  shadow
} = spacing;

export default spacing;
