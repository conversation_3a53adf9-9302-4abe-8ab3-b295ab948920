import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

class ApiClient {
  private client: any;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(email: string, password: string) {
    const formData = new FormData();
    formData.append('username', email);
    formData.append('password', password);
    
    const response = await this.client.post('/api/v1/users/login', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async register(userData: any) {
    const response = await this.client.post('/api/v1/users/register', userData);
    return response.data;
  }

  async getCurrentUser() {
    const response = await this.client.get('/api/v1/users/me');
    return response.data;
  }

  // Analysis endpoints
  async analyzeSentiment(data: any) {
    const response = await this.client.post('/api/v1/analysis/sentiment', data);
    return response.data;
  }

  async performCustomerSegmentation(data: any) {
    const response = await this.client.post('/api/v1/analysis/customer-segmentation', data);
    return response.data;
  }

  async analyzeTrends(data: any) {
    const response = await this.client.post('/api/v1/analysis/trend-analysis', data);
    return response.data;
  }

  async getCustomerSegments() {
    const response = await this.client.get('/api/v1/analysis/segments');
    return response.data;
  }

  async getAnalyticsSummary() {
    const response = await this.client.get('/api/v1/analysis/analytics/summary');
    return response.data;
  }

  // Content generation endpoints
  async generateContent(data: any) {
    const response = await this.client.post('/api/v1/content/generate', data);
    return response.data;
  }

  async getContentHistory(limit = 20) {
    const response = await this.client.get(`/api/v1/content/history?limit=${limit}`);
    return response.data;
  }

  async rateContent(contentId: number, rating: number) {
    const response = await this.client.put(`/api/v1/content/${contentId}/rate`, { rating });
    return response.data;
  }

  async getContentTemplates(contentType: string) {
    const response = await this.client.post('/api/v1/content/templates', { content_type: contentType });
    return response.data;
  }

  async optimizeContent(data: any) {
    const response = await this.client.post('/api/v1/content/optimize', data);
    return response.data;
  }

  // Prediction endpoints
  async predictChurn(data: any) {
    const response = await this.client.post('/api/v1/prediction/churn', data);
    return response.data;
  }

  async predictTrends(data: any) {
    const response = await this.client.post('/api/v1/prediction/trends', data);
    return response.data;
  }

  async forecastSales(data: any) {
    const response = await this.client.post('/api/v1/prediction/sales-forecast', data);
    return response.data;
  }

  async getPredictionModels() {
    const response = await this.client.get('/api/v1/prediction/models');
    return response.data;
  }

  // Campaign endpoints
  async createCampaign(data: any) {
    const response = await this.client.post('/api/v1/campaigns/', data);
    return response.data;
  }

  async getCampaigns(status?: string) {
    const params = status ? `?status=${status}` : '';
    const response = await this.client.get(`/api/v1/campaigns/${params}`);
    return response.data;
  }

  async optimizeCampaign(campaignId: number, data: any) {
    const response = await this.client.post(`/api/v1/campaigns/${campaignId}/optimize`, data);
    return response.data;
  }

  async createABTest(campaignId: number, data: any) {
    const response = await this.client.post(`/api/v1/campaigns/${campaignId}/ab-test`, data);
    return response.data;
  }

  async getCampaignAnalytics(campaignId: number) {
    const response = await this.client.get(`/api/v1/campaigns/${campaignId}/analytics`);
    return response.data;
  }

  async updateCampaign(campaignId: number, data: any) {
    const response = await this.client.put(`/api/v1/campaigns/${campaignId}`, data);
    return response.data;
  }

  // Admin endpoints
  async getAdminDashboard() {
    const response = await this.client.get('/api/v1/admin/dashboard');
    return response.data;
  }

  async getAllUsers(params: any = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await this.client.get(`/api/v1/admin/users?${queryString}`);
    return response.data;
  }

  async updateUserAdmin(userId: number, data: any) {
    const response = await this.client.put(`/api/v1/admin/users/${userId}`, data);
    return response.data;
  }

  async getAllSubscriptions(params: any = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await this.client.get(`/api/v1/admin/subscriptions?${queryString}`);
    return response.data;
  }

  async getSystemLogs(params: any = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await this.client.get(`/api/v1/admin/system-logs?${queryString}`);
    return response.data;
  }

  async getAdminAnalyticsSummary() {
    const response = await this.client.get('/api/v1/admin/analytics/summary');
    return response.data;
  }

  async createSystemLog(data: any) {
    const response = await this.client.post('/api/v1/admin/system-logs', data);
    return response.data;
  }

  // Health check
  async healthCheck() {
    const response = await this.client.get('/health');
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
