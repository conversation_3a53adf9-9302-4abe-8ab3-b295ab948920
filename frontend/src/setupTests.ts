// إزالة أو تعليق الكود الذي يسبب أخطاء
// import '@testing-library/jest-dom';

// Mock IntersectionObserver
// global.IntersectionObserver = class IntersectionObserver {
//   constructor() {}
//   observe() {}
//   unobserve() {}
//   disconnect() {}
// };

// Mock ResizeObserver
// global.ResizeObserver = class ResizeObserver {
//   constructor() {}
//   observe() {}
//   unobserve() {}
//   disconnect() {}
// };

// Mock matchMedia
// Object.defineProperty(window, 'matchMedia', {
//   writable: true,
//   value: jest.fn().mockImplementation(query => ({
//     matches: false,
//     media: query,
//     onchange: null,
//     addListener: jest.fn(), // deprecated
//     removeListener: jest.fn(), // deprecated
//     addEventListener: jest.fn(),
//     removeEventListener: jest.fn(),
//     dispatchEvent: jest.fn(),
//   })),
// });

// Mock scrollTo
// Object.defineProperty(window, 'scrollTo', {
//   writable: true,
//   value: jest.fn().mockReturnValue({
//     scrollTo: jest.fn(),
//   }),
// });

// Mock console methods
// global.console = {
//   ...console,
//   log: jest.fn(),
//   warn: jest.fn(),
//   error: jest.fn(),
// };

// Mock SVG elements for chart testing
// Object.defineProperty(SVGElement.prototype, 'getBBox', {
//   writable: true,
//   value: jest.fn().mockReturnValue({
//     x: 0,
//     y: 0,
//     width: 100,
//     height: 100,
//   }),
// });

// Mock SVG elements for chart testing
// Object.defineProperty(window, 'getComputedStyle', {
//   value: jest.fn().mockReturnValue({
//     getPropertyValue: jest.fn().mockReturnValue(''),
//   }),
// });
