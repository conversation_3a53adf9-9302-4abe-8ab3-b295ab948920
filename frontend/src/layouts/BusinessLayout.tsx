import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { theme as customTheme } from '../theme';
import '../styles/business-theme.css';
import {
  Menu as MenuIcon,
  Dashboard,
  People,
  Analytics,
  Assignment,
  AccountCircle,
  Logout,
  AutoAwesome,
  BugReport,
  TrendingDown,
  Campaign,
  Timeline,
  SentimentSatisfied,
  Notifications,
  Settings,
  Link
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';

const drawerWidth = 240;

// تطبيق النظام الجديد على التخطيط
const StyledDrawer = styled(Drawer)({
  '& .MuiDrawer-paper': {
    width: drawerWidth,
    backgroundColor: customTheme.colors.background.primary,
    borderRight: `1px solid ${customTheme.colors.border.light}`,
    boxShadow: customTheme.effects.shadows.lg,
    overflow: 'hidden'
  }
});

const StyledAppBar = styled(AppBar)({
  backgroundColor: customTheme.colors.background.primary,
  borderBottom: `1px solid ${customTheme.colors.border.light}`,
  boxShadow: customTheme.effects.shadows.sm,
  color: customTheme.colors.text.primary
});

const StyledListItemButton = styled(ListItemButton)<{ selected?: boolean }>(({ selected }) => ({
  borderRadius: customTheme.spacing.border.radius.lg,
  margin: `${customTheme.spacing.fine['1']} ${customTheme.spacing.fine['2']}`,
  transition: customTheme.effects.transitions.component.button,
  backgroundColor: selected ? customTheme.colors.primary[50] : 'transparent',

  '&:hover': {
    backgroundColor: selected ? customTheme.colors.primary[100] : customTheme.colors.background.tertiary,
    transform: customTheme.effects.transforms.translate.x.sm
  },

  '& .MuiListItemIcon-root': {
    color: selected ? customTheme.colors.primary[600] : customTheme.colors.text.secondary,
    minWidth: '40px'
  },

  '& .MuiListItemText-primary': {
    color: selected ? customTheme.colors.primary[700] : customTheme.colors.text.primary,
    fontWeight: selected ? customTheme.typography.fontWeight.semibold : customTheme.typography.fontWeight.medium,
    fontSize: customTheme.typography.fontSize.sm
  }
}));

interface BusinessLayoutProps {
  children: React.ReactNode;
}

const BusinessLayout: React.FC<BusinessLayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationCount, setNotificationCount] = useState(3); // Mock notification count
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();

  const menuItems = [
    { text: 'مؤشرات الأداء', icon: <Dashboard />, path: '/business/kpis' },
    { text: 'إدارة الفريق', icon: <People />, path: '/business/team' },
    { text: 'التحليلات المتقدمة', icon: <Analytics />, path: '/business/analytics' },
    { text: 'تجزئة العملاء', icon: <People />, path: '/business/customer-segmentation' },
    { text: 'التنبؤ بتوقف العملاء', icon: <TrendingDown />, path: '/business/churn-prediction' },
    { text: 'توليد المحتوى', icon: <AutoAwesome />, path: '/business/content-generation' },
    { text: 'تحسين الحملات', icon: <Campaign />, path: '/business/campaign-optimization' },
    { text: 'التنبؤ بالمبيعات', icon: <Analytics />, path: '/business/sales-prediction' },
    { text: 'رحلة العميل', icon: <Timeline />, path: '/business/customer-journey' },
    { text: 'تحليل المشاعر', icon: <SentimentSatisfied />, path: '/business/sentiment-analysis' },
    { text: 'متابعة المشاريع', icon: <Assignment />, path: '/business/projects' },
    { text: 'أدوات الذكاء الاصطناعي', icon: <AutoAwesome />, path: '/business/ai-tools' },
    { text: 'أدوات الذكاء الاصطناعي المتقدمة', icon: <AutoAwesome />, path: '/business/ai-tools-advanced' },
    { text: 'سلوك العملاء', icon: <People />, path: '/business/customer-behavior' },
    { text: 'تحليل المنافسين', icon: <Analytics />, path: '/business/competitor-analysis' },
    { text: 'توصية المنتجات', icon: <AutoAwesome />, path: '/business/product-recommendation' },
    { text: 'التكاملات والربط', icon: <Link />, path: '/business/integrations' },
    { text: '🧪 اختبار التحسينات', icon: <BugReport />, path: '/business/test-ai' },
    { text: '🎨 اختبار النظام الجديد', icon: <AutoAwesome />, path: '/business/ui-test' },
    { text: '🔧 اختبار التنقل', icon: <BugReport />, path: '/business/navigation-test' }
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleProfileMenuClose();
    navigate('/');
  };

  const handleNotificationClick = () => {
    // Navigate to notifications page
    navigate('/notifications');
    setNotificationCount(0); // Clear notifications when clicked
  };

  const handleProfileClick = () => {
    navigate('/profile');
    handleProfileMenuClose();
  };

  const handleSettingsClick = () => {
    navigate('/settings');
    handleProfileMenuClose();
  };

  const drawer = (
    <Box sx={{
      height: '100%',
      background: 'linear-gradient(180deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%)',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Background Pattern */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(147, 197, 253, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(96, 165, 250, 0.05) 0%, transparent 50%)
        `,
        zIndex: 0
      }} />

      {/* Header */}
      <Toolbar sx={{
        position: 'relative',
        zIndex: 1,
        minHeight: '80px !important',
        px: 3,
        py: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
          <Box sx={{
            width: 48,
            height: 48,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.8rem',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '2px solid rgba(255,255,255,0.2)'
          }}>
            🎯
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h5" noWrap component="div" sx={{
              fontWeight: 800,
              color: 'white',
              fontSize: '1.4rem',
              letterSpacing: '-0.02em'
            }}>
              MarketMind
            </Typography>
            <Typography variant="caption" sx={{
              opacity: 0.9,
              color: 'rgba(255,255,255,0.8)',
              fontSize: '0.75rem',
              fontWeight: 500
            }}>
              Business Dashboard
            </Typography>
          </Box>
        </Box>
      </Toolbar>

      <Divider sx={{ borderColor: 'rgba(255,255,255,0.1)' }} />

      {/* Navigation Menu */}
      <Box sx={{
        flex: 1,
        overflowY: 'auto',
        overflowX: 'hidden',
        maxHeight: 'calc(100vh - 140px)', // تحديد ارتفاع أقصى للتمرير
        '&::-webkit-scrollbar': {
          width: '6px',
        },
        '&::-webkit-scrollbar-track': {
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '3px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: 'rgba(255,255,255,0.3)',
          borderRadius: '3px',
        },
        '&::-webkit-scrollbar-thumb:hover': {
          background: 'rgba(255,255,255,0.5)',
        },
      }}>
        <List sx={{
          p: 3,
          position: 'relative',
          zIndex: 1,
          pb: 8 // إضافة مساحة إضافية في الأسفل لضمان الوصول للأزرار الأخيرة
        }}>
        {menuItems.map((item, index) => (
          <ListItem key={item.text} disablePadding sx={{
            mb: 1.5,
            // إضافة مساحة إضافية بعد آخر عنصرين
            ...(index >= menuItems.length - 2 && { mb: 3 })
          }}>
            <StyledListItemButton
              selected={location.pathname === item.path}
              onClick={() => {
                console.log('Navigating to:', item.path);
                navigate(item.path);
              }}
              sx={{
                borderRadius: 3,
                py: 1.5,
                px: 2,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                overflow: 'hidden',
                '&.Mui-selected': {
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
                  color: 'white',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  '& .MuiListItemIcon-root': {
                    color: 'white',
                    transform: 'scale(1.1)'
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: 4,
                    background: 'linear-gradient(180deg, #fbbf24 0%, #f59e0b 100%)',
                    borderRadius: '0 2px 2px 0'
                  }
                },
                '&:hover': {
                  background: 'rgba(255,255,255,0.1)',
                  transform: 'translateX(4px)',
                  '& .MuiListItemIcon-root': {
                    transform: 'scale(1.05)',
                    color: '#fbbf24'
                  }
                }
              }}
            >
              <ListItemIcon sx={{
                minWidth: 44,
                transition: 'all 0.3s ease',
                color: 'rgba(255,255,255,0.8)'
              }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Typography sx={{
                    fontSize: '0.95rem',
                    fontWeight: 600,
                    color: 'rgba(255,255,255,0.95)',
                    letterSpacing: '0.01em'
                  }}>
                    {item.text}
                  </Typography>
                }
              />
            </StyledListItemButton>
          </ListItem>
        ))}

        {/* مساحة إضافية في نهاية القائمة لضمان إمكانية التمرير للأزرار الأخيرة */}
        <Box sx={{ height: '120px' }} />
        </List>
      </Box>

      {/* Bottom Section */}
      <Box sx={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        p: 3,
        zIndex: 1
      }}>
        <Box sx={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: 3,
          p: 2,
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.1)'
        }}>
          <Typography variant="caption" sx={{
            color: 'rgba(255,255,255,0.8)',
            fontSize: '0.7rem',
            fontWeight: 500
          }}>
            الإصدار 2.0.0
          </Typography>
          <Typography variant="caption" sx={{
            color: 'rgba(255,255,255,0.6)',
            fontSize: '0.65rem',
            display: 'block',
            mt: 0.5
          }}>
            آخر تحديث: ديسمبر 2024
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <StyledAppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          left: 0,
          right: { sm: `${drawerWidth}px` },
          zIndex: 1200
        }}
      >
        <Toolbar sx={{
          minHeight: '80px !important',
          px: 3,
          direction: 'rtl'
        }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="end"
            onClick={handleDrawerToggle}
            sx={{
              ml: 2,
              display: { sm: 'none' },
              background: 'rgba(59, 130, 246, 0.1)',
              '&:hover': {
                background: 'rgba(59, 130, 246, 0.2)',
                transform: 'scale(1.05)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h5" noWrap component="div" sx={{
            flexGrow: 1,
            fontWeight: 700,
            background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontSize: '1.4rem',
            letterSpacing: '-0.02em',
            textAlign: 'right'
          }}>
            منصة MarketMind للأعمال
          </Typography>

          {/* Notifications Button */}
          <IconButton
            size="large"
            aria-label="show notifications"
            onClick={handleNotificationClick}
            sx={{
              ml: 2,
              background: 'rgba(107, 114, 128, 0.1)',
              color: '#6b7280',
              '&:hover': {
                background: 'rgba(107, 114, 128, 0.2)',
                transform: 'scale(1.05)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            <Badge
              badgeContent={notificationCount}
              color="error"
              sx={{
                '& .MuiBadge-badge': {
                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                  boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)'
                }
              }}
            >
              <Notifications />
            </Badge>
          </IconButton>

          {/* Profile Button */}
          <IconButton
            size="large"
            edge="start"
            aria-label="account of current user"
            onClick={handleProfileMenuOpen}
            sx={{
              p: 0,
              '&:hover': {
                transform: 'scale(1.05)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            <Avatar sx={{
              width: 48,
              height: 48,
              background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
              fontWeight: 700,
              fontSize: '1.1rem',
              boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
              border: '2px solid rgba(255, 255, 255, 0.9)'
            }}>
              {user?.name?.charAt(0) || 'U'}
            </Avatar>
          </IconButton>
        </Toolbar>
      </StyledAppBar>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              borderRadius: 2,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileClick}>
          <ListItemIcon>
            <AccountCircle fontSize="small" />
          </ListItemIcon>
          الملف الشخصي
        </MenuItem>
        <MenuItem onClick={handleSettingsClick}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          الإعدادات
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          تسجيل الخروج
        </MenuItem>
      </Menu>

      <Box
        component="nav"
        sx={{
          width: { sm: drawerWidth },
          flexShrink: { sm: 0 },
          position: 'fixed',
          right: 0,
          top: 0,
          height: '100vh',
          zIndex: 1100
        }}
      >
        <StyledDrawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
        >
          {drawer}
        </StyledDrawer>
        <StyledDrawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
          open
        >
          {drawer}
        </StyledDrawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { xs: '100%', sm: `calc(100% - ${drawerWidth}px)` },
          mr: { xs: 0, sm: `${drawerWidth}px` },
          background: 'var(--business-bg-secondary)',
          minHeight: '100vh',
          position: 'relative',
          zIndex: 1
        }}
      >
        <Toolbar sx={{ minHeight: '80px !important' }} />
        <Box className="business-dashboard">
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default BusinessLayout;
