import { create } from 'zustand';

export interface User {
  id: string;
  email: string;
  name: string;
  userType: 'personal' | 'business' | 'admin';
  phone?: string;
  location?: string;
  bio?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ userType: string; redirectUrl: string }>;
  logout: () => void;
  setUser: (user: User) => void;
  initializeAuth: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,

  login: async (email: string, password: string) => {
    set({ isLoading: true });

    try {
      console.log('Attempting login with:', { email, password });

      // Call real API with proper error handling
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        mode: 'cors',
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const data = await response.json();
      console.log('Response data:', data);

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
        } else if (response.status === 404) {
          throw new Error('الخادم غير متاح. تأكد من تشغيل الخادم الخلفي');
        } else if (response.status >= 500) {
          throw new Error('خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً');
        } else {
          throw new Error(data.message || 'فشل في تسجيل الدخول. تحقق من البيانات المدخلة.');
        }
      }

      if (!data.success) {
        throw new Error(data.message || 'فشل في تسجيل الدخول. تحقق من البيانات المدخلة.');
      }

      const user: User = {
        id: data.user.id,
        email: data.user.email,
        name: data.user.name,
        userType: data.user.user_type,
        phone: data.user.phone || '',
        location: data.user.location || ''
      };

      set({
        user,
        isAuthenticated: true,
        isLoading: false
      });

      // Store in localStorage
      localStorage.setItem('auth_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(user));

      const redirectUrl = user.userType === 'admin' ? '/admin' :
                         user.userType === 'business' ? '/business' : '/personal';

      return { userType: user.userType, redirectUrl };

    } catch (error: any) {
      console.error('Login error:', error);
      set({ isLoading: false });

      // Handle different types of errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('خطأ في الاتصال بالخادم. تأكد من تشغيل الخادم على المنفذ 8000.');
      }

      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        throw new Error('لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم الخلفي.');
      }

      throw new Error(error.message || 'فشل في تسجيل الدخول. تحقق من البيانات المدخلة.');
    }
  },

  logout: () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    set({ 
      user: null, 
      isAuthenticated: false 
    });
  },

  setUser: (user: User) => {
    set({ user, isAuthenticated: true });
  },

  initializeAuth: () => {
    const token = localStorage.getItem('auth_token');
    const userStr = localStorage.getItem('user');
    
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        set({ 
          user, 
          isAuthenticated: true 
        });
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
      }
    }
  }
}));
