/* Business Dashboard Theme */
:root {
  /* Primary Colors - Blue Gradient */
  --business-primary: #1e3a8a;
  --business-primary-light: #3b82f6;
  --business-primary-dark: #1e40af;
  --business-secondary: #3b82f6;
  
  /* Accent Colors */
  --business-accent: #f59e0b;
  --business-accent-light: #fbbf24;
  --business-accent-dark: #d97706;
  
  /* Status Colors */
  --business-success: #10b981;
  --business-success-light: #34d399;
  --business-success-dark: #059669;
  
  --business-warning: #f59e0b;
  --business-warning-light: #fbbf24;
  --business-warning-dark: #d97706;
  
  --business-error: #ef4444;
  --business-error-light: #f87171;
  --business-error-dark: #dc2626;
  
  --business-info: #3b82f6;
  --business-info-light: #60a5fa;
  --business-info-dark: #2563eb;
  
  /* Neutral Colors */
  --business-gray-50: #f9fafb;
  --business-gray-100: #f3f4f6;
  --business-gray-200: #e5e7eb;
  --business-gray-300: #d1d5db;
  --business-gray-400: #9ca3af;
  --business-gray-500: #6b7280;
  --business-gray-600: #4b5563;
  --business-gray-700: #374151;
  --business-gray-800: #1f2937;
  --business-gray-900: #111827;
  
  /* Background Colors */
  --business-bg-primary: #ffffff;
  --business-bg-secondary: #f9fafb;
  --business-bg-tertiary: #f3f4f6;
  
  /* Text Colors */
  --business-text-primary: #111827;
  --business-text-secondary: #6b7280;
  --business-text-tertiary: #9ca3af;
  
  /* Border Colors */
  --business-border-light: #e5e7eb;
  --business-border-medium: #d1d5db;
  --business-border-dark: #9ca3af;
  
  /* Shadow Colors */
  --business-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --business-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --business-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --business-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Gradients */
  --business-gradient-primary: linear-gradient(135deg, var(--business-primary) 0%, var(--business-secondary) 100%);
  --business-gradient-accent: linear-gradient(135deg, var(--business-accent) 0%, var(--business-accent-light) 100%);
  --business-gradient-success: linear-gradient(135deg, var(--business-success) 0%, var(--business-success-light) 100%);
  
  /* Spacing */
  --business-spacing-xs: 0.25rem;
  --business-spacing-sm: 0.5rem;
  --business-spacing-md: 1rem;
  --business-spacing-lg: 1.5rem;
  --business-spacing-xl: 2rem;
  --business-spacing-2xl: 3rem;
  
  /* Border Radius */
  --business-radius-sm: 0.375rem;
  --business-radius-md: 0.5rem;
  --business-radius-lg: 0.75rem;
  --business-radius-xl: 1rem;
  
  /* Typography */
  --business-font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --business-font-size-xs: 0.75rem;
  --business-font-size-sm: 0.875rem;
  --business-font-size-base: 1rem;
  --business-font-size-lg: 1.125rem;
  --business-font-size-xl: 1.25rem;
  --business-font-size-2xl: 1.5rem;
  --business-font-size-3xl: 1.875rem;
  --business-font-size-4xl: 2.25rem;
  
  /* Transitions */
  --business-transition-fast: 0.15s ease-in-out;
  --business-transition-normal: 0.3s ease-in-out;
  --business-transition-slow: 0.5s ease-in-out;
}

/* Business Dashboard Global Styles */
.business-dashboard {
  font-family: var(--business-font-family);
  color: var(--business-text-primary);
  background-color: var(--business-bg-secondary);
  min-height: 100vh;
}

/* Card Styles */
.business-card {
  background: var(--business-bg-primary);
  border: 1px solid var(--business-border-light);
  border-radius: var(--business-radius-lg);
  box-shadow: var(--business-shadow-sm);
  transition: var(--business-transition-normal);
}

.business-card:hover {
  box-shadow: var(--business-shadow-md);
  transform: translateY(-2px);
}

.business-card-header {
  padding: var(--business-spacing-lg);
  border-bottom: 1px solid var(--business-border-light);
  background: var(--business-bg-secondary);
  border-radius: var(--business-radius-lg) var(--business-radius-lg) 0 0;
}

.business-card-content {
  padding: var(--business-spacing-lg);
}

/* Button Styles */
.business-btn {
  padding: var(--business-spacing-sm) var(--business-spacing-lg);
  border-radius: var(--business-radius-md);
  font-weight: 600;
  font-size: var(--business-font-size-sm);
  transition: var(--business-transition-fast);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--business-spacing-sm);
}

.business-btn-primary {
  background: var(--business-gradient-primary);
  color: white;
}

.business-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--business-shadow-md);
}

.business-btn-secondary {
  background: var(--business-bg-primary);
  color: var(--business-primary);
  border: 1px solid var(--business-border-medium);
}

.business-btn-secondary:hover {
  background: var(--business-bg-secondary);
}

/* Stat Card Styles */
.business-stat-card {
  background: var(--business-bg-primary);
  border: 1px solid var(--business-border-light);
  border-radius: var(--business-radius-lg);
  padding: var(--business-spacing-xl);
  text-align: center;
  transition: var(--business-transition-normal);
}

.business-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--business-shadow-lg);
}

.business-stat-value {
  font-size: var(--business-font-size-3xl);
  font-weight: 700;
  color: var(--business-primary);
  margin-bottom: var(--business-spacing-sm);
}

.business-stat-label {
  font-size: var(--business-font-size-sm);
  color: var(--business-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Chart Container */
.business-chart-container {
  background: var(--business-bg-primary);
  border: 1px solid var(--business-border-light);
  border-radius: var(--business-radius-lg);
  overflow: hidden;
}

.business-chart-header {
  padding: var(--business-spacing-lg);
  border-bottom: 1px solid var(--business-border-light);
  background: var(--business-bg-secondary);
}

.business-chart-content {
  padding: var(--business-spacing-lg);
}

/* Table Styles */
.business-table {
  background: var(--business-bg-primary);
  border: 1px solid var(--business-border-light);
  border-radius: var(--business-radius-lg);
  overflow: hidden;
}

.business-table-header {
  background: var(--business-bg-secondary);
  font-weight: 600;
  color: var(--business-text-primary);
}

.business-table-row:hover {
  background: var(--business-bg-tertiary);
}

/* Status Indicators */
.business-status-active {
  color: var(--business-success);
  background: rgba(16, 185, 129, 0.1);
  padding: var(--business-spacing-xs) var(--business-spacing-sm);
  border-radius: var(--business-radius-sm);
  font-size: var(--business-font-size-xs);
  font-weight: 600;
}

.business-status-inactive {
  color: var(--business-error);
  background: rgba(239, 68, 68, 0.1);
  padding: var(--business-spacing-xs) var(--business-spacing-sm);
  border-radius: var(--business-radius-sm);
  font-size: var(--business-font-size-xs);
  font-weight: 600;
}

.business-status-pending {
  color: var(--business-warning);
  background: rgba(245, 158, 11, 0.1);
  padding: var(--business-spacing-xs) var(--business-spacing-sm);
  border-radius: var(--business-radius-sm);
  font-size: var(--business-font-size-xs);
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .business-dashboard {
    padding: var(--business-spacing-md);
  }

  .business-card-content {
    padding: var(--business-spacing-lg);
  }
}

@media (max-width: 768px) {
  .business-dashboard {
    padding: var(--business-spacing-sm);
  }

  .business-card-content {
    padding: var(--business-spacing-md);
  }

  .business-stat-value {
    font-size: var(--business-font-size-2xl);
  }

  .business-chart-content {
    padding: var(--business-spacing-md);
  }

  /* Mobile navigation improvements */
  .business-mobile-header {
    padding: var(--business-spacing-sm) var(--business-spacing-md);
    flex-direction: column;
    gap: var(--business-spacing-sm);
  }

  .business-mobile-header h4 {
    font-size: var(--business-font-size-xl);
    text-align: center;
  }

  .business-mobile-controls {
    display: flex;
    flex-direction: column;
    gap: var(--business-spacing-sm);
    width: 100%;
  }

  .business-mobile-controls .MuiFormControl-root {
    min-width: 100% !important;
  }

  /* Grid adjustments for mobile */
  .business-grid-mobile {
    grid-template-columns: 1fr !important;
    gap: var(--business-spacing-md) !important;
  }

  /* Table responsive */
  .business-table-mobile {
    overflow-x: auto;
  }

  .business-table-mobile table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .business-dashboard {
    padding: var(--business-spacing-xs);
  }

  .business-card-content {
    padding: var(--business-spacing-sm);
  }

  .business-stat-value {
    font-size: var(--business-font-size-xl);
  }

  .business-mobile-header h4 {
    font-size: var(--business-font-size-lg);
  }

  /* Ultra mobile adjustments */
  .business-btn {
    padding: var(--business-spacing-xs) var(--business-spacing-sm);
    font-size: var(--business-font-size-xs);
  }

  .business-chart-content {
    padding: var(--business-spacing-sm);
  }
}

/* Animation Classes */
.business-fade-in {
  animation: businessFadeIn 0.5s ease-in-out;
}

.business-slide-up {
  animation: businessSlideUp 0.5s ease-out;
}

@keyframes businessFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes businessSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Sidebar Styles */
.business-sidebar-enhanced {
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%);
  position: relative;
  overflow: hidden;
}

.business-sidebar-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(147, 197, 253, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(96, 165, 250, 0.05) 0%, transparent 50%);
  z-index: 0;
}

.business-sidebar-logo {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 2px solid rgba(255,255,255,0.2);
}

.business-sidebar-menu-item {
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.business-sidebar-menu-item:hover {
  background: rgba(255,255,255,0.1);
  transform: translateX(4px);
}

.business-sidebar-menu-item.active {
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.business-sidebar-menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 0 2px 2px 0;
}

/* Enhanced Header Styles */
.business-header-enhanced {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
}

.business-header-title {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.business-header-button {
  transition: all 0.2s ease;
}

.business-header-button:hover {
  transform: scale(1.05);
}

.business-notification-badge {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.business-avatar-enhanced {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.9);
}
