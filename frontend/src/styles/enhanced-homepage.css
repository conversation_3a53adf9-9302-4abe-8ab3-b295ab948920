:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --bg-light: #f8fafc;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    width: 100%;
    height: 100%;
    font-size: 16px;
}

/* Responsive Font Size */
@media (max-width: 768px) {
    html {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 14px;
    }
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    width: 100%;
    min-height: 100vh;
    direction: rtl;
}

#root {
    width: 100%;
    min-height: 100vh;
}

/* Responsive Images and Media */
img {
    max-width: 100%;
    height: auto;
}

/* Prevent horizontal scroll */
.overflow-hidden {
    overflow-x: hidden;
}

/* Ensure all sections are responsive */
section {
    width: 100%;
    overflow-x: hidden;
}

/* Contact Form Styles */
.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
    direction: rtl;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-submit {
    text-align: center;
    margin-top: 2rem;
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

/* Responsive Form */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-form {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .contact-form {
        padding: 1.5rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.875rem;
    }
}

/* Support Page Styles */
.search-bar input::placeholder {
    color: rgba(255,255,255,0.7);
}

.category-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.status-item {
    transition: transform 0.3s ease;
}

.status-item:hover {
    transform: translateY(-3px);
}

/* Documentation Page Styles */
.docs-sidebar {
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.docs-content h3 {
    color: var(--primary-color);
    margin: 2rem 0 1rem 0;
    font-size: 1.5rem;
}

.docs-content h4 {
    color: var(--text-dark);
    margin: 1.5rem 0 1rem 0;
    font-size: 1.25rem;
}

.docs-content ul {
    margin: 1rem 0;
    padding-right: 1.5rem;
}

.docs-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.docs-content pre {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.docs-content code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.docs-content pre code {
    background: none;
    padding: 0;
}

/* Responsive Documentation */
@media (max-width: 968px) {
    .docs-page .container > div {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }

    .docs-sidebar {
        position: static;
        max-height: none;
        order: 2;
    }

    .docs-content {
        order: 1;
    }
}

/* About Page Enhancements */
.about-page .feature-card,
.about-page .problem-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-page .feature-card:hover,
.about-page .problem-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

/* General Page Improvements */
.page-header {
    text-align: center;
    padding: 2rem 0;
}

.page-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* Responsive Container */
@media (max-width: 1240px) {
    .container {
        max-width: 100%;
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
}

/* Enhanced Hero Section */
.enhanced-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
    color: var(--white);
    padding: 8rem 0 4rem;
    position: relative;
    overflow: hidden;
}

/* Hero Section (alias for enhanced-hero) */
.hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
    color: var(--white);
    padding: 8rem 0 4rem;
    position: relative;
    overflow: hidden;
}

.enhanced-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

/* Responsive Hero Content */
@media (max-width: 968px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .enhanced-hero, .hero {
        padding: 6rem 0 3rem;
    }
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Typography */
@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-text p {
        font-size: 1rem;
    }
}

.hero-text p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    justify-content: center;
}

/* Responsive Buttons */
@media (max-width: 480px) {
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .btn-hero {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}

.btn-hero {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 12px;
    border: none;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
}

.btn-white {
    background: var(--white);
    color: var(--primary-color);
    box-shadow: var(--shadow-medium);
}

.btn-white:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-large);
}

.btn-outline-white {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-outline-white:hover {
    background: var(--white);
    color: var(--primary-color);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Stats */
@media (max-width: 768px) {
    .hero-stats {
        gap: 1.5rem;
        margin-top: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
}

.stat {
    text-align: center;
    min-width: 120px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 900;
    display: block;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Stats Items */
@media (max-width: 768px) {
    .stat-number {
        font-size: 1.75rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .stat {
        min-width: 100px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

.hero-visual {
    position: relative;
}

.dashboard-mockup {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-large);
    transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
    transition: transform 0.3s ease;
    max-width: 100%;
    overflow: hidden;
}

/* Responsive Dashboard Mockup */
@media (max-width: 968px) {
    .dashboard-mockup {
        transform: none;
        margin: 0 auto;
        max-width: 500px;
    }

    .dashboard-mockup:hover {
        transform: none;
    }
}

@media (max-width: 768px) {
    .dashboard-mockup {
        padding: 1.5rem;
        border-radius: 15px;
    }
}

@media (max-width: 480px) {
    .dashboard-mockup {
        padding: 1rem;
        border-radius: 12px;
    }
}

.dashboard-mockup:hover {
    transform: perspective(1000px) rotateY(-10deg) rotateX(2deg);
}

.mockup-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.mockup-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot-red { background: #ef4444; }
.dot-yellow { background: #f59e0b; }
.dot-green { background: #10b981; }

.mockup-content {
    color: var(--text-dark);
}

.chart-bar {
    height: 8px;
    background: var(--gradient-primary);
    border-radius: 4px;
    margin: 0.5rem 0;
    animation: growWidth 2s ease-in-out infinite alternate;
}

@keyframes growWidth {
    from { width: 60%; }
    to { width: 90%; }
}

/* Enhanced Features */
.enhanced-features {
    padding: 6rem 0;
    background: var(--white);
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 1px solid #e2e8f0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
    background: var(--white);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    background: var(--gradient-accent);
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* Enhanced Stats */
.enhanced-stats {
    padding: 6rem 0;
    background: var(--gradient-primary);
    color: var(--white);
}

.stats-grid {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
}

.enhanced-stat {
    text-align: center;
    padding: 2rem;
}

.enhanced-stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    display: block;
}

.enhanced-stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Enhanced Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Header Enhancements */
.header {
    background: var(--white);
    box-shadow: var(--shadow-soft);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    margin-left: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        gap: 3rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .problems-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .nav-buttons {
        gap: 0.5rem;
    }

    .nav-buttons .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-text h1 {
        font-size: 2.5rem;
        line-height: 1.3;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1rem;
    }

    .dashboard-mockup {
        transform: none;
        max-width: 300px;
        margin: 0 auto;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .stats-grid {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .hero {
        padding: 6rem 0 3rem;
    }

    .enhanced-hero {
        padding: 6rem 0 3rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .problems-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cta h2 {
        font-size: 2rem;
    }

    .cta-form {
        flex-direction: column;
        max-width: 300px;
        gap: 0.5rem;
    }

    .cta-button {
        justify-content: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .btn-hero {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .feature-card,
    .problem-card,
    .story-card,
    .pricing-card {
        padding: 1.5rem;
    }

    .feature-icon,
    .problem-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .cta {
        padding: 4rem 0;
    }

    .cta h2 {
        font-size: 1.8rem;
    }

    .cta p {
        font-size: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pricing Section */
.pricing {
    padding: 6rem 0;
    background: var(--bg-light);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-large);
}

.pricing-card.featured {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'الأكثر شعبية';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-accent);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.plan-price {
    font-size: 3rem;
    font-weight: 900;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.plan-period {
    color: var(--text-light);
    margin-bottom: 2rem;
}

.plan-features {
    list-style: none;
    margin-bottom: 2rem;
}

.plan-features li {
    padding: 0.5rem 0;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.plan-features li i {
    color: var(--accent-color);
}

/* Problems Section */
.problems {
    padding: 6rem 0;
    background: var(--bg-light);
}

.problems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.problem-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.problem-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

.problem-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.problem-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.problem-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.problem-description {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.solution {
    color: var(--accent-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Success Stories */
.success-stories {
    padding: 6rem 0;
    background: var(--white);
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.story-card {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    padding: 2rem;
    border-radius: 16px;
    border-left: 4px solid var(--accent-color);
    transition: all 0.3s ease;
}

.story-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.story-result {
    font-size: 2rem;
    font-weight: 900;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.story-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.story-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.story-company {
    font-weight: 600;
    color: var(--text-dark);
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: var(--gradient-primary);
    color: var(--white);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta h2 {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
}

.cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-form {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto 2rem;
    background: var(--white);
    padding: 0.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-large);
}

.cta-input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    font-family: inherit;
}

.cta-button {
    background: var(--gradient-accent);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: inherit;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.cta-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--accent-color);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
