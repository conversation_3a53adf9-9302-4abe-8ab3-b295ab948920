import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Tab,
  Tabs,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  Campaign,
  Analytics,
  PeopleAlt,
  Timeline,
  Assessment,
  AutoAwesome,
  CheckCircle,
  Warning,
  Info,
  Speed
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`marketmind-tabpanel-${index}`}
      aria-labelledby={`marketmind-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const MarketMind: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [modelsStatus, setModelsStatus] = useState<any>(null);
  const [sentimentTexts, setSentimentTexts] = useState<string>('');

  useEffect(() => {
    checkModelsStatus();
  }, []);

  const checkModelsStatus = async () => {
    try {
      const response = await apiClient.get('/marketmind/status');
      setModelsStatus(response.data);
    } catch (error) {
      console.error('Failed to check models status:', error);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setResult(null);
    setError('');
  };

  const runAnalysis = async (endpoint: string, data: any = {}) => {
    setLoading(true);
    setError('');
    
    try {
      const response = await apiClient.post(`/marketmind/${endpoint}`, data);
      
      if (response.data.success) {
        setResult(response.data.data);
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || `فشل في تشغيل التحليل`);
    } finally {
      setLoading(false);
    }
  };

  const trainAllModels = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await apiClient.post('/marketmind/train-all-models');
      
      if (response.data.success) {
        setResult(response.data.data);
        await checkModelsStatus(); // Refresh models status
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في تدريب النماذج');
    } finally {
      setLoading(false);
    }
  };

  const runSentimentAnalysis = async () => {
    if (!sentimentTexts.trim()) {
      setError('يرجى إدخال النصوص المراد تحليلها');
      return;
    }

    const texts = sentimentTexts.split('\n').filter(text => text.trim());
    await runAnalysis('sentiment-analysis', { texts });
  };

  const renderSegmentationResults = (data: any) => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            معلومات التجزئة
          </Typography>
          <Typography variant="body2">
            <strong>عدد الشرائح:</strong> {data.n_clusters}
          </Typography>
          <Typography variant="body2">
            <strong>نقاط الجودة (Silhouette Score):</strong> {(data.kmeans_silhouette_score * 100).toFixed(1)}%
          </Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            ملفات الشرائح
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(data.segment_profiles || {}).map(([segmentName, profile]: [string, any]) => (
              <Grid item xs={12} md={6} lg={4} key={segmentName}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      {segmentName.replace('segment_', 'الشريحة ')}
                    </Typography>
                    <Typography variant="body2">الحجم: {profile.size} عميل</Typography>
                    <Typography variant="body2">متوسط العمر: {profile.avg_age.toFixed(0)} سنة</Typography>
                    <Typography variant="body2">متوسط الدخل: ${profile.avg_income.toFixed(0)}</Typography>
                    <Typography variant="body2">متوسط الإنفاق: ${profile.avg_total_spent.toFixed(0)}</Typography>
                    <Typography variant="body2">القناة الرئيسية: {profile.top_channel}</Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderChurnResults = (data: any) => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h4" color="error">
            {(data.churn_rate * 100).toFixed(1)}%
          </Typography>
          <Typography variant="body2">معدل التوقف المتوقع</Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h4" color="warning.main">
            {data.high_risk_count}
          </Typography>
          <Typography variant="body2">عملاء عالي المخاطر</Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h4" color="success.main">
            {(data.rf_accuracy * 100).toFixed(1)}%
          </Typography>
          <Typography variant="body2">دقة النموذج</Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            العوامل الرئيسية للتوقف
          </Typography>
          <List>
            {data.top_churn_factors?.slice(0, 5).map(([factor, importance]: [string, number], index: number) => (
              <ListItem key={factor}>
                <ListItemIcon>
                  <Warning color={index < 2 ? 'error' : 'warning'} />
                </ListItemIcon>
                <ListItemText
                  primary={factor}
                  secondary={`أهمية: ${(importance * 100).toFixed(1)}%`}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderSalesResults = (data: any) => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            ملخص التنبؤات
          </Typography>
          <Typography variant="body2">
            <strong>إجمالي المبيعات المتوقعة:</strong> ${data.total_predicted_sales?.toFixed(0)}
          </Typography>
          <Typography variant="body2">
            <strong>متوسط المبيعات اليومية:</strong> ${data.avg_daily_sales?.toFixed(0)}
          </Typography>
          <Typography variant="body2">
            <strong>دقة النموذج (MAE):</strong> ${data.test_mae?.toFixed(0)}
          </Typography>
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            التنبؤات المستقبلية (أول 10 أيام)
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>التاريخ</TableCell>
                  <TableCell align="right">المبيعات المتوقعة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.future_predictions?.slice(0, 10).map((prediction: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell>{prediction.date}</TableCell>
                    <TableCell align="right">${prediction.predicted_sales.toFixed(0)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderSentimentResults = (data: any) => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            توزيع المشاعر
          </Typography>
          {Object.entries(data.sentiment_distribution || {}).map(([sentiment, percentage]: [string, any]) => (
            <Box key={sentiment} sx={{ mb: 1 }}>
              <Typography variant="body2">
                {sentiment === 'positive' ? 'إيجابي' : sentiment === 'negative' ? 'سلبي' : 'محايد'}: {(percentage * 100).toFixed(1)}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={percentage * 100} 
                color={sentiment === 'positive' ? 'success' : sentiment === 'negative' ? 'error' : 'info'}
              />
            </Box>
          ))}
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            نتائج التحليل التفصيلية
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>النص</TableCell>
                  <TableCell>المشاعر</TableCell>
                  <TableCell>الثقة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.results?.map((result: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell>{result.text}</TableCell>
                    <TableCell>
                      <Chip 
                        label={result.sentiment === 'positive' ? 'إيجابي' : result.sentiment === 'negative' ? 'سلبي' : 'محايد'}
                        color={result.sentiment === 'positive' ? 'success' : result.sentiment === 'negative' ? 'error' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{(result.confidence * 100).toFixed(1)}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Psychology color="primary" />
        MarketMind - نماذج الذكاء الاصطناعي المتقدمة
      </Typography>

      <Alert severity="info" sx={{ mb: 2 }}>
        نماذج ذكاء اصطناعي متقدمة لتحليل العملاء والتنبؤ بالمبيعات وتحسين الحملات التسويقية
      </Alert>

      {modelsStatus && (
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              حالة النماذج
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} md={3}>
                <Typography variant="h4" color="primary">
                  {modelsStatus.models_count}
                </Typography>
                <Typography variant="body2">نماذج مدربة</Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Button
                  variant="contained"
                  onClick={trainAllModels}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <AutoAwesome />}
                >
                  {loading ? 'جاري التدريب...' : 'تدريب جميع النماذج'}
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="MarketMind tabs">
            <Tab icon={<PeopleAlt />} label="تجزئة العملاء" />
            <Tab icon={<Warning />} label="التنبؤ بالتوقف" />
            <Tab icon={<TrendingUp />} label="التنبؤ بالمبيعات" />
            <Tab icon={<Psychology />} label="تحليل المشاعر" />
            <Tab icon={<Campaign />} label="تحسين الحملات" />
            <Tab icon={<Timeline />} label="رحلة العميل" />
          </Tabs>
        </Box>

        {/* Customer Segmentation Tab */}
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => runAnalysis('customer-segmentation', { n_clusters: 5, use_sample_data: true })}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <PeopleAlt />}
            >
              {loading ? 'جاري التحليل...' : 'تشغيل تجزئة العملاء'}
            </Button>
          </Box>
          
          {result && renderSegmentationResults(result)}
        </TabPanel>

        {/* Churn Prediction Tab */}
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => runAnalysis('churn-prediction', { use_sample_data: true })}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <Warning />}
            >
              {loading ? 'جاري التحليل...' : 'تشغيل التنبؤ بالتوقف'}
            </Button>
          </Box>
          
          {result && renderChurnResults(result)}
        </TabPanel>

        {/* Sales Prediction Tab */}
        <TabPanel value={activeTab} index={2}>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => runAnalysis('sales-prediction', { periods: 30, use_sample_data: true })}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
            >
              {loading ? 'جاري التحليل...' : 'تشغيل التنبؤ بالمبيعات'}
            </Button>
          </Box>
          
          {result && renderSalesResults(result)}
        </TabPanel>

        {/* Sentiment Analysis Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                multiline
                rows={8}
                label="النصوص المراد تحليلها (نص واحد في كل سطر)"
                value={sentimentTexts}
                onChange={(e) => setSentimentTexts(e.target.value)}
                placeholder="هذا المنتج رائع جداً&#10;الخدمة سيئة للغاية&#10;المنتج عادي لا بأس به"
                sx={{ mb: 2 }}
              />
              
              <Button
                variant="contained"
                onClick={runSentimentAnalysis}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <Psychology />}
                fullWidth
              >
                {loading ? 'جاري التحليل...' : 'تحليل المشاعر'}
              </Button>
            </Grid>
            
            <Grid item xs={12} md={6}>
              {result && renderSentimentResults(result)}
            </Grid>
          </Grid>
        </TabPanel>

        {/* Campaign Optimization Tab */}
        <TabPanel value={activeTab} index={4}>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => runAnalysis('campaign-optimization', { use_sample_data: true })}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <Campaign />}
            >
              {loading ? 'جاري التحليل...' : 'تشغيل تحسين الحملات'}
            </Button>
          </Box>
          
          {result && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    التوصيات
                  </Typography>
                  <List>
                    {result.recommendations?.map((rec: any, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <Info color={rec.impact === 'high' ? 'error' : 'info'} />
                        </ListItemIcon>
                        <ListItemText primary={rec.recommendation} />
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Customer Journey Tab */}
        <TabPanel value={activeTab} index={5}>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => runAnalysis('customer-journey', { use_sample_data: true })}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <Timeline />}
            >
              {loading ? 'جاري التحليل...' : 'تشغيل تحليل رحلة العميل'}
            </Button>
          </Box>
          
          {result && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    ملخص رحلة العميل
                  </Typography>
                  <Typography variant="body2">
                    إجمالي العملاء: {result.summary?.total_customers}
                  </Typography>
                  <Typography variant="body2">
                    متوسط طول الرحلة: {result.summary?.avg_journey_length?.toFixed(1)} خطوة
                  </Typography>
                  <Typography variant="body2">
                    معدل الشراء: {(result.summary?.purchase_rate * 100).toFixed(1)}%
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          )}
        </TabPanel>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default MarketMind;
