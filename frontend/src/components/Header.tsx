import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container
} from '@mui/material';
import { useAuthStore } from '../store/authStore';

const Header: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getDashboardUrl = () => {
    switch (user?.userType) {
      case 'personal':
        return '/personal/dashboard';
      case 'business':
        return '/business/kpis';
      case 'admin':
        return '/admin/dashboard';
      default:
        return '/login';
    }
  };

  return (
    <AppBar position="static" sx={{ bgcolor: 'white', color: 'text.primary', boxShadow: 1 }}>
      <Container maxWidth="lg">
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography 
              variant="h5" 
              component="div" 
              sx={{ 
                fontWeight: 'bold', 
                color: 'primary.main',
                cursor: 'pointer'
              }}
              onClick={() => navigate('/')}
            >
              🎯 MarketMind
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            {isAuthenticated ? (
              <>
                <Typography variant="body2" color="text.secondary">
                  مرحباً، {user?.name || user?.email}
                </Typography>
                <Button 
                  variant="contained" 
                  onClick={() => navigate(getDashboardUrl())}
                >
                  لوحة التحكم
                </Button>
                <Button 
                  variant="outlined" 
                  onClick={handleLogout}
                >
                  تسجيل الخروج
                </Button>
              </>
            ) : (
              <>
                <Button 
                  variant="outlined" 
                  onClick={() => navigate('/login')}
                >
                  تسجيل الدخول
                </Button>
                <Button
                  variant="contained"
                  onClick={() => navigate('/signup')}
                >
                  ابدأ الآن
                </Button>
              </>
            )}
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Header;
