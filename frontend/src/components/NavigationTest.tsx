import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Box, Typography } from '@mui/material';

const NavigationTest: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const testRoutes = [
    { name: 'تجزئة العملاء', path: '/business/customer-segmentation' },
    { name: 'التنبؤ بتوقف العملاء', path: '/business/churn-prediction' },
    { name: 'توليد المحتوى', path: '/business/content-generation' },
    { name: 'تحسين الحملات', path: '/business/campaign-optimization' },
    { name: 'التنبؤ بالمبيعات', path: '/business/sales-prediction' },
    { name: 'رحلة العميل', path: '/business/customer-journey' },
    { name: 'تحليل المشاعر', path: '/business/sentiment-analysis' }
  ];

  const handleNavigate = (path: string, name: string) => {
    console.log(`Attempting to navigate to: ${path} (${name})`);
    try {
      navigate(path);
      console.log(`Navigation successful to: ${path}`);
    } catch (error) {
      console.error(`Navigation failed to: ${path}`, error);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        اختبار التنقل
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 2 }}>
        المسار الحالي: {location.pathname}
      </Typography>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {testRoutes.map((route) => (
          <Button
            key={route.path}
            variant={location.pathname === route.path ? 'contained' : 'outlined'}
            onClick={() => handleNavigate(route.path, route.name)}
            sx={{ justifyContent: 'flex-start' }}
          >
            {route.name} - {route.path}
          </Button>
        ))}
      </Box>
    </Box>
  );
};

export default NavigationTest;
