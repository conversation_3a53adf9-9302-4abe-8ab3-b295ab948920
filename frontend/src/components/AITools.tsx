import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ert,
  Tab,
  <PERSON>bs,
  Card,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  CircularProgress
} from '@mui/material';
import {
  AutoAwesome,
  Psychology,
  TrendingUp,
  Tag,
  Analytics,
  ModelTraining,
  Assessment
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tabpanel-${index}`}
      aria-labelledby={`ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AITools: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');

  // Content Generation State
  const [contentForm, setContentForm] = useState({
    content_type: 'social',
    prompt: '',
    target_audience: 'general',
    tone: 'professional'
  });

  // Sentiment Analysis State
  const [sentimentText, setSentimentText] = useState('');

  // Competitor Analysis State
  const [competitorForm, setCompetitorForm] = useState({
    name: '',
    website: '',
    description: ''
  });

  // Strategy Generation State
  const [strategyForm, setStrategyForm] = useState({
    company_name: '',
    industry: '',
    target_audience: '',
    budget: '',
    goals: ''
  });

  // Hashtag Generation State
  const [hashtagForm, setHashtagForm] = useState({
    content: '',
    platform: 'instagram'
  });

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setResult(null);
    setError('');
  };

  const generateContent = async () => {
    if (!contentForm.prompt.trim()) {
      setError('يرجى إدخال وصف للمحتوى المطلوب');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await apiClient.generateContent(contentForm);
      setResult(response.data);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في إنشاء المحتوى');
    } finally {
      setLoading(false);
    }
  };

  const analyzeSentiment = async () => {
    if (!sentimentText.trim()) {
      setError('يرجى إدخال النص المراد تحليله');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await apiClient.analyzeSentiment({
        text: sentimentText
      });
      setResult(response.data);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في تحليل المشاعر');
    } finally {
      setLoading(false);
    }
  };

  const analyzeCompetitor = async () => {
    if (!competitorForm.name.trim()) {
      setError('يرجى إدخال اسم المنافس');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate competitor analysis since it's not in apiClient
      const mockResult = {
        competitor: competitorForm.name,
        analysis: `تحليل شامل للمنافس ${competitorForm.name}:\n\n- نقاط القوة: ${competitorForm.description || 'غير محدد'}\n- نقاط الضعف: تحتاج إلى مزيد من التحليل\n- التوصيات: التركيز على التميز في الخدمة`
      };
      setResult(mockResult);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في تحليل المنافس');
    } finally {
      setLoading(false);
    }
  };

  const generateStrategy = async () => {
    if (!strategyForm.company_name.trim()) {
      setError('يرجى إدخال اسم الشركة');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate strategy generation since it's not in apiClient
      const mockResult = {
        company: strategyForm.company_name,
        strategy: `استراتيجية تسويقية شاملة لشركة ${strategyForm.company_name}:\n\n- الصناعة: ${strategyForm.industry || 'غير محدد'}\n- الجمهور المستهدف: ${strategyForm.target_audience || 'عام'}\n- الميزانية: ${strategyForm.budget || 'غير محدد'}\n- الأهداف: ${strategyForm.goals || 'زيادة المبيعات'}\n\nالتوصيات:\n1. التركيز على التسويق الرقمي\n2. تطوير استراتيجية محتوى قوية\n3. تحسين تجربة العملاء`
      };
      setResult(mockResult);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في إنشاء الاستراتيجية');
    } finally {
      setLoading(false);
    }
  };

  const generateHashtags = async () => {
    if (!hashtagForm.content.trim()) {
      setError('يرجى إدخال المحتوى');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate hashtag generation since it's not in apiClient
      const mockResult = {
        platform: hashtagForm.platform,
        hashtags: [
          '#تسويق_رقمي',
          '#استراتيجية_تسويقية',
          '#محتوى_إبداعي',
          '#تسويق_اجتماعي',
          '#نمو_الأعمال',
          '#عملاء_سعداء',
          '#نجاح_الأعمال',
          '#تسويق_ذكي'
        ]
      };
      setResult(mockResult);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في إنشاء الهاشتاجات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      p: 3
    }}>
      <Box sx={{
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 3,
        p: 4,
        backdropFilter: 'blur(10px)'
      }}>
        <Typography variant="h3" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <AutoAwesome sx={{ fontSize: 40, color: '#667eea' }} />
          أدوات الذكاء الاصطناعي
        </Typography>

        <Typography variant="h6" sx={{ mb: 3, color: 'text.secondary', textAlign: 'center' }}>
          مجموعة شاملة من أدوات الذكاء الاصطناعي المتقدمة لتحسين استراتيجياتك التسويقية
        </Typography>

        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }}>
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            background: 'linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%)'
          }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="AI tools tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab icon={<AutoAwesome />} label="إنشاء المحتوى" />
              <Tab icon={<Psychology />} label="تحليل المشاعر" />
              <Tab icon={<Analytics />} label="تحليل المنافسين" />
              <Tab icon={<TrendingUp />} label="استراتيجية التسويق" />
              <Tab icon={<Tag />} label="إنشاء هاشتاجات" />
              <Tab icon={<ModelTraining />} label="النماذج المخصصة" />
              <Tab icon={<Assessment />} label="MarketMind AI" />
            </Tabs>
          </Box>

          {/* Content Generation Tab */}
          <TabPanel value={activeTab} index={0}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>نوع المحتوى</InputLabel>
                  <Select
                    value={contentForm.content_type}
                    onChange={(e) => setContentForm({...contentForm, content_type: e.target.value})}
                  >
                    <MenuItem value="social">منشورات وسائل التواصل</MenuItem>
                    <MenuItem value="email">رسائل البريد الإلكتروني</MenuItem>
                    <MenuItem value="blog">مقالات المدونة</MenuItem>
                    <MenuItem value="ad">إعلانات</MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="وصف المحتوى المطلوب"
                  value={contentForm.prompt}
                  onChange={(e) => setContentForm({...contentForm, prompt: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="اكتب وصفاً للمحتوى الذي تريد إنشاءه..."
                />

                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>الجمهور المستهدف</InputLabel>
                  <Select
                    value={contentForm.target_audience}
                    onChange={(e) => setContentForm({...contentForm, target_audience: e.target.value})}
                  >
                    <MenuItem value="general">عام</MenuItem>
                    <MenuItem value="business">أعمال</MenuItem>
                    <MenuItem value="youth">شباب</MenuItem>
                    <MenuItem value="professionals">محترفون</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>نبرة المحتوى</InputLabel>
                  <Select
                    value={contentForm.tone}
                    onChange={(e) => setContentForm({...contentForm, tone: e.target.value})}
                  >
                    <MenuItem value="professional">مهني</MenuItem>
                    <MenuItem value="casual">عادي</MenuItem>
                    <MenuItem value="friendly">ودي</MenuItem>
                    <MenuItem value="formal">رسمي</MenuItem>
                  </Select>
                </FormControl>

                <Button
                  variant="contained"
                  onClick={generateContent}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <AutoAwesome />}
                  fullWidth
                  sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء المحتوى'}
                </Button>
              </Box>

              <Box sx={{ flex: 1, minWidth: 300 }}>
                {result && (
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      المحتوى المُنشأ
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.content}
                    </Typography>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Sentiment Analysis Tab */}
          <TabPanel value={activeTab} index={1}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="النص المراد تحليله"
                  value={sentimentText}
                  onChange={(e) => setSentimentText(e.target.value)}
                  sx={{ mb: 2 }}
                  placeholder="أدخل النص الذي تريد تحليل مشاعره..."
                />

                <Button
                  variant="contained"
                  onClick={analyzeSentiment}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Psychology />}
                  fullWidth
                  sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التحليل...' : 'تحليل المشاعر'}
                </Button>
              </Box>

              <Box sx={{ flex: 1, minWidth: 300 }}>
                {result && (
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      نتائج تحليل المشاعر
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Chip
                        label={result.sentiment === 'positive' ? 'إيجابي' : 'سلبي'}
                        color={result.sentiment === 'positive' ? 'success' : 'error'}
                        sx={{ mr: 2 }}
                      />
                      <Typography>النتيجة: {result.confidence}%</Typography>
                    </Box>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Competitor Analysis Tab */}
          <TabPanel value={activeTab} index={2}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <TextField
                  fullWidth
                  label="اسم المنافس"
                  value={competitorForm.name}
                  onChange={(e) => setCompetitorForm({...competitorForm, name: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="أدخل اسم المنافس..."
                />

                <TextField
                  fullWidth
                  label="الموقع الإلكتروني"
                  value={competitorForm.website}
                  onChange={(e) => setCompetitorForm({...competitorForm, website: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="مثال: www.example.com"
                />

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="وصف المنافس"
                  value={competitorForm.description}
                  onChange={(e) => setCompetitorForm({...competitorForm, description: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="أدخل معلومات عن المنافس..."
                />

                <Button
                  variant="contained"
                  onClick={analyzeCompetitor}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Analytics />}
                  fullWidth
                  sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التحليل...' : 'تحليل المنافس'}
                </Button>
              </Box>

              <Box sx={{ flex: 1, minWidth: 300 }}>
                {result && (
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      نتائج تحليل المنافس
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.analysis}
                    </Typography>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Strategy Generation Tab */}
          <TabPanel value={activeTab} index={3}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <TextField
                  fullWidth
                  label="اسم الشركة"
                  value={strategyForm.company_name}
                  onChange={(e) => setStrategyForm({...strategyForm, company_name: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="أدخل اسم الشركة..."
                />

                <TextField
                  fullWidth
                  label="نوع النشاط"
                  value={strategyForm.industry}
                  onChange={(e) => setStrategyForm({...strategyForm, industry: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="مثال: التجارة الإلكترونية..."
                />

                <TextField
                  fullWidth
                  label="الجمهور المستهدف"
                  value={strategyForm.target_audience}
                  onChange={(e) => setStrategyForm({...strategyForm, target_audience: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="مثال: الشباب من 18-35 سنة..."
                />

                <TextField
                  fullWidth
                  label="الميزانية التسويقية"
                  value={strategyForm.budget}
                  onChange={(e) => setStrategyForm({...strategyForm, budget: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="مثال: 10,000 ريال شهرياً"
                />

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="الأهداف"
                  value={strategyForm.goals}
                  onChange={(e) => setStrategyForm({...strategyForm, goals: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="مثال: زيادة المبيعات بنسبة 30%..."
                />

                <Button
                  variant="contained"
                  onClick={generateStrategy}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <TrendingUp />}
                  fullWidth
                  sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء الاستراتيجية'}
                </Button>
              </Box>

              <Box sx={{ flex: 1, minWidth: 300 }}>
                {result && (
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      استراتيجية التسويق
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.strategy}
                    </Typography>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Hashtag Generation Tab */}
          <TabPanel value={activeTab} index={4}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>المنصة</InputLabel>
                  <Select
                    value={hashtagForm.platform}
                    onChange={(e) => setHashtagForm({...hashtagForm, platform: e.target.value})}
                  >
                    <MenuItem value="instagram">Instagram</MenuItem>
                    <MenuItem value="twitter">Twitter</MenuItem>
                    <MenuItem value="linkedin">LinkedIn</MenuItem>
                    <MenuItem value="facebook">Facebook</MenuItem>
                    <MenuItem value="tiktok">TikTok</MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="المحتوى"
                  value={hashtagForm.content}
                  onChange={(e) => setHashtagForm({...hashtagForm, content: e.target.value})}
                  sx={{ mb: 2 }}
                  placeholder="أدخل المحتوى الذي تريد إنشاء هاشتاجات له..."
                />

                <Button
                  variant="contained"
                  onClick={generateHashtags}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Tag />}
                  fullWidth
                  sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء الهاشتاجات'}
                </Button>
              </Box>

              <Box sx={{ flex: 1, minWidth: 300 }}>
                {result && (
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      الهاشتاجات المقترحة
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {result.hashtags?.map((hashtag: string, index: number) => (
                        <Chip
                          key={index}
                          label={hashtag}
                          variant="outlined"
                          size="small"
                          onClick={() => navigator.clipboard.writeText(hashtag)}
                          sx={{ cursor: 'pointer' }}
                        />
                      ))}
                    </Box>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Custom AI Models Tab */}
          <TabPanel value={activeTab} index={5}>
            <Typography variant="h6">النماذج المخصصة - قريباً</Typography>
            <Typography variant="body1" color="text.secondary">
              ستتمكن قريباً من إنشاء وتدريب نماذج ذكاء اصطناعي مخصصة لاحتياجاتك الخاصة.
            </Typography>
          </TabPanel>

          {/* MarketMind AI Tab */}
          <TabPanel value={activeTab} index={6}>
            <Typography variant="h6">MarketMind AI - قريباً</Typography>
            <Typography variant="body1" color="text.secondary">
              مجموعة متقدمة من نماذج الذكاء الاصطناعي المخصصة للتسويق والتحليل.
            </Typography>
          </TabPanel>
        </Card>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Box>
    </Box>
  );
};

export default AITools;
