import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { Box, CircularProgress, Typography } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredUserType?: 'personal' | 'business' | 'admin';
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredUserType,
  redirectTo = '/login'
}) => {
  const { isAuthenticated, user, isLoading } = useAuthStore();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column"
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        gap={2}
        sx={{
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
        }}
      >
        <CircularProgress size={60} sx={{ color: '#3b82f6' }} />
        <Typography variant="h6" color="text.secondary">
          جاري التحقق من صحة تسجيل الدخول...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    console.log('User not authenticated, redirecting to login');
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if user has required user type
  if (requiredUserType && user.userType !== requiredUserType) {
    console.log(`User type ${user.userType} does not match required ${requiredUserType}`);
    // Redirect to appropriate dashboard based on user type
    const dashboardUrl = getDashboardUrl(user.userType);
    console.log('Redirecting to dashboard:', dashboardUrl);
    return <Navigate to={dashboardUrl} replace />;
  }

  console.log('Rendering protected route for user:', user.userType);
  return <>{children}</>;
};

// Helper function to get dashboard URL
const getDashboardUrl = (userType: string) => {
  switch (userType) {
    case 'personal':
      return '/personal/dashboard';
    case 'business':
      return '/business';
    case 'admin':
      return '/admin';
    default:
      return '/login';
  }
};

export default ProtectedRoute;
