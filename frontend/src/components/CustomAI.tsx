import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  Paper,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Psychology,
  ModelTraining,
  Computer,
  Delete,
  Add,
  PlayArrow,
  Assessment
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`custom-ai-tabpanel-${index}`}
      aria-labelledby={`custom-ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CustomAI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [models, setModels] = useState<any[]>([]);
  const [deleteDialog, setDeleteDialog] = useState<{open: boolean, modelName: string}>({open: false, modelName: ''});

  // Model creation state
  const [newModel, setNewModel] = useState({
    name: '',
    type: 'sentiment',
    trainingData: ''
  });

  // Prediction state
  const [prediction, setPrediction] = useState({
    modelName: '',
    text: ''
  });

  // Ollama state
  const [ollama, setOllama] = useState({
    prompt: '',
    modelName: 'llama3.2'
  });

  useEffect(() => {
    loadModels();
    checkCustomAIStatus();
  }, []);

  const loadModels = async () => {
    try {
      const response = await apiClient.get('/custom-ai/models');
      if (response.data.success) {
        setModels(Object.entries(response.data.data).map(([name, info]: [string, any]) => ({
          name,
          ...info
        })));
      }
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const checkCustomAIStatus = async () => {
    try {
      const response = await apiClient.get('/custom-ai/status');
      console.log('Custom AI Status:', response.data);
    } catch (error) {
      console.error('Failed to check status:', error);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setResult(null);
    setError('');
  };

  const createDemoModel = async (type: string) => {
    setLoading(true);
    setError('');
    
    try {
      const endpoint = type === 'sentiment' ? '/custom-ai/train-sentiment-demo' : '/custom-ai/train-content-demo';
      const response = await apiClient.post(endpoint);
      
      if (response.data.success) {
        setResult(response.data.data);
        await loadModels(); // Reload models list
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || `فشل في إنشاء النموذج التجريبي`);
    } finally {
      setLoading(false);
    }
  };

  const createCustomModel = async () => {
    if (!newModel.name || !newModel.trainingData) {
      setError('يرجى إدخال اسم النموذج وبيانات التدريب');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      // Parse training data
      const lines = newModel.trainingData.split('\n').filter(line => line.trim());
      const trainingData = lines.map(line => {
        const parts = line.split('|');
        if (parts.length !== 2) {
          throw new Error('تنسيق البيانات غير صحيح. استخدم: النص|التصنيف');
        }
        
        const data: any = { text: parts[0].trim() };
        if (newModel.type === 'sentiment') {
          data.sentiment = parts[1].trim();
        } else {
          data.category = parts[1].trim();
        }
        return data;
      });

      const response = await apiClient.post('/custom-ai/create-model', {
        model_name: newModel.name,
        model_type: newModel.type,
        training_data: trainingData
      });
      
      if (response.data.success) {
        setResult(response.data.data);
        setNewModel({ name: '', type: 'sentiment', trainingData: '' });
        await loadModels();
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || error.message || 'فشل في إنشاء النموذج');
    } finally {
      setLoading(false);
    }
  };

  const makePrediction = async () => {
    if (!prediction.modelName || !prediction.text) {
      setError('يرجى اختيار نموذج وإدخال النص');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const response = await apiClient.post('/custom-ai/predict', prediction);
      
      if (response.data.success) {
        setResult(response.data.data);
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في التنبؤ');
    } finally {
      setLoading(false);
    }
  };

  const deleteModel = async (modelName: string) => {
    try {
      const response = await apiClient.delete(`/custom-ai/models/${modelName}`);
      
      if (response.data.success) {
        await loadModels();
        setDeleteDialog({open: false, modelName: ''});
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في حذف النموذج');
    }
  };

  const generateWithOllama = async () => {
    if (!ollama.prompt) {
      setError('يرجى إدخال النص المطلوب');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const response = await apiClient.post('/custom-ai/ollama/generate', ollama);
      
      if (response.data.success) {
        setResult(response.data.data);
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || 'فشل في استخدام Ollama');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <ModelTraining color="primary" />
        النماذج المخصصة
      </Typography>

      <Alert severity="info" sx={{ mb: 2 }}>
        قم بإنشاء وتدريب نماذج ذكاء اصطناعي مخصصة لاحتياجاتك الخاصة
      </Alert>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="Custom AI tabs">
            <Tab icon={<Add />} label="إنشاء نموذج" />
            <Tab icon={<PlayArrow />} label="استخدام النماذج" />
            <Tab icon={<Assessment />} label="إدارة النماذج" />
            <Tab icon={<Computer />} label="Ollama المحلي" />
          </Tabs>
        </Box>

        {/* Create Model Tab */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                إنشاء نموذج جديد
              </Typography>
              
              <TextField
                fullWidth
                label="اسم النموذج"
                value={newModel.name}
                onChange={(e) => setNewModel({...newModel, name: e.target.value})}
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>نوع النموذج</InputLabel>
                <Select
                  value={newModel.type}
                  onChange={(e) => setNewModel({...newModel, type: e.target.value})}
                >
                  <MenuItem value="sentiment">تحليل المشاعر</MenuItem>
                  <MenuItem value="content_classification">تصنيف المحتوى</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={8}
                label="بيانات التدريب"
                value={newModel.trainingData}
                onChange={(e) => setNewModel({...newModel, trainingData: e.target.value})}
                placeholder="النص|التصنيف&#10;مثال: هذا المنتج رائع|positive&#10;هذا المنتج سيء|negative"
                sx={{ mb: 2 }}
              />

              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Button
                  variant="contained"
                  onClick={createCustomModel}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <ModelTraining />}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء النموذج'}
                </Button>
              </Box>

              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                نماذج تجريبية
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  onClick={() => createDemoModel('sentiment')}
                  disabled={loading}
                >
                  نموذج تحليل مشاعر تجريبي
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => createDemoModel('content')}
                  disabled={loading}
                >
                  نموذج تصنيف محتوى تجريبي
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              {result && (
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    نتيجة إنشاء النموذج
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>اسم النموذج:</strong> {result.model_name}
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>الدقة:</strong> {(result.accuracy * 100).toFixed(1)}%
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>عينات التدريب:</strong> {result.training_samples}
                  </Typography>
                  
                  {result.categories && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        <strong>الفئات:</strong>
                      </Typography>
                      {result.categories.map((category: string, index: number) => (
                        <Chip key={index} label={category} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                      ))}
                    </Box>
                  )}
                </Paper>
              )}
            </Grid>
          </Grid>
        </TabPanel>

        {/* Use Models Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>اختر النموذج</InputLabel>
                <Select
                  value={prediction.modelName}
                  onChange={(e) => setPrediction({...prediction, modelName: e.target.value})}
                >
                  {models.map((model) => (
                    <MenuItem key={model.name} value={model.name}>
                      {model.name} ({model.type})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={4}
                label="النص للتحليل"
                value={prediction.text}
                onChange={(e) => setPrediction({...prediction, text: e.target.value})}
                sx={{ mb: 2 }}
              />

              <Button
                variant="contained"
                onClick={makePrediction}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <Psychology />}
                fullWidth
              >
                {loading ? 'جاري التحليل...' : 'تحليل النص'}
              </Button>
            </Grid>

            <Grid item xs={12} md={6}>
              {result && result.prediction && (
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    نتيجة التحليل
                  </Typography>
                  
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    <strong>التنبؤ:</strong> {result.prediction}
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>الثقة:</strong> {(result.confidence * 100).toFixed(1)}%
                  </Typography>
                  
                  {result.probabilities && Object.keys(result.probabilities).length > 0 && (
                    <Box>
                      <Typography variant="body2" gutterBottom>
                        <strong>الاحتماليات:</strong>
                      </Typography>
                      {Object.entries(result.probabilities).map(([label, prob]: [string, any]) => (
                        <Typography key={label} variant="body2">
                          {label}: {(prob * 100).toFixed(1)}%
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Paper>
              )}
            </Grid>
          </Grid>
        </TabPanel>

        {/* Manage Models Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            النماذج المتاحة ({models.length})
          </Typography>
          
          <List>
            {models.map((model) => (
              <ListItem key={model.name}>
                <ListItemText
                  primary={model.name}
                  secondary={`النوع: ${model.type} | الدقة: ${(model.accuracy * 100).toFixed(1)}% | العينات: ${model.training_samples}`}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => setDeleteDialog({open: true, modelName: model.name})}
                  >
                    <Delete />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          
          {models.length === 0 && (
            <Typography variant="body2" color="text.secondary">
              لا توجد نماذج مخصصة. قم بإنشاء نموذج جديد من التبويب الأول.
            </Typography>
          )}
        </TabPanel>

        {/* Ollama Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Ollama - النماذج المحلية
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                استخدم نماذج الذكاء الاصطناعي المحلية بدون إنترنت
              </Alert>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>النموذج</InputLabel>
                <Select
                  value={ollama.modelName}
                  onChange={(e) => setOllama({...ollama, modelName: e.target.value})}
                >
                  <MenuItem value="llama3.2">Llama 3.2</MenuItem>
                  <MenuItem value="mistral">Mistral</MenuItem>
                  <MenuItem value="codellama">CodeLlama</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={6}
                label="النص المطلوب"
                value={ollama.prompt}
                onChange={(e) => setOllama({...ollama, prompt: e.target.value})}
                sx={{ mb: 2 }}
                placeholder="اكتب مقال عن فوائد التسويق الرقمي..."
              />

              <Button
                variant="contained"
                onClick={generateWithOllama}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <Computer />}
                fullWidth
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء بالنموذج المحلي'}
              </Button>
            </Grid>

            <Grid item xs={12} md={6}>
              {result && result.content && (
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    المحتوى المُنشأ
                  </Typography>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {result.content}
                  </Typography>
                </Paper>
              )}
            </Grid>
          </Grid>
        </TabPanel>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({open: false, modelName: ''})}>
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          هل أنت متأكد من حذف النموذج "{deleteDialog.modelName}"؟
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({open: false, modelName: ''})}>
            إلغاء
          </Button>
          <Button onClick={() => deleteModel(deleteDialog.modelName)} color="error">
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default CustomAI;
