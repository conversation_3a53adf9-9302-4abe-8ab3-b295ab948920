import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Paper,
  Checkbox,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Box,
  Typography,
  TextField,
  InputAdornment
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { 
  MoreVert, 
  Search, 
  FilterList,
  GetApp,
  Visibility
} from '@mui/icons-material';
import { theme } from '../../theme';

// الجدول المحسن مع تأثيرات تفاعلية
const StyledTableContainer = styled(TableContainer)({
  backgroundColor: theme.colors.background.primary,
  borderRadius: theme.spacing.border.radius.xl,
  boxShadow: theme.effects.shadows.card,
  border: `1px solid ${theme.colors.border.light}`,
  overflow: 'hidden'
});

const StyledTable = styled(Table)({
  minWidth: 650
});

const StyledTableHead = styled(TableHead)({
  backgroundColor: theme.colors.background.secondary,
  
  '& .MuiTableCell-head': {
    backgroundColor: 'transparent',
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    fontSize: theme.typography.fontSize.sm,
    padding: theme.spacing.component.table.headerPadding,
    borderBottom: `2px solid ${theme.colors.border.medium}`,
    position: 'relative',
    
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: '2px',
      background: theme.effects.gradients.primary,
      transform: 'scaleX(0)',
      transition: theme.effects.transitions.common.transform,
      transformOrigin: 'left'
    },
    
    '&:hover::after': {
      transform: 'scaleX(1)'
    }
  }
});

const StyledTableRow = styled(TableRow)({
  transition: theme.effects.transitions.common.colors,
  cursor: 'pointer',
  
  '&:hover': {
    backgroundColor: theme.colors.background.tertiary,
    transform: 'translateX(2px)'
  },
  
  '&.selected': {
    backgroundColor: theme.colors.primary[50],
    
    '&:hover': {
      backgroundColor: theme.colors.primary[100]
    }
  },
  
  '& .MuiTableCell-body': {
    padding: theme.spacing.component.table.cellPadding,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.text.secondary,
    borderBottom: `1px solid ${theme.colors.border.light}`,
    transition: theme.effects.transitions.common.colors
  }
});

const TableToolbar = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing.base.lg,
  backgroundColor: theme.colors.background.primary,
  borderBottom: `1px solid ${theme.colors.border.light}`
});

const SearchField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing.border.radius.lg,
    backgroundColor: theme.colors.background.secondary,
    
    '& fieldset': {
      borderColor: theme.colors.border.light
    },
    
    '&:hover fieldset': {
      borderColor: theme.colors.border.medium
    },
    
    '&.Mui-focused fieldset': {
      borderColor: theme.colors.primary[500],
      boxShadow: `0 0 0 3px ${theme.colors.interactive.focus.primary}`
    }
  }
});

const ActionButton = styled(IconButton)({
  backgroundColor: theme.colors.background.secondary,
  borderRadius: theme.spacing.border.radius.md,
  transition: theme.effects.transitions.component.button,
  
  '&:hover': {
    backgroundColor: theme.colors.primary[500],
    color: theme.colors.text.inverse,
    transform: theme.effects.transforms.scale.hover
  }
});

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string | React.ReactNode;
}

interface EnhancedTableProps {
  columns: Column[];
  data: any[];
  title?: string;
  selectable?: boolean;
  searchable?: boolean;
  filterable?: boolean;
  exportable?: boolean;
  onRowClick?: (row: any, index: number) => void;
  onSelectionChange?: (selectedRows: any[]) => void;
  actions?: (row: any, index: number) => React.ReactNode;
  emptyMessage?: string;
  loading?: boolean;
}

const EnhancedTable: React.FC<EnhancedTableProps> = ({
  columns,
  data,
  title,
  selectable = false,
  searchable = true,
  filterable = false,
  exportable = false,
  onRowClick,
  onSelectionChange,
  actions,
  emptyMessage = 'لا توجد بيانات للعرض',
  loading = false
}) => {
  const [selected, setSelected] = useState<number[]>([]);
  const [orderBy, setOrderBy] = useState<string>('');
  const [order, setOrder] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // معالجة الترتيب
  const handleSort = (columnId: string) => {
    const isAsc = orderBy === columnId && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(columnId);
  };

  // معالجة التحديد
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = data.map((_, index) => index);
      setSelected(newSelected);
      onSelectionChange?.(data);
    } else {
      setSelected([]);
      onSelectionChange?.([]);
    }
  };

  const handleSelectRow = (index: number) => {
    const selectedIndex = selected.indexOf(index);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, index);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }

    setSelected(newSelected);
    onSelectionChange?.(newSelected.map(i => data[i]));
  };

  // فلترة البيانات
  const filteredData = data.filter(row =>
    searchTerm === '' || 
    Object.values(row).some(value => 
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // ترتيب البيانات
  const sortedData = [...filteredData].sort((a, b) => {
    if (orderBy === '') return 0;
    
    const aValue = a[orderBy];
    const bValue = b[orderBy];
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return order === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    const aString = String(aValue).toLowerCase();
    const bString = String(bValue).toLowerCase();
    
    if (order === 'asc') {
      return aString < bString ? -1 : aString > bString ? 1 : 0;
    } else {
      return aString > bString ? -1 : aString < bString ? 1 : 0;
    }
  });

  const isSelected = (index: number) => selected.indexOf(index) !== -1;

  return (
    <Paper elevation={0}>
      {/* شريط الأدوات */}
      <TableToolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {title && (
            <Typography variant="h6" sx={{ 
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary
            }}>
              {title}
            </Typography>
          )}
          
          {selected.length > 0 && (
            <Chip
              label={`${selected.length} محدد`}
              color="primary"
              size="small"
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {searchable && (
            <SearchField
              size="small"
              placeholder="البحث..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: theme.colors.text.tertiary }} />
                  </InputAdornment>
                )
              }}
            />
          )}

          {filterable && (
            <ActionButton size="small">
              <FilterList />
            </ActionButton>
          )}

          {exportable && (
            <ActionButton size="small">
              <GetApp />
            </ActionButton>
          )}
        </Box>
      </TableToolbar>

      {/* الجدول */}
      <StyledTableContainer>
        <StyledTable>
          <StyledTableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < data.length}
                    checked={data.length > 0 && selected.length === data.length}
                    onChange={handleSelectAll}
                    color="primary"
                  />
                </TableCell>
              )}
              
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? order : 'asc'}
                      onClick={() => handleSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
              
              {actions && (
                <TableCell align="center">الإجراءات</TableCell>
              )}
            </TableRow>
          </StyledTableHead>
          
          <TableBody>
            {sortedData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={columns.length + (selectable ? 1 : 0) + (actions ? 1 : 0)}
                  align="center"
                  sx={{ py: 4 }}
                >
                  <Typography color="textSecondary">
                    {emptyMessage}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              sortedData.map((row, index) => {
                const isItemSelected = isSelected(index);
                
                return (
                  <StyledTableRow
                    key={index}
                    className={isItemSelected ? 'selected' : ''}
                    onClick={() => onRowClick?.(row, index)}
                  >
                    {selectable && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={isItemSelected}
                          onChange={() => handleSelectRow(index)}
                          color="primary"
                        />
                      </TableCell>
                    )}
                    
                    {columns.map((column) => (
                      <TableCell key={column.id} align={column.align || 'left'}>
                        {column.format ? column.format(row[column.id]) : row[column.id]}
                      </TableCell>
                    ))}
                    
                    {actions && (
                      <TableCell align="center">
                        {actions(row, index)}
                      </TableCell>
                    )}
                  </StyledTableRow>
                );
              })
            )}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
    </Paper>
  );
};

export default EnhancedTable;
