import React from 'react';
import { Box, Card, CardContent, Typography, Chip } from '@mui/material';
import { styled } from '@mui/material/styles';
import { TrendingUp, TrendingDown } from '@mui/icons-material';
import { theme } from '../../theme';

// البطاقة المحسنة مع تأثيرات تفاعلية
const StyledCard = styled(Card)(({ theme: muiTheme }) => ({
  position: 'relative',
  backgroundColor: theme.colors.background.primary,
  borderRadius: theme.spacing.border.radius.xl,
  boxShadow: theme.effects.shadows.card,
  border: `1px solid ${theme.colors.border.light}`,
  transition: theme.effects.transitions.component.card,
  overflow: 'hidden',
  cursor: 'pointer',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: theme.effects.gradients.primary,
    opacity: 0,
    transition: theme.effects.transitions.common.opacity
  },

  '&:hover': {
    boxShadow: theme.effects.shadows.cardHover,
    transform: theme.effects.transforms.translate.y.sm,
    
    '&::before': {
      opacity: 1
    }
  },

  '&:active': {
    transform: theme.effects.transforms.scale.active
  }
}));

const CardHeader = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  marginBottom: theme.spacing.base.md,
  padding: `${theme.spacing.base.lg} ${theme.spacing.base.lg} 0`
});

const CardBody = styled(CardContent)({
  padding: `0 ${theme.spacing.base.lg} ${theme.spacing.base.lg}`,
  '&:last-child': {
    paddingBottom: theme.spacing.base.lg
  }
});

const ValueContainer = styled(Box)({
  display: 'flex',
  alignItems: 'baseline',
  gap: theme.spacing.base.sm,
  marginBottom: theme.spacing.base.sm
});

const MainValue = styled(Typography)({
  fontSize: theme.typography.fontSize['4xl'],
  fontWeight: theme.typography.fontWeight.bold,
  lineHeight: theme.typography.lineHeight.tight,
  color: theme.colors.text.primary,
  fontFamily: theme.typography.fontFamily.heading
});

const TrendContainer = styled(Box)<{ trend: 'up' | 'down' | 'neutral' }>(({ trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing.fine['1'],
  padding: `${theme.spacing.fine['1']} ${theme.spacing.fine['2']}`,
  borderRadius: theme.spacing.border.radius.md,
  backgroundColor: 
    trend === 'up' ? theme.colors.success[50] :
    trend === 'down' ? theme.colors.error[50] :
    theme.colors.gray[50],
  color:
    trend === 'up' ? theme.colors.success[700] :
    trend === 'down' ? theme.colors.error[700] :
    theme.colors.gray[700]
}));

const SubtitleText = styled(Typography)({
  fontSize: theme.typography.fontSize.sm,
  color: theme.colors.text.secondary,
  fontWeight: theme.typography.fontWeight.medium
});

const DescriptionText = styled(Typography)({
  fontSize: theme.typography.fontSize.sm,
  color: theme.colors.text.tertiary,
  lineHeight: theme.typography.lineHeight.relaxed
});

interface EnhancedCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  description?: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    label?: string;
  };
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  variant?: 'default' | 'elevated' | 'glass' | 'gradient';
  onClick?: () => void;
  actions?: React.ReactNode;
  badge?: {
    label: string;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  };
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  title,
  value,
  subtitle,
  description,
  trend,
  icon,
  color = 'primary',
  variant = 'default',
  onClick,
  actions,
  badge
}) => {
  const getCardStyles = () => {
    const baseStyles = {};
    
    switch (variant) {
      case 'elevated':
        return {
          ...baseStyles,
          boxShadow: theme.effects.shadows.lg,
          '&:hover': {
            boxShadow: theme.effects.shadows.xl
          }
        };
      
      case 'glass':
        return {
          ...baseStyles,
          background: theme.effects.glass.background,
          backdropFilter: theme.effects.glass.backdropFilter,
          border: theme.effects.glass.border
        };
      
      case 'gradient':
        return {
          ...baseStyles,
          background: theme.effects.gradients[color],
          color: theme.colors.text.inverse,
          '& .MuiTypography-root': {
            color: 'inherit'
          }
        };
      
      default:
        return baseStyles;
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    switch (trend.direction) {
      case 'up':
        return <TrendingUp sx={{ fontSize: '1rem' }} />;
      case 'down':
        return <TrendingDown sx={{ fontSize: '1rem' }} />;
      default:
        return null;
    }
  };

  return (
    <StyledCard 
      onClick={onClick}
      sx={getCardStyles()}
    >
      <CardHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {icon && (
            <Box sx={{ 
              color: theme.colors[color][500],
              display: 'flex',
              alignItems: 'center'
            }}>
              {icon}
            </Box>
          )}
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.semibold,
                color: variant === 'gradient' ? 'inherit' : theme.colors.text.primary,
                marginBottom: 0
              }}
            >
              {title}
            </Typography>
            {badge && (
              <Chip
                label={badge.label}
                size="small"
                color={badge.color || 'primary'}
                sx={{ 
                  mt: 0.5,
                  height: '20px',
                  fontSize: '0.75rem'
                }}
              />
            )}
          </Box>
        </Box>
        
        {actions && (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {actions}
          </Box>
        )}
      </CardHeader>

      <CardBody>
        <ValueContainer>
          <MainValue>
            {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}
          </MainValue>
          
          {trend && (
            <TrendContainer trend={trend.direction}>
              {getTrendIcon()}
              <Typography
                variant="caption"
                sx={{
                  fontSize: theme.typography.fontSize.xs,
                  fontWeight: theme.typography.fontWeight.medium
                }}
              >
                {trend.value}
              </Typography>
            </TrendContainer>
          )}
        </ValueContainer>

        {subtitle && (
          <SubtitleText gutterBottom>
            {subtitle}
          </SubtitleText>
        )}

        {description && (
          <DescriptionText>
            {description}
          </DescriptionText>
        )}

        {trend?.label && (
          <Typography
            variant="caption"
            sx={{
              fontSize: theme.typography.fontSize.xs,
              color: theme.colors.text.tertiary,
              mt: 1,
              display: 'block'
            }}
          >
            {trend.label}
          </Typography>
        )}
      </CardBody>
    </StyledCard>
  );
};

export default EnhancedCard;
