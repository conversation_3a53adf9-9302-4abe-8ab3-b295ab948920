import React from 'react';
import { Box, CircularProgress, LinearProgress, Skeleton, Typography } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { theme } from '../../theme';

// رسوم متحركة مخصصة
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
`;

// مؤشر التحميل الدائري المحسن
const StyledCircularProgress = styled(CircularProgress)<{ variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' }>(({ variant = 'primary' }) => ({
  color: theme.colors[variant][500],
  animation: `${spin} 1s linear infinite`,
  
  '& .MuiCircularProgress-circle': {
    strokeLinecap: 'round'
  }
}));

// مؤشر التحميل الخطي المحسن
const StyledLinearProgress = styled(LinearProgress)<{ variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' }>(({ variant = 'primary' }) => ({
  height: '6px',
  borderRadius: theme.spacing.border.radius.full,
  backgroundColor: theme.colors[variant][100],
  
  '& .MuiLinearProgress-bar': {
    backgroundColor: theme.colors[variant][500],
    borderRadius: theme.spacing.border.radius.full
  }
}));

// حاوية الرسوم المتحركة
const AnimatedContainer = styled(Box)<{ 
  animation?: 'fadeIn' | 'slideInRight' | 'pulse' | 'bounce';
  delay?: number;
  duration?: number;
}>(({ animation = 'fadeIn', delay = 0, duration = 0.3 }) => {
  const animationMap = {
    fadeIn,
    slideInRight,
    pulse,
    bounce
  };

  return {
    animation: `${animationMap[animation]} ${duration}s ease-out ${delay}s both`
  };
});

// هيكل التحميل المحسن
const StyledSkeleton = styled(Skeleton)({
  borderRadius: theme.spacing.border.radius.lg,
  backgroundColor: theme.colors.gray[200],
  
  '&::after': {
    background: `linear-gradient(90deg, transparent, ${theme.colors.gray[100]}, transparent)`,
    animation: `${shimmer} 1.5s ease-in-out infinite`
  }
});

// نقاط التحميل المتحركة
const DotsContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing.fine['1']
});

const Dot = styled(Box)<{ delay: number }>(({ delay }) => ({
  width: '8px',
  height: '8px',
  borderRadius: '50%',
  backgroundColor: theme.colors.primary[500],
  animation: `${bounce} 1.4s ease-in-out ${delay}s infinite both`
}));

// مؤشر التحميل مع النص
const LoadingContainer = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing.base.md,
  padding: theme.spacing.base.xl,
  textAlign: 'center'
});

// مكونات التحميل
interface LoadingSpinnerProps {
  size?: number;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  thickness?: number;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 40,
  color = 'primary',
  thickness = 4
}) => (
  <StyledCircularProgress
    size={size}
    thickness={thickness}
    variant={color}
  />
);

interface LoadingBarProps {
  value: number;
  variant?: 'determinate' | 'indeterminate';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  animated?: boolean;
}

export const LoadingBar: React.FC<LoadingBarProps> = ({
  value,
  color = 'primary',
  variant = 'indeterminate',
  size = 'medium',
  showLabel = true,
  animated = true
}) => (
  <StyledLinearProgress
    variant={variant}
    value={value}
    color={color}
  />
);

interface LoadingDotsProps {
  color?: string;
  size?: number;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({
  color = theme.colors.primary[500],
  size = 8
}) => (
  <DotsContainer>
    {[0, 0.2, 0.4].map((delay, index) => (
      <Dot
        key={index}
        delay={delay}
        sx={{
          width: `${size}px`,
          height: `${size}px`,
          backgroundColor: color
        }}
      />
    ))}
  </DotsContainer>
);

interface LoadingOverlayProps {
  loading: boolean;
  children: React.ReactNode;
  message?: string;
  backdrop?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  loading,
  children,
  message = 'جاري التحميل...',
  backdrop = true
}) => (
  <Box sx={{ position: 'relative' }}>
    {children}
    {loading && (
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: backdrop ? 'rgba(255, 255, 255, 0.8)' : 'transparent',
          backdropFilter: backdrop ? 'blur(2px)' : 'none',
          zIndex: 1000,
          borderRadius: 'inherit'
        }}
      >
        <LoadingContainer>
          <LoadingSpinner />
          <Typography
            variant="body2"
            sx={{
              color: theme.colors.text.secondary,
              fontWeight: theme.typography.fontWeight.medium
            }}
          >
            {message}
          </Typography>
        </LoadingContainer>
      </Box>
    )}
  </Box>
);

// مكونات الرسوم المتحركة
interface AnimatedBoxProps {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideInRight' | 'pulse' | 'bounce';
  delay?: number;
  duration?: number;
  trigger?: boolean;
}

export const AnimatedBox: React.FC<AnimatedBoxProps> = ({
  children,
  animation = 'fadeIn',
  delay = 0,
  duration = 0.3,
  trigger = true
}) => (
  <AnimatedContainer
    animation={animation}
    delay={delay}
    duration={duration}
    sx={{
      opacity: trigger ? 1 : 0
    }}
  >
    {children}
  </AnimatedContainer>
);

// هياكل التحميل للمكونات المختلفة
export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => (
  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
    {Array.from({ length: count }).map((_, index) => (
      <Box key={index} sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <StyledSkeleton variant="circular" width={40} height={40} />
          <Box sx={{ ml: 2, flex: 1 }}>
            <StyledSkeleton variant="text" width="60%" height={20} />
            <StyledSkeleton variant="text" width="40%" height={16} />
          </Box>
        </Box>
        <StyledSkeleton variant="rectangular" width="100%" height={120} />
        <Box sx={{ mt: 2 }}>
          <StyledSkeleton variant="text" width="80%" height={16} />
          <StyledSkeleton variant="text" width="60%" height={16} />
        </Box>
      </Box>
    ))}
  </Box>
);

export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <Box>
    {/* رأس الجدول */}
    <Box sx={{ display: 'flex', gap: 2, mb: 2, p: 2 }}>
      {Array.from({ length: columns }).map((_, index) => (
        <StyledSkeleton key={index} variant="text" width="100%" height={24} />
      ))}
    </Box>
    
    {/* صفوف الجدول */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <Box key={rowIndex} sx={{ display: 'flex', gap: 2, mb: 1, p: 2 }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <StyledSkeleton key={colIndex} variant="text" width="100%" height={20} />
        ))}
      </Box>
    ))}
  </Box>
);

export const ListSkeleton: React.FC<{ count?: number }> = ({ count = 5 }) => (
  <Box>
    {Array.from({ length: count }).map((_, index) => (
      <Box key={index} sx={{ display: 'flex', alignItems: 'center', p: 2, gap: 2 }}>
        <StyledSkeleton variant="circular" width={32} height={32} />
        <Box sx={{ flex: 1 }}>
          <StyledSkeleton variant="text" width="70%" height={18} />
          <StyledSkeleton variant="text" width="50%" height={14} />
        </Box>
        <StyledSkeleton variant="rectangular" width={60} height={24} />
      </Box>
    ))}
  </Box>
);

// مؤشر التقدم المحسن
interface ProgressIndicatorProps {
  value: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'small' | 'medium' | 'large';
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  value,
  max = 100,
  label,
  showPercentage = true,
  color = 'primary',
  size = 'medium'
}) => {
  const percentage = Math.round((value / max) * 100);
  
  const sizeMap = {
    small: { height: 4, fontSize: theme.typography.fontSize.xs },
    medium: { height: 6, fontSize: theme.typography.fontSize.sm },
    large: { height: 8, fontSize: theme.typography.fontSize.base }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {(label || showPercentage) && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          {label && (
            <Typography
              variant="body2"
              sx={{
                fontSize: sizeMap[size].fontSize,
                color: theme.colors.text.secondary,
                fontWeight: theme.typography.fontWeight.medium
              }}
            >
              {label}
            </Typography>
          )}
          {showPercentage && (
            <Typography
              variant="body2"
              sx={{
                fontSize: sizeMap[size].fontSize,
                color: theme.colors.text.primary,
                fontWeight: theme.typography.fontWeight.semibold
              }}
            >
              {percentage}%
            </Typography>
          )}
        </Box>
      )}
      
      <StyledLinearProgress
        variant="determinate"
        value={percentage}
        color={color}
        sx={{ height: sizeMap[size].height }}
      />
    </Box>
  );
};

export default {
  LoadingSpinner,
  LoadingBar,
  LoadingDots,
  LoadingOverlay,
  AnimatedBox,
  CardSkeleton,
  TableSkeleton,
  ListSkeleton,
  ProgressIndicator
};
