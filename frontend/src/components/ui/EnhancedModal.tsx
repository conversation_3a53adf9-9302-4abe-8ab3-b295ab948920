import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Box,
  Slide,
  Fade,
  Zoom,
  Backdrop
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Close } from '@mui/icons-material';
import { theme } from '../../theme';

// استيراد النوع بشكل يدوي لأن MUI v7 لا يوفره مباشرة
// يمكنك تخصيص الخصائص حسب الحاجة
export type TransitionProps = {
  children?: React.ReactElement<any, any>;
  in?: boolean;
  onEnter?: () => void;
  onExited?: () => void;
  // أضف أي خصائص تحتاجها فقط
};

// انتقالات مخصصة
const SlideTransition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const ZoomTransition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Zoom ref={ref} {...props} />;
});

// النافذة المنبثقة المحسنة
const StyledDialog = styled(Dialog)({
  '& .MuiDialog-paper': {
    borderRadius: theme.spacing.border.radius['2xl'],
    boxShadow: theme.effects.shadows.modal,
    border: `1px solid ${theme.colors.border.light}`,
    overflow: 'hidden',
    position: 'relative',
    
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '4px',
      background: theme.effects.gradients.primary,
      zIndex: 1
    }
  },
  
  '& .MuiBackdrop-root': {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    backdropFilter: 'blur(4px)'
  }
});

// رأس النافذة المحسن
const StyledDialogTitle = styled(DialogTitle)({
  backgroundColor: theme.colors.background.secondary,
  padding: theme.spacing.component.modal.headerPadding,
  borderBottom: `1px solid ${theme.colors.border.light}`,
  position: 'relative',
  
  '& .MuiTypography-root': {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    paddingRight: theme.spacing.base.xl // مساحة لزر الإغلاق
  }
});

// محتوى النافذة المحسن
const StyledDialogContent = styled(DialogContent)({
  backgroundColor: theme.colors.background.primary,
  padding: theme.spacing.component.modal.padding,
  
  '&.MuiDialogContent-dividers': {
    borderTop: `1px solid ${theme.colors.border.light}`,
    borderBottom: `1px solid ${theme.colors.border.light}`
  }
});

// أسفل النافذة المحسن
const StyledDialogActions = styled(DialogActions)({
  backgroundColor: theme.colors.background.secondary,
  padding: theme.spacing.component.modal.footerPadding,
  borderTop: `1px solid ${theme.colors.border.light}`,
  gap: theme.spacing.component.modal.gap,
  
  '& .MuiButton-root': {
    borderRadius: theme.spacing.border.radius.lg,
    fontWeight: theme.typography.fontWeight.medium,
    textTransform: 'none'
  }
});

// زر الإغلاق المحسن
const CloseButton = styled(IconButton)({
  position: 'absolute',
  right: theme.spacing.base.md,
  top: '50%',
  transform: 'translateY(-50%)',
  backgroundColor: theme.colors.background.primary,
  border: `1px solid ${theme.colors.border.light}`,
  borderRadius: theme.spacing.border.radius.md,
  transition: theme.effects.transitions.component.button,
  
  '&:hover': {
    backgroundColor: theme.colors.error[500],
    color: theme.colors.text.inverse,
    transform: 'translateY(-50%) scale(1.05)',
    borderColor: theme.colors.error[500]
  }
});

// حاوية الأيقونة
const IconContainer = styled(Box)<{ color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' }>(({ color }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '48px',
  height: '48px',
  borderRadius: theme.spacing.border.radius.full,
  marginBottom: theme.spacing.base.md,
  marginLeft: 'auto',
  marginRight: 'auto'
}));

interface EnhancedModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  fullScreen?: boolean;
  disableBackdropClick?: boolean;
  disableEscapeKeyDown?: boolean;
  showCloseButton?: boolean;
  transition?: 'slide' | 'fade' | 'zoom';
  icon?: React.ReactNode;
  iconColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  variant?: 'default' | 'confirmation' | 'form' | 'info';
}

const EnhancedModal: React.FC<EnhancedModalProps> = ({
  open,
  onClose,
  title,
  children,
  actions,
  maxWidth = 'sm',
  fullWidth = true,
  fullScreen = false,
  disableBackdropClick = false,
  disableEscapeKeyDown = false,
  showCloseButton = true,
  transition = 'slide',
  icon,
  iconColor = 'primary',
  variant = 'default'
}) => {
  const handleClose = (event: {}, reason: 'backdropClick' | 'escapeKeyDown') => {
    if (reason === 'backdropClick' && disableBackdropClick) return;
    if (reason === 'escapeKeyDown' && disableEscapeKeyDown) return;
    onClose();
  };

  const getTransitionComponent = () => {
    switch (transition) {
      case 'zoom':
        return ZoomTransition;
      case 'fade':
        return Fade;
      case 'slide':
      default:
        return SlideTransition;
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'confirmation':
        return {
          textAlign: 'center' as const,
          '& .MuiDialogContent-root': {
            paddingTop: theme.spacing.base.xl
          }
        };
      
      case 'form':
        return {
          '& .MuiDialogContent-root': {
            paddingTop: theme.spacing.base.lg,
            paddingBottom: theme.spacing.base.lg
          }
        };
      
      case 'info':
        return {
          '& .MuiDialogTitle-root': {
            textAlign: 'center' as const
          },
          '& .MuiDialogContent-root': {
            textAlign: 'center' as const,
            paddingTop: theme.spacing.base.lg
          }
        };
      
      default:
        return {};
    }
  };

  return (
    <StyledDialog
      open={open}
      onClose={handleClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      fullScreen={fullScreen}
      sx={getVariantStyles()}
    >
      {title && (
        <StyledDialogTitle>
          <Typography component="h2">
            {title}
          </Typography>
          {showCloseButton && (
            <CloseButton onClick={onClose} size="small">
              <Close />
            </CloseButton>
          )}
        </StyledDialogTitle>
      )}

      <StyledDialogContent dividers={Boolean(title && actions)}>
        {icon && (
          <IconContainer color={iconColor}>
            {icon}
          </IconContainer>
        )}
        {children}
      </StyledDialogContent>

      {actions && (
        <StyledDialogActions>
          {actions}
        </StyledDialogActions>
      )}
    </StyledDialog>
  );
};

// مكونات متخصصة للنوافذ المنبثقة
interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'warning' | 'error' | 'info';
  loading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'تأكيد',
  cancelText = 'إلغاء',
  variant = 'warning',
  loading = false
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'error':
        return <Close />;
      case 'info':
        return <Close />; // يمكن تغييرها لأيقونة معلومات
      case 'warning':
      default:
        return <Close />; // يمكن تغييرها لأيقونة تحذير
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'error':
        return 'error' as const;
      case 'info':
        return 'info' as const;
      case 'warning':
      default:
        return 'warning' as const;
    }
  };

  return (
    <EnhancedModal
      open={open}
      onClose={onClose}
      title={title}
      variant="confirmation"
      icon={getIcon()}
      iconColor={getIconColor()}
      maxWidth="xs"
      actions={
        <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              flex: 1,
              padding: `${theme.spacing.component.button.paddingY} ${theme.spacing.component.button.paddingX}`,
              borderRadius: theme.spacing.border.radius.lg,
              border: `1px solid ${theme.colors.border.medium}`,
              backgroundColor: 'transparent',
              color: theme.colors.text.secondary,
              cursor: 'pointer',
              transition: theme.effects.transitions.component.button
            }}
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            style={{
              flex: 1,
              padding: `${theme.spacing.component.button.paddingY} ${theme.spacing.component.button.paddingX}`,
              borderRadius: theme.spacing.border.radius.lg,
              border: 'none',
              backgroundColor: theme.colors[getIconColor()][500],
              color: theme.colors.text.inverse,
              cursor: 'pointer',
              transition: theme.effects.transitions.component.button
            }}
          >
            {loading ? 'جاري التحميل...' : confirmText}
          </button>
        </Box>
      }
    >
      <Typography
        variant="body1"
        sx={{
          color: theme.colors.text.secondary,
          lineHeight: theme.typography.lineHeight.relaxed,
          fontSize: theme.typography.fontSize.base
        }}
      >
        {message}
      </Typography>
    </EnhancedModal>
  );
};

// نافذة منبثقة للمعلومات
interface InfoModalProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
}

export const InfoModal: React.FC<InfoModalProps> = ({
  open,
  onClose,
  title,
  children,
  icon
}) => (
  <EnhancedModal
    open={open}
    onClose={onClose}
    title={title}
    variant="info"
    icon={icon}
    iconColor="info"
    maxWidth="md"
    actions={
      <button
        onClick={onClose}
        style={{
          padding: `${theme.spacing.component.button.paddingY} ${theme.spacing.component.button.paddingX}`,
          borderRadius: theme.spacing.border.radius.lg,
          border: 'none',
          backgroundColor: theme.colors.primary[500],
          color: theme.colors.text.inverse,
          cursor: 'pointer',
          transition: theme.effects.transitions.component.button
        }}
      >
        فهمت
      </button>
    }
  >
    {children}
  </EnhancedModal>
);

export default EnhancedModal;
