import React, { useState } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  FormLabel,
  FormHelperText,
  InputAdornment,
  IconButton,
  Box,
  Typography,
  Divider,
  Alert
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Visibility, VisibilityOff, Error, CheckCircle } from '@mui/icons-material';
import { theme } from '../../theme';

// حقل الإدخال المحسن
const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing.border.radius.lg,
    backgroundColor: theme.colors.background.primary,
    transition: theme.effects.transitions.common.all,
    
    '& fieldset': {
      borderColor: theme.colors.border.light,
      borderWidth: '1px'
    },
    
    '&:hover fieldset': {
      borderColor: theme.colors.border.medium
    },
    
    '&.Mui-focused fieldset': {
      borderColor: theme.colors.primary[500],
      borderWidth: '2px',
      boxShadow: `0 0 0 3px ${theme.colors.interactive.focus.primary}`
    },
    
    '&.Mui-error fieldset': {
      borderColor: theme.colors.error[500]
    }
  },
  
  '& .MuiInputLabel-root': {
    color: theme.colors.text.secondary,
    fontWeight: theme.typography.fontWeight.medium,
    fontSize: theme.typography.fontSize.sm,
    
    '&.Mui-focused': {
      color: theme.colors.primary[500]
    },
    
    '&.Mui-error': {
      color: theme.colors.error[500]
    }
  },
  
  '& .MuiFormHelperText-root': {
    fontSize: theme.typography.fontSize.xs,
    marginTop: theme.spacing.fine['1'],
    
    '&.Mui-error': {
      color: theme.colors.error[500]
    }
  }
});

// قائمة الاختيار المحسنة
const StyledSelect = styled(Select)({
  borderRadius: theme.spacing.border.radius.lg,
  backgroundColor: theme.colors.background.primary,
  
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.colors.border.light
  },
  
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.colors.border.medium
  },
  
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: theme.colors.primary[500],
    borderWidth: '2px',
    boxShadow: `0 0 0 3px ${theme.colors.interactive.focus.primary}`
  }
});

// تسمية الحقل المحسنة
const StyledFormLabel = styled(FormLabel)({
  color: theme.colors.text.primary,
  fontWeight: theme.typography.fontWeight.semibold,
  fontSize: theme.typography.fontSize.sm,
  marginBottom: theme.spacing.fine['2'],
  display: 'block',
  
  '&.Mui-focused': {
    color: theme.colors.primary[500]
  },
  
  '&.Mui-error': {
    color: theme.colors.error[500]
  }
});

// حاوية الحقل
const FieldContainer = styled(Box)({
  marginBottom: theme.spacing.component.form.fieldGap,
  
  '&:last-child': {
    marginBottom: 0
  }
});

// حاوية القسم
const SectionContainer = styled(Box)({
  marginBottom: theme.spacing.component.form.sectionGap,
  
  '&:last-child': {
    marginBottom: 0
  }
});

// عنوان القسم
const SectionTitle = styled(Typography)({
  fontSize: theme.typography.fontSize.lg,
  fontWeight: theme.typography.fontWeight.semibold,
  color: theme.colors.text.primary,
  marginBottom: theme.spacing.base.md,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing.fine['2']
});

// مؤشر الحالة
const StatusIndicator = styled(Box)<{ status: 'success' | 'error' | 'default' }>(({ status }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing.fine['1'],
  fontSize: theme.typography.fontSize.xs,
  color: 
    status === 'success' ? theme.colors.success[600] :
    status === 'error' ? theme.colors.error[600] :
    theme.colors.text.tertiary
}));

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value: string | number;
  onChange: (value: string | number) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  multiline?: boolean;
  rows?: number;
  fullWidth?: boolean;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error,
  helperText,
  startIcon,
  endIcon,
  multiline = false,
  rows = 4,
  fullWidth = true
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = type === 'password';
  const hasError = Boolean(error);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = type === 'number' ? Number(event.target.value) : event.target.value;
    onChange(newValue);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <FieldContainer>
      <StyledFormLabel required={required} error={hasError}>
        {label}
      </StyledFormLabel>
      
      <StyledTextField
        name={name}
        type={isPassword ? (showPassword ? 'text' : 'password') : type}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        error={hasError}
        helperText={error || helperText}
        multiline={multiline}
        rows={multiline ? rows : undefined}
        fullWidth={fullWidth}
        InputProps={{
          startAdornment: startIcon && (
            <InputAdornment position="start">
              {startIcon}
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {isPassword && (
                <IconButton
                  onClick={togglePasswordVisibility}
                  edge="end"
                  size="small"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              )}
              {!isPassword && endIcon}
              {hasError && <Error color="error" />}
              {!hasError && value && !error && <CheckCircle color="success" />}
            </InputAdornment>
          )
        }}
      />
    </FieldContainer>
  );
};

interface SelectFieldProps {
  label: string;
  name: string;
  value: string | number;
  onChange: (value: string | number) => void;
  options: { value: string | number; label: string }[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  name,
  value,
  onChange,
  options,
  placeholder,
  required = false,
  disabled = false,
  error,
  helperText,
  fullWidth = true
}) => {
  const hasError = Boolean(error);

  return (
    <FieldContainer>
      <StyledFormLabel required={required} error={hasError}>
        {label}
      </StyledFormLabel>
      
      <FormControl fullWidth={fullWidth} error={hasError}>
        <StyledSelect
          name={name}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          displayEmpty
        >
          {placeholder && (
            <MenuItem value="" disabled>
              <em>{placeholder}</em>
            </MenuItem>
          )}
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </StyledSelect>
        
        {(error || helperText) && (
          <FormHelperText>
            {error || helperText}
          </FormHelperText>
        )}
      </FormControl>
    </FieldContainer>
  );
};

interface FormSectionProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  icon,
  children
}) => {
  return (
    <SectionContainer>
      <SectionTitle>
        {icon}
        {title}
      </SectionTitle>
      
      {description && (
        <Typography
          variant="body2"
          sx={{
            color: theme.colors.text.secondary,
            mb: theme.spacing.base.md,
            lineHeight: theme.typography.lineHeight.relaxed
          }}
        >
          {description}
        </Typography>
      )}
      
      <Divider sx={{ mb: theme.spacing.base.lg, borderColor: theme.colors.border.light }} />
      
      {children}
    </SectionContainer>
  );
};

interface FormContainerProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  error?: string;
  success?: string;
}

export const FormContainer: React.FC<FormContainerProps> = ({
  children,
  title,
  description,
  error,
  success
}) => {
  return (
    <Box sx={{
      backgroundColor: theme.colors.background.primary,
      borderRadius: theme.spacing.border.radius.xl,
      padding: theme.spacing.component.form.padding,
      boxShadow: theme.effects.shadows.card,
      border: `1px solid ${theme.colors.border.light}`
    }}>
      {title && (
        <Typography
          variant="h4"
          sx={{
            fontSize: theme.typography.fontSize['2xl'],
            fontWeight: theme.typography.fontWeight.bold,
            color: theme.colors.text.primary,
            mb: theme.spacing.base.sm,
            textAlign: 'center'
          }}
        >
          {title}
        </Typography>
      )}
      
      {description && (
        <Typography
          variant="body1"
          sx={{
            color: theme.colors.text.secondary,
            mb: theme.spacing.base.lg,
            textAlign: 'center',
            lineHeight: theme.typography.lineHeight.relaxed
          }}
        >
          {description}
        </Typography>
      )}
      
      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: theme.spacing.base.lg,
            borderRadius: theme.spacing.border.radius.lg
          }}
        >
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert 
          severity="success" 
          sx={{ 
            mb: theme.spacing.base.lg,
            borderRadius: theme.spacing.border.radius.lg
          }}
        >
          {success}
        </Alert>
      )}
      
      {children}
    </Box>
  );
};

// مكون مساعد لعرض حالة التحقق
export const ValidationStatus: React.FC<{
  isValid: boolean;
  message: string;
}> = ({ isValid, message }) => (
  <StatusIndicator status={isValid ? 'success' : 'error'}>
    {isValid ? <CheckCircle /> : <Error />}
    {message}
  </StatusIndicator>
);

export default {
  FormField,
  SelectField,
  FormSection,
  FormContainer,
  ValidationStatus
};
