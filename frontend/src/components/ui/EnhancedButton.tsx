import React from 'react';
import { Button, CircularProgress, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { theme } from '../../theme';

// الأزرار المحسنة مع تأثيرات تفاعلية
const StyledButton = styled(Button)<{
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}>(({ variant, size, fullWidth }) => {
  const baseStyles = {
    borderRadius: theme.spacing.border.radius.lg,
    fontWeight: theme.typography.fontWeight.medium,
    fontFamily: theme.typography.fontFamily.heading,
    textTransform: 'none' as const,
    transition: theme.effects.transitions.component.button,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    width: fullWidth ? '100%' : 'auto',
    
    '&::before': {
      content: '""',
      position: 'absolute' as const,
      top: 0,
      left: '-100%',
      width: '100%',
      height: '100%',
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
      transition: theme.effects.transitions.duration.slow,
    },

    '&:hover::before': {
      left: '100%'
    },

    '&:disabled': {
      opacity: 0.6,
      cursor: 'not-allowed',
      transform: 'none'
    }
  };

  // أحجام الأزرار
  const sizeStyles = {
    small: {
      padding: `${theme.spacing.component.button.paddingYSmall} ${theme.spacing.component.button.paddingXSmall}`,
      fontSize: theme.typography.fontSize.button.small,
      minHeight: theme.spacing.dimensions.height.buttonSmall
    },
    medium: {
      padding: `${theme.spacing.component.button.paddingY} ${theme.spacing.component.button.paddingX}`,
      fontSize: theme.typography.fontSize.button.medium,
      minHeight: theme.spacing.dimensions.height.button
    },
    large: {
      padding: `${theme.spacing.component.button.paddingYLarge} ${theme.spacing.component.button.paddingXLarge}`,
      fontSize: theme.typography.fontSize.button.large,
      minHeight: theme.spacing.dimensions.height.buttonLarge
    }
  };

  // أنماط الأزرار
  const variantStyles = {
    primary: {
      backgroundColor: theme.colors.primary[500],
      color: theme.colors.text.inverse,
      border: 'none',
      boxShadow: theme.effects.shadows.button,
      
      '&:hover': {
        backgroundColor: theme.colors.interactive.hover.primary,
        transform: theme.effects.transforms.translate.y.sm,
        boxShadow: theme.effects.shadows.buttonHover
      },
      
      '&:active': {
        backgroundColor: theme.colors.interactive.active.primary,
        transform: theme.effects.transforms.scale.active
      },
      
      '&:focus': {
        outline: 'none',
        boxShadow: `0 0 0 3px ${theme.colors.interactive.focus.primary}`
      }
    },

    secondary: {
      backgroundColor: theme.colors.secondary[500],
      color: theme.colors.text.inverse,
      border: 'none',
      boxShadow: theme.effects.shadows.button,
      
      '&:hover': {
        backgroundColor: theme.colors.interactive.hover.secondary,
        transform: theme.effects.transforms.translate.y.sm,
        boxShadow: theme.effects.shadows.buttonHover
      },
      
      '&:active': {
        backgroundColor: theme.colors.interactive.active.secondary,
        transform: theme.effects.transforms.scale.active
      }
    },

    outline: {
      backgroundColor: 'transparent',
      color: theme.colors.primary[500],
      border: `2px solid ${theme.colors.primary[500]}`,
      boxShadow: 'none',
      
      '&:hover': {
        backgroundColor: theme.colors.primary[500],
        color: theme.colors.text.inverse,
        transform: theme.effects.transforms.translate.y.sm,
        boxShadow: theme.effects.shadows.button
      },
      
      '&:active': {
        transform: theme.effects.transforms.scale.active
      }
    },

    ghost: {
      backgroundColor: 'transparent',
      color: theme.colors.primary[500],
      border: 'none',
      boxShadow: 'none',
      
      '&:hover': {
        backgroundColor: theme.colors.primary[50],
        transform: theme.effects.transforms.translate.y.sm
      },
      
      '&:active': {
        backgroundColor: theme.colors.primary[100],
        transform: theme.effects.transforms.scale.active
      }
    },

    gradient: {
      background: theme.effects.gradients.primary,
      color: theme.colors.text.inverse,
      border: 'none',
      boxShadow: theme.effects.shadows.colored.primary,
      
      '&:hover': {
        background: theme.effects.gradients.primary,
        filter: 'brightness(1.1)',
        transform: theme.effects.transforms.translate.y.sm,
        boxShadow: theme.effects.shadows.lg
      },
      
      '&:active': {
        filter: 'brightness(0.9)',
        transform: theme.effects.transforms.scale.active
      }
    }
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant]
  };
});

const LoadingSpinner = styled(CircularProgress)({
  color: 'inherit',
  marginRight: theme.spacing.base.sm
});

interface EnhancedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  id?: string;
  'data-testid'?: string;
}

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  startIcon,
  endIcon,
  onClick,
  type = 'button',
  className,
  id,
  'data-testid': testId,
  ...props
}) => {
  const isDisabled = disabled || loading;

  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={isDisabled}
      onClick={onClick}
      type={type}
      className={className}
      id={id}
      data-testid={testId}
      {...props}
    >
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: theme.spacing.component.button.gap,
        position: 'relative',
        zIndex: 1
      }}>
        {loading && (
          <LoadingSpinner 
            size={size === 'small' ? 16 : size === 'large' ? 24 : 20} 
          />
        )}
        
        {!loading && startIcon && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {startIcon}
          </Box>
        )}
        
        <span>{children}</span>
        
        {!loading && endIcon && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {endIcon}
          </Box>
        )}
      </Box>
    </StyledButton>
  );
};

// مكونات أزرار متخصصة
export const PrimaryButton: React.FC<Omit<EnhancedButtonProps, 'variant'>> = (props) => (
  <EnhancedButton variant="primary" {...props} />
);

export const SecondaryButton: React.FC<Omit<EnhancedButtonProps, 'variant'>> = (props) => (
  <EnhancedButton variant="secondary" {...props} />
);

export const OutlineButton: React.FC<Omit<EnhancedButtonProps, 'variant'>> = (props) => (
  <EnhancedButton variant="outline" {...props} />
);

export const GhostButton: React.FC<Omit<EnhancedButtonProps, 'variant'>> = (props) => (
  <EnhancedButton variant="ghost" {...props} />
);

export const GradientButton: React.FC<Omit<EnhancedButtonProps, 'variant'>> = (props) => (
  <EnhancedButton variant="gradient" {...props} />
);

export default EnhancedButton;
