import React, { useState } from 'react';
import {
  Box,
  Card,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper,
  Rating,
  Avatar
} from '@mui/material';
import {
  AutoAwesome,
  Psychology,
  TrendingUp,
  Analytics,
  Tag,
  People,
  ShoppingCart,
  Business,
  Recommend,
  Timeline
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tabpanel-${index}`}
      aria-labelledby={`ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AIToolsSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Content Generation State
  const [contentPrompt, setContentPrompt] = useState('');
  const [contentType, setContentType] = useState('social_media');
  const [contentTone, setContentTone] = useState('professional');

  // Sentiment Analysis State
  const [sentimentText, setSentimentText] = useState('');
  const [sentimentResults, setSentimentResults] = useState<any>(null);

  // Competitor Analysis State
  const [competitorName, setCompetitorName] = useState('');
  const [competitorData, setCompetitorData] = useState('');
  const [competitorResults, setCompetitorResults] = useState<any>(null);

  // Customer Behavior State
  const [customerData, setCustomerData] = useState('');
  const [behaviorResults, setBehaviorResults] = useState<any>(null);

  // Product Recommendation State
  const [userId, setUserId] = useState('');
  const [recommendationResults, setRecommendationResults] = useState<any>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setError('');
    setSuccess('');
  };

  const handleContentGeneration = async () => {
    if (!contentPrompt.trim()) {
      setError('يرجى إدخال نص للإنشاء');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const generatedContent = `محتوى مولد: ${contentPrompt}\n\nهذا مثال على المحتوى المولد باستخدام الذكاء الاصطناعي. يمكن تخصيصه حسب النوع والنبرة المطلوبة.`;
      
      setSuccess('تم إنشاء المحتوى بنجاح!');
      // Here you would typically set the generated content to state
    } catch (err) {
      setError('فشل في إنشاء المحتوى');
    } finally {
      setLoading(false);
    }
  };

  const handleSentimentAnalysis = async () => {
    if (!sentimentText.trim()) {
      setError('يرجى إدخال نص للتحليل');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const sentiment = Math.random() > 0.5 ? 'positive' : 'negative';
      const score = Math.random() * 100;
      
      setSentimentResults({
        sentiment,
        score: score.toFixed(1),
        confidence: (Math.random() * 20 + 80).toFixed(1)
      });
    } catch (err) {
      setError('فشل في تحليل المشاعر');
    } finally {
      setLoading(false);
    }
  };

  const handleCompetitorAnalysis = async () => {
    if (!competitorName.trim() || !competitorData.trim()) {
      setError('يرجى إدخال اسم المنافس وبياناته');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      setCompetitorResults({
        name: competitorName,
        sentiment: 'neutral',
        marketPosition: 'challenger',
        strengths: ['خدمة عملاء جيدة', 'أسعار تنافسية'],
        weaknesses: ['نطاق محدود', 'تقنية قديمة'],
        recommendations: ['تحسين التقنية', 'توسيع النطاق']
      });
    } catch (err) {
      setError('فشل في تحليل المنافس');
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerBehavior = async () => {
    if (!customerData.trim()) {
      setError('يرجى إدخال بيانات العميل');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1800));
      
      setBehaviorResults({
        purchaseIntent: 'high',
        probability: '85%',
        recommendations: ['عرض خاص', 'خدمة مخصصة', 'متابعة مباشرة']
      });
    } catch (err) {
      setError('فشل في تحليل سلوك العميل');
    } finally {
      setLoading(false);
    }
  };

  const handleProductRecommendation = async () => {
    if (!userId.trim()) {
      setError('يرجى إدخال معرف المستخدم');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      setRecommendationResults({
        userId,
        recommendations: [
          { id: 1, name: 'منتج أ', score: 0.95, reason: 'مشابه للمشتريات السابقة' },
          { id: 2, name: 'منتج ب', score: 0.87, reason: 'شائع بين المستخدمين المشابهين' },
          { id: 3, name: 'منتج ج', score: 0.82, reason: 'يتناسب مع التفضيلات' }
        ]
      });
    } catch (err) {
      setError('فشل في توليد التوصيات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      p: 3
    }}>
      <Box sx={{
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 3,
        p: 4,
        backdropFilter: 'blur(10px)'
      }}>
        <Typography variant="h3" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          <AutoAwesome sx={{ fontSize: 40, color: '#667eea' }} />
          أدوات الذكاء الاصطناعي
        </Typography>

        <Typography variant="h6" sx={{ mb: 3, color: 'text.secondary', textAlign: 'center' }}>
          مجموعة شاملة من أدوات الذكاء الاصطناعي المتقدمة لتحسين استراتيجياتك التسويقية
        </Typography>

        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }}>
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            background: 'linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%)'
          }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="AI tools tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab icon={<AutoAwesome />} label="إنشاء المحتوى" />
              <Tab icon={<Psychology />} label="تحليل المشاعر" />
              <Tab icon={<Business />} label="تحليل المنافسين" />
              <Tab icon={<People />} label="سلوك العملاء" />
              <Tab icon={<Recommend />} label="توصية المنتجات" />
              <Tab icon={<Tag />} label="إنشاء هاشتاجات" />
            </Tabs>
          </Box>

          {/* Content Generation Tab */}
          <TabPanel value={activeTab} index={0}>
            <Typography variant="h6" gutterBottom>إنشاء المحتوى التسويقي</Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="وصف المحتوى المطلوب"
                  value={contentPrompt}
                  onChange={(e) => setContentPrompt(e.target.value)}
                  placeholder="اكتب وصفاً للمحتوى الذي تريد إنشاءه..."
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>نوع المحتوى</InputLabel>
                  <Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value)}
                  >
                    <MenuItem value="social_media">منشورات وسائل التواصل</MenuItem>
                    <MenuItem value="email">رسائل البريد الإلكتروني</MenuItem>
                    <MenuItem value="blog">مقالات المدونة</MenuItem>
                    <MenuItem value="ad">إعلانات</MenuItem>
                  </Select>
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>نبرة المحتوى</InputLabel>
                  <Select
                    value={contentTone}
                    onChange={(e) => setContentTone(e.target.value)}
                  >
                    <MenuItem value="professional">مهني</MenuItem>
                    <MenuItem value="casual">عادي</MenuItem>
                    <MenuItem value="friendly">ودي</MenuItem>
                    <MenuItem value="formal">رسمي</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                onClick={handleContentGeneration}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <AutoAwesome />}
                sx={{ background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
              >
                {loading ? 'جاري الإنشاء...' : 'إنشاء المحتوى'}
              </Button>
            </Box>
          </TabPanel>

          {/* Sentiment Analysis Tab */}
          <TabPanel value={activeTab} index={1}>
            <Typography variant="h6" gutterBottom>تحليل مشاعر العملاء</Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="النص المراد تحليله"
                  value={sentimentText}
                  onChange={(e) => setSentimentText(e.target.value)}
                  placeholder="أدخل النص الذي تريد تحليل مشاعره..."
                />
                <Button
                  variant="contained"
                  onClick={handleSentimentAnalysis}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Psychology />}
                  sx={{ mt: 2, background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التحليل...' : 'تحليل المشاعر'}
                </Button>
              </Box>
              <Box sx={{ flex: 1 }}>
                {sentimentResults && (
                  <Paper sx={{ p: 2, background: '#f8f9fa' }}>
                    <Typography variant="h6" gutterBottom>نتائج التحليل</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Chip
                        label={sentimentResults.sentiment === 'positive' ? 'إيجابي' : 'سلبي'}
                        color={sentimentResults.sentiment === 'positive' ? 'success' : 'error'}
                        sx={{ mr: 2 }}
                      />
                      <Typography>النتيجة: {sentimentResults.score}%</Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      مستوى الثقة: {sentimentResults.confidence}%
                    </Typography>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Competitor Analysis Tab */}
          <TabPanel value={activeTab} index={2}>
            <Typography variant="h6" gutterBottom>تحليل المنافسين</Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="اسم المنافس"
                  value={competitorName}
                  onChange={(e) => setCompetitorName(e.target.value)}
                  placeholder="أدخل اسم المنافس..."
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="بيانات المنافس"
                  value={competitorData}
                  onChange={(e) => setCompetitorData(e.target.value)}
                  placeholder="أدخل معلومات عن المنافس (المنتجات، الأسعار، الخدمات)..."
                />
                <Button
                  variant="contained"
                  onClick={handleCompetitorAnalysis}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Business />}
                  sx={{ mt: 2, background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التحليل...' : 'تحليل المنافس'}
                </Button>
              </Box>
              <Box sx={{ flex: 1 }}>
                {competitorResults && (
                  <Paper sx={{ p: 2, background: '#f8f9fa' }}>
                    <Typography variant="h6" gutterBottom>نتائج تحليل {competitorResults.name}</Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>الموقع السوقي:</Typography>
                      <Chip label={competitorResults.marketPosition} color="primary" />
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>نقاط القوة:</Typography>
                      {competitorResults.strengths.map((strength: string, index: number) => (
                        <Chip key={index} label={strength} size="small" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>نقاط الضعف:</Typography>
                      {competitorResults.weaknesses.map((weakness: string, index: number) => (
                        <Chip key={index} label={weakness} size="small" color="error" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>التوصيات:</Typography>
                      <List dense>
                        {competitorResults.recommendations.map((rec: string, index: number) => (
                          <ListItem key={index}>
                            <ListItemText primary={rec} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Customer Behavior Tab */}
          <TabPanel value={activeTab} index={3}>
            <Typography variant="h6" gutterBottom>توقع سلوك العملاء</Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="بيانات العميل"
                  value={customerData}
                  onChange={(e) => setCustomerData(e.target.value)}
                  placeholder="أدخل بيانات العميل (العمر، المشتريات السابقة، التفاعل)..."
                />
                <Button
                  variant="contained"
                  onClick={handleCustomerBehavior}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <People />}
                  sx={{ mt: 2, background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التحليل...' : 'تحليل السلوك'}
                </Button>
              </Box>
              <Box sx={{ flex: 1 }}>
                {behaviorResults && (
                  <Paper sx={{ p: 2, background: '#f8f9fa' }}>
                    <Typography variant="h6" gutterBottom>نتائج تحليل السلوك</Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>نية الشراء:</Typography>
                      <Chip
                        label={behaviorResults.purchaseIntent === 'high' ? 'عالية' : 'منخفضة'}
                        color={behaviorResults.purchaseIntent === 'high' ? 'success' : 'warning'}
                      />
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>احتمالية الشراء:</Typography>
                      <Typography variant="h6" color="primary">{behaviorResults.probability}</Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>التوصيات:</Typography>
                      <List dense>
                        {behaviorResults.recommendations.map((rec: string, index: number) => (
                          <ListItem key={index}>
                            <ListItemText primary={rec} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Product Recommendation Tab */}
          <TabPanel value={activeTab} index={4}>
            <Typography variant="h6" gutterBottom>توصية المنتجات</Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="معرف المستخدم"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  placeholder="أدخل معرف المستخدم..."
                />
                <Button
                  variant="contained"
                  onClick={handleProductRecommendation}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Recommend />}
                  sx={{ mt: 2, background: 'linear-gradient(45deg, #667eea, #764ba2)' }}
                >
                  {loading ? 'جاري التوصية...' : 'توليد التوصيات'}
                </Button>
              </Box>
              <Box sx={{ flex: 1 }}>
                {recommendationResults && (
                  <Paper sx={{ p: 2, background: '#f8f9fa' }}>
                    <Typography variant="h6" gutterBottom>توصيات للمستخدم {recommendationResults.userId}</Typography>
                    <List>
                      {recommendationResults.recommendations.map((rec: any, index: number) => (
                        <ListItem key={index} sx={{ border: 1, borderColor: 'divider', borderRadius: 1, mb: 1 }}>
                          <ListItemText
                            primary={rec.name}
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  السبب: {rec.reason}
                                </Typography>
                                <Rating value={rec.score * 5} readOnly size="small" />
                                <Typography variant="caption" color="primary">
                                  {Math.round(rec.score * 100)}% تطابق
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                )}
              </Box>
            </Box>
          </TabPanel>

          {/* Hashtag Generation Tab */}
          <TabPanel value={activeTab} index={5}>
            <Typography variant="h6" gutterBottom>إنشاء هاشتاجات</Typography>
            <Typography variant="body1">قريباً...</Typography>
          </TabPanel>
        </Card>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mt: 2 }}>
            {success}
          </Alert>
        )}
      </Box>
    </Box>
  );
};

export default AIToolsSimple;
