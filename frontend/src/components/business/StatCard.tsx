import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  InfoOutlined
} from '@mui/icons-material';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  subtitle?: string;
  tooltip?: string;
  onClick?: () => void;
  loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  trend = 'neutral',
  icon,
  color = 'primary',
  subtitle,
  tooltip,
  onClick,
  loading = false
}) => {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp fontSize="small" />;
    if (trend === 'down') return <TrendingDown fontSize="small" />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return 'success';
    if (trend === 'down') return 'error';
    return 'default';
  };

  return (
    <Card 
      sx={{ 
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        '&:hover': onClick ? {
          transform: 'translateY(-4px)',
          boxShadow: 4
        } : {},
        background: loading ? 'rgba(0,0,0,0.02)' : 'white',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon && (
              <Box sx={{ 
                color: `${color}.main`,
                display: 'flex',
                alignItems: 'center',
                p: 1,
                borderRadius: 1,
                bgcolor: `${color}.light`,
                opacity: 0.1
              }}>
                {icon}
              </Box>
            )}
            <Typography 
              variant="h6" 
              color="text.secondary"
              sx={{ 
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: 0.5
              }}
            >
              {title}
            </Typography>
            {tooltip && (
              <Tooltip title={tooltip} arrow>
                <IconButton size="small" sx={{ p: 0.5 }}>
                  <InfoOutlined fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
          
          {change && (
            <Chip
              icon={getTrendIcon()}
              label={change}
              color={getTrendColor()}
              size="small"
              variant="outlined"
              sx={{ 
                fontWeight: 600,
                fontSize: '0.75rem'
              }}
            />
          )}
        </Box>

        <Typography 
          variant="h3" 
          component="div"
          sx={{ 
            fontWeight: 700,
            color: `${color}.main`,
            mb: subtitle ? 1 : 0,
            fontSize: loading ? '2rem' : '2.5rem',
            opacity: loading ? 0.5 : 1
          }}
        >
          {loading ? '...' : value}
        </Typography>

        {subtitle && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              fontSize: '0.8rem',
              lineHeight: 1.4
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
