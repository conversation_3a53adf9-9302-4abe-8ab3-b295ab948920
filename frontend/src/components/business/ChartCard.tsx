import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>eader,
  Typo<PERSON>,
  <PERSON>,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  Skeleton
} from '@mui/material';
import {
  MoreVert,
  Download,
  Refresh,
  Fullscreen
} from '@mui/icons-material';

interface ChartCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
  height?: number | string;
  onDownload?: () => void;
  onRefresh?: () => void;
  onFullscreen?: () => void;
  headerColor?: string;
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  subtitle,
  children,
  actions,
  loading = false,
  height = 400,
  onDownload,
  onRefresh,
  onFullscreen,
  headerColor = 'transparent'
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDownload = () => {
    onDownload?.();
    handleMenuClose();
  };

  const handleRefresh = () => {
    onRefresh?.();
    handleMenuClose();
  };

  const handleFullscreen = () => {
    onFullscreen?.();
    handleMenuClose();
  };

  return (
    <Card 
      sx={{ 
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2,
        overflow: 'hidden'
      }}
    >
      <CardHeader
        title={
          <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
            {title}
          </Typography>
        }
        subheader={subtitle && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            {subtitle}
          </Typography>
        )}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {actions}
            {(onDownload || onRefresh || onFullscreen) && (
              <>
                <IconButton
                  aria-label="more options"
                  onClick={handleMenuClick}
                  size="small"
                >
                  <MoreVert />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={open}
                  onClose={handleMenuClose}
                  PaperProps={{
                    elevation: 3,
                    sx: {
                      minWidth: 150
                    }
                  }}
                >
                  {onRefresh && (
                    <MenuItem onClick={handleRefresh}>
                      <Refresh sx={{ mr: 1 }} fontSize="small" />
                      تحديث البيانات
                    </MenuItem>
                  )}
                  {onDownload && (
                    <MenuItem onClick={handleDownload}>
                      <Download sx={{ mr: 1 }} fontSize="small" />
                      تحميل التقرير
                    </MenuItem>
                  )}
                  {onFullscreen && (
                    <>
                      <Divider />
                      <MenuItem onClick={handleFullscreen}>
                        <Fullscreen sx={{ mr: 1 }} fontSize="small" />
                        عرض كامل
                      </MenuItem>
                    </>
                  )}
                </Menu>
              </>
            )}
          </Box>
        }
        sx={{
          bgcolor: headerColor,
          '& .MuiCardHeader-content': {
            overflow: 'hidden'
          }
        }}
      />
      
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box 
          sx={{ 
            height: typeof height === 'number' ? `${height}px` : height,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {loading ? (
            <Box sx={{ p: 3 }}>
              <Skeleton variant="rectangular" height={height} />
            </Box>
          ) : (
            children
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ChartCard;
