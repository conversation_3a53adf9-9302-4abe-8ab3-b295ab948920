import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import StatCard from '../StatCard';
import { TrendingUp } from '@mui/icons-material';

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('StatCard', () => {
  const defaultProps = {
    title: 'Test Title',
    value: '100',
    icon: <TrendingUp />,
    color: 'primary' as const
  };

  it('renders correctly with basic props', () => {
    renderWithTheme(<StatCard {...defaultProps} />);
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });

  it('displays change and trend when provided', () => {
    renderWithTheme(
      <StatCard 
        {...defaultProps} 
        change="+10%" 
        trend="up" 
      />
    );
    
    expect(screen.getByText('+10%')).toBeInTheDocument();
  });

  it('displays subtitle when provided', () => {
    renderWithTheme(
      <StatCard 
        {...defaultProps} 
        subtitle="Test subtitle" 
      />
    );
    
    expect(screen.getByText('Test subtitle')).toBeInTheDocument();
  });

  it('calls onClick when clicked and onClick is provided', () => {
    const handleClick = jest.fn();
    renderWithTheme(
      <StatCard 
        {...defaultProps} 
        onClick={handleClick} 
      />
    );
    
    const card = screen.getByRole('button');
    fireEvent.click(card);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state when loading is true', () => {
    renderWithTheme(
      <StatCard 
        {...defaultProps} 
        loading={true} 
      />
    );
    
    expect(screen.getByText('...')).toBeInTheDocument();
  });

  it('displays tooltip when provided', () => {
    renderWithTheme(
      <StatCard 
        {...defaultProps} 
        tooltip="Test tooltip" 
      />
    );
    
    // Tooltip is present in the DOM but may not be visible initially
    expect(screen.getByLabelText('Test tooltip')).toBeInTheDocument();
  });

  it('applies correct trend colors', () => {
    const { rerender } = renderWithTheme(
      <StatCard 
        {...defaultProps} 
        change="+10%" 
        trend="up" 
      />
    );
    
    expect(screen.getByText('+10%')).toBeInTheDocument();
    
    rerender(
      <ThemeProvider theme={theme}>
        <StatCard 
          {...defaultProps} 
          change="-5%" 
          trend="down" 
        />
      </ThemeProvider>
    );
    
    expect(screen.getByText('-5%')).toBeInTheDocument();
  });

  it('handles different color variants', () => {
    const colors = ['primary', 'secondary', 'success', 'error', 'warning', 'info'] as const;
    
    colors.forEach(color => {
      const { unmount } = renderWithTheme(
        <StatCard 
          {...defaultProps} 
          color={color} 
        />
      );
      
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      unmount();
    });
  });
});
