import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import LineChart from '../LineChart';
import <PERSON><PERSON><PERSON> from '../BarChart';
import <PERSON><PERSON><PERSON> from '../PieChart';

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('Chart Components', () => {
  const mockData = [
    { label: 'Jan', value: 100 },
    { label: 'Feb', value: 150 },
    { label: 'Mar', value: 120 },
    { label: 'Apr', value: 180 }
  ];

  describe('LineChart', () => {
    it('renders correctly with data', () => {
      renderWithTheme(
        <LineChart 
          data={mockData} 
          title="Test Line Chart" 
        />
      );
      
      expect(screen.getByText('Test Line Chart')).toBeInTheDocument();
      expect(screen.getByText('Jan')).toBeInTheDocument();
      expect(screen.getByText('Feb')).toBeInTheDocument();
    });

    it('shows empty state when no data provided', () => {
      renderWithTheme(
        <LineChart 
          data={[]} 
          title="Empty Chart" 
        />
      );
      
      expect(screen.getByText('لا توجد بيانات للعرض')).toBeInTheDocument();
    });

    it('renders SVG elements correctly', () => {
      const { container } = renderWithTheme(
        <LineChart data={mockData} />
      );
      
      const svg = container.querySelector('svg');
      expect(svg).toBeInTheDocument();
      
      const polyline = container.querySelector('polyline');
      expect(polyline).toBeInTheDocument();
    });
  });

  describe('BarChart', () => {
    it('renders correctly with data', () => {
      renderWithTheme(
        <BarChart 
          data={mockData} 
          title="Test Bar Chart" 
          showValues={true}
        />
      );
      
      expect(screen.getByText('Test Bar Chart')).toBeInTheDocument();
      expect(screen.getByText('Jan')).toBeInTheDocument();
      expect(screen.getByText('100')).toBeInTheDocument();
    });

    it('shows empty state when no data provided', () => {
      renderWithTheme(
        <BarChart 
          data={[]} 
          title="Empty Chart" 
        />
      );
      
      expect(screen.getByText('لا توجد بيانات للعرض')).toBeInTheDocument();
    });

    it('renders horizontal bars when horizontal prop is true', () => {
      const { container } = renderWithTheme(
        <BarChart 
          data={mockData} 
          horizontal={true}
        />
      );
      
      // Check if bars are rendered horizontally
      const bars = container.querySelectorAll('[style*="width"]');
      expect(bars.length).toBeGreaterThan(0);
    });

    it('hides values when showValues is false', () => {
      renderWithTheme(
        <BarChart 
          data={mockData} 
          showValues={false}
        />
      );
      
      // Values should not be visible in the bars
      expect(screen.queryByText('100')).not.toBeInTheDocument();
    });
  });

  describe('PieChart', () => {
    const pieData = [
      { label: 'Category A', value: 30, color: '#ff0000' },
      { label: 'Category B', value: 45, color: '#00ff00' },
      { label: 'Category C', value: 25, color: '#0000ff' }
    ];

    it('renders correctly with data', () => {
      renderWithTheme(
        <PieChart 
          data={pieData} 
          title="Test Pie Chart" 
          showLegend={true}
        />
      );
      
      expect(screen.getByText('Test Pie Chart')).toBeInTheDocument();
      expect(screen.getByText('Category A')).toBeInTheDocument();
      expect(screen.getByText('Category B')).toBeInTheDocument();
      expect(screen.getByText('Category C')).toBeInTheDocument();
    });

    it('shows empty state when no data provided', () => {
      renderWithTheme(
        <PieChart 
          data={[]} 
          title="Empty Chart" 
        />
      );
      
      expect(screen.getByText('لا توجد بيانات للعرض')).toBeInTheDocument();
    });

    it('renders SVG elements correctly', () => {
      const { container } = renderWithTheme(
        <PieChart data={pieData} />
      );
      
      const svg = container.querySelector('svg');
      expect(svg).toBeInTheDocument();
      
      const paths = container.querySelectorAll('path');
      expect(paths.length).toBe(pieData.length);
    });

    it('hides legend when showLegend is false', () => {
      renderWithTheme(
        <PieChart 
          data={pieData} 
          showLegend={false}
        />
      );
      
      // Legend items should not be visible
      const legendItems = screen.queryAllByText(/Category/);
      expect(legendItems).toHaveLength(0);
    });

    it('shows percentages when showPercentages is true', () => {
      const { container } = renderWithTheme(
        <PieChart 
          data={pieData} 
          showPercentages={true}
        />
      );
      
      // Check for percentage text elements in SVG
      const percentageTexts = container.querySelectorAll('text');
      expect(percentageTexts.length).toBeGreaterThan(0);
    });

    it('calculates percentages correctly', () => {
      renderWithTheme(
        <PieChart 
          data={pieData} 
          showLegend={true}
        />
      );
      
      // Total is 100, so percentages should be exact
      expect(screen.getByText(/30\.0%/)).toBeInTheDocument();
      expect(screen.getByText(/45\.0%/)).toBeInTheDocument();
      expect(screen.getByText(/25\.0%/)).toBeInTheDocument();
    });
  });

  describe('Chart Error Handling', () => {
    it('handles invalid data gracefully', () => {
      const invalidData = [
        { label: 'Test', value: NaN },
        { label: 'Test2', value: undefined as any }
      ];

      renderWithTheme(<LineChart data={invalidData} />);
      renderWithTheme(<BarChart data={invalidData} />);
      renderWithTheme(<PieChart data={invalidData} />);
      
      // Should not crash and should handle gracefully
      expect(screen.getAllByText('لا توجد بيانات للعرض')).toHaveLength(3);
    });

    it('handles null/undefined data', () => {
      renderWithTheme(<LineChart data={null as any} />);
      renderWithTheme(<BarChart data={undefined as any} />);
      renderWithTheme(<PieChart data={null as any} />);
      
      expect(screen.getAllByText('لا توجد بيانات للعرض')).toHaveLength(3);
    });
  });
});
