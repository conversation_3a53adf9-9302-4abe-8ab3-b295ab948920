import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import DataTable from '../DataTable';
import type { Column } from '../DataTable';

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('DataTable', () => {
  const mockColumns: Column[] = [
    { id: 'name', label: 'Name', sortable: true },
    { id: 'age', label: 'Age', sortable: true, align: 'right' },
    { id: 'email', label: 'Email' },
    { 
      id: 'status', 
      label: 'Status', 
      format: (value) => <span style={{ color: value === 'active' ? 'green' : 'red' }}>{value}</span>
    }
  ];

  const mockRows = [
    { id: 1, name: '<PERSON>', age: 30, email: '<EMAIL>', status: 'active' },
    { id: 2, name: '<PERSON>', age: 25, email: '<EMAIL>', status: 'inactive' },
    { id: 3, name: '<PERSON>', age: 35, email: '<EMAIL>', status: 'active' }
  ];

  const defaultProps = {
    columns: mockColumns,
    rows: mockRows
  };

  it('renders table with data correctly', () => {
    renderWithTheme(<DataTable {...defaultProps} />);
    
    // Check headers
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Age')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    
    // Check data
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
  });

  it('displays title when provided', () => {
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        title="Test Table" 
      />
    );
    
    expect(screen.getByText('Test Table')).toBeInTheDocument();
  });

  it('shows search field when searchable is true', () => {
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        searchable={true} 
      />
    );
    
    expect(screen.getByPlaceholderText('البحث...')).toBeInTheDocument();
  });

  it('filters data when searching', () => {
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        searchable={true} 
      />
    );
    
    const searchInput = screen.getByPlaceholderText('البحث...');
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('sorts data when clicking sortable headers', () => {
    renderWithTheme(<DataTable {...defaultProps} />);
    
    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);
    
    // After sorting, the order should change
    const rows = screen.getAllByRole('row');
    expect(rows).toHaveLength(4); // 3 data rows + 1 header row
  });

  it('calls onRowClick when row is clicked', () => {
    const handleRowClick = jest.fn();
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        onRowClick={handleRowClick} 
      />
    );
    
    const firstDataRow = screen.getAllByRole('row')[1]; // Skip header row
    fireEvent.click(firstDataRow);
    
    expect(handleRowClick).toHaveBeenCalledWith(mockRows[0]);
  });

  it('shows loading state correctly', () => {
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        loading={true} 
      />
    );
    
    // Should show skeleton loaders
    expect(screen.getAllByTestId('skeleton')).toHaveLength(5);
  });

  it('handles pagination correctly', () => {
    const manyRows = Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `User ${i + 1}`,
      age: 20 + i,
      email: `user${i + 1}@example.com`,
      status: i % 2 === 0 ? 'active' : 'inactive'
    }));

    renderWithTheme(
      <DataTable 
        columns={mockColumns}
        rows={manyRows}
        defaultRowsPerPage={10}
      />
    );
    
    // Should show pagination controls
    expect(screen.getByText('عدد الصفوف في الصفحة:')).toBeInTheDocument();
    expect(screen.getByText('1-10 من 25')).toBeInTheDocument();
  });

  it('applies custom formatting to columns', () => {
    renderWithTheme(<DataTable {...defaultProps} />);
    
    // Status column should be formatted with colors
    const activeStatus = screen.getAllByText('active')[0];
    expect(activeStatus).toHaveStyle('color: green');
  });

  it('shows download option when downloadable is true', () => {
    renderWithTheme(
      <DataTable 
        {...defaultProps} 
        downloadable={true} 
      />
    );
    
    const moreButton = screen.getByLabelText('more options');
    fireEvent.click(moreButton);
    
    expect(screen.getByText('تحميل البيانات')).toBeInTheDocument();
  });
});
