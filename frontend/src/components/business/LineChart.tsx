import React from 'react';
import { Box, Typography } from '@mui/material';
import { TrendingUp } from '@mui/icons-material';

interface DataPoint {
  label: string;
  value: number;
}

interface LineChartProps {
  data: DataPoint[];
  title?: string;
  color?: string;
  height?: number;
  showGrid?: boolean;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  color = '#3b82f6',
  height = 300,
  showGrid = true
}) => {
  if (!data || data.length === 0) {
    return (
      <Box sx={{ 
        height, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        flexDirection: 'column',
        color: 'text.secondary'
      }}>
        <TrendingUp sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
        <Typography variant="body2">لا توجد بيانات للعرض</Typography>
      </Box>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;

  const svgWidth = 100;
  const svgHeight = 80;
  const padding = 10;

  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * (svgWidth - 2 * padding);
    const y = svgHeight - padding - ((point.value - minValue) / range) * (svgHeight - 2 * padding);
    return `${x},${y}`;
  }).join(' ');

  return (
    <Box sx={{ height, p: 2 }}>
      {title && (
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
      )}
      
      <Box sx={{ position: 'relative', height: '100%' }}>
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${svgWidth} ${svgHeight}`}
          style={{ overflow: 'visible' }}
        >
          {/* Grid lines */}
          {showGrid && (
            <g stroke="#e5e7eb" strokeWidth="0.5">
              {/* Horizontal grid lines */}
              {[0, 25, 50, 75, 100].map(y => (
                <line
                  key={y}
                  x1={padding}
                  y1={y * (svgHeight - 2 * padding) / 100 + padding}
                  x2={svgWidth - padding}
                  y2={y * (svgHeight - 2 * padding) / 100 + padding}
                />
              ))}
              {/* Vertical grid lines */}
              {data.map((_, index) => (
                <line
                  key={index}
                  x1={padding + (index / (data.length - 1)) * (svgWidth - 2 * padding)}
                  y1={padding}
                  x2={padding + (index / (data.length - 1)) * (svgWidth - 2 * padding)}
                  y2={svgHeight - padding}
                />
              ))}
            </g>
          )}

          {/* Line */}
          <polyline
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={points}
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
            }}
          />

          {/* Data points */}
          {data.map((point, index) => {
            const x = padding + (index / (data.length - 1)) * (svgWidth - 2 * padding);
            const y = svgHeight - padding - ((point.value - minValue) / range) * (svgHeight - 2 * padding);
            
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r="3"
                  fill={color}
                  stroke="white"
                  strokeWidth="2"
                  style={{
                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))'
                  }}
                />
                {/* Tooltip on hover */}
                <circle
                  cx={x}
                  cy={y}
                  r="8"
                  fill="transparent"
                  style={{ cursor: 'pointer' }}
                >
                  <title>{`${point.label}: ${point.value}`}</title>
                </circle>
              </g>
            );
          })}
        </svg>

        {/* X-axis labels */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          mt: 1,
          px: 1
        }}>
          {data.map((point, index) => (
            <Typography 
              key={index}
              variant="caption" 
              color="text.secondary"
              sx={{ 
                fontSize: '0.7rem',
                textAlign: 'center',
                minWidth: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {point.label}
            </Typography>
          ))}
        </Box>

        {/* Y-axis labels */}
        <Box sx={{ 
          position: 'absolute',
          left: -10,
          top: 0,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          py: 1
        }}>
          {[maxValue, (maxValue + minValue) / 2, minValue].map((value, index) => (
            <Typography 
              key={index}
              variant="caption" 
              color="text.secondary"
              sx={{ 
                fontSize: '0.7rem',
                textAlign: 'right',
                transform: 'translateY(-50%)'
              }}
            >
              {Math.round(value)}
            </Typography>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default LineChart;
