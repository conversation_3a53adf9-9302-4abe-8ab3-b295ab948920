import React from 'react';
import { Box, useTheme, useMediaQuery } from '@mui/material';

interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = 3,
  className
}) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  const isLg = useMediaQuery(theme.breakpoints.only('lg'));

  const getGridColumns = () => {
    if (isXs && columns.xs) return `repeat(${columns.xs}, 1fr)`;
    if (isSm && columns.sm) return `repeat(${columns.sm}, 1fr)`;
    if (isMd && columns.md) return `repeat(${columns.md}, 1fr)`;
    if (isLg && columns.lg) return `repeat(${columns.lg}, 1fr)`;
    if (columns.xl) return `repeat(${columns.xl}, 1fr)`;
    
    // Default fallback
    return {
      xs: `repeat(${columns.xs || 1}, 1fr)`,
      sm: `repeat(${columns.sm || 2}, 1fr)`,
      md: `repeat(${columns.md || 3}, 1fr)`,
      lg: `repeat(${columns.lg || 4}, 1fr)`,
      xl: `repeat(${columns.xl || 4}, 1fr)`
    };
  };

  return (
    <Box
      className={className}
      sx={{
        display: 'grid',
        gridTemplateColumns: getGridColumns(),
        gap,
        width: '100%'
      }}
    >
      {children}
    </Box>
  );
};

export default ResponsiveGrid;
