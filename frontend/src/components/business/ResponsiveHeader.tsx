import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  useMediaQuery
} from '@mui/material';

interface ResponsiveHeaderProps {
  title: string;
  subtitle?: string;
  primaryAction?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
  };
  filters?: Array<{
    label: string;
    value: string;
    options: Array<{ value: string; label: string }>;
    onChange: (value: string) => void;
  }>;
  children?: React.ReactNode;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  title,
  subtitle,
  primaryAction,
  filters = [],
  children
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box 
      className={isMobile ? 'business-mobile-header' : ''}
      sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: isMobile ? 'stretch' : 'center', 
        mb: 4,
        p: 3,
        background: 'linear-gradient(135deg, var(--business-primary) 0%, var(--business-secondary) 100%)',
        borderRadius: 2,
        color: 'white',
        flexDirection: isMobile ? 'column' : 'row',
        gap: isMobile ? 2 : 0
      }}
    >
      {/* Title Section */}
      <Box sx={{ 
        textAlign: isMobile ? 'center' : 'left',
        mb: isMobile ? 2 : 0
      }}>
        <Typography 
          variant={isSmallMobile ? 'h5' : 'h4'} 
          sx={{ 
            fontWeight: 700, 
            mb: subtitle ? 1 : 0,
            fontSize: isSmallMobile ? '1.25rem' : undefined
          }}
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography 
            variant="body1" 
            sx={{ 
              opacity: 0.9,
              fontSize: isSmallMobile ? '0.875rem' : undefined
            }}
          >
            {subtitle}
          </Typography>
        )}
      </Box>

      {/* Controls Section */}
      <Box 
        className={isMobile ? 'business-mobile-controls' : ''}
        sx={{ 
          display: 'flex', 
          gap: 2,
          alignItems: 'center',
          flexDirection: isMobile ? 'column' : 'row',
          width: isMobile ? '100%' : 'auto'
        }}
      >
        {/* Filters */}
        {filters.map((filter, index) => (
          <FormControl 
            key={index}
            size="small" 
            sx={{ 
              minWidth: isMobile ? '100%' : 150,
              '& .MuiInputLabel-root': {
                color: 'white'
              },
              '& .MuiOutlinedInput-root': {
                color: 'white',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255,255,255,0.3)'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255,255,255,0.5)'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'white'
                }
              },
              '& .MuiSvgIcon-root': {
                color: 'white'
              }
            }}
          >
            <InputLabel>{filter.label}</InputLabel>
            <Select
              value={filter.value}
              label={filter.label}
              onChange={(e) => filter.onChange(e.target.value)}
            >
              {filter.options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        ))}

        {/* Primary Action */}
        {primaryAction && (
          <Button
            variant="contained"
            startIcon={primaryAction.icon}
            onClick={primaryAction.onClick}
            sx={{
              bgcolor: 'white',
              color: 'var(--business-primary)',
              '&:hover': {
                bgcolor: 'rgba(255,255,255,0.9)',
                transform: 'translateY(-2px)'
              },
              fontWeight: 600,
              px: 3,
              py: 1.5,
              minWidth: isMobile ? '100%' : 'auto',
              fontSize: isSmallMobile ? '0.875rem' : undefined
            }}
          >
            {primaryAction.label}
          </Button>
        )}

        {/* Additional children */}
        {children}
      </Box>
    </Box>
  );
};

export default ResponsiveHeader;
