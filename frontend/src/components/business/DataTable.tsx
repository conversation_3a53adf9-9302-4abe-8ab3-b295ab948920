import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  TextField,
  InputAdornment,
  Skeleton,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  MoreVert,
  Search,
  FilterList,
  Download
} from '@mui/icons-material';

export interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => string | React.ReactNode;
  sortable?: boolean;
}

export interface DataTableProps {
  columns: Column[];
  rows: any[];
  title?: string;
  loading?: boolean;
  searchable?: boolean;
  filterable?: boolean;
  downloadable?: boolean;
  onRowClick?: (row: any) => void;
  onDownload?: () => void;
  rowsPerPageOptions?: number[];
  defaultRowsPerPage?: number;
  stickyHeader?: boolean;
  maxHeight?: number;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  rows,
  title,
  loading = false,
  searchable = true,
  filterable = false,
  downloadable = false,
  onRowClick,
  onDownload,
  rowsPerPageOptions = [10, 25, 50],
  defaultRowsPerPage = 10,
  stickyHeader = true,
  maxHeight = 600
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);
  const [orderBy, setOrderBy] = useState<string>('');
  const [order, setOrder] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const filteredRows = rows.filter(row =>
    searchTerm === '' || 
    Object.values(row).some(value => 
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const sortedRows = filteredRows.sort((a, b) => {
    if (orderBy === '') return 0;
    
    const aValue = a[orderBy];
    const bValue = b[orderBy];
    
    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });

  const paginatedRows = sortedRows.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  if (loading) {
    return (
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        {title && (
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Skeleton variant="text" width={200} height={32} />
          </Box>
        )}
        <Box sx={{ p: 2 }}>
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} variant="rectangular" height={48} sx={{ mb: 1 }} />
          ))}
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {(title || searchable || filterable || downloadable) && (
        <Box sx={{ 
          p: 2, 
          borderBottom: 1, 
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <Box>
            {title && (
              <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
                {title}
              </Typography>
            )}
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {searchable && (
              <TextField
                size="small"
                placeholder="البحث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search fontSize="small" />
                      </InputAdornment>
                    ),
                  }
                }}
                sx={{ minWidth: 200 }}
              />
            )}
            
            {(filterable || downloadable) && (
              <>
                <IconButton onClick={handleMenuClick} size="small">
                  <MoreVert />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  {filterable && (
                    <MenuItem onClick={handleMenuClose}>
                      <FilterList sx={{ mr: 1 }} fontSize="small" />
                      تصفية البيانات
                    </MenuItem>
                  )}
                  {downloadable && (
                    <MenuItem onClick={() => { onDownload?.(); handleMenuClose(); }}>
                      <Download sx={{ mr: 1 }} fontSize="small" />
                      تحميل البيانات
                    </MenuItem>
                  )}
                </Menu>
              </>
            )}
          </Box>
        </Box>
      )}

      <TableContainer
        sx={{
          maxHeight,
          overflowX: isMobile ? 'auto' : 'hidden'
        }}
        className={isMobile ? 'business-table-mobile' : ''}
      >
        <Table
          stickyHeader={stickyHeader}
          sx={{
            minWidth: isMobile ? 600 : 'auto'
          }}
        >
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{
                    minWidth: column.minWidth,
                    fontSize: isMobile ? '0.75rem' : '0.875rem'
                  }}
                  sx={{
                    fontWeight: 600,
                    bgcolor: 'grey.50',
                    whiteSpace: isMobile ? 'nowrap' : 'normal'
                  }}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? order : 'asc'}
                      onClick={() => handleRequestSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRows.map((row, index) => (
              <TableRow 
                hover 
                key={index}
                onClick={() => onRowClick?.(row)}
                sx={{ 
                  cursor: onRowClick ? 'pointer' : 'default',
                  '&:hover': onRowClick ? { bgcolor: 'action.hover' } : {}
                }}
              >
                {columns.map((column) => {
                  const value = row[column.id];
                  return (
                    <TableCell
                      key={column.id}
                      align={column.align}
                      sx={{
                        fontSize: isMobile ? '0.75rem' : '0.875rem',
                        whiteSpace: isMobile ? 'nowrap' : 'normal',
                        overflow: isMobile ? 'hidden' : 'visible',
                        textOverflow: isMobile ? 'ellipsis' : 'clip'
                      }}
                    >
                      {column.format ? column.format(value) : value}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={rowsPerPageOptions}
        component="div"
        count={filteredRows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="عدد الصفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => 
          `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
        }
      />
    </Paper>
  );
};

export default DataTable;
