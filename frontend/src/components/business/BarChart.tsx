import React from 'react';
import { Box, Typography } from '@mui/material';
import { Bar<PERSON>hart as BarChartIcon } from '@mui/icons-material';

interface DataPoint {
  label: string;
  value: number;
  color?: string;
}

interface BarChartProps {
  data: DataPoint[];
  title?: string;
  height?: number;
  showValues?: boolean;
  horizontal?: boolean;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  height = 300,
  showValues = true,
  horizontal = false
}) => {
  if (!data || data.length === 0) {
    return (
      <Box sx={{ 
        height, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        flexDirection: 'column',
        color: 'text.secondary'
      }}>
        <BarChartIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
        <Typography variant="body2">لا توجد بيانات للعرض</Typography>
      </Box>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  return (
    <Box sx={{ height, p: 2 }}>
      {title && (
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
      )}
      
      <Box sx={{ 
        height: 'calc(100% - 40px)',
        display: 'flex',
        flexDirection: horizontal ? 'column' : 'row',
        alignItems: horizontal ? 'stretch' : 'flex-end',
        justifyContent: 'space-around',
        gap: 1,
        p: 1
      }}>
        {data.map((item, index) => {
          const barColor = item.color || colors[index % colors.length];
          const percentage = (item.value / maxValue) * 100;
          
          return (
            <Box
              key={index}
              sx={{
                display: 'flex',
                flexDirection: horizontal ? 'row' : 'column',
                alignItems: 'center',
                flex: 1,
                minWidth: horizontal ? 'auto' : '40px',
                minHeight: horizontal ? '40px' : 'auto'
              }}
            >
              {/* Bar */}
              <Box
                sx={{
                  width: horizontal ? `${percentage}%` : '100%',
                  height: horizontal ? '100%' : `${percentage}%`,
                  backgroundColor: barColor,
                  borderRadius: 1,
                  position: 'relative',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  '&:hover': {
                    opacity: 0.8,
                    transform: horizontal ? 'scaleX(1.05)' : 'scaleY(1.05)',
                    transformOrigin: horizontal ? 'left' : 'bottom'
                  },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: horizontal ? '30px' : '20px',
                  minWidth: horizontal ? '20px' : '30px'
                }}
              >
                {/* Value inside bar */}
                {showValues && percentage > 20 && (
                  <Typography
                    variant="caption"
                    sx={{
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.7rem'
                    }}
                  >
                    {item.value}
                  </Typography>
                )}
              </Box>

              {/* Value outside bar (for small bars) */}
              {showValues && percentage <= 20 && (
                <Typography
                  variant="caption"
                  sx={{
                    color: 'text.secondary',
                    fontWeight: 600,
                    fontSize: '0.7rem',
                    mt: horizontal ? 0 : 0.5,
                    ml: horizontal ? 0.5 : 0
                  }}
                >
                  {item.value}
                </Typography>
              )}

              {/* Label */}
              <Typography
                variant="caption"
                sx={{
                  color: 'text.secondary',
                  textAlign: 'center',
                  fontSize: '0.7rem',
                  mt: horizontal ? 0 : 1,
                  ml: horizontal ? 1 : 0,
                  maxWidth: horizontal ? 'none' : '60px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: horizontal ? 'nowrap' : 'normal',
                  lineHeight: horizontal ? 1 : 1.2
                }}
                title={item.label}
              >
                {item.label}
              </Typography>
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default BarChart;
