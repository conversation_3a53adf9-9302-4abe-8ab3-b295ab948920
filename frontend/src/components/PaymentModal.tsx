import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Chip,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  CreditCard,
  AccountBalance,
  Phone,
  Payment
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

interface PaymentModalProps {
  open: boolean;
  onClose: () => void;
  plan: string;
  amount: number;
  onSuccess: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  open,
  onClose,
  plan,
  amount,
  onSuccess
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [paymentData, setPaymentData] = useState({
    phone: '',
    currency: 'USD'
  });

  const paymentMethods = [
    { id: 'stripe', name: 'Credit/Debit Card', icon: <CreditCard />, description: 'Visa, Mastercard, American Express' },
    { id: 'paypal', name: 'PayPal', icon: <AccountBalance />, description: 'Pay with your PayPal account' },
    { id: 'paymob', name: 'Paymob (Egypt)', icon: <Payment />, description: 'Egyptian payment gateway' },
    { id: 'fawry', name: 'Fawry (Egypt)', icon: <Phone />, description: 'Pay at Fawry locations' }
  ];

  const handleStripePayment = async () => {
    try {
      setLoading(true);
      setError('');

      // Create Stripe subscription
      const result = await apiClient.post('/api/v1/payments/stripe/create-subscription', {
        plan: plan.toLowerCase()
      });

      if (result.data.success) {
        // In a real implementation, you would use Stripe Elements here
        // For demo purposes, we'll simulate success
        setTimeout(() => {
          setLoading(false);
          onSuccess();
          onClose();
        }, 2000);
      }
    } catch (err: any) {
      setLoading(false);
      setError(err.response?.data?.detail || 'Payment failed');
    }
  };

  const handlePayPalPayment = async () => {
    try {
      setLoading(true);
      setError('');

      const result = await apiClient.post('/api/v1/payments/paypal/create-payment', {
        amount,
        currency: paymentData.currency,
        return_url: `${window.location.origin}/payment/success`,
        cancel_url: `${window.location.origin}/payment/cancel`
      });

      if (result.data.success) {
        // Redirect to PayPal
        window.location.href = result.data.approval_url;
      }
    } catch (err: any) {
      setLoading(false);
      setError(err.response?.data?.detail || 'Payment failed');
    }
  };

  const handlePaymobPayment = async () => {
    try {
      setLoading(true);
      setError('');

      const result = await apiClient.post('/api/v1/payments/egyptian/paymob', {
        amount: amount * 30, // Convert to EGP (approximate rate)
        phone: paymentData.phone
      });

      if (result.data.success) {
        // Open Paymob iframe
        window.open(result.data.iframe_url, '_blank');
        setLoading(false);
      }
    } catch (err: any) {
      setLoading(false);
      setError(err.response?.data?.detail || 'Payment failed');
    }
  };

  const handleFawryPayment = async () => {
    try {
      setLoading(true);
      setError('');

      const result = await apiClient.post('/api/v1/payments/egyptian/fawry', {
        amount: amount * 30, // Convert to EGP
        phone: paymentData.phone
      });

      if (result.data.success) {
        // Show Fawry reference number
        alert(`Fawry Reference Number: ${result.data.reference_number}\nVisit any Fawry location to complete payment.`);
        setLoading(false);
      }
    } catch (err: any) {
      setLoading(false);
      setError(err.response?.data?.detail || 'Payment failed');
    }
  };

  const handlePayment = () => {
    switch (tabValue) {
      case 0:
        handleStripePayment();
        break;
      case 1:
        handlePayPalPayment();
        break;
      case 2:
        handlePaymobPayment();
        break;
      case 3:
        handleFawryPayment();
        break;
    }
  };

  const renderPaymentForm = () => {
    const method = paymentMethods[tabValue];

    return (
      <Box sx={{ mt: 2 }}>
        <Card variant="outlined" sx={{ mb: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              {method.icon}
              <Typography variant="h6" sx={{ ml: 1 }}>
                {method.name}
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              {method.description}
            </Typography>
          </CardContent>
        </Card>

        {/* Payment specific forms */}
        {(tabValue === 2 || tabValue === 3) && (
          <TextField
            fullWidth
            label="Phone Number"
            value={paymentData.phone}
            onChange={(e) => setPaymentData({ ...paymentData, phone: e.target.value })}
            placeholder="+20 1234567890"
            sx={{ mb: 2 }}
            required
          />
        )}

        {tabValue === 0 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              In a production environment, Stripe Elements would be integrated here for secure card input.
            </Alert>
            <TextField
              fullWidth
              label="Card Number"
              placeholder="4242 4242 4242 4242"
              sx={{ mb: 2 }}
              disabled
            />
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                label="Expiry"
                placeholder="MM/YY"
                disabled
              />
              <TextField
                label="CVC"
                placeholder="123"
                disabled
              />
            </Box>
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
          <Typography variant="h6">
            Total: {tabValue >= 2 ? `${(amount * 30).toFixed(0)} EGP` : `$${amount}`}
          </Typography>
          <Chip
            label={plan}
            color="primary"
            variant="outlined"
          />
        </Box>
      </Box>
    );
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h5">
          Subscribe to {plan} Plan
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ mb: 2 }}
        >
          {paymentMethods.map((method, index) => (
            <Tab
              key={method.id}
              label={method.name}
              icon={method.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>

        {renderPaymentForm()}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handlePayment}
          variant="contained"
          disabled={loading || (tabValue >= 2 && !paymentData.phone)}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Processing...' : `Pay ${tabValue >= 2 ? `${(amount * 30).toFixed(0)} EGP` : `$${amount}`}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentModal;
