import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Alert,
  Tab,
  Tabs,
  Paper,
  Chip
} from '@mui/material';
import {
  AutoAwesome,
  Psychology,
  TrendingUp,
  Campaign,
  Tag,
  Analytics,
  ModelTraining,
  Assessment,
  Lightbulb,
  Speed,
  Security,
  Star
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

// استيراد المكونات المحسنة
import EnhancedCard from './ui/EnhancedCard';
import { PrimaryButton, SecondaryButton, OutlineButton } from './ui/EnhancedButton';
import { FormField, SelectField, FormContainer } from './ui/EnhancedForm';
import { LoadingOverlay, AnimatedBox, LoadingSpinner } from './ui/LoadingAndAnimations';
import { theme } from '../theme';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tabpanel-${index}`}
      aria-labelledby={`ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const EnhancedAITools: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [aiStatus, setAiStatus] = useState<any>(null);

  // Content Generation State
  const [contentForm, setContentForm] = useState({
    content_type: 'social',
    prompt: '',
    target_audience: 'general',
    tone: 'professional'
  });

  // Sentiment Analysis State
  const [sentimentText, setSentimentText] = useState('');

  // Competitor Analysis State
  const [competitorForm, setCompetitorForm] = useState({
    name: '',
    website: '',
    description: ''
  });

  // Strategy Generation State
  const [strategyForm, setStrategyForm] = useState({
    company_name: '',
    industry: '',
    target_audience: '',
    budget: '',
    goals: ''
  });

  // Hashtag Generation State
  const [hashtagForm, setHashtagForm] = useState({
    content: '',
    platform: 'instagram'
  });

  useEffect(() => {
    checkAIStatus();
  }, []);

  const checkAIStatus = async () => {
    try {
      const response = await apiClient.get('/ai/status');
      setAiStatus(response.data);
    } catch (error) {
      console.error('Failed to check AI status:', error);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setResult(null);
    setError('');
  };

  const generateContent = async () => {
    if (!contentForm.prompt.trim()) {
      setError('يرجى إدخال وصف للمحتوى المطلوب');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await apiClient.post('/ai/generate-content', contentForm);
      const basicResult = response.data.data;

      // Enhance with MarketMind sentiment analysis
      try {
        const sentimentResponse = await apiClient.post('/marketmind/sentiment-analysis', {
          texts: [basicResult.content]
        });

        if (sentimentResponse.data.success) {
          const sentimentData = sentimentResponse.data.data.results[0];
          basicResult.sentiment_analysis = {
            sentiment: sentimentData.sentiment,
            confidence: sentimentData.confidence,
            source: 'MarketMind AI'
          };
          basicResult.enhanced = true;
        }
      } catch (sentimentError) {
        console.log('Failed to enhance with sentiment analysis:', sentimentError);
      }

      setResult(basicResult);
    } catch (error: any) {
      console.error('Content generation error:', error);
      setError(error.response?.data?.detail || 'فشل في إنشاء المحتوى');
    } finally {
      setLoading(false);
    }
  };

  const analyzeSentiment = async () => {
    if (!sentimentText.trim()) {
      setError('يرجى إدخال النص المراد تحليله');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const texts = [sentimentText];
      const marketmindResponse = await apiClient.post('/marketmind/sentiment-analysis', { texts });

      if (marketmindResponse.data.success) {
        const marketmindResult = marketmindResponse.data.data.results[0];
        const transformedResult = {
          sentiment: marketmindResult.sentiment,
          confidence: marketmindResult.confidence,
          positive: marketmindResult.probabilities?.positive || 0,
          negative: marketmindResult.probabilities?.negative || 0,
          neutral: marketmindResult.probabilities?.neutral || 0,
          compound_score: marketmindResult.confidence,
          word_count: sentimentText.split(' ').length,
          source: 'MarketMind AI',
          advanced: true
        };
        setResult(transformedResult);
        return;
      }
    } catch (marketmindError) {
      console.log('MarketMind sentiment analysis failed, falling back to basic analysis:', marketmindError);
    }

    // Fallback to basic sentiment analysis
    try {
      const response = await apiClient.post('/ai/analyze-sentiment', {
        text: sentimentText,
        use_ai: true
      });
      const basicResult = response.data.data;
      setResult({
        ...basicResult,
        source: 'Basic AI',
        advanced: false,
        positive: basicResult.positive || 0,
        negative: basicResult.negative || 0,
        neutral: basicResult.neutral || 0,
        word_count: sentimentText.split(' ').length
      });
    } catch (error: any) {
      console.error('Sentiment analysis error:', error);
      setError(error.response?.data?.detail || 'فشل في تحليل المشاعر');
    } finally {
      setLoading(false);
    }
  };

  // إحصائيات الأدوات
  const toolsStats = [
    {
      title: 'أدوات ذكية',
      value: '7+',
      subtitle: 'أداة متقدمة',
      icon: <AutoAwesome />,
      color: 'primary' as const,
      trend: { direction: 'up' as const, value: '+2', label: 'أدوات جديدة' }
    },
    {
      title: 'نماذج AI',
      value: 'متقدمة',
      subtitle: 'تقنية حديثة',
      icon: <Psychology />,
      color: 'info' as const,
      trend: { direction: 'up' as const, value: '99%', label: 'دقة عالية' }
    },
    {
      title: 'متاح دائماً',
      value: '24/7',
      subtitle: 'خدمة مستمرة',
      icon: <Speed />,
      color: 'success' as const,
      trend: { direction: 'neutral' as const, value: '100%', label: 'وقت التشغيل' }
    },
    {
      title: 'آمن ومحمي',
      value: 'SSL',
      subtitle: 'حماية البيانات',
      icon: <Security />,
      color: 'warning' as const,
      trend: { direction: 'up' as const, value: 'A+', label: 'تقييم الأمان' }
    }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <LoadingOverlay loading={false}>
        <AnimatedBox animation="fadeIn" duration={0.6}>
          {/* العنوان الرئيسي */}
          <Box sx={{ mb: 6, textAlign: 'center' }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: theme.typography.fontSize['4xl'],
                fontWeight: theme.typography.fontWeight.bold,
                background: theme.effects.gradients.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2
              }}
            >
              <AutoAwesome sx={{ fontSize: '3rem', color: theme.colors.primary[500] }} />
              أدوات الذكاء الاصطناعي
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: theme.colors.text.secondary,
                fontWeight: theme.typography.fontWeight.medium,
                maxWidth: '600px',
                mx: 'auto',
                lineHeight: theme.typography.lineHeight.relaxed
              }}
            >
              مجموعة شاملة من أدوات الذكاء الاصطناعي المتقدمة لتحسين استراتيجياتك التسويقية وتطوير أعمالك
            </Typography>
          </Box>

          {/* إحصائيات الأدوات */}
          <AnimatedBox animation="slideInRight" delay={0.2} duration={0.5}>
            <Grid container spacing={3} sx={{ mb: 6 }}>
              {toolsStats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={stat.title}>
                  <EnhancedCard
                    title={stat.title}
                    value={stat.value}
                    subtitle={stat.subtitle}
                    trend={stat.trend}
                    icon={stat.icon}
                    color={stat.color}
                    variant="gradient"
                  />
                </Grid>
              ))}
            </Grid>
          </AnimatedBox>

          {/* تنبيهات الحالة */}
          <AnimatedBox animation="fadeIn" delay={0.4} duration={0.5}>
            {aiStatus && !aiStatus.openai_available && (
              <Alert 
                severity="warning" 
                sx={{ 
                  mb: 3,
                  borderRadius: theme.spacing.border.radius.lg,
                  '& .MuiAlert-message': {
                    fontSize: theme.typography.fontSize.sm
                  }
                }}
              >
                خدمة الذكاء الاصطناعي غير متاحة حالياً. يتم استخدام القوالب الاحتياطية.
              </Alert>
            )}

            <Alert 
              severity="success" 
              sx={{ 
                mb: 4,
                borderRadius: theme.spacing.border.radius.lg,
                background: theme.effects.gradients.success,
                color: theme.colors.text.inverse,
                '& .MuiAlert-icon': {
                  color: 'inherit'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  🚀 تم تحسين أدوات الذكاء الاصطناعي بنماذج MarketMind المتقدمة للحصول على نتائج أكثر دقة ودقة
                </Typography>
                <SecondaryButton
                  size="small"
                  onClick={() => setActiveTab(6)}
                  startIcon={<Assessment />}
                  sx={{
                    backgroundColor: theme.colors.background.primary,
                    color: theme.colors.primary[600],
                    '&:hover': {
                      backgroundColor: theme.colors.primary[50]
                    }
                  }}
                >
                  نماذج متقدمة
                </SecondaryButton>
              </Box>
            </Alert>
          </AnimatedBox>

          {/* الأدوات الرئيسية */}
          <AnimatedBox animation="fadeIn" delay={0.6} duration={0.6}>
            <Paper
              sx={{
                borderRadius: theme.spacing.border.radius['2xl'],
                boxShadow: theme.effects.shadows.xl,
                overflow: 'hidden',
                border: `1px solid ${theme.colors.border.light}`
              }}
            >
              {/* شريط التبويبات */}
              <Box
                sx={{
                  borderBottom: `1px solid ${theme.colors.border.light}`,
                  background: theme.effects.gradients.card
                }}
              >
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  aria-label="AI tools tabs"
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    '& .MuiTab-root': {
                      minHeight: '72px',
                      fontWeight: theme.typography.fontWeight.semibold,
                      fontSize: theme.typography.fontSize.sm,
                      transition: theme.effects.transitions.component.button,
                      '&.Mui-selected': {
                        background: theme.effects.gradients.primary,
                        color: theme.colors.text.inverse,
                        borderRadius: `${theme.spacing.border.radius.lg} ${theme.spacing.border.radius.lg} 0 0`,
                        transform: theme.effects.transforms.translate.y.sm
                      },
                      '&:hover': {
                        backgroundColor: theme.colors.primary[50],
                        transform: theme.effects.transforms.translate.y.sm
                      }
                    }
                  }}
                >
                  <Tab
                    icon={<AutoAwesome />}
                    label="إنشاء المحتوى"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<Psychology />}
                    label="تحليل المشاعر"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<Analytics />}
                    label="تحليل المنافسين"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<TrendingUp />}
                    label="استراتيجية التسويق"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<Tag />}
                    label="إنشاء هاشتاجات"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<ModelTraining />}
                    label="النماذج المخصصة"
                    sx={{ minWidth: 140 }}
                  />
                  <Tab
                    icon={<Assessment />}
                    label="MarketMind AI"
                    sx={{ minWidth: 140 }}
                  />
                </Tabs>
              </Box>

              {/* محتوى التبويبات */}

              {/* تبويب إنشاء المحتوى */}
              <TabPanel value={activeTab} index={0}>
                <Alert
                  severity="success"
                  sx={{
                    mb: 3,
                    borderRadius: theme.spacing.border.radius.lg,
                    backgroundColor: theme.colors.success[50],
                    border: `1px solid ${theme.colors.success[200]}`
                  }}
                >
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
                    🚀 تم تحسين إنشاء المحتوى بتحليل المشاعر التلقائي من MarketMind AI
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    احصل على محتوى مُحسّن ومُخصص لجمهورك المستهدف مع تحليل فوري للمشاعر
                  </Typography>
                </Alert>

                <Grid container spacing={4}>
                  <Grid item xs={12} md={6}>
                    <FormContainer title="إعدادات المحتوى" description="حدد نوع المحتوى والمتطلبات">
                      <SelectField
                        label="نوع المحتوى"
                        name="content_type"
                        value={contentForm.content_type}
                        onChange={(value) => setContentForm({...contentForm, content_type: value as string})}
                        options={[
                          { value: 'email', label: 'رسالة بريد إلكتروني' },
                          { value: 'social', label: 'منشور وسائل التواصل' },
                          { value: 'ad_copy', label: 'نص إعلاني' },
                          { value: 'blog', label: 'مقال مدونة' },
                          { value: 'post', label: 'منشور عام' }
                        ]}
                        required
                      />

                      <SelectField
                        label="النبرة"
                        name="tone"
                        value={contentForm.tone}
                        onChange={(value) => setContentForm({...contentForm, tone: value as string})}
                        options={[
                          { value: 'professional', label: 'مهني' },
                          { value: 'casual', label: 'غير رسمي' },
                          { value: 'friendly', label: 'ودود' },
                          { value: 'urgent', label: 'عاجل' }
                        ]}
                        required
                      />

                      <FormField
                        label="الجمهور المستهدف"
                        name="target_audience"
                        value={contentForm.target_audience}
                        onChange={(value) => setContentForm({...contentForm, target_audience: value as string})}
                        placeholder="مثال: الشباب من 18-35 سنة"
                        required
                      />

                      <FormField
                        label="وصف المحتوى المطلوب"
                        name="prompt"
                        value={contentForm.prompt}
                        onChange={(value) => setContentForm({...contentForm, prompt: value as string})}
                        placeholder="مثال: اكتب منشور عن فوائد التسويق الرقمي للشركات الصغيرة"
                        multiline
                        rows={4}
                        required
                      />

                      <PrimaryButton
                        onClick={generateContent}
                        loading={loading}
                        startIcon={<AutoAwesome />}
                        fullWidth
                        size="large"
                      >
                        {loading ? 'جاري الإنشاء...' : 'إنشاء المحتوى'}
                      </PrimaryButton>
                    </FormContainer>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    {result && (
                      <AnimatedBox animation="fadeIn" duration={0.5}>
                        <Paper
                          sx={{
                            p: 3,
                            borderRadius: theme.spacing.border.radius.xl,
                            border: `1px solid ${theme.colors.border.light}`,
                            boxShadow: theme.effects.shadows.lg
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              mb: 2,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              color: theme.colors.text.primary,
                              fontWeight: theme.typography.fontWeight.semibold
                            }}
                          >
                            المحتوى المُنشأ
                            {result.enhanced && (
                              <Chip
                                label="محسن بـ MarketMind"
                                color="primary"
                                size="small"
                                icon={<Star />}
                              />
                            )}
                          </Typography>

                          <Typography
                            variant="body1"
                            sx={{
                              whiteSpace: 'pre-wrap',
                              mb: 3,
                              lineHeight: theme.typography.lineHeight.relaxed,
                              color: theme.colors.text.secondary
                            }}
                          >
                            {result.content}
                          </Typography>

                          {result.sentiment_analysis && (
                            <>
                              <Box sx={{
                                p: 2,
                                backgroundColor: theme.colors.background.secondary,
                                borderRadius: theme.spacing.border.radius.lg,
                                border: `1px solid ${theme.colors.border.light}`
                              }}>
                                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                                  تحليل المشاعر للمحتوى:
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                  <Chip
                                    label={`${result.sentiment_analysis.sentiment === 'positive' ? 'إيجابي' :
                                             result.sentiment_analysis.sentiment === 'negative' ? 'سلبي' : 'محايد'}
                                           (${(result.sentiment_analysis.confidence * 100).toFixed(1)}%)`}
                                    color={result.sentiment_analysis.sentiment === 'positive' ? 'success' :
                                           result.sentiment_analysis.sentiment === 'negative' ? 'error' : 'info'}
                                    size="small"
                                  />
                                  <Chip
                                    label={result.sentiment_analysis.source}
                                    variant="outlined"
                                    size="small"
                                  />
                                </Box>
                              </Box>
                            </>
                          )}

                          {result.suggestions && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                                اقتراحات للتحسين:
                              </Typography>
                              {result.suggestions.map((suggestion: string, index: number) => (
                                <Typography key={index} variant="body2" sx={{ mb: 0.5, color: theme.colors.text.tertiary }}>
                                  • {suggestion}
                                </Typography>
                              ))}
                            </Box>
                          )}
                        </Paper>
                      </AnimatedBox>
                    )}
                  </Grid>
                </Grid>
              </TabPanel>

              {/* تبويب تحليل المشاعر */}
              <TabPanel value={activeTab} index={1}>
                <Alert
                  severity="info"
                  sx={{
                    mb: 3,
                    borderRadius: theme.spacing.border.radius.lg,
                    backgroundColor: theme.colors.info[50],
                    border: `1px solid ${theme.colors.info[200]}`
                  }}
                >
                  <Typography variant="body2">
                    ⚡ تحليل المشاعر المحسن: يستخدم نماذج MarketMind المتقدمة تلقائياً للحصول على دقة أعلى
                  </Typography>
                </Alert>

                <Grid container spacing={4}>
                  <Grid item xs={12} md={6}>
                    <FormContainer title="تحليل المشاعر" description="أدخل النص لتحليل المشاعر والعواطف">
                      <FormField
                        label="النص المراد تحليله"
                        name="sentimentText"
                        value={sentimentText}
                        onChange={(value) => setSentimentText(value as string)}
                        placeholder="أدخل النص الذي تريد تحليل مشاعره..."
                        multiline
                        rows={6}
                        required
                      />

                      <PrimaryButton
                        onClick={analyzeSentiment}
                        loading={loading}
                        startIcon={<Psychology />}
                        fullWidth
                        size="large"
                      >
                        {loading ? 'جاري التحليل...' : 'تحليل المشاعر'}
                      </PrimaryButton>
                    </FormContainer>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    {result && (
                      <AnimatedBox animation="fadeIn" duration={0.5}>
                        <Paper
                          sx={{
                            p: 3,
                            borderRadius: theme.spacing.border.radius.xl,
                            border: `1px solid ${theme.colors.border.light}`,
                            boxShadow: theme.effects.shadows.lg
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              mb: 2,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              color: theme.colors.text.primary,
                              fontWeight: theme.typography.fontWeight.semibold
                            }}
                          >
                            نتيجة تحليل المشاعر
                            {result.advanced && (
                              <Chip
                                label="MarketMind AI"
                                color="primary"
                                size="small"
                                icon={<AutoAwesome />}
                              />
                            )}
                          </Typography>

                          <Box sx={{ mb: 3 }}>
                            <Chip
                              label={`المشاعر: ${result.sentiment === 'positive' ? 'إيجابي' :
                                                result.sentiment === 'negative' ? 'سلبي' : 'محايد'}`}
                              color={result.sentiment === 'positive' ? 'success' :
                                     result.sentiment === 'negative' ? 'error' : 'info'}
                              sx={{ mr: 1, mb: 1 }}
                            />
                            <Chip
                              label={`الثقة: ${(result.confidence * 100).toFixed(1)}%`}
                              variant="outlined"
                              sx={{ mb: 1 }}
                            />
                            {result.source && (
                              <Chip
                                label={result.source}
                                variant="outlined"
                                size="small"
                                sx={{ ml: 1, mb: 1 }}
                              />
                            )}
                          </Box>

                          <Grid container spacing={2} sx={{ mb: 2 }}>
                            <Grid item xs={4}>
                              <Box sx={{
                                p: 2,
                                textAlign: 'center',
                                backgroundColor: theme.colors.success[50],
                                borderRadius: theme.spacing.border.radius.md,
                                border: `1px solid ${theme.colors.success[200]}`
                              }}>
                                <Typography variant="body2" sx={{ color: theme.colors.success[700], fontWeight: 600 }}>
                                  إيجابي
                                </Typography>
                                <Typography variant="h6" sx={{ color: theme.colors.success[800] }}>
                                  {(result.positive * 100).toFixed(1)}%
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={4}>
                              <Box sx={{
                                p: 2,
                                textAlign: 'center',
                                backgroundColor: theme.colors.info[50],
                                borderRadius: theme.spacing.border.radius.md,
                                border: `1px solid ${theme.colors.info[200]}`
                              }}>
                                <Typography variant="body2" sx={{ color: theme.colors.info[700], fontWeight: 600 }}>
                                  محايد
                                </Typography>
                                <Typography variant="h6" sx={{ color: theme.colors.info[800] }}>
                                  {(result.neutral * 100).toFixed(1)}%
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={4}>
                              <Box sx={{
                                p: 2,
                                textAlign: 'center',
                                backgroundColor: theme.colors.error[50],
                                borderRadius: theme.spacing.border.radius.md,
                                border: `1px solid ${theme.colors.error[200]}`
                              }}>
                                <Typography variant="body2" sx={{ color: theme.colors.error[700], fontWeight: 600 }}>
                                  سلبي
                                </Typography>
                                <Typography variant="h6" sx={{ color: theme.colors.error[800] }}>
                                  {(result.negative * 100).toFixed(1)}%
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>

                          {result.word_count && (
                            <Typography variant="body2" sx={{ color: theme.colors.text.tertiary }}>
                              عدد الكلمات: {result.word_count}
                            </Typography>
                          )}
                        </Paper>
                      </AnimatedBox>
                    )}
                  </Grid>
                </Grid>
              </TabPanel>

              {/* باقي التبويبات - سأضيفها في الجزء التالي */}
              <TabPanel value={activeTab} index={2}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <Analytics sx={{ fontSize: '4rem', color: theme.colors.primary[300], mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, color: theme.colors.text.primary }}>
                    تحليل المنافسين
                  </Typography>
                  <Typography variant="body1" sx={{ color: theme.colors.text.secondary, mb: 4 }}>
                    قريباً - أداة متقدمة لتحليل المنافسين وفهم استراتيجياتهم
                  </Typography>
                  <OutlineButton startIcon={<Lightbulb />}>
                    اقتراح ميزة
                  </OutlineButton>
                </Box>
              </TabPanel>

              <TabPanel value={activeTab} index={3}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <TrendingUp sx={{ fontSize: '4rem', color: theme.colors.primary[300], mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, color: theme.colors.text.primary }}>
                    استراتيجية التسويق
                  </Typography>
                  <Typography variant="body1" sx={{ color: theme.colors.text.secondary, mb: 4 }}>
                    قريباً - مولد استراتيجيات تسويقية مخصصة باستخدام الذكاء الاصطناعي
                  </Typography>
                  <OutlineButton startIcon={<Lightbulb />}>
                    اقتراح ميزة
                  </OutlineButton>
                </Box>
              </TabPanel>

              <TabPanel value={activeTab} index={4}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <Tag sx={{ fontSize: '4rem', color: theme.colors.primary[300], mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, color: theme.colors.text.primary }}>
                    إنشاء هاشتاجات
                  </Typography>
                  <Typography variant="body1" sx={{ color: theme.colors.text.secondary, mb: 4 }}>
                    قريباً - مولد هاشتاجات ذكي لجميع منصات التواصل الاجتماعي
                  </Typography>
                  <OutlineButton startIcon={<Lightbulb />}>
                    اقتراح ميزة
                  </OutlineButton>
                </Box>
              </TabPanel>

              <TabPanel value={activeTab} index={5}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <ModelTraining sx={{ fontSize: '4rem', color: theme.colors.primary[300], mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, color: theme.colors.text.primary }}>
                    النماذج المخصصة
                  </Typography>
                  <Typography variant="body1" sx={{ color: theme.colors.text.secondary, mb: 4 }}>
                    قريباً - إمكانية تدريب نماذج ذكاء اصطناعي مخصصة لاحتياجاتك
                  </Typography>
                  <OutlineButton startIcon={<Lightbulb />}>
                    اقتراح ميزة
                  </OutlineButton>
                </Box>
              </TabPanel>

              <TabPanel value={activeTab} index={6}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <Assessment sx={{ fontSize: '4rem', color: theme.colors.primary[300], mb: 2 }} />
                  <Typography variant="h5" sx={{ mb: 2, color: theme.colors.text.primary }}>
                    MarketMind AI
                  </Typography>
                  <Typography variant="body1" sx={{ color: theme.colors.text.secondary, mb: 4 }}>
                    نماذج الذكاء الاصطناعي المتقدمة الخاصة بمنصة MarketMind
                  </Typography>
                  <PrimaryButton startIcon={<Star />}>
                    استكشف النماذج
                  </PrimaryButton>
                </Box>
              </TabPanel>
            </Paper>
          </AnimatedBox>

          {/* رسالة الخطأ */}
          {error && (
            <AnimatedBox animation="fadeIn" delay={0.2}>
              <Alert
                severity="error"
                sx={{
                  mt: 3,
                  borderRadius: theme.spacing.border.radius.lg
                }}
              >
                {error}
              </Alert>
            </AnimatedBox>
          )}
        </AnimatedBox>
      </LoadingOverlay>
    </Container>
  );
};

export default EnhancedAITools;
