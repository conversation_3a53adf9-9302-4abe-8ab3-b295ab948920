import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  Visibility,
  ThumbUp,
  AttachMoney,
  Campaign,
  Analytics,
  Lightbulb,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import { apiClient } from '../api/apiClient';

interface IntegratedAnalyticsProps {
  open: boolean;
  onClose: () => void;
}

const IntegratedAnalytics: React.FC<IntegratedAnalyticsProps> = ({ open, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (open) {
      fetchAnalyticsData();
    }
  }, [open]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await apiClient.post('/integrations/analytics/unified-report', {
        dateRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        }
      });
      
      setAnalyticsData(response.data.data);
    } catch (error: any) {
      setError('فشل في جلب البيانات التحليلية');
      console.error('Analytics fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'reach': return <People color="primary" />;
      case 'impressions': return <Visibility color="info" />;
      case 'engagement': return <ThumbUp color="success" />;
      case 'spend': return <AttachMoney color="warning" />;
      case 'conversions': return <Campaign color="secondary" />;
      default: return <Analytics />;
    }
  };

  const getTrendIcon = (value: number) => {
    return value >= 0 ? <TrendingUp color="success" /> : <TrendingDown color="error" />;
  };

  if (!open) return null;

  return (
    <Box sx={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      bgcolor: 'rgba(0,0,0,0.5)', 
      zIndex: 9999,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      p: 2
    }}>
      <Box sx={{ 
        bgcolor: 'background.paper', 
        borderRadius: 2, 
        maxWidth: '90vw', 
        maxHeight: '90vh', 
        overflow: 'auto',
        width: '100%'
      }}>
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Analytics color="primary" />
            التحليلات المدمجة
          </Typography>
          <Typography 
            variant="h6" 
            sx={{ cursor: 'pointer', color: 'text.secondary' }}
            onClick={onClose}
          >
            ✕
          </Typography>
        </Box>

        <Box sx={{ p: 3 }}>
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {analyticsData && (
            <Grid container spacing={3}>
              {/* Overview Metrics */}
              <Grid item xs={12}>
                <Typography variant="h5" gutterBottom>
                  📊 نظرة عامة على الأداء
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(analyticsData.overview?.totals || {}).map(([key, value]) => (
                    <Grid item xs={12} sm={6} md={4} lg={2} key={key}>
                      <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                          {getMetricIcon(key)}
                          <Typography variant="h6" sx={{ mt: 1 }}>
                            {formatNumber(value as number)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {key === 'reach' ? 'الوصول' :
                             key === 'impressions' ? 'المشاهدات' :
                             key === 'engagement' ? 'التفاعل' :
                             key === 'spend' ? 'الإنفاق' :
                             key === 'conversions' ? 'التحويلات' :
                             key === 'clicks' ? 'النقرات' : key}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              {/* Platform Breakdown */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📱 الأداء حسب المنصة
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>المنصة</TableCell>
                            <TableCell align="right">الوصول</TableCell>
                            <TableCell align="right">التفاعل</TableCell>
                            <TableCell align="right">معدل التفاعل</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.entries(analyticsData.overview?.platform_breakdown || {}).map(([platform, data]: [string, any]) => (
                            <TableRow key={platform}>
                              <TableCell>
                                <Chip 
                                  label={platform} 
                                  size="small" 
                                  color={platform === 'instagram' ? 'secondary' : 'primary'}
                                />
                              </TableCell>
                              <TableCell align="right">{formatNumber(data.reach)}</TableCell>
                              <TableCell align="right">{formatNumber(data.engagement)}</TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {data.engagement_rate.toFixed(1)}%
                                  <LinearProgress 
                                    variant="determinate" 
                                    value={Math.min(data.engagement_rate, 10) * 10} 
                                    sx={{ width: 50, height: 4 }}
                                  />
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>

              {/* ROI Analysis */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      💰 تحليل العائد على الاستثمار
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        العائد الإجمالي
                      </Typography>
                      <Typography variant="h4" color={analyticsData.roi_analysis?.overall_roi >= 0 ? 'success.main' : 'error.main'}>
                        {getTrendIcon(analyticsData.roi_analysis?.overall_roi || 0)}
                        {(analyticsData.roi_analysis?.overall_roi || 0).toFixed(1)}%
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      الإنفاق الإجمالي: {formatNumber(analyticsData.roi_analysis?.total_spend || 0)} ريال
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الإيرادات الإجمالية: {formatNumber(analyticsData.roi_analysis?.total_revenue || 0)} ريال
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* AI Recommendations */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Lightbulb color="warning" />
                      توصيات ذكية للتحسين
                    </Typography>
                    <List>
                      {(analyticsData.recommendations || []).map((rec: any, index: number) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            {rec.priority === 'high' ? <Warning color="error" /> : <CheckCircle color="success" />}
                          </ListItemIcon>
                          <ListItemText
                            primary={rec.title}
                            secondary={
                              <Box>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                  {rec.description}
                                </Typography>
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                  {rec.actions?.map((action: string, actionIndex: number) => (
                                    <Chip 
                                      key={actionIndex}
                                      label={action}
                                      size="small"
                                      variant="outlined"
                                    />
                                  ))}
                                </Box>
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Engagement Trends */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📈 اتجاهات التفاعل
                    </Typography>
                    <Box sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h3" color={analyticsData.engagement_trends?.trend_percentage >= 0 ? 'success.main' : 'error.main'}>
                        {getTrendIcon(analyticsData.engagement_trends?.trend_percentage || 0)}
                        {Math.abs(analyticsData.engagement_trends?.trend_percentage || 0).toFixed(1)}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {analyticsData.engagement_trends?.trend_percentage >= 0 ? 'زيادة' : 'انخفاض'} في التفاعل هذا الأسبوع
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      متوسط التفاعل اليومي: {formatNumber(analyticsData.engagement_trends?.average_daily_engagement || 0)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Audience Insights */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      👥 رؤى الجمهور
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      المنصات المربوطة: {analyticsData.overview?.connected_platforms || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      تداخل الجمهور المقدر: {analyticsData.audience_insights?.audience_overlap?.estimated_overlap || 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل نمو الجمهور: {analyticsData.audience_insights?.growth_trends?.growth_rate || 0}%
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default IntegratedAnalytics;
