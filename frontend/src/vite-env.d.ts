/// <reference types="vite/client" />

// AOS (Animate On Scroll) library type declaration
declare global {
  interface Window {
    AOS: {
      init: (options?: {
        duration?: number;
        easing?: string;
        once?: boolean;
        offset?: number;
        delay?: number;
        anchor?: string;
        anchorPlacement?: string;
        disable?: boolean | string | (() => boolean);
        startEvent?: string;
        animatedClassName?: string;
        initClassName?: string;
        useClassNames?: boolean;
        disableMutationObserver?: boolean;
        throttleDelay?: number;
        debounceDelay?: number;
      }) => void;
      refresh: () => void;
      refreshHard: () => void;
    };
  }
}
