<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MarketMind - منصة الذكاء الاصطناعي للتسويق الرقمي</title>

  <!-- SEO Meta Tags -->
  <meta name="description" content="حول بياناتك إلى قرارات ذكية مع أقوى منصة ذكاء اصطناعي للتسويق في المنطقة">
  <meta name="keywords" content="ذكاء اصطناعي, تسويق رقمي, تحليل البيانات, MarketMind">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="MarketMind - منصة الذكاء الاصطناعي للتسويق الرقمي">
  <meta property="og:description" content="حول بياناتك إلى قرارات ذكية مع أقوى منصة ذكاء اصطناعي للتسويق في المنطقة">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://marketmind.ai">
  <meta property="og:image" content="https://marketmind.ai/assets/og-image.jpg">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="MarketMind - منصة الذكاء الاصطناعي للتسويق الرقمي">
  <meta name="twitter:description" content="حول بياناتك إلى قرارات ذكية مع أقوى منصة ذكاء اصطناعي للتسويق في المنطقة">
  <meta name="twitter:image" content="https://marketmind.ai/assets/twitter-image.jpg">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/vite.svg">

  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

  <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script>
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });
  </script>
</body>
</html>