# Flask Configuration
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=jwt-secret-string
DEBUG=False
HOST=0.0.0.0
PORT=5000

# Database Configuration
DATABASE_URL=sqlite:///marketmind.db
# For PostgreSQL: postgresql://username:password@localhost/marketmind
# For MySQL: mysql://username:password@localhost/marketmind

# AI Models Configuration
AI_MODELS_PATH=./models
MODEL_CACHE_SIZE=100
PREDICTION_CONFIDENCE_THRESHOLD=0.7

# External APIs (Optional)
OPENAI_API_KEY=your-openai-api-key
GOOGLE_ANALYTICS_API_KEY=your-ga-api-key
FACEBOOK_API_KEY=your-facebook-api-key
TWITTER_API_KEY=your-twitter-api-key

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174
SESSION_TIMEOUT=24
MAX_LOGIN_ATTEMPTS=5

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_FOLDER=./uploads
ALLOWED_EXTENSIONS=csv,xlsx,json

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
