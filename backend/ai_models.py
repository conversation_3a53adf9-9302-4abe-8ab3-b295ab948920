import numpy as np
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error
import joblib
import random
from datetime import datetime, timedelta
import json
import re
from textblob import TextBlob
import logging

logger = logging.getLogger(__name__)

class CustomerSegmentationModel:
    def __init__(self):
        self.model = KMeans(n_clusters=5, random_state=42)
        self.scaler = StandardScaler()
        self.segment_names = [
            'العملاء ذوو القيمة العالية',
            'العملاء الجدد',
            'العملاء المعرضون للخطر',
            'العملاء المنتظمون',
            'العملاء الخاملون'
        ]

    def segment_customers(self, customer_data):
        try:
            if not customer_data:
                return []

            # Prepare features for clustering
            features = []
            for customer in customer_data:
                features.append([
                    customer['total_spent'],
                    customer['order_frequency'],
                    customer['last_order_days'],
                    customer['avg_order_value']
                ])

            features_array = np.array(features)

            # Scale features
            features_scaled = self.scaler.fit_transform(features_array)

            # Perform clustering
            clusters = self.model.fit_predict(features_scaled)

            # Create segments with detailed information
            segments = []
            for i, segment_name in enumerate(self.segment_names):
                segment_customers = [customer_data[j] for j, cluster in enumerate(clusters) if cluster == i]

                if segment_customers:
                    total_customers = len(segment_customers)
                    avg_value = np.mean([c['total_spent'] for c in segment_customers])
                    avg_frequency = np.mean([c['order_frequency'] for c in segment_customers])

                    # Generate characteristics and recommendations based on segment
                    characteristics, recommendations = self._get_segment_insights(i, avg_value, avg_frequency)

                    segments.append({
                        'id': f'segment_{i}',
                        'name': segment_name,
                        'customer_count': total_customers,
                        'percentage': (total_customers / len(customer_data)) * 100,
                        'avg_value': avg_value,
                        'avg_frequency': avg_frequency,
                        'characteristics': characteristics,
                        'recommendations': recommendations,
                        'customers': segment_customers[:10]  # Return sample customers
                    })

            return segments

        except Exception as e:
            logger.error(f"Customer segmentation error: {str(e)}")
            return []

    def _get_segment_insights(self, segment_id, avg_value, avg_frequency):
        insights = {
            0: {  # High-value customers
                'characteristics': ['إنفاق عالي', 'تفاعل منتظم', 'ولاء عالي', 'مشتريات متكررة'],
                'recommendations': ['برامج VIP خاصة', 'خدمة عملاء مميزة', 'عروض حصرية', 'تواصل شخصي']
            },
            1: {  # New customers
                'characteristics': ['حديثو الانضمام', 'استكشاف المنتجات', 'حاجة للتوجيه'],
                'recommendations': ['برامج ترحيب', 'دعم إضافي', 'عروض تجريبية', 'محتوى تعليمي']
            },
            2: {  # At-risk customers
                'characteristics': ['تفاعل منخفض', 'عدم شراء حديث', 'شكاوى سابقة'],
                'recommendations': ['حملات استرداد', 'عروض خاصة', 'تواصل مباشر', 'حل المشاكل']
            },
            3: {  # Regular customers
                'characteristics': ['شراء منتظم', 'قيمة متوسطة', 'رضا جيد'],
                'recommendations': ['برامج ولاء', 'تحفيز للترقية', 'منتجات مكملة', 'تجربة محسنة']
            },
            4: {  # Dormant customers
                'characteristics': ['عدم تفاعل', 'آخر شراء قديم', 'عدم فتح الإيميلات'],
                'recommendations': ['حملات إعادة تفعيل', 'استطلاعات رأي', 'عروض مغرية', 'تحديث البيانات']
            }
        }

        return insights.get(segment_id, {
            'characteristics': ['تحليل مخصص'],
            'recommendations': ['استراتيجية مخصصة']
        }).values()

class ChurnPredictionModel:
    def __init__(self):
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False

    def predict_churn(self, customer_data):
        try:
            if not customer_data:
                return []

            predictions = []

            for customer in customer_data:
                # Calculate churn probability based on customer metrics
                churn_prob = self._calculate_churn_probability(customer)
                risk_level = self._get_risk_level(churn_prob)
                factors = self._identify_churn_factors(customer)
                recommendations = self._get_retention_recommendations(risk_level, factors)

                predictions.append({
                    'customer_id': customer['customer_id'],
                    'name': customer['name'],
                    'email': customer['email'],
                    'churn_probability': churn_prob,
                    'risk_level': risk_level,
                    'factors': factors,
                    'recommendations': recommendations,
                    'customer_value': customer['total_spent'],
                    'last_activity': customer['last_order_days']
                })

            # Sort by churn probability (highest risk first)
            predictions.sort(key=lambda x: x['churn_probability'], reverse=True)

            return predictions

        except Exception as e:
            logger.error(f"Churn prediction error: {str(e)}")
            return []

    def _calculate_churn_probability(self, customer):
        # Simple rule-based churn probability calculation
        score = 0

        # Days since last order
        if customer['last_order_days'] > 60:
            score += 30
        elif customer['last_order_days'] > 30:
            score += 15

        # Order frequency
        if customer['order_frequency'] < 1:
            score += 25
        elif customer['order_frequency'] < 2:
            score += 10

        # Support tickets
        if customer['support_tickets'] > 3:
            score += 20
        elif customer['support_tickets'] > 1:
            score += 10

        # Email engagement
        if customer['email_engagement'] < 20:
            score += 15
        elif customer['email_engagement'] < 50:
            score += 5

        return min(score, 95)  # Cap at 95%

    def _get_risk_level(self, churn_prob):
        if churn_prob >= 70:
            return 'high'
        elif churn_prob >= 40:
            return 'medium'
        else:
            return 'low'

    def _identify_churn_factors(self, customer):
        factors = []

        if customer['last_order_days'] > 30:
            factors.append('عدم شراء حديث')
        if customer['order_frequency'] < 2:
            factors.append('انخفاض التفاعل')
        if customer['support_tickets'] > 1:
            factors.append('شكاوى متعددة')
        if customer['email_engagement'] < 50:
            factors.append('عدم فتح الإيميلات')

        return factors if factors else ['تحليل مخصص مطلوب']

    def _get_retention_recommendations(self, risk_level, factors):
        base_recommendations = {
            'high': ['اتصال شخصي فوري', 'عرض خصم كبير', 'حل المشاكل العاجلة'],
            'medium': ['حملة إيميل مخصصة', 'عرض منتجات جديدة', 'استطلاع رضا'],
            'low': ['متابعة دورية', 'عروض تكميلية', 'محتوى قيم']
        }

        return base_recommendations.get(risk_level, ['استراتيجية مخصصة'])

class ContentGenerationModel:
    def __init__(self):
        self.templates = {
            'social-post': {
                'ar': [
                    '🌟 {keywords} - تجربة استثنائية تنتظرك!\n\n✨ اكتشف الجديد\n🚀 جودة مضمونة\n💎 أسعار مناسبة\n\n#{keywords_hash}',
                    '🎯 هل تبحث عن {keywords}؟\n\nنحن هنا لنقدم لك:\n• جودة عالية\n• خدمة ممتازة\n• أسعار تنافسية\n\n#{keywords_hash}',
                    '💫 {keywords} الآن متوفر!\n\nاستمتع بـ:\n🔥 عروض حصرية\n⚡ توصيل سريع\n🎁 هدايا مجانية\n\n#{keywords_hash}'
                ]
            },
            'email-subject': {
                'ar': [
                    '{keywords} - عرض حصري لك فقط! 🎉',
                    'لا تفوت فرصة {keywords} - خصم 50%! ⏰',
                    '{keywords} الجديد وصل - اكتشفه الآن! ✨',
                    'عاجل: {keywords} بأفضل الأسعار! 🔥'
                ]
            },
            'ad-copy': {
                'ar': [
                    'استمتع بأفضل {keywords} مع منتجاتنا المميزة. عروض حصرية وجودة عالية. اطلب الآن!',
                    'اكتشف {keywords} الذي يلبي احتياجاتك. خدمة متميزة وأسعار لا تقاوم. احجز مكانك!',
                    '{keywords} بجودة استثنائية وأسعار مناسبة. توصيل مجاني وضمان شامل. اشتر الآن!'
                ]
            },
            'blog-title': {
                'ar': [
                    'دليلك الشامل لـ {keywords} - كل ما تحتاج معرفته',
                    'أفضل طرق استخدام {keywords} في عام 2024',
                    '{keywords}: النصائح والحيل التي يجب أن تعرفها',
                    'كيف تختار {keywords} المناسب لاحتياجاتك؟'
                ]
            },
            'product-description': {
                'ar': [
                    'منتج عالي الجودة يتميز بـ {keywords}. مصمم خصيصاً لتلبية احتياجاتك بأفضل الأسعار.',
                    'اكتشف {keywords} في منتجنا المتميز. جودة فائقة وأداء موثوق لتجربة لا تُنسى.',
                    'منتج {keywords} الأفضل في فئته. تقنية متطورة وتصميم أنيق بسعر مناسب.'
                ]
            }
        }

    def generate_content(self, content_request):
        try:
            content_type = content_request['type']
            language = content_request.get('language', 'ar')
            keywords = content_request['keywords']
            tone = content_request['tone']
            audience = content_request['audience']

            # Get template based on content type and language
            templates = self.templates.get(content_type, {}).get(language, [])

            if not templates:
                return "عذراً، لا يمكن إنشاء هذا النوع من المحتوى حالياً."

            # Select template based on tone and audience
            template = self._select_template(templates, tone, audience)

            # Format template with keywords
            keywords_text = ' و'.join(keywords) if isinstance(keywords, list) else str(keywords)
            keywords_hash = '_'.join(keywords).replace(' ', '_') if isinstance(keywords, list) else str(keywords).replace(' ', '_')

            content = template.format(
                keywords=keywords_text,
                keywords_hash=keywords_hash
            )

            # Apply tone adjustments
            content = self._apply_tone(content, tone)

            return content

        except Exception as e:
            logger.error(f"Content generation error: {str(e)}")
            return "حدث خطأ في إنشاء المحتوى. يرجى المحاولة مرة أخرى."

    def _select_template(self, templates, tone, audience):
        # Simple template selection based on tone
        if tone == 'enthusiastic':
            return templates[0] if len(templates) > 0 else templates[0]
        elif tone == 'professional':
            return templates[1] if len(templates) > 1 else templates[0]
        elif tone == 'urgent':
            return templates[2] if len(templates) > 2 else templates[0]
        else:
            return random.choice(templates)

    def _apply_tone(self, content, tone):
        # Apply tone-specific modifications
        if tone == 'enthusiastic':
            content = content.replace('!', '! 🎉')
        elif tone == 'urgent':
            content = f"⚡ عاجل: {content}"
        elif tone == 'friendly':
            content = content.replace('منتجاتنا', 'منتجاتنا الرائعة')

        return content

class CampaignOptimizationModel:
    def __init__(self):
        self.performance_thresholds = {
            'excellent': {'roas': 4.0, 'ctr': 3.0, 'conversion_rate': 5.0},
            'good': {'roas': 2.5, 'ctr': 2.0, 'conversion_rate': 3.0},
            'average': {'roas': 1.5, 'ctr': 1.0, 'conversion_rate': 1.5},
            'poor': {'roas': 0.0, 'ctr': 0.0, 'conversion_rate': 0.0}
        }

    def optimize_campaigns(self, campaign_data):
        try:
            optimized_campaigns = []
            recommendations = []

            for campaign in campaign_data:
                # Calculate performance metrics
                ctr = (campaign['clicks'] / campaign['impressions'] * 100) if campaign['impressions'] > 0 else 0
                cpc = campaign['spent'] / campaign['clicks'] if campaign['clicks'] > 0 else 0
                roas = (campaign['conversions'] * 100) / campaign['spent'] if campaign['spent'] > 0 else 0
                conversion_rate = (campaign['conversions'] / campaign['clicks'] * 100) if campaign['clicks'] > 0 else 0

                # Determine performance level
                performance = self._evaluate_performance(roas, ctr, conversion_rate)

                # Identify weaknesses and opportunities
                weaknesses = self._identify_weaknesses(campaign, ctr, cpc, roas, conversion_rate)
                campaign_recommendations = self._generate_recommendations(campaign, weaknesses, performance)

                optimized_campaign = {
                    **campaign,
                    'ctr': round(ctr, 2),
                    'cpc': round(cpc, 2),
                    'roas': round(roas, 2),
                    'conversion_rate': round(conversion_rate, 2),
                    'performance': performance,
                    'weaknesses': weaknesses,
                    'recommendations': campaign_recommendations
                }

                optimized_campaigns.append(optimized_campaign)
                recommendations.extend(campaign_recommendations)

            # Generate overall recommendations
            overall_recommendations = self._generate_overall_recommendations(optimized_campaigns)

            # Calculate performance summary
            performance_summary = self._calculate_performance_summary(optimized_campaigns)

            return {
                'campaigns': optimized_campaigns,
                'recommendations': overall_recommendations,
                'performance_summary': performance_summary
            }

        except Exception as e:
            logger.error(f"Campaign optimization error: {str(e)}")
            return {'campaigns': [], 'recommendations': [], 'performance_summary': {}}

    def _evaluate_performance(self, roas, ctr, conversion_rate):
        thresholds = self.performance_thresholds

        if (roas >= thresholds['excellent']['roas'] and
            ctr >= thresholds['excellent']['ctr'] and
            conversion_rate >= thresholds['excellent']['conversion_rate']):
            return 'excellent'
        elif (roas >= thresholds['good']['roas'] and
              ctr >= thresholds['good']['ctr'] and
              conversion_rate >= thresholds['good']['conversion_rate']):
            return 'good'
        elif (roas >= thresholds['average']['roas'] or
              ctr >= thresholds['average']['ctr'] or
              conversion_rate >= thresholds['average']['conversion_rate']):
            return 'average'
        else:
            return 'poor'

    def _identify_weaknesses(self, campaign, ctr, cpc, roas, conversion_rate):
        weaknesses = []

        if ctr < 1.0:
            weaknesses.append('معدل نقر منخفض')
        if cpc > 15:
            weaknesses.append('تكلفة نقرة عالية')
        if roas < 2.0:
            weaknesses.append('عائد استثمار ضعيف')
        if conversion_rate < 2.0:
            weaknesses.append('معدل تحويل منخفض')
        if campaign['spent'] / campaign['budget'] > 0.9:
            weaknesses.append('استنزاف الميزانية')

        return weaknesses if weaknesses else ['أداء جيد عموماً']

    def _generate_recommendations(self, campaign, weaknesses, performance):
        recommendations = []

        if 'معدل نقر منخفض' in weaknesses:
            recommendations.append('تحسين نص الإعلان والصور')
            recommendations.append('إعادة تقييم الاستهداف')

        if 'تكلفة نقرة عالية' in weaknesses:
            recommendations.append('تحسين نقاط الجودة')
            recommendations.append('اختبار كلمات مفتاحية جديدة')

        if 'عائد استثمار ضعيف' in weaknesses:
            recommendations.append('تحسين صفحة الهبوط')
            recommendations.append('مراجعة استراتيجية التسعير')

        if 'معدل تحويل منخفض' in weaknesses:
            recommendations.append('تحسين عملية الشراء')
            recommendations.append('إضافة عروض مغرية')

        if performance == 'excellent':
            recommendations.append('زيادة الميزانية للتوسع')
            recommendations.append('تطبيق نفس الاستراتيجية على حملات أخرى')

        return recommendations[:3]  # Return top 3 recommendations

    def _generate_overall_recommendations(self, campaigns):
        poor_campaigns = [c for c in campaigns if c['performance'] == 'poor']
        excellent_campaigns = [c for c in campaigns if c['performance'] == 'excellent']

        recommendations = []

        if poor_campaigns:
            recommendations.append({
                'type': 'budget',
                'priority': 'high',
                'title': 'إعادة توزيع الميزانية',
                'description': f'تحويل الميزانية من {len(poor_campaigns)} حملة ضعيفة الأداء',
                'expected_impact': '+15% في العائد'
            })

        if excellent_campaigns:
            recommendations.append({
                'type': 'scaling',
                'priority': 'high',
                'title': 'توسيع الحملات الناجحة',
                'description': f'زيادة ميزانية {len(excellent_campaigns)} حملة ممتازة',
                'expected_impact': '+25% في التحويلات'
            })

        return recommendations

    def _calculate_performance_summary(self, campaigns):
        if not campaigns:
            return {}

        total_spent = sum(c['spent'] for c in campaigns)
        total_conversions = sum(c['conversions'] for c in campaigns)
        avg_roas = sum(c['roas'] for c in campaigns) / len(campaigns)

        performance_distribution = {}
        for performance in ['excellent', 'good', 'average', 'poor']:
            count = len([c for c in campaigns if c['performance'] == performance])
            performance_distribution[performance] = count

        return {
            'total_campaigns': len(campaigns),
            'total_spent': total_spent,
            'total_conversions': total_conversions,
            'average_roas': round(avg_roas, 2),
            'performance_distribution': performance_distribution
        }

class SalesPredictionModel:
    def __init__(self):
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()

    def predict_sales(self, historical_data, period='quarterly', confidence_level=95):
        try:
            # Extract historical sales data
            monthly_sales = historical_data['monthly_sales']
            seasonal_factors = historical_data['seasonal_factors']
            marketing_spend = historical_data['marketing_spend']

            # Generate predictions based on period
            if period == 'quarterly':
                predictions = self._predict_quarterly(monthly_sales, seasonal_factors, marketing_spend)
            elif period == 'monthly':
                predictions = self._predict_monthly(monthly_sales, seasonal_factors, marketing_spend)
            else:
                predictions = self._predict_yearly(monthly_sales, seasonal_factors, marketing_spend)

            # Calculate confidence intervals
            for pred in predictions:
                margin = pred['predicted'] * (100 - confidence_level) / 200
                pred['lower_bound'] = max(0, pred['predicted'] - margin)
                pred['upper_bound'] = pred['predicted'] + margin
                pred['confidence'] = confidence_level

            # Generate influencing factors
            factors = self._analyze_influencing_factors(historical_data)

            # Generate scenarios
            scenarios = self._generate_scenarios(predictions[-1]['predicted'])

            return {
                'predictions': predictions,
                'influencing_factors': factors,
                'scenarios': scenarios
            }

        except Exception as e:
            logger.error(f"Sales prediction error: {str(e)}")
            return {'predictions': [], 'influencing_factors': [], 'scenarios': []}

    def predict_with_factors(self, historical_data, updated_factors):
        try:
            base_prediction = historical_data['monthly_sales'][-1]

            # Apply factor adjustments
            adjusted_prediction = base_prediction

            for factor_name, factor_value in updated_factors.items():
                if factor_name == 'marketing_budget':
                    # Marketing budget impact (35% influence)
                    impact = (factor_value / 50000 - 1) * 0.35
                    adjusted_prediction *= (1 + impact)
                elif factor_name == 'competition':
                    # Competition impact (15% influence)
                    impact = (0.8 - factor_value) * 0.15
                    adjusted_prediction *= (1 + impact)
                elif factor_name == 'promotional_campaigns':
                    # Promotional campaigns impact (5% influence)
                    impact = (factor_value / 3 - 1) * 0.05
                    adjusted_prediction *= (1 + impact)

            return {
                'adjusted_prediction': round(adjusted_prediction, 0),
                'base_prediction': base_prediction,
                'adjustment_percentage': round(((adjusted_prediction / base_prediction) - 1) * 100, 1)
            }

        except Exception as e:
            logger.error(f"Sales prediction with factors error: {str(e)}")
            return {}

    def _predict_quarterly(self, monthly_sales, seasonal_factors, marketing_spend):
        predictions = []
        base_value = np.mean(monthly_sales[-3:])  # Average of last 3 months

        quarters = ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024']
        seasonal_multipliers = [1.0, 1.1, 1.3, 1.6]  # Q4 is typically highest

        for i, quarter in enumerate(quarters):
            predicted_value = base_value * 3 * seasonal_multipliers[i]  # 3 months per quarter
            predicted_value *= (1 + random.uniform(-0.1, 0.15))  # Add some variance

            predictions.append({
                'period': quarter,
                'predicted': round(predicted_value, 0)
            })

        return predictions

    def _predict_monthly(self, monthly_sales, seasonal_factors, marketing_spend):
        predictions = []
        months = ['أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر']

        # Calculate trend
        trend = (monthly_sales[-1] - monthly_sales[0]) / len(monthly_sales)

        for i, month in enumerate(months):
            base_prediction = monthly_sales[-1] + (trend * (i + 1))
            seasonal_adjustment = seasonal_factors[i % len(seasonal_factors)]
            predicted_value = base_prediction * seasonal_adjustment

            predictions.append({
                'period': month,
                'predicted': round(predicted_value, 0)
            })

        return predictions

    def _predict_yearly(self, monthly_sales, seasonal_factors, marketing_spend):
        yearly_total = sum(monthly_sales) * 2  # Extrapolate from 6 months to 12
        growth_rate = 0.12  # Assume 12% annual growth

        return [{
            'period': '2024',
            'predicted': round(yearly_total * (1 + growth_rate), 0)
        }]

    def _analyze_influencing_factors(self, historical_data):
        return [
            {
                'name': 'الميزانية التسويقية',
                'impact': 35,
                'description': 'تأثير الإنفاق التسويقي على المبيعات',
                'current_value': 50000,
                'adjustable': True
            },
            {
                'name': 'الموسمية',
                'impact': 25,
                'description': 'تأثير المواسم والأعياد على المبيعات',
                'current_value': 1.2,
                'adjustable': False
            },
            {
                'name': 'المؤشرات الاقتصادية',
                'impact': 20,
                'description': 'تأثير الوضع الاقتصادي العام',
                'current_value': 0.95,
                'adjustable': False
            },
            {
                'name': 'المنافسة',
                'impact': 15,
                'description': 'تأثير النشاط التنافسي في السوق',
                'current_value': 0.8,
                'adjustable': True
            },
            {
                'name': 'حملات ترويجية',
                'impact': 5,
                'description': 'تأثير العروض والخصومات',
                'current_value': 3,
                'adjustable': True
            }
        ]

    def _generate_scenarios(self, base_prediction):
        return [
            {
                'name': 'متفائل',
                'prediction': round(base_prediction * 1.25, 0),
                'probability': 25,
                'description': 'نمو قوي في السوق، زيادة الميزانية التسويقية'
            },
            {
                'name': 'متوقع',
                'prediction': round(base_prediction, 0),
                'probability': 50,
                'description': 'نمو طبيعي، استمرار الاستراتيجيات الحالية'
            },
            {
                'name': 'متحفظ',
                'prediction': round(base_prediction * 0.85, 0),
                'probability': 25,
                'description': 'تحديات اقتصادية، زيادة المنافسة'
            }
        ]

class CustomerJourneyModel:
    def __init__(self):
        self.stage_names = ['الوعي', 'الاهتمام', 'الشراء', 'التسليم', 'الاحتفاظ']

    def analyze_journey(self, journey_data):
        try:
            analyzed_stages = []
            bottlenecks = []
            opportunities = []

            for stage in journey_data:
                # Analyze stage performance
                performance = self._evaluate_stage_performance(stage)
                pain_points = self._identify_pain_points(stage)
                stage_opportunities = self._identify_opportunities(stage)

                analyzed_stage = {
                    **stage,
                    'performance': performance,
                    'pain_points': pain_points,
                    'opportunities': stage_opportunities
                }

                analyzed_stages.append(analyzed_stage)

                # Identify bottlenecks
                if stage['dropoff_rate'] > 50:
                    bottlenecks.append({
                        'stage': stage['name'],
                        'issue': f"معدل تسرب عالي ({stage['dropoff_rate']}%)",
                        'impact': 'high',
                        'customers_lost': round(stage['customers'] * stage['dropoff_rate'] / 100)
                    })

                opportunities.extend(stage_opportunities)

            # Analyze touchpoints
            touchpoints = self._analyze_touchpoints()

            return {
                'stages': analyzed_stages,
                'bottlenecks': bottlenecks,
                'opportunities': opportunities[:5],  # Top 5 opportunities
                'touchpoints': touchpoints
            }

        except Exception as e:
            logger.error(f"Customer journey analysis error: {str(e)}")
            return {'stages': [], 'bottlenecks': [], 'opportunities': [], 'touchpoints': []}

    def _evaluate_stage_performance(self, stage):
        if stage['conversion_rate'] > 70:
            return 'excellent'
        elif stage['conversion_rate'] > 50:
            return 'good'
        elif stage['conversion_rate'] > 30:
            return 'average'
        else:
            return 'poor'

    def _identify_pain_points(self, stage):
        pain_points = []

        if stage['dropoff_rate'] > 60:
            pain_points.append('معدل تسرب مرتفع جداً')
        if stage['avg_time_spent'] > 48:  # More than 2 days
            pain_points.append('وقت طويل في المرحلة')
        if stage['conversion_rate'] < 30:
            pain_points.append('معدل تحويل منخفض')

        # Stage-specific pain points
        stage_specific = {
            'الوعي': ['صعوبة العثور على المعلومات', 'رسائل غير واضحة'],
            'الاهتمام': ['معلومات غير كافية', 'صعوبة المقارنة'],
            'الشراء': ['عملية دفع معقدة', 'خيارات دفع محدودة'],
            'التسليم': ['تأخير في التسليم', 'تتبع غير دقيق'],
            'الاحتفاظ': ['قلة التواصل', 'عروض غير مناسبة']
        }

        pain_points.extend(stage_specific.get(stage['name'], []))

        return pain_points[:3]  # Return top 3 pain points

    def _identify_opportunities(self, stage):
        opportunities = []

        if stage['conversion_rate'] < 50:
            opportunities.append({
                'type': 'conversion',
                'description': f"تحسين معدل التحويل في مرحلة {stage['name']}",
                'potential_impact': f"+{round((50 - stage['conversion_rate']) * stage['customers'] / 100)} عميل"
            })

        if stage['dropoff_rate'] > 40:
            opportunities.append({
                'type': 'retention',
                'description': f"تقليل التسرب في مرحلة {stage['name']}",
                'potential_impact': f"حفظ {round(stage['dropoff_rate'] * stage['customers'] / 200)} عميل"
            })

        return opportunities

    def _analyze_touchpoints(self):
        return [
            {
                'name': 'موقع الويب',
                'channel': 'رقمي',
                'satisfaction': 78,
                'conversion_rate': 12,
                'issues': ['بطء التحميل', 'صعوبة التنقل']
            },
            {
                'name': 'تطبيق الجوال',
                'channel': 'رقمي',
                'satisfaction': 85,
                'conversion_rate': 18,
                'issues': ['أخطاء تقنية', 'واجهة معقدة']
            },
            {
                'name': 'خدمة العملاء',
                'channel': 'مباشر',
                'satisfaction': 72,
                'conversion_rate': 35,
                'issues': ['أوقات انتظار طويلة', 'عدم حل المشاكل']
            }
        ]

class SentimentAnalysisModel:
    def __init__(self):
        self.positive_keywords = ['ممتاز', 'رائع', 'جيد', 'أحب', 'مذهل', 'أنصح', 'جودة عالية']
        self.negative_keywords = ['سيء', 'فظيع', 'مشكلة', 'متأخر', 'لا أنصح', 'جودة ضعيفة']

    def analyze_sentiment(self, posts_data):
        try:
            analyzed_posts = []
            platform_sentiment = {}
            keyword_sentiment = {}

            for post in posts_data:
                # Analyze individual post sentiment
                sentiment_score = self._calculate_sentiment_score(post['content'])
                sentiment_label = self._get_sentiment_label(sentiment_score)
                confidence = abs(sentiment_score) * 100

                analyzed_post = {
                    **post,
                    'sentiment_score': sentiment_score,
                    'sentiment_label': sentiment_label,
                    'confidence': round(confidence, 1),
                    'keywords': self._extract_keywords(post['content'])
                }

                analyzed_posts.append(analyzed_post)

                # Aggregate by platform
                if post['platform'] not in platform_sentiment:
                    platform_sentiment[post['platform']] = {'positive': 0, 'negative': 0, 'neutral': 0, 'total': 0}

                platform_sentiment[post['platform']][sentiment_label] += 1
                platform_sentiment[post['platform']]['total'] += 1

            # Calculate overall sentiment
            overall_sentiment = self._calculate_overall_sentiment(analyzed_posts)

            # Analyze keyword sentiment
            keyword_sentiment = self._analyze_keyword_sentiment(analyzed_posts)

            # Identify trending topics
            trending_topics = self._identify_trending_topics(analyzed_posts)

            return {
                'overall_sentiment': overall_sentiment,
                'platform_breakdown': self._format_platform_breakdown(platform_sentiment),
                'keyword_sentiment': keyword_sentiment,
                'trending_topics': trending_topics,
                'posts': analyzed_posts
            }

        except Exception as e:
            logger.error(f"Sentiment analysis error: {str(e)}")
            return {
                'overall_sentiment': {},
                'platform_breakdown': [],
                'keyword_sentiment': [],
                'trending_topics': [],
                'posts': []
            }

    def _calculate_sentiment_score(self, text):
        # Simple rule-based sentiment analysis
        text_lower = text.lower()
        positive_count = sum(1 for word in self.positive_keywords if word in text_lower)
        negative_count = sum(1 for word in self.negative_keywords if word in text_lower)

        # Calculate score between -1 and 1
        total_words = len(text.split())
        if total_words == 0:
            return 0

        score = (positive_count - negative_count) / max(total_words / 10, 1)
        return max(-1, min(1, score))

    def _get_sentiment_label(self, score):
        if score > 0.1:
            return 'positive'
        elif score < -0.1:
            return 'negative'
        else:
            return 'neutral'

    def _extract_keywords(self, text):
        # Simple keyword extraction
        words = text.split()
        keywords = []

        for word in words:
            if len(word) > 4 and word not in ['الذي', 'التي', 'هذا', 'هذه']:
                keywords.append(word)

        return keywords[:5]  # Return top 5 keywords

    def _calculate_overall_sentiment(self, posts):
        if not posts:
            return {}

        positive_count = len([p for p in posts if p['sentiment_label'] == 'positive'])
        negative_count = len([p for p in posts if p['sentiment_label'] == 'negative'])
        neutral_count = len([p for p in posts if p['sentiment_label'] == 'neutral'])
        total = len(posts)

        return {
            'positive_percentage': round((positive_count / total) * 100, 1),
            'negative_percentage': round((negative_count / total) * 100, 1),
            'neutral_percentage': round((neutral_count / total) * 100, 1),
            'sentiment_score': round(sum(p['sentiment_score'] for p in posts) / total, 2),
            'total_mentions': total
        }

    def _format_platform_breakdown(self, platform_sentiment):
        breakdown = []

        for platform, sentiment in platform_sentiment.items():
            if sentiment['total'] > 0:
                breakdown.append({
                    'platform': platform,
                    'positive_percentage': round((sentiment['positive'] / sentiment['total']) * 100, 1),
                    'negative_percentage': round((sentiment['negative'] / sentiment['total']) * 100, 1),
                    'neutral_percentage': round((sentiment['neutral'] / sentiment['total']) * 100, 1),
                    'total_mentions': sentiment['total']
                })

        return breakdown

    def _analyze_keyword_sentiment(self, posts):
        keyword_scores = {}

        for post in posts:
            for keyword in post['keywords']:
                if keyword not in keyword_scores:
                    keyword_scores[keyword] = {'scores': [], 'mentions': 0}

                keyword_scores[keyword]['scores'].append(post['sentiment_score'])
                keyword_scores[keyword]['mentions'] += 1

        # Calculate average sentiment for each keyword
        keyword_sentiment = []
        for keyword, data in keyword_scores.items():
            if data['mentions'] >= 3:  # Only include keywords with 3+ mentions
                avg_sentiment = sum(data['scores']) / len(data['scores'])
                keyword_sentiment.append({
                    'keyword': keyword,
                    'sentiment_score': round(avg_sentiment * 100, 1),
                    'mentions': data['mentions'],
                    'trend': 'up' if avg_sentiment > 0 else 'down' if avg_sentiment < 0 else 'neutral'
                })

        # Sort by mentions count
        keyword_sentiment.sort(key=lambda x: x['mentions'], reverse=True)

        return keyword_sentiment[:10]  # Return top 10 keywords

    def _identify_trending_topics(self, posts):
        # Simple trending topics identification
        topics = {}

        for post in posts:
            for keyword in post['keywords']:
                if keyword not in topics:
                    topics[keyword] = 0
                topics[keyword] += 1

        # Sort by frequency
        trending = sorted(topics.items(), key=lambda x: x[1], reverse=True)

        return [{'topic': topic, 'mentions': count} for topic, count in trending[:5]]