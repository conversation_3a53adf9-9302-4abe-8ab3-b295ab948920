#!/usr/bin/env python3
"""
Simple server runner for MarketMind Backend
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import uvicorn
    from main import app
    
    if __name__ == "__main__":
        print("🚀 Starting MarketMind Backend Server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📖 API Documentation: http://localhost:8000/docs")
        print("🔄 Auto-reload is enabled")
        print("-" * 50)
        
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
except ImportError as e:
    print(f"❌ Missing dependency: {e}")
    print("💡 Please install required packages:")
    print("   python3 -m pip install fastapi uvicorn python-multipart")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)
