"""
Simple FastAPI server for testing AI tools without database dependencies
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import uvicorn
from contextlib import asynccontextmanager

# Import AI routers
from routers import ai_tools, custom_ai, marketmind

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 AI Marketing Platform API Starting...")
    print("✅ OpenAI client initialized")
    yield
    # Shutdown
    print("👋 AI Marketing Platform API Shutting down...")

# Create FastAPI app
app = FastAPI(
    title="AI Marketing Platform API",
    description="AI-powered marketing automation platform with advanced analytics",
    version="1.0.0",
    lifespan=lifespan
)

# No rate limiting for simple version

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security headers middleware
@app.middleware("http")
async def add_security_headers(request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

    return response

# Include AI routers
app.include_router(ai_tools.router, prefix="/api/v1", tags=["ai-tools"])
app.include_router(custom_ai.router, prefix="/api/v1", tags=["custom-ai"])
app.include_router(marketmind.router, prefix="/api/v1", tags=["marketmind"])

@app.get("/")
async def root():
    return {
        "message": "AI Marketing Platform API",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "AI Content Generation",
            "Sentiment Analysis", 
            "Competitor Analysis",
            "Marketing Strategy Generation",
            "Hashtag Generation"
        ]
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "services": {
            "api": "running",
            "ai_tools": "available"
        }
    }

# Mock login endpoint for testing
@app.post("/api/v1/auth/login")
async def mock_login(credentials: dict):
    """Mock login endpoint for testing"""
    email = credentials.get("email", "")
    password = credentials.get("password", "")
    
    # Mock authentication
    if email == "<EMAIL>" and password == "demo123":
        return {
            "access_token": "mock-jwt-token-for-testing",
            "token_type": "bearer",
            "user": {
                "id": 1,
                "email": email,
                "name": "Demo Business User",
                "role": "business",
                "subscription_plan": "professional"
            }
        }
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

if __name__ == "__main__":
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
