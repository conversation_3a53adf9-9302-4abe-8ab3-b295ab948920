"""
ML Models API Router for MarketMind
===================================

This module provides REST API endpoints for all MarketMind AI models.
It handles model training, prediction, evaluation, and management operations.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import pandas as pd
import json
from datetime import datetime

from dependencies import get_current_user
from ml_models.model_manager import ModelManager

# Initialize router
router = APIRouter()

# Initialize model manager
model_manager = ModelManager()

# Pydantic models for request/response
class TrainingRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to train")
    data: List[Dict[str, Any]] = Field(..., description="Training data")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="Training parameters")

class PredictionRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model")
    data: List[Dict[str, Any]] = Field(..., description="Input data for prediction")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="Prediction parameters")

class ModelResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@router.get("/models", response_model=Dict[str, Any])
async def list_models(current_user: dict = Depends(get_current_user)):
    """
    List all available AI models and their status.
    
    Returns:
        Dictionary of model information
    """
    try:
        models = model_manager.list_models()
        return {
            "success": True,
            "models": models,
            "total_models": len(models)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_name}/status", response_model=Dict[str, Any])
async def get_model_status(model_name: str, current_user: dict = Depends(get_current_user)):
    """
    Get detailed status of a specific model.
    
    Args:
        model_name: Name of the model
        
    Returns:
        Model status information
    """
    try:
        status = model_manager.get_model_status(model_name)
        if not status.get("success"):
            raise HTTPException(status_code=404, detail=status.get("error"))
        return status
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/train", response_model=ModelResponse)
async def train_model(
    model_name: str,
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """
    Train a specific AI model.
    
    Args:
        model_name: Name of the model to train
        request: Training request with data and parameters
        
    Returns:
        Training job information
    """
    try:
        # Convert data to DataFrame
        df = pd.DataFrame(request.data)
        
        # Start training in background
        def train_model_task():
            return model_manager.train_model(model_name, df, **request.parameters)
        
        # For now, run synchronously (can be made async later)
        result = train_model_task()
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Model {model_name} training completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Training failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/predict", response_model=ModelResponse)
async def predict(
    model_name: str,
    request: PredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Make predictions using a trained model.
    
    Args:
        model_name: Name of the model
        request: Prediction request with input data
        
    Returns:
        Prediction results
    """
    try:
        # Convert data to DataFrame
        df = pd.DataFrame(request.data)
        
        # Make prediction
        result = model_manager.predict(model_name, df, **request.parameters)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Prediction completed using {model_name}",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Prediction failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/evaluate", response_model=ModelResponse)
async def evaluate_model(
    model_name: str,
    request: PredictionRequest,  # Reuse same structure
    current_user: dict = Depends(get_current_user)
):
    """
    Evaluate a model's performance on test data.
    
    Args:
        model_name: Name of the model
        request: Evaluation request with test data
        
    Returns:
        Evaluation results
    """
    try:
        # Convert data to DataFrame
        df = pd.DataFrame(request.data)
        
        # Evaluate model
        result = model_manager.evaluate_model(model_name, df)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Model {model_name} evaluation completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Evaluation failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/retrain", response_model=ModelResponse)
async def retrain_model(
    model_name: str,
    request: TrainingRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Retrain an existing model with new data.
    
    Args:
        model_name: Name of the model to retrain
        request: Training request with new data
        
    Returns:
        Retraining results
    """
    try:
        # Convert data to DataFrame
        df = pd.DataFrame(request.data)
        
        # Retrain model
        result = model_manager.retrain_model(model_name, df, **request.parameters)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Model {model_name} retrained successfully",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Retraining failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/{model_name}/deploy", response_model=ModelResponse)
async def deploy_model(
    model_name: str,
    deployment_config: Optional[Dict[str, Any]] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Deploy a trained model for production use.
    
    Args:
        model_name: Name of the model to deploy
        deployment_config: Optional deployment configuration
        
    Returns:
        Deployment status
    """
    try:
        result = model_manager.deploy_model(model_name, deployment_config)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Model {model_name} deployed successfully",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Deployment failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training-jobs", response_model=Dict[str, Any])
async def get_training_jobs(current_user: dict = Depends(get_current_user)):
    """
    Get information about all training jobs.
    
    Returns:
        Training jobs information
    """
    try:
        jobs = model_manager.get_training_jobs()
        return jobs
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/health", response_model=Dict[str, Any])
async def get_system_health(current_user: dict = Depends(get_current_user)):
    """
    Get overall system health and model status.
    
    Returns:
        System health information
    """
    try:
        health = model_manager.get_system_health()
        return health
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/reports", response_model=Dict[str, Any])
async def generate_models_report(
    model_name: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate comprehensive model performance report.
    
    Args:
        model_name: Optional specific model name
        
    Returns:
        Model performance report
    """
    try:
        report = model_manager.generate_model_report(model_name)
        
        if report.get("success"):
            return report
        else:
            raise HTTPException(status_code=500, detail=report.get("error"))
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Specific model endpoints for convenience

@router.post("/customer-segmentation/analyze", response_model=ModelResponse)
async def analyze_customer_segments(
    request: PredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Analyze customer segments using the customer segmentation model.
    
    Args:
        request: Customer data for segmentation
        
    Returns:
        Segmentation results
    """
    try:
        df = pd.DataFrame(request.data)
        result = model_manager.predict("customer_segmentation", df, **request.parameters)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Customer segmentation analysis completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Segmentation failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/churn-prediction/risk-analysis", response_model=ModelResponse)
async def analyze_churn_risk(
    request: PredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Analyze churn risk for customers.
    
    Args:
        request: Customer data for churn analysis
        
    Returns:
        Churn risk analysis results
    """
    try:
        df = pd.DataFrame(request.data)
        result = model_manager.predict("churn_prediction", df, **request.parameters)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Churn risk analysis completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Churn analysis failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/content-generation/generate", response_model=ModelResponse)
async def generate_content(
    prompt: str,
    content_type: str = "social_media",
    tone: str = "professional",
    language: str = "arabic",
    target_audience: str = "general",
    current_user: dict = Depends(get_current_user)
):
    """
    Generate marketing content using AI.
    
    Args:
        prompt: Content generation prompt
        content_type: Type of content to generate
        tone: Tone of the content
        language: Language for content generation
        target_audience: Target audience
        
    Returns:
        Generated content
    """
    try:
        # Create data structure for content generation
        data = [{
            "prompt": prompt,
            "content_type": content_type,
            "tone": tone,
            "language": language,
            "target_audience": target_audience
        }]
        
        df = pd.DataFrame(data)
        result = model_manager.predict("content_generation", df)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Content generated successfully",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Content generation failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sentiment-analysis/analyze", response_model=ModelResponse)
async def analyze_sentiment(
    texts: List[str],
    current_user: dict = Depends(get_current_user)
):
    """
    Analyze sentiment of text data.
    
    Args:
        texts: List of texts to analyze
        
    Returns:
        Sentiment analysis results
    """
    try:
        # Create data structure for sentiment analysis
        data = [{"text": text} for text in texts]
        df = pd.DataFrame(data)
        
        result = model_manager.predict("sentiment_analysis", df)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Sentiment analysis completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Sentiment analysis failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# New model endpoints

@router.post("/customer-behavior/predict", response_model=ModelResponse)
async def predict_customer_behavior(
    request: PredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Predict customer behavior such as purchase intent and engagement.
    
    Args:
        request: Customer data for behavior prediction
        
    Returns:
        Behavior prediction results
    """
    try:
        df = pd.DataFrame(request.data)
        result = model_manager.predict("customer_behavior", df, **request.parameters)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Customer behavior prediction completed",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Behavior prediction failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/product-recommendation/recommend", response_model=ModelResponse)
async def get_product_recommendations(
    user_id: int,
    n_recommendations: int = 10,
    current_user: dict = Depends(get_current_user)
):
    """
    Get product recommendations for a specific user.
    
    Args:
        user_id: User ID to get recommendations for
        n_recommendations: Number of recommendations to return
        
    Returns:
        Product recommendations
    """
    try:
        result = model_manager.predict(
            "product_recommendation", 
            pd.DataFrame(), 
            user_id=user_id, 
            n_recommendations=n_recommendations
        )
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Product recommendations generated for user {user_id}",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Recommendation generation failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/product-recommendation/similar-products", response_model=ModelResponse)
async def find_similar_products(
    product_id: int,
    n_similar: int = 5,
    current_user: dict = Depends(get_current_user)
):
    """
    Find similar products to a given product.
    
    Args:
        product_id: Product ID to find similar products for
        n_similar: Number of similar products to return
        
    Returns:
        Similar products
    """
    try:
        model = model_manager.get_model("product_recommendation")
        if not model:
            raise HTTPException(status_code=404, detail="Product recommendation model not found")
        
        result = model.get_similar_products(product_id, n_similar)
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Similar products found for product {product_id}",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Similar products search failed")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/competitor-analysis/analyze", response_model=ModelResponse)
async def analyze_competitor(
    competitor_name: str,
    competitor_data: Dict[str, Any],
    current_user: dict = Depends(get_current_user)
):
    """
    Analyze a specific competitor.
    
    Args:
        competitor_name: Name of the competitor
        competitor_data: Competitor data for analysis
        
    Returns:
        Competitor analysis results
    """
    try:
        result = model_manager.predict(
            "competitor_analysis",
            pd.DataFrame(),
            competitor_name=competitor_name,
            competitor_data=competitor_data
        )
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message=f"Competitor analysis completed for {competitor_name}",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Competitor analysis failed")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/competitor-analysis/insights", response_model=ModelResponse)
async def get_competitive_insights(
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive competitive insights.
    
    Returns:
        Competitive insights and recommendations
    """
    try:
        model = model_manager.get_model("competitor_analysis")
        if not model:
            raise HTTPException(status_code=404, detail="Competitor analysis model not found")
        
        result = model.get_competitive_insights()
        
        if result.get("success"):
            return ModelResponse(
                success=True,
                message="Competitive insights generated",
                data=result
            )
        else:
            return ModelResponse(
                success=False,
                error=result.get("error", "Failed to generate competitive insights")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
