from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import requests
import json
from datetime import datetime, timedelta
import asyncio
import logging

from ..database.connection import get_db
from ..auth.dependencies import get_current_user
from ..schemas.user import User
from ..services.integration_service import IntegrationService
from ..services.data_sync_service import DataSyncService
from ..services.integrated_analytics_service import IntegratedAnalyticsService

router = APIRouter(prefix="/integrations", tags=["integrations"])
logger = logging.getLogger(__name__)

# OAuth Configuration for different platforms
OAUTH_CONFIGS = {
    "facebook": {
        "auth_url": "https://www.facebook.com/v18.0/dialog/oauth",
        "token_url": "https://graph.facebook.com/v18.0/oauth/access_token",
        "api_base": "https://graph.facebook.com/v18.0",
        "scopes": ["pages_read_engagement", "pages_show_list", "ads_read", "business_management"]
    },
    "instagram": {
        "auth_url": "https://api.instagram.com/oauth/authorize",
        "token_url": "https://api.instagram.com/oauth/access_token",
        "api_base": "https://graph.instagram.com/v18.0",
        "scopes": ["user_profile", "user_media", "instagram_basic", "instagram_manage_insights"]
    },
    "google": {
        "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
        "token_url": "https://oauth2.googleapis.com/token",
        "api_base": "https://www.googleapis.com/adwords/api",
        "scopes": ["https://www.googleapis.com/auth/adwords", "https://www.googleapis.com/auth/analytics.readonly"]
    },
    "linkedin": {
        "auth_url": "https://www.linkedin.com/oauth/v2/authorization",
        "token_url": "https://www.linkedin.com/oauth/v2/accessToken",
        "api_base": "https://api.linkedin.com/v2",
        "scopes": ["r_organization_social", "r_ads", "r_ads_reporting"]
    },
    "twitter": {
        "auth_url": "https://twitter.com/i/oauth2/authorize",
        "token_url": "https://api.twitter.com/2/oauth2/token",
        "api_base": "https://api.twitter.com/2",
        "scopes": ["tweet.read", "users.read", "offline.access"]
    },
    "youtube": {
        "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
        "token_url": "https://oauth2.googleapis.com/token",
        "api_base": "https://www.googleapis.com/youtube/v3",
        "scopes": ["https://www.googleapis.com/auth/youtube.readonly", "https://www.googleapis.com/auth/youtube-analytics.readonly"]
    }
}

@router.post("/oauth/token")
async def exchange_oauth_token(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """تبديل authorization code بـ access token"""
    try:
        platform = request.get("platform")
        code = request.get("code")
        client_id = request.get("clientId")
        client_secret = request.get("clientSecret")
        redirect_uri = request.get("redirectUri")

        if not all([platform, code, client_id, client_secret, redirect_uri]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        config = OAUTH_CONFIGS.get(platform)
        if not config:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {platform}")

        # تبديل code بـ token
        token_data = {
            "grant_type": "authorization_code",
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": redirect_uri,
            "code": code
        }

        response = requests.post(config["token_url"], data=token_data)
        if response.status_code != 200:
            raise HTTPException(status_code=400, detail="Failed to exchange code for token")

        token_info = response.json()
        
        return {
            "success": True,
            "token": {
                "accessToken": token_info.get("access_token"),
                "refreshToken": token_info.get("refresh_token"),
                "expiresIn": token_info.get("expires_in", 3600),
                "tokenType": token_info.get("token_type", "Bearer"),
                "scope": token_info.get("scope", "")
            }
        }

    except Exception as e:
        logger.error(f"OAuth token exchange failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/oauth/refresh")
async def refresh_oauth_token(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """تحديث access token باستخدام refresh token"""
    try:
        platform = request.get("platform")
        refresh_token = request.get("refreshToken")

        if not all([platform, refresh_token]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        config = OAUTH_CONFIGS.get(platform)
        if not config:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {platform}")

        # الحصول على معلومات العميل من قاعدة البيانات
        integration_service = IntegrationService(db)
        account = integration_service.get_user_account(current_user.id, platform)
        
        if not account:
            raise HTTPException(status_code=404, detail="Account not found")

        # تحديث token
        token_data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": account.client_id,
            "client_secret": account.client_secret
        }

        response = requests.post(config["token_url"], data=token_data)
        if response.status_code != 200:
            raise HTTPException(status_code=400, detail="Failed to refresh token")

        token_info = response.json()
        
        # تحديث token في قاعدة البيانات
        integration_service.update_account_token(
            account.id,
            token_info.get("access_token"),
            token_info.get("refresh_token", refresh_token)
        )

        return {
            "success": True,
            "token": {
                "accessToken": token_info.get("access_token"),
                "refreshToken": token_info.get("refresh_token", refresh_token),
                "expiresIn": token_info.get("expires_in", 3600),
                "tokenType": token_info.get("token_type", "Bearer"),
                "scope": token_info.get("scope", "")
            }
        }

    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connect")
async def connect_account(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """ربط حساب جديد"""
    try:
        platform = request.get("platform")
        access_token = request.get("accessToken")
        refresh_token = request.get("refreshToken")
        expires_in = request.get("expiresIn", 3600)

        if not all([platform, access_token]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        integration_service = IntegrationService(db)
        
        # الحصول على معلومات الحساب من المنصة
        account_info = await integration_service.fetch_account_info(platform, access_token)
        
        # حفظ الحساب في قاعدة البيانات
        account = integration_service.create_account(
            user_id=current_user.id,
            platform=platform,
            account_id=account_info.get("id"),
            account_name=account_info.get("name"),
            email=account_info.get("email"),
            username=account_info.get("username"),
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + timedelta(seconds=expires_in)
        )

        return {
            "success": True,
            "account": {
                "id": account.id,
                "platform": account.platform,
                "accountId": account.account_id,
                "accountName": account.account_name,
                "email": account.email,
                "username": account.username,
                "isActive": account.is_active,
                "lastSync": account.last_sync.isoformat() if account.last_sync else None,
                "permissions": account.permissions or []
            }
        }

    except Exception as e:
        logger.error(f"Account connection failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts")
async def get_connected_accounts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الحسابات المربوطة"""
    try:
        integration_service = IntegrationService(db)
        accounts = integration_service.get_user_accounts(current_user.id)

        return {
            "success": True,
            "accounts": [
                {
                    "id": account.id,
                    "platform": account.platform,
                    "accountId": account.account_id,
                    "accountName": account.account_name,
                    "email": account.email,
                    "username": account.username,
                    "isActive": account.is_active,
                    "lastSync": account.last_sync.isoformat() if account.last_sync else None,
                    "permissions": account.permissions or []
                }
                for account in accounts
            ]
        }

    except Exception as e:
        logger.error(f"Failed to get accounts: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/accounts/{account_id}")
async def disconnect_account(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """قطع ربط حساب"""
    try:
        integration_service = IntegrationService(db)
        
        # التحقق من ملكية الحساب
        account = integration_service.get_account(account_id)
        if not account or account.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="Account not found")

        # حذف الحساب
        integration_service.delete_account(account_id)

        return {"success": True, "message": "Account disconnected successfully"}

    except Exception as e:
        logger.error(f"Account disconnection failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/accounts/{account_id}/sync")
async def sync_account_data(
    account_id: str,
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """مزامنة البيانات من حساب محدد"""
    try:
        data_types = request.get("dataTypes", [])
        
        integration_service = IntegrationService(db)
        
        # التحقق من ملكية الحساب
        account = integration_service.get_account(account_id)
        if not account or account.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="Account not found")

        # بدء المزامنة في الخلفية
        data_sync_service = DataSyncService(db)
        background_tasks.add_task(
            data_sync_service.sync_account_data,
            account_id,
            data_types
        )

        return {
            "success": True,
            "message": "Data sync started",
            "accountId": account_id
        }

    except Exception as e:
        logger.error(f"Data sync failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/accounts/{account_id}/sync-status")
async def get_sync_status(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """الحصول على حالة المزامنة"""
    try:
        integration_service = IntegrationService(db)
        
        # التحقق من ملكية الحساب
        account = integration_service.get_account(account_id)
        if not account or account.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="Account not found")

        sync_status = integration_service.get_sync_status(account_id)

        return {
            "success": True,
            "isRunning": sync_status.get("is_running", False),
            "lastSync": sync_status.get("last_sync"),
            "nextSync": sync_status.get("next_sync"),
            "progress": sync_status.get("progress", 0),
            "errors": sync_status.get("errors", [])
        }

    except Exception as e:
        logger.error(f"Failed to get sync status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate-token")
async def validate_token(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """التحقق من صحة token"""
    try:
        platform = request.get("platform")
        token = request.get("token")

        if not all([platform, token]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        config = OAUTH_CONFIGS.get(platform)
        if not config:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {platform}")

        # التحقق من صحة token حسب المنصة
        is_valid = False
        
        if platform == "facebook" or platform == "instagram":
            response = requests.get(f"{config['api_base']}/me", params={"access_token": token})
            is_valid = response.status_code == 200
        elif platform == "google":
            response = requests.get(f"https://www.googleapis.com/oauth2/v1/tokeninfo", 
                                  params={"access_token": token})
            is_valid = response.status_code == 200
        elif platform == "linkedin":
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{config['api_base']}/people/~", headers=headers)
            is_valid = response.status_code == 200
        elif platform == "twitter":
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{config['api_base']}/users/me", headers=headers)
            is_valid = response.status_code == 200

        return {"success": True, "valid": is_valid}

    except Exception as e:
        logger.error(f"Token validation failed: {str(e)}")
        return {"success": True, "valid": False}

@router.post("/analytics/unified-report")
async def generate_unified_analytics_report(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """إنشاء تقرير تحليلي موحد من جميع المنصات المربوطة"""
    try:
        date_range = request.get("dateRange", {
            "start": (datetime.utcnow() - timedelta(days=30)).isoformat(),
            "end": datetime.utcnow().isoformat()
        })

        analytics_service = IntegratedAnalyticsService(db)
        report = await analytics_service.generate_unified_report(current_user.id, date_range)

        return report

    except Exception as e:
        logger.error(f"Failed to generate unified report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
