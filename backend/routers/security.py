from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTT<PERSON>Exception, Request, status
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime

from database.db_session import get_db
from database.models import User
from routers.users import get_current_user
from services.security_service import (
    TwoFactorAuthService,
    EncryptionService,
    AuditLogService,
    RateLimitService,
    SessionManagementService
)

router = APIRouter()

# Initialize security services
tfa_service = TwoFactorAuthService()
encryption_service = EncryptionService()
audit_service = AuditLogService()
rate_limit_service = RateLimitService()
session_service = SessionManagementService()

@router.post("/2fa/setup")
async def setup_2fa(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    request: Request = None
):
    """Setup Two-Factor Authentication"""
    try:
        # Check rate limit
        client_ip = request.client.host if request else "unknown"
        rate_check = rate_limit_service.check_rate_limit(client_ip, "api_general")
        if not rate_check["allowed"]:
            raise HTTPException(status_code=429, detail="Rate limit exceeded")
        
        if current_user.two_factor_enabled:
            raise HTTPException(status_code=400, detail="2FA is already enabled")
        
        # Generate secret
        secret = tfa_service.generate_secret()
        
        # Generate QR code
        qr_code = tfa_service.generate_qr_code(current_user.email, secret)
        
        # Generate backup codes
        backup_codes = tfa_service.generate_backup_codes()
        
        # Encrypt and store secret temporarily (user needs to verify first)
        encrypted_secret = encryption_service.encrypt(secret)
        encrypted_backup_codes = encryption_service.encrypt_dict({"codes": backup_codes})
        
        # Store in session or temporary storage
        # In production, use Redis or secure session storage
        
        # Log action
        audit_service.log_action(
            user_id=current_user.id,
            action="2fa_setup_initiated",
            resource="user_security",
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "qr_code": qr_code,
            "secret": secret,  # In production, don't return this
            "backup_codes": backup_codes,
            "message": "Scan the QR code with your authenticator app and verify with a token"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/2fa/verify-setup")
async def verify_2fa_setup(
    verification_data: Dict[str, str],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    request: Request = None
):
    """Verify and complete 2FA setup"""
    try:
        client_ip = request.client.host if request else "unknown"
        
        secret = verification_data.get("secret")
        token = verification_data.get("token")
        
        if not secret or not token:
            raise HTTPException(status_code=400, detail="Secret and token are required")
        
        # Verify token
        if not tfa_service.verify_token(secret, token):
            # Log failed attempt
            audit_service.log_action(
                user_id=current_user.id,
                action="2fa_setup_failed",
                resource="user_security",
                details={"reason": "invalid_token"},
                ip_address=client_ip
            )
            raise HTTPException(status_code=400, detail="Invalid token")
        
        # Enable 2FA for user
        encrypted_secret = encryption_service.encrypt(secret)
        current_user.two_factor_enabled = True
        current_user.two_factor_secret = encrypted_secret
        
        db.commit()
        
        # Log successful setup
        audit_service.log_action(
            user_id=current_user.id,
            action="2fa_enabled",
            resource="user_security",
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "message": "Two-Factor Authentication has been enabled successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/2fa/verify")
async def verify_2fa_token(
    token_data: Dict[str, str],
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """Verify 2FA token for login"""
    try:
        client_ip = request.client.host if request else "unknown"
        token = token_data.get("token")
        
        if not token:
            raise HTTPException(status_code=400, detail="Token is required")
        
        if not current_user.two_factor_enabled or not current_user.two_factor_secret:
            raise HTTPException(status_code=400, detail="2FA is not enabled")
        
        # Decrypt secret
        secret = encryption_service.decrypt(current_user.two_factor_secret)
        
        # Verify token
        if not tfa_service.verify_token(secret, token):
            audit_service.log_action(
                user_id=current_user.id,
                action="2fa_verification_failed",
                resource="user_security",
                ip_address=client_ip
            )
            raise HTTPException(status_code=400, detail="Invalid token")
        
        # Log successful verification
        audit_service.log_action(
            user_id=current_user.id,
            action="2fa_verified",
            resource="user_security",
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "message": "Token verified successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/2fa/disable")
async def disable_2fa(
    disable_data: Dict[str, str],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    request: Request = None
):
    """Disable Two-Factor Authentication"""
    try:
        client_ip = request.client.host if request else "unknown"
        password = disable_data.get("password")
        token = disable_data.get("token")
        
        if not password or not token:
            raise HTTPException(status_code=400, detail="Password and 2FA token are required")
        
        # Verify password (implement password verification)
        # verify_password(password, current_user.hashed_password)
        
        # Verify 2FA token
        if current_user.two_factor_secret:
            secret = encryption_service.decrypt(current_user.two_factor_secret)
            if not tfa_service.verify_token(secret, token):
                raise HTTPException(status_code=400, detail="Invalid token")
        
        # Disable 2FA
        current_user.two_factor_enabled = False
        current_user.two_factor_secret = None
        db.commit()
        
        # Log action
        audit_service.log_action(
            user_id=current_user.id,
            action="2fa_disabled",
            resource="user_security",
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "message": "Two-Factor Authentication has been disabled"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/audit-logs")
async def get_audit_logs(
    limit: int = 50,
    current_user: User = Depends(get_current_user)
):
    """Get user's audit logs"""
    try:
        logs = audit_service.get_user_audit_logs(current_user.id, limit)
        
        return {
            "success": True,
            "logs": logs,
            "total": len(logs)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions")
async def get_active_sessions(
    current_user: User = Depends(get_current_user)
):
    """Get user's active sessions"""
    try:
        # This would require implementing session tracking
        # For now, return mock data
        sessions = [
            {
                "session_id": "sess_123",
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0...",
                "created_at": "2024-01-15T10:30:00Z",
                "last_activity": "2024-01-15T14:30:00Z",
                "current": True
            }
        ]
        
        return {
            "success": True,
            "sessions": sessions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/{session_id}/revoke")
async def revoke_session(
    session_id: str,
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """Revoke a specific session"""
    try:
        client_ip = request.client.host if request else "unknown"
        
        # Revoke session
        session_service.invalidate_session(session_id)
        
        # Log action
        audit_service.log_action(
            user_id=current_user.id,
            action="session_revoked",
            resource="user_security",
            details={"session_id": session_id},
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "message": "Session revoked successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions/revoke-all")
async def revoke_all_sessions(
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """Revoke all user sessions"""
    try:
        client_ip = request.client.host if request else "unknown"
        
        # Revoke all sessions
        session_service.invalidate_all_user_sessions(current_user.id)
        
        # Log action
        audit_service.log_action(
            user_id=current_user.id,
            action="all_sessions_revoked",
            resource="user_security",
            ip_address=client_ip
        )
        
        return {
            "success": True,
            "message": "All sessions revoked successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/security-status")
async def get_security_status(
    current_user: User = Depends(get_current_user)
):
    """Get user's security status"""
    try:
        return {
            "success": True,
            "security_status": {
                "two_factor_enabled": current_user.two_factor_enabled,
                "password_last_changed": None,  # Implement if needed
                "failed_login_attempts": getattr(current_user, 'failed_login_attempts', 0),
                "account_locked": getattr(current_user, 'locked_until', None) is not None,
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
