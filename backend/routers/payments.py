from fastapi import APIRout<PERSON>, Depends, HTTPEx<PERSON>, Request, Header
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
from datetime import datetime
import json

from database.db_session import get_db
from database.models import User, Subscription
from routers.users import get_current_user
from services.payment_service import StripePaymentService, PayPalPaymentService, EgyptianPaymentService

router = APIRouter()

# Initialize payment services
stripe_service = StripePaymentService()
paypal_service = PayPalPaymentService()
egyptian_service = EgyptianPaymentService()

@router.post("/stripe/create-customer")
async def create_stripe_customer(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create Stripe customer for user"""
    try:
        user_data = {
            "user_id": current_user.id,
            "email": current_user.email,
            "name": current_user.name,
            "user_type": current_user.user_type
        }
        
        result = stripe_service.create_customer(user_data)
        
        if result["success"]:
            # Store Stripe customer ID in user record
            current_user.stripe_customer_id = result["customer_id"]
            db.commit()
            
            return {
                "success": True,
                "customer_id": result["customer_id"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stripe/create-subscription")
async def create_stripe_subscription(
    subscription_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create Stripe subscription"""
    try:
        plan = subscription_data.get("plan")
        if not plan:
            raise HTTPException(status_code=400, detail="Plan is required")
        
        # Get or create Stripe customer
        customer_id = getattr(current_user, 'stripe_customer_id', None)
        if not customer_id:
            customer_result = stripe_service.create_customer({
                "user_id": current_user.id,
                "email": current_user.email,
                "name": current_user.name,
                "user_type": current_user.user_type
            })
            if not customer_result["success"]:
                raise HTTPException(status_code=400, detail=customer_result["error"])
            customer_id = customer_result["customer_id"]
            current_user.stripe_customer_id = customer_id
            db.commit()
        
        # Create subscription
        result = stripe_service.create_subscription(customer_id, plan)
        
        if result["success"]:
            # Create or update subscription record
            subscription = db.query(Subscription).filter(
                Subscription.user_id == current_user.id
            ).first()
            
            if subscription:
                subscription.plan = plan
                subscription.status = "active"
                subscription.stripe_subscription_id = result["subscription_id"]
            else:
                subscription = Subscription(
                    user_id=current_user.id,
                    plan=plan,
                    status="active",
                    stripe_subscription_id=result["subscription_id"],
                    current_period_start=datetime.utcnow(),
                    current_period_end=datetime.utcnow()
                )
                db.add(subscription)
            
            db.commit()
            
            return {
                "success": True,
                "subscription_id": result["subscription_id"],
                "client_secret": result["client_secret"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stripe/update-subscription")
async def update_stripe_subscription(
    update_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update Stripe subscription plan"""
    try:
        new_plan = update_data.get("plan")
        if not new_plan:
            raise HTTPException(status_code=400, detail="New plan is required")
        
        subscription = db.query(Subscription).filter(
            Subscription.user_id == current_user.id
        ).first()
        
        if not subscription or not subscription.stripe_subscription_id:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        result = stripe_service.update_subscription(subscription.stripe_subscription_id, new_plan)
        
        if result["success"]:
            subscription.plan = new_plan
            db.commit()
            
            return {
                "success": True,
                "message": "Subscription updated successfully"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stripe/cancel-subscription")
async def cancel_stripe_subscription(
    cancel_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel Stripe subscription"""
    try:
        at_period_end = cancel_data.get("at_period_end", True)
        
        subscription = db.query(Subscription).filter(
            Subscription.user_id == current_user.id
        ).first()
        
        if not subscription or not subscription.stripe_subscription_id:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        result = stripe_service.cancel_subscription(subscription.stripe_subscription_id, at_period_end)
        
        if result["success"]:
            subscription.status = "cancelled" if not at_period_end else "cancel_at_period_end"
            db.commit()
            
            return {
                "success": True,
                "message": "Subscription cancelled successfully"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/paypal/create-payment")
async def create_paypal_payment(
    payment_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Create PayPal payment"""
    try:
        amount = payment_data.get("amount")
        currency = payment_data.get("currency", "USD")
        return_url = payment_data.get("return_url", "")
        cancel_url = payment_data.get("cancel_url", "")
        
        if not amount:
            raise HTTPException(status_code=400, detail="Amount is required")
        
        result = paypal_service.create_payment(amount, currency, return_url, cancel_url)
        
        if result["success"]:
            return {
                "success": True,
                "payment_id": result["payment_id"],
                "approval_url": result["approval_url"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/paypal/execute-payment")
async def execute_paypal_payment(
    execution_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Execute PayPal payment"""
    try:
        payment_id = execution_data.get("payment_id")
        payer_id = execution_data.get("payer_id")
        
        if not payment_id or not payer_id:
            raise HTTPException(status_code=400, detail="Payment ID and Payer ID are required")
        
        result = paypal_service.execute_payment(payment_id, payer_id)
        
        if result["success"]:
            return {
                "success": True,
                "transaction_id": result["transaction_id"],
                "message": "Payment completed successfully"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/egyptian/paymob")
async def create_paymob_payment(
    payment_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Create Paymob payment"""
    try:
        amount = payment_data.get("amount")
        if not amount:
            raise HTTPException(status_code=400, detail="Amount is required")
        
        user_data = {
            "email": current_user.email,
            "name": current_user.name,
            "phone": payment_data.get("phone", "")
        }
        
        result = egyptian_service.create_paymob_payment(amount, user_data)
        
        if result["success"]:
            return {
                "success": True,
                "payment_token": result["payment_token"],
                "iframe_url": result["iframe_url"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/egyptian/fawry")
async def create_fawry_payment(
    payment_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Create Fawry payment"""
    try:
        amount = payment_data.get("amount")
        phone = payment_data.get("phone")
        
        if not amount or not phone:
            raise HTTPException(status_code=400, detail="Amount and phone are required")
        
        user_data = {
            "email": current_user.email,
            "name": current_user.name,
            "phone": phone
        }
        
        result = egyptian_service.create_fawry_payment(amount, user_data)
        
        if result["success"]:
            return {
                "success": True,
                "reference_number": result["reference_number"],
                "payment_url": result["payment_url"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stripe/webhook")
async def stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="stripe-signature")
):
    """Handle Stripe webhook events"""
    try:
        payload = await request.body()
        
        result = stripe_service.handle_webhook(payload.decode(), stripe_signature)
        
        if result["success"]:
            # Handle different webhook events
            event_type = result["event"]
            data = result["data"]
            
            # TODO: Update database based on webhook events
            # For example, update subscription status, send notifications, etc.
            
            return {"received": True}
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subscription/status")
async def get_subscription_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's subscription status"""
    try:
        subscription = db.query(Subscription).filter(
            Subscription.user_id == current_user.id
        ).first()
        
        if subscription:
            return {
                "has_subscription": True,
                "plan": subscription.plan,
                "status": subscription.status,
                "current_period_start": subscription.current_period_start.isoformat(),
                "current_period_end": subscription.current_period_end.isoformat()
            }
        else:
            return {
                "has_subscription": False,
                "plan": None,
                "status": None
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
