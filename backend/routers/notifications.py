from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List
from datetime import datetime, timedelta

from database.db_session import get_db
from database.models import User, Campaign, Subscription, Analytics
from routers.users import get_current_user
from services.email_service import NotificationService

router = APIRouter()

# Initialize notification service
notification_service = NotificationService()

@router.post("/send-welcome")
async def send_welcome_notification(
    user_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Send welcome email notification"""
    try:
        # Add to background tasks for async processing
        background_tasks.add_task(
            notification_service.send_welcome_email,
            user_data
        )
        
        return {
            "success": True,
            "message": "Welcome email queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/campaign-milestone")
async def send_campaign_milestone_notification(
    notification_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send campaign milestone notification"""
    try:
        campaign_id = notification_data.get("campaign_id")
        milestone = notification_data.get("milestone")
        
        if not campaign_id or not milestone:
            raise HTTPException(status_code=400, detail="Campaign ID and milestone are required")
        
        # Get campaign data
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        user_data = {
            "name": current_user.name,
            "email": current_user.email
        }
        
        campaign_data = {
            "id": campaign.id,
            "name": campaign.name,
            "milestone": milestone,
            "performance": {
                "impressions": campaign.impressions or 0,
                "clicks": campaign.clicks or 0,
                "conversions": campaign.conversions or 0,
                "spend": float(campaign.spent or 0)
            }
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_campaign_milestone_alert,
            user_data,
            campaign_data
        )
        
        return {
            "success": True,
            "message": "Campaign milestone notification queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/budget-alert")
async def send_budget_alert_notification(
    alert_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send budget alert notification"""
    try:
        campaign_id = alert_data.get("campaign_id")
        
        if not campaign_id:
            raise HTTPException(status_code=400, detail="Campaign ID is required")
        
        # Get campaign data
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Calculate budget usage
        budget = float(campaign.budget or 0)
        spent = float(campaign.spent or 0)
        used_percentage = (spent / budget * 100) if budget > 0 else 0
        remaining = budget - spent
        
        user_data = {
            "name": current_user.name,
            "email": current_user.email
        }
        
        budget_data = {
            "campaign_id": campaign.id,
            "campaign_name": campaign.name,
            "used_percentage": round(used_percentage, 1),
            "remaining": round(remaining, 2),
            "total_budget": budget,
            "spent": spent
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_budget_alert,
            user_data,
            budget_data
        )
        
        return {
            "success": True,
            "message": "Budget alert notification queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/performance-report")
async def send_performance_report_notification(
    report_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send performance report notification"""
    try:
        period = report_data.get("period", "weekly")
        
        # Get user's campaigns for the report
        campaigns = db.query(Campaign).filter(
            Campaign.user_id == current_user.id
        ).all()
        
        # Calculate summary metrics
        total_impressions = sum(c.impressions or 0 for c in campaigns)
        total_clicks = sum(c.clicks or 0 for c in campaigns)
        total_spend = sum(float(c.spent or 0) for c in campaigns)
        total_conversions = sum(c.conversions or 0 for c in campaigns)
        
        # Get top performing campaigns
        top_campaigns = sorted(
            campaigns,
            key=lambda c: c.conversions or 0,
            reverse=True
        )[:3]
        
        user_data = {
            "name": current_user.name,
            "email": current_user.email
        }
        
        performance_data = {
            "period": period,
            "summary": {
                "total_impressions": total_impressions,
                "total_clicks": total_clicks,
                "total_spend": total_spend,
                "total_conversions": total_conversions,
                "avg_ctr": (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
            },
            "top_campaigns": [
                {
                    "name": c.name,
                    "conversions": c.conversions or 0,
                    "spend": float(c.spent or 0)
                }
                for c in top_campaigns
            ],
            "insights": [
                "Your campaigns are performing well this period",
                "Consider increasing budget for top-performing campaigns",
                "Review underperforming campaigns for optimization opportunities"
            ],
            "pdf_path": report_data.get("pdf_path")  # Optional PDF attachment
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_performance_report,
            user_data,
            performance_data
        )
        
        return {
            "success": True,
            "message": "Performance report notification queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/churn-risk-alert")
async def send_churn_risk_alert(
    risk_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Send churn risk alert to admin team"""
    try:
        # This would typically be called by an automated system
        # For demo purposes, we'll use current user data
        
        user_data = {
            "id": current_user.id,
            "name": current_user.name,
            "email": current_user.email
        }
        
        churn_risk_data = {
            "risk_score": risk_data.get("risk_score", 75),
            "factors": risk_data.get("factors", [
                "Low recent activity",
                "Decreased campaign performance",
                "No recent logins"
            ]),
            "last_activity": current_user.last_login.isoformat() if current_user.last_login else None
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_churn_risk_alert,
            user_data,
            churn_risk_data
        )
        
        return {
            "success": True,
            "message": "Churn risk alert queued for delivery to admin team"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/subscription-expiry")
async def send_subscription_expiry_notification(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send subscription expiry notification"""
    try:
        # Get user's subscription
        subscription = db.query(Subscription).filter(
            Subscription.user_id == current_user.id
        ).first()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="No subscription found")
        
        # Calculate days until expiry
        expiry_date = subscription.current_period_end
        days_until_expiry = (expiry_date - datetime.utcnow()).days
        
        user_data = {
            "name": current_user.name,
            "email": current_user.email
        }
        
        subscription_data = {
            "plan": subscription.plan,
            "expiry_date": expiry_date.strftime("%B %d, %Y"),
            "days_until_expiry": days_until_expiry
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_subscription_expiry_notice,
            user_data,
            subscription_data
        )
        
        return {
            "success": True,
            "message": "Subscription expiry notification queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/onboarding/{step}")
async def send_onboarding_notification(
    step: int,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Send onboarding sequence email"""
    try:
        if step < 1 or step > 3:
            raise HTTPException(status_code=400, detail="Invalid onboarding step (1-3)")
        
        user_data = {
            "name": current_user.name,
            "email": current_user.email
        }
        
        # Send notification in background
        background_tasks.add_task(
            notification_service.send_onboarding_sequence,
            user_data,
            step
        )
        
        return {
            "success": True,
            "message": f"Onboarding step {step} notification queued for delivery"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/preferences")
async def get_notification_preferences(
    current_user: User = Depends(get_current_user)
):
    """Get user's notification preferences"""
    try:
        # Mock notification preferences
        # In production, store these in database
        preferences = {
            "email_notifications": True,
            "campaign_milestones": True,
            "budget_alerts": True,
            "performance_reports": True,
            "weekly_reports": True,
            "monthly_reports": False,
            "marketing_emails": True,
            "system_updates": True,
            "frequency": "immediate",  # immediate, daily, weekly
            "quiet_hours": {
                "enabled": False,
                "start": "22:00",
                "end": "08:00"
            }
        }
        
        return {
            "success": True,
            "preferences": preferences
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/preferences")
async def update_notification_preferences(
    preferences: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user's notification preferences"""
    try:
        # In production, save preferences to database
        # For now, just return success
        
        # Log the preference update
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="notification_preferences_updated",
            metric_value=1,
            meta_data=preferences
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "success": True,
            "message": "Notification preferences updated successfully",
            "preferences": preferences
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_notification_history(
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's notification history"""
    try:
        # Get notification-related analytics
        notifications = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type.like("%notification%")
        ).order_by(Analytics.date_recorded.desc()).limit(limit).all()
        
        history = []
        for notification in notifications:
            metadata = notification.meta_data or {}
            history.append({
                "id": notification.id,
                "type": notification.metric_type,
                "sent_at": notification.date_recorded.isoformat(),
                "status": "delivered",  # Mock status
                "metadata": metadata
            })
        
        return {
            "success": True,
            "notifications": history,
            "total": len(history)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
