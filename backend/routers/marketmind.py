"""
MarketMind Router - Advanced AI Models for Marketing Intelligence
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import pandas as pd
from services.marketmind_ai import MarketMindAI
from auth import get_current_user

router = APIRouter(prefix="/marketmind", tags=["MarketMind AI"])

# Initialize MarketMind AI service
marketmind_ai = MarketMindAI()

# Pydantic models
class CustomerSegmentationRequest(BaseModel):
    n_clusters: int = 5
    use_sample_data: bool = True

class ChurnPredictionRequest(BaseModel):
    use_sample_data: bool = True

class SalesPredictionRequest(BaseModel):
    periods: int = 30
    use_sample_data: bool = True

class SentimentAnalysisRequest(BaseModel):
    texts: List[str]

class CampaignOptimizationRequest(BaseModel):
    use_sample_data: bool = True

class CustomerJourneyRequest(BaseModel):
    use_sample_data: bool = True

@router.get("/status")
async def get_marketmind_status():
    """Get status of MarketMind AI models"""
    return marketmind_ai.get_models_status()

@router.post("/customer-segmentation")
async def customer_segmentation(
    request: CustomerSegmentationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Perform customer segmentation analysis"""
    try:
        # Generate or use provided customer data
        if request.use_sample_data:
            customer_data = marketmind_ai.generate_sample_customer_data(1000)
        else:
            # In production, this would load real customer data
            customer_data = marketmind_ai.generate_sample_customer_data(1000)
        
        result = marketmind_ai.customer_segmentation(customer_data, request.n_clusters)
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم تحليل تجزئة العملاء بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحليل تجزئة العملاء: {str(e)}"
        )

@router.post("/churn-prediction")
async def churn_prediction(
    request: ChurnPredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """Predict customer churn"""
    try:
        # Generate or use provided customer data
        if request.use_sample_data:
            customer_data = marketmind_ai.generate_sample_customer_data(1000)
        else:
            # In production, this would load real customer data
            customer_data = marketmind_ai.generate_sample_customer_data(1000)
        
        result = marketmind_ai.churn_prediction(customer_data)
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم التنبؤ بتوقف العملاء بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في التنبؤ بتوقف العملاء: {str(e)}"
        )

@router.post("/sales-prediction")
async def sales_prediction(
    request: SalesPredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """Predict future sales"""
    try:
        result = marketmind_ai.sales_prediction(periods=request.periods)
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم التنبؤ بالمبيعات بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في التنبؤ بالمبيعات: {str(e)}"
        )

@router.post("/sentiment-analysis")
async def advanced_sentiment_analysis(
    request: SentimentAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Perform advanced sentiment analysis"""
    try:
        if not request.texts:
            raise HTTPException(
                status_code=400,
                detail="يرجى إدخال النصوص المراد تحليلها"
            )
        
        result = marketmind_ai.advanced_sentiment_analysis(request.texts)
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم تحليل المشاعر بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحليل المشاعر: {str(e)}"
        )

@router.post("/campaign-optimization")
async def campaign_optimization(
    request: CampaignOptimizationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Optimize marketing campaigns"""
    try:
        result = marketmind_ai.campaign_optimization()
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم تحليل وتحسين الحملات بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحسين الحملات: {str(e)}"
        )

@router.post("/customer-journey")
async def customer_journey_analysis(
    request: CustomerJourneyRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyze customer journey"""
    try:
        result = marketmind_ai.customer_journey_analysis()
        
        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم تحليل رحلة العميل بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحليل رحلة العميل: {str(e)}"
        )

@router.post("/train-all-models")
async def train_all_models(current_user: dict = Depends(get_current_user)):
    """Train all MarketMind models with sample data"""
    try:
        results = {}
        
        # Generate sample data
        customer_data = marketmind_ai.generate_sample_customer_data(1000)
        
        # Train customer segmentation
        segmentation_result = marketmind_ai.customer_segmentation(customer_data, 5)
        results['customer_segmentation'] = segmentation_result
        
        # Train churn prediction
        churn_result = marketmind_ai.churn_prediction(customer_data)
        results['churn_prediction'] = churn_result
        
        # Train sales prediction
        sales_result = marketmind_ai.sales_prediction(periods=30)
        results['sales_prediction'] = sales_result
        
        # Train sentiment analysis
        sample_texts = [
            "هذا المنتج رائع جداً",
            "خدمة سيئة للغاية",
            "المنتج عادي لا بأس به",
            "This product is amazing",
            "Terrible customer service",
            "The product is okay"
        ]
        sentiment_result = marketmind_ai.advanced_sentiment_analysis(sample_texts)
        results['sentiment_analysis'] = sentiment_result
        
        # Train campaign optimization
        campaign_result = marketmind_ai.campaign_optimization()
        results['campaign_optimization'] = campaign_result
        
        # Train customer journey analysis
        journey_result = marketmind_ai.customer_journey_analysis()
        results['customer_journey'] = journey_result
        
        # Count successful models
        successful_models = sum(1 for result in results.values() if result.get('success', False))
        
        return {
            "success": True,
            "data": {
                "models_trained": successful_models,
                "total_models": len(results),
                "results": results
            },
            "message": f"تم تدريب {successful_models} من أصل {len(results)} نماذج بنجاح"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تدريب النماذج: {str(e)}"
        )

@router.get("/model-performance")
async def get_model_performance(current_user: dict = Depends(get_current_user)):
    """Get performance metrics for all trained models"""
    try:
        models_status = marketmind_ai.get_models_status()
        
        performance_summary = {
            'total_models': models_status['models_count'],
            'models_by_type': {},
            'latest_training': None,
            'model_details': []
        }
        
        # Analyze models by type
        for model_name, metadata in models_status['models'].items():
            model_type = metadata.get('model_type', 'unknown')
            if model_type not in performance_summary['models_by_type']:
                performance_summary['models_by_type'][model_type] = 0
            performance_summary['models_by_type'][model_type] += 1
            
            # Find latest training
            created_at = metadata.get('created_at')
            if created_at and (not performance_summary['latest_training'] or created_at > performance_summary['latest_training']):
                performance_summary['latest_training'] = created_at
            
            # Add model details
            performance_summary['model_details'].append({
                'name': model_name,
                'type': model_type,
                'algorithm': metadata.get('algorithm', 'unknown'),
                'created_at': created_at,
                'performance_metrics': {
                    k: v for k, v in metadata.items() 
                    if k in ['accuracy', 'silhouette_score', 'train_mae', 'test_mae', 'train_rmse', 'test_rmse']
                }
            })
        
        return {
            "success": True,
            "data": performance_summary,
            "message": "تم جلب معلومات أداء النماذج بنجاح"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في جلب معلومات الأداء: {str(e)}"
        )
