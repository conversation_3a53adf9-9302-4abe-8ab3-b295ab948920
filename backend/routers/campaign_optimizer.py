from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, List, Any, Optional
from datetime import datetime
import random

from database.db_session import get_db
from database.models import User, Campaign, Analytics
from routers.users import get_current_user

router = APIRouter()

@router.post("/")
async def create_campaign(
    campaign_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new marketing campaign"""
    try:
        campaign = Campaign(
            user_id=current_user.id,
            name=campaign_data.get("name", "Untitled Campaign"),
            description=campaign_data.get("description", ""),
            status="draft",
            budget=campaign_data.get("budget", 0.0),
            start_date=datetime.fromisoformat(campaign_data.get("start_date", datetime.now().isoformat())),
            end_date=datetime.fromisoformat(campaign_data.get("end_date", datetime.now().isoformat()))
        )
        
        db.add(campaign)
        db.commit()
        db.refresh(campaign)
        
        return {
            "id": campaign.id,
            "name": campaign.name,
            "status": campaign.status,
            "created_at": campaign.created_at.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/")
async def get_campaigns(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's campaigns"""
    query = db.query(Campaign).filter(Campaign.user_id == current_user.id)
    
    if status:
        query = query.filter(Campaign.status == status)
    
    campaigns = query.all()
    
    return {
        "campaigns": [
            {
                "id": campaign.id,
                "name": campaign.name,
                "description": campaign.description,
                "status": campaign.status,
                "budget": campaign.budget,
                "spent": campaign.spent,
                "start_date": campaign.start_date.isoformat() if campaign.start_date else None,
                "end_date": campaign.end_date.isoformat() if campaign.end_date else None,
                "impressions": campaign.impressions,
                "clicks": campaign.clicks,
                "conversions": campaign.conversions,
                "created_at": campaign.created_at.isoformat()
            }
            for campaign in campaigns
        ]
    }

@router.post("/{campaign_id}/optimize")
async def optimize_campaign(
    campaign_id: int,
    optimization_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Optimize campaign performance"""
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        optimization_type = optimization_data.get("type", "performance")
        
        # Mock optimization suggestions
        optimizations = {
            "performance": {
                "current_metrics": {
                    "ctr": "2.3%",
                    "conversion_rate": "1.8%",
                    "cpc": "$0.45",
                    "roas": "3.2x"
                },
                "optimized_metrics": {
                    "ctr": "3.1%",
                    "conversion_rate": "2.4%",
                    "cpc": "$0.38",
                    "roas": "4.1x"
                },
                "recommendations": [
                    {
                        "type": "targeting",
                        "suggestion": "Narrow audience to high-converting demographics",
                        "impact": "15% improvement in conversion rate",
                        "effort": "Low"
                    },
                    {
                        "type": "bidding",
                        "suggestion": "Switch to automated bidding strategy",
                        "impact": "12% reduction in CPC",
                        "effort": "Medium"
                    },
                    {
                        "type": "creative",
                        "suggestion": "Test new ad creative variations",
                        "impact": "20% improvement in CTR",
                        "effort": "High"
                    }
                ]
            },
            "budget": {
                "current_allocation": {
                    "search": "40%",
                    "display": "30%",
                    "social": "30%"
                },
                "optimized_allocation": {
                    "search": "50%",
                    "display": "20%",
                    "social": "30%"
                },
                "recommendations": [
                    {
                        "type": "reallocation",
                        "suggestion": "Increase search budget by 10%",
                        "impact": "25% more conversions",
                        "effort": "Low"
                    },
                    {
                        "type": "timing",
                        "suggestion": "Adjust dayparting for peak hours",
                        "impact": "18% improvement in efficiency",
                        "effort": "Medium"
                    }
                ]
            }
        }
        
        result = optimizations.get(optimization_type, optimizations["performance"])
        
        # Save optimization analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            campaign_id=campaign_id,
            metric_type="campaign_optimization",
            metric_value=len(result["recommendations"]),
            metadata=result
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "campaign_id": campaign_id,
            "optimization_type": optimization_type,
            "results": result,
            "estimated_improvement": "25-35% performance increase",
            "implementation_time": "2-3 days"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{campaign_id}/ab-test")
async def create_ab_test(
    campaign_id: int,
    test_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create A/B test for campaign"""
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        test_type = test_data.get("test_type", "creative")
        variants = test_data.get("variants", [])
        
        # Mock A/B test setup
        ab_test = {
            "test_id": f"test_{campaign_id}_{random.randint(1000, 9999)}",
            "campaign_id": campaign_id,
            "test_type": test_type,
            "status": "running",
            "variants": [
                {
                    "variant_id": f"variant_{i}",
                    "name": variant.get("name", f"Variant {i+1}"),
                    "traffic_split": variant.get("traffic_split", 50),
                    "content": variant.get("content", ""),
                    "metrics": {
                        "impressions": random.randint(1000, 5000),
                        "clicks": random.randint(50, 200),
                        "conversions": random.randint(5, 25),
                        "ctr": round(random.uniform(1.5, 4.0), 2),
                        "conversion_rate": round(random.uniform(1.0, 3.5), 2)
                    }
                }
                for i, variant in enumerate(variants[:2])  # Limit to 2 variants
            ],
            "duration_days": test_data.get("duration_days", 14),
            "confidence_level": 95,
            "statistical_significance": random.choice([True, False]),
            "winner": None if random.choice([True, False]) else "variant_0"
        }
        
        # Determine winner if test is complete
        if ab_test["statistical_significance"] and ab_test["winner"]:
            winner_variant = next(v for v in ab_test["variants"] if v["variant_id"] == ab_test["winner"])
            ab_test["results"] = {
                "winner": ab_test["winner"],
                "improvement": f"+{random.randint(15, 45)}%",
                "confidence": "95%",
                "recommendation": f"Implement {winner_variant['name']} for full campaign"
            }
        
        return ab_test
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{campaign_id}/analytics")
async def get_campaign_analytics(
    campaign_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed campaign analytics"""
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Mock detailed analytics
        analytics = {
            "overview": {
                "impressions": campaign.impressions or random.randint(10000, 50000),
                "clicks": campaign.clicks or random.randint(500, 2000),
                "conversions": campaign.conversions or random.randint(25, 150),
                "spend": campaign.spent or random.uniform(500, 2000),
                "ctr": round(random.uniform(2.0, 5.0), 2),
                "cpc": round(random.uniform(0.30, 0.80), 2),
                "conversion_rate": round(random.uniform(1.5, 4.0), 2),
                "roas": round(random.uniform(2.5, 6.0), 1)
            },
            "performance_by_day": [
                {
                    "date": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"),
                    "impressions": random.randint(800, 1500),
                    "clicks": random.randint(40, 80),
                    "conversions": random.randint(2, 8),
                    "spend": round(random.uniform(50, 150), 2)
                }
                for i in range(7)
            ],
            "audience_insights": {
                "top_demographics": [
                    {"age_group": "25-34", "percentage": 35, "performance": "High"},
                    {"age_group": "35-44", "percentage": 28, "performance": "Medium"},
                    {"age_group": "18-24", "percentage": 22, "performance": "High"},
                    {"age_group": "45-54", "percentage": 15, "performance": "Low"}
                ],
                "top_locations": [
                    {"location": "New York", "percentage": 25, "performance": "High"},
                    {"location": "California", "percentage": 20, "performance": "Medium"},
                    {"location": "Texas", "percentage": 15, "performance": "High"},
                    {"location": "Florida", "percentage": 12, "performance": "Medium"}
                ]
            },
            "recommendations": [
                "Increase budget allocation to 25-34 age group",
                "Expand targeting to similar demographics",
                "Test new creative variations",
                "Optimize landing page for mobile users"
            ]
        }
        
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{campaign_id}")
async def update_campaign(
    campaign_id: int,
    update_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update campaign details"""
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(status_code=404, detail="Campaign not found")
        
        # Update allowed fields
        allowed_fields = ["name", "description", "budget", "status", "start_date", "end_date"]
        for field in allowed_fields:
            if field in update_data:
                if field in ["start_date", "end_date"] and update_data[field]:
                    setattr(campaign, field, datetime.fromisoformat(update_data[field]))
                else:
                    setattr(campaign, field, update_data[field])
        
        campaign.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(campaign)
        
        return {
            "id": campaign.id,
            "name": campaign.name,
            "status": campaign.status,
            "updated_at": campaign.updated_at.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
