"""
AI Tools Router - Endpoints for AI-powered marketing tools
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from services.ai_service import AIService
from auth import get_current_user

router = APIRouter(prefix="/ai", tags=["AI Tools"])

# Initialize AI service
ai_service = AIService()

# Pydantic models for request/response
class ContentGenerationRequest(BaseModel):
    content_type: str  # email, social, ad_copy, blog, post
    prompt: str
    target_audience: str = "general"
    tone: str = "professional"

class SentimentAnalysisRequest(BaseModel):
    text: str
    use_ai: bool = True

class CompetitorAnalysisRequest(BaseModel):
    name: str
    website: Optional[str] = None
    description: Optional[str] = None

class StrategyGenerationRequest(BaseModel):
    company_name: str
    industry: str
    target_audience: str
    budget: Optional[str] = None
    goals: Optional[str] = None

class CampaignOptimizationRequest(BaseModel):
    name: str
    type: str
    objective: str
    target_audience: str
    budget: Optional[str] = None
    duration: Optional[str] = None
    current_results: Optional[str] = None

class HashtagGenerationRequest(BaseModel):
    content: str
    platform: str = "instagram"

@router.get("/status")
async def get_ai_status():
    """Get AI service status"""
    return ai_service.get_ai_status()

@router.post("/generate-content")
async def generate_content(
    request: ContentGenerationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate marketing content using AI"""
    try:
        result = ai_service.generate_content(
            content_type=request.content_type,
            prompt=request.prompt,
            target_audience=request.target_audience,
            tone=request.tone
        )
        
        return {
            "success": True,
            "data": result,
            "message": "تم إنشاء المحتوى بنجاح"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء المحتوى: {str(e)}"
        )

@router.post("/analyze-sentiment")
async def analyze_sentiment(
    request: SentimentAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyze sentiment of text"""
    try:
        result = ai_service.analyze_sentiment(
            text=request.text,
            use_ai=request.use_ai
        )
        
        return {
            "success": True,
            "data": result,
            "message": "تم تحليل المشاعر بنجاح"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحليل المشاعر: {str(e)}"
        )

@router.post("/analyze-competitor")
async def analyze_competitor(
    request: CompetitorAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyze competitor using AI"""
    try:
        competitor_data = {
            "name": request.name,
            "website": request.website,
            "description": request.description
        }
        
        result = ai_service.analyze_competitor(competitor_data)

        # Check if there's a critical error (not just a warning)
        if "error" in result and not result.get("analysis"):
            raise HTTPException(status_code=400, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "message": "تم تحليل المنافس بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحليل المنافس: {str(e)}"
        )

@router.post("/generate-strategy")
async def generate_strategy(
    request: StrategyGenerationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate marketing strategy using AI"""
    try:
        business_data = {
            "company_name": request.company_name,
            "industry": request.industry,
            "target_audience": request.target_audience,
            "budget": request.budget,
            "goals": request.goals
        }
        
        result = ai_service.generate_marketing_strategy(business_data)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "data": result,
            "message": "تم إنشاء الاستراتيجية بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء الاستراتيجية: {str(e)}"
        )

@router.post("/optimize-campaign")
async def optimize_campaign(
    request: CampaignOptimizationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Optimize marketing campaign using AI"""
    try:
        campaign_data = {
            "name": request.name,
            "type": request.type,
            "objective": request.objective,
            "target_audience": request.target_audience,
            "budget": request.budget,
            "duration": request.duration,
            "current_results": request.current_results
        }
        
        result = ai_service.optimize_campaign(campaign_data)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "data": result,
            "message": "تم تحسين الحملة بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في تحسين الحملة: {str(e)}"
        )

@router.post("/generate-hashtags")
async def generate_hashtags(
    request: HashtagGenerationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate hashtags for social media content"""
    try:
        result = ai_service.generate_hashtags(
            content=request.content,
            platform=request.platform
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "data": result,
            "message": "تم إنشاء الهاشتاجات بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء الهاشتاجات: {str(e)}"
        )

@router.get("/content-types")
async def get_content_types():
    """Get available content types for generation"""
    return {
        "content_types": [
            {
                "id": "email",
                "name": "رسالة بريد إلكتروني",
                "description": "إنشاء رسائل بريد إلكتروني تسويقية احترافية"
            },
            {
                "id": "social",
                "name": "منشور وسائل التواصل",
                "description": "إنشاء منشورات جذابة لوسائل التواصل الاجتماعي"
            },
            {
                "id": "ad_copy",
                "name": "نص إعلاني",
                "description": "كتابة نصوص إعلانية مقنعة وفعالة"
            },
            {
                "id": "blog",
                "name": "مقال مدونة",
                "description": "إنشاء مقالات مدونة شاملة ومفيدة"
            },
            {
                "id": "post",
                "name": "منشور عام",
                "description": "إنشاء محتوى تسويقي عام"
            }
        ],
        "tones": [
            {"id": "professional", "name": "مهني"},
            {"id": "casual", "name": "غير رسمي"},
            {"id": "friendly", "name": "ودود"},
            {"id": "urgent", "name": "عاجل"}
        ]
    }
