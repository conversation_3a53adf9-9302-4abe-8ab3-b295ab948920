from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, List, Any
from datetime import datetime, timedelta
import random

from database.db_session import get_db
from database.models import User, Analytics
from routers.users import get_current_user

router = APIRouter()

@router.post("/churn")
async def predict_churn(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Predict customer churn probability"""
    try:
        customers = data.get("customers", [])
        if not customers:
            raise HTTPException(status_code=400, detail="Customer data is required")
        
        # Mock churn prediction
        predictions = []
        for customer in customers:
            # Simple mock logic based on customer attributes
            recency = customer.get("recency", 30)
            frequency = customer.get("frequency", 5)
            monetary = customer.get("monetary", 1000)
            
            # Calculate churn probability (mock)
            churn_score = 0.0
            if recency > 60:
                churn_score += 0.3
            if frequency < 2:
                churn_score += 0.4
            if monetary < 500:
                churn_score += 0.2
            
            churn_score = min(1.0, churn_score + random.uniform(-0.1, 0.1))
            
            risk_level = "High" if churn_score > 0.7 else "Medium" if churn_score > 0.4 else "Low"
            
            predictions.append({
                "customer_id": customer.get("id", "unknown"),
                "churn_probability": round(churn_score, 3),
                "risk_level": risk_level,
                "factors": {
                    "recency_impact": recency > 60,
                    "frequency_impact": frequency < 2,
                    "monetary_impact": monetary < 500
                },
                "recommendations": [
                    "Send re-engagement email" if churn_score > 0.5 else "Continue regular communication",
                    "Offer special discount" if churn_score > 0.7 else "Monitor activity",
                    "Personal outreach" if churn_score > 0.8 else "Automated follow-up"
                ]
            })
        
        # Save analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="churn_prediction",
            metric_value=sum(p["churn_probability"] for p in predictions) / len(predictions),
            metadata={"total_customers": len(customers), "high_risk_count": sum(1 for p in predictions if p["risk_level"] == "High")}
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "predictions": predictions,
            "summary": {
                "total_customers": len(customers),
                "high_risk": sum(1 for p in predictions if p["risk_level"] == "High"),
                "medium_risk": sum(1 for p in predictions if p["risk_level"] == "Medium"),
                "low_risk": sum(1 for p in predictions if p["risk_level"] == "Low"),
                "average_churn_probability": round(sum(p["churn_probability"] for p in predictions) / len(predictions), 3)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/trends")
async def predict_trends(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Predict market trends"""
    try:
        industry = data.get("industry", "general")
        time_horizon = data.get("time_horizon", "3_months")
        
        # Mock trend predictions
        trends = {
            "emerging_trends": [
                {"trend": "AI-Powered Personalization", "growth_rate": "+45%", "confidence": 0.85},
                {"trend": "Voice Commerce", "growth_rate": "+32%", "confidence": 0.78},
                {"trend": "Sustainable Marketing", "growth_rate": "+28%", "confidence": 0.82}
            ],
            "declining_trends": [
                {"trend": "Traditional Display Ads", "decline_rate": "-15%", "confidence": 0.75},
                {"trend": "Mass Email Campaigns", "decline_rate": "-22%", "confidence": 0.80}
            ],
            "stable_trends": [
                {"trend": "Content Marketing", "growth_rate": "+5%", "confidence": 0.90},
                {"trend": "Social Media Marketing", "growth_rate": "+8%", "confidence": 0.88}
            ],
            "predictions": {
                "next_quarter": {
                    "market_growth": "+12%",
                    "key_opportunities": ["Mobile-first strategies", "Interactive content", "Community building"],
                    "threats": ["Privacy regulations", "Ad fatigue", "Economic uncertainty"]
                },
                "next_year": {
                    "market_growth": "+25%",
                    "key_opportunities": ["AR/VR marketing", "Blockchain loyalty programs", "Hyper-personalization"],
                    "threats": ["Increased competition", "Technology disruption", "Changing consumer behavior"]
                }
            }
        }
        
        # Save analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="trend_prediction",
            metric_value=0.82,  # Average confidence
            metadata={"industry": industry, "time_horizon": time_horizon}
        )
        db.add(analytics_record)
        db.commit()
        
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sales-forecast")
async def forecast_sales(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Forecast sales based on historical data"""
    try:
        historical_data = data.get("historical_sales", [])
        forecast_period = data.get("forecast_period", 30)  # days
        
        if not historical_data:
            # Generate mock historical data
            base_date = datetime.now() - timedelta(days=90)
            historical_data = []
            for i in range(90):
                date = base_date + timedelta(days=i)
                # Mock sales with some seasonality and trend
                base_sales = 1000 + (i * 5)  # Growth trend
                seasonal = 200 * (1 + 0.3 * (i % 7))  # Weekly seasonality
                noise = random.uniform(-100, 100)
                sales = max(0, base_sales + seasonal + noise)
                historical_data.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "sales": round(sales, 2)
                })
        
        # Generate forecast
        last_sales = historical_data[-1]["sales"] if historical_data else 1000
        forecast = []
        
        for i in range(forecast_period):
            date = datetime.now() + timedelta(days=i+1)
            # Simple trend + seasonality forecast
            trend = last_sales * (1 + 0.02)  # 2% growth
            seasonal = 1 + 0.1 * ((i % 7) / 7)  # Weekly pattern
            predicted_sales = trend * seasonal
            
            # Add confidence interval
            confidence_lower = predicted_sales * 0.85
            confidence_upper = predicted_sales * 1.15
            
            forecast.append({
                "date": date.strftime("%Y-%m-%d"),
                "predicted_sales": round(predicted_sales, 2),
                "confidence_lower": round(confidence_lower, 2),
                "confidence_upper": round(confidence_upper, 2),
                "confidence": round(max(0.6, 1 - (i * 0.01)), 2)  # Decreasing confidence
            })
            
            last_sales = predicted_sales
        
        # Calculate summary statistics
        total_forecast = sum(f["predicted_sales"] for f in forecast)
        avg_daily_sales = total_forecast / len(forecast)
        
        # Save analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="sales_forecast",
            metric_value=total_forecast,
            metadata={"forecast_period": forecast_period, "avg_daily_sales": avg_daily_sales}
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "forecast": forecast,
            "summary": {
                "total_forecast": round(total_forecast, 2),
                "average_daily_sales": round(avg_daily_sales, 2),
                "growth_rate": "+2% daily",
                "confidence": "High for next 7 days, Medium for 8-30 days"
            },
            "insights": [
                "Sales show consistent upward trend",
                "Weekly seasonality pattern detected",
                "Recommend increasing inventory for peak days",
                "Monitor actual vs predicted for model improvement"
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models")
async def get_prediction_models(
    current_user: User = Depends(get_current_user)
):
    """Get available prediction models"""
    models = [
        {
            "name": "Customer Churn Prediction",
            "type": "classification",
            "accuracy": 0.87,
            "description": "Predicts likelihood of customer churn based on behavior patterns",
            "features": ["recency", "frequency", "monetary", "engagement_score"]
        },
        {
            "name": "Sales Forecasting",
            "type": "regression",
            "accuracy": 0.82,
            "description": "Forecasts future sales based on historical data and trends",
            "features": ["historical_sales", "seasonality", "marketing_spend", "external_factors"]
        },
        {
            "name": "Trend Analysis",
            "type": "time_series",
            "accuracy": 0.75,
            "description": "Identifies emerging market trends and opportunities",
            "features": ["search_volume", "social_mentions", "competitor_activity", "market_indicators"]
        }
    ]
    
    return {"models": models}
