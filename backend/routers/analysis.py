from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import pandas as pd
import numpy as np
from datetime import datetime

from database.db_session import get_db
from database.models import User, Analytics, CustomerSegment
from routers.users import get_current_user
from services.segmentation import CustomerSegmentationService
from services.sentiment_analysis import SentimentAnalysisService

router = APIRouter()

@router.post("/sentiment")
async def analyze_sentiment(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Analyze sentiment of text data"""
    try:
        text_data = data.get("text", "")
        if not text_data:
            raise HTTPException(status_code=400, detail="Text data is required")
        
        # Use sentiment analysis service
        sentiment_service = SentimentAnalysisService()
        results = sentiment_service.analyze_sentiment(text_data)
        
        # Save analytics data
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="sentiment",
            metric_value=results["compound_score"],
            metadata=results
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "sentiment": results["sentiment"],
            "scores": {
                "positive": results["positive"],
                "neutral": results["neutral"], 
                "negative": results["negative"],
                "compound": results["compound_score"]
            },
            "confidence": results["confidence"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/customer-segmentation")
async def perform_customer_segmentation(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform customer segmentation analysis"""
    try:
        # Get customer data
        customer_data = data.get("customers", [])
        if not customer_data:
            raise HTTPException(status_code=400, detail="Customer data is required")
        
        # Use segmentation service
        segmentation_service = CustomerSegmentationService()
        segments = segmentation_service.segment_customers(customer_data)
        
        # Save segments to database
        for segment_name, segment_info in segments.items():
            segment_record = CustomerSegment(
                user_id=current_user.id,
                segment_name=segment_name,
                description=segment_info.get("description", ""),
                criteria=segment_info.get("criteria", {}),
                customer_count=len(segment_info.get("customers", []))
            )
            db.add(segment_record)
        
        db.commit()
        
        return {
            "segments": segments,
            "total_segments": len(segments),
            "analysis_date": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/segments")
async def get_customer_segments(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's customer segments"""
    segments = db.query(CustomerSegment).filter(
        CustomerSegment.user_id == current_user.id
    ).all()
    
    return {
        "segments": [
            {
                "id": segment.id,
                "name": segment.segment_name,
                "description": segment.description,
                "customer_count": segment.customer_count,
                "created_at": segment.created_at.isoformat()
            }
            for segment in segments
        ]
    }

@router.post("/trend-analysis")
async def analyze_trends(
    data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Analyze market trends from data"""
    try:
        # Mock trend analysis - in production, use real ML models
        time_series_data = data.get("time_series", [])
        keywords = data.get("keywords", [])
        
        # Simulate trend analysis
        trends = {
            "trending_up": ["AI Marketing", "Personalization", "Automation"],
            "trending_down": ["Traditional Advertising", "Mass Marketing"],
            "stable": ["Email Marketing", "Social Media"],
            "emerging": ["Voice Search", "AR/VR Marketing"],
            "forecast": {
                "next_month": "+15% growth expected",
                "next_quarter": "+25% growth expected",
                "confidence": 0.85
            }
        }
        
        # Save analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="trend_analysis",
            metric_value=0.85,  # confidence score
            metadata=trends
        )
        db.add(analytics_record)
        db.commit()
        
        return trends
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/summary")
async def get_analytics_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get analytics summary for user"""
    analytics = db.query(Analytics).filter(
        Analytics.user_id == current_user.id
    ).all()
    
    # Group by metric type
    summary = {}
    for record in analytics:
        metric_type = record.metric_type
        if metric_type not in summary:
            summary[metric_type] = {
                "count": 0,
                "latest_value": None,
                "latest_date": None
            }
        
        summary[metric_type]["count"] += 1
        if not summary[metric_type]["latest_date"] or record.date_recorded > summary[metric_type]["latest_date"]:
            summary[metric_type]["latest_value"] = record.metric_value
            summary[metric_type]["latest_date"] = record.date_recorded
    
    return {
        "total_analyses": len(analytics),
        "metrics": summary,
        "last_analysis": max([a.date_recorded for a in analytics]) if analytics else None
    }
