from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import os

from database.db_session import get_db
from database.models import User, Campaign, Analytics
from routers.users import get_current_user
from services.reporting_service import (
    AdvancedReportingService,
    ReportExportService,
    ScheduledReportService
)

router = APIRouter()

# Initialize services
reporting_service = AdvancedReportingService()
export_service = ReportExportService()
scheduled_service = ScheduledReportService()

@router.post("/generate")
async def generate_report(
    report_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a comprehensive performance report"""
    try:
        report_type = report_request.get("report_type", "comprehensive")
        date_range = report_request.get("date_range", 30)
        
        # Get user's campaigns data
        campaigns = db.query(Campaign).filter(
            Campaign.user_id == current_user.id
        ).all()
        
        # Convert to dict format for reporting
        campaign_data = []
        for campaign in campaigns:
            campaign_data.append({
                "id": campaign.id,
                "name": campaign.name,
                "status": campaign.status,
                "impressions": campaign.impressions or 0,
                "clicks": campaign.clicks or 0,
                "conversions": campaign.conversions or 0,
                "spend": float(campaign.spent or 0),
                "budget": float(campaign.budget or 0),
                "start_date": campaign.start_date.isoformat() if campaign.start_date else None,
                "end_date": campaign.end_date.isoformat() if campaign.end_date else None
            })
        
        # Generate report
        report = reporting_service.generate_performance_report(
            {"campaigns": campaign_data}, 
            report_type
        )
        
        # Save report metadata to analytics
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="report_generated",
            metric_value=len(campaign_data),
            meta_data={
                "report_id": report["report_id"],
                "report_type": report_type,
                "campaigns_included": len(campaign_data)
            }
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/export/pdf")
async def export_report_pdf(
    export_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Export report to PDF"""
    try:
        report_data = export_request.get("report_data")
        if not report_data:
            raise HTTPException(status_code=400, detail="Report data is required")
        
        # Generate PDF
        filename = f"report_{current_user.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join("reports", filename)
        
        # Ensure reports directory exists
        os.makedirs("reports", exist_ok=True)
        
        # Export to PDF
        export_service.export_to_pdf(report_data, filepath)
        
        # Schedule cleanup of file after download
        background_tasks.add_task(cleanup_file, filepath, delay=3600)  # Delete after 1 hour
        
        return FileResponse(
            filepath,
            media_type="application/pdf",
            filename=filename
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/export/excel")
async def export_report_excel(
    export_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Export report to Excel"""
    try:
        report_data = export_request.get("report_data")
        if not report_data:
            raise HTTPException(status_code=400, detail="Report data is required")
        
        # Generate Excel file
        filename = f"report_{current_user.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join("reports", filename)
        
        # Ensure reports directory exists
        os.makedirs("reports", exist_ok=True)
        
        # Export to Excel
        export_service.export_to_excel(report_data, filepath)
        
        # Schedule cleanup of file after download
        background_tasks.add_task(cleanup_file, filepath, delay=3600)
        
        return FileResponse(
            filepath,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=filename
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/schedule")
async def schedule_report(
    schedule_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Schedule automated report generation"""
    try:
        result = scheduled_service.schedule_report(current_user.id, schedule_request)
        
        if result["success"]:
            # Save schedule to database (you would create a ReportSchedule model)
            # For now, just log it in analytics
            analytics_record = Analytics(
                user_id=current_user.id,
                metric_type="report_scheduled",
                metric_value=1,
                meta_data=result["schedule"]
            )
            db.add(analytics_record)
            db.commit()
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/templates")
async def get_report_templates(
    current_user: User = Depends(get_current_user)
):
    """Get available report templates"""
    try:
        templates = [
            {
                "id": "performance_summary",
                "name": "Performance Summary",
                "description": "Overview of campaign performance with key metrics",
                "type": "summary",
                "estimated_time": "2-3 minutes",
                "includes": ["KPIs", "Charts", "Basic insights"]
            },
            {
                "id": "comprehensive_analysis",
                "name": "Comprehensive Analysis",
                "description": "Detailed analysis with advanced charts and insights",
                "type": "comprehensive",
                "estimated_time": "5-7 minutes",
                "includes": ["All metrics", "Advanced charts", "Detailed insights", "Recommendations"]
            },
            {
                "id": "roi_analysis",
                "name": "ROI Analysis",
                "description": "Focus on return on investment and profitability",
                "type": "roi",
                "estimated_time": "3-4 minutes",
                "includes": ["ROI metrics", "Profitability analysis", "Budget optimization"]
            },
            {
                "id": "audience_insights",
                "name": "Audience Insights",
                "description": "Deep dive into audience behavior and demographics",
                "type": "audience",
                "estimated_time": "4-5 minutes",
                "includes": ["Demographics", "Behavior analysis", "Segmentation insights"]
            },
            {
                "id": "competitive_analysis",
                "name": "Competitive Analysis",
                "description": "Compare performance against industry benchmarks",
                "type": "competitive",
                "estimated_time": "6-8 minutes",
                "includes": ["Industry benchmarks", "Competitive positioning", "Market insights"]
            }
        ]
        
        return {
            "success": True,
            "templates": templates
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_report_history(
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's report generation history"""
    try:
        # Get report analytics records
        reports = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type == "report_generated"
        ).order_by(Analytics.date_recorded.desc()).limit(limit).all()
        
        report_history = []
        for report in reports:
            metadata = report.meta_data or {}
            report_history.append({
                "id": report.id,
                "report_id": metadata.get("report_id"),
                "report_type": metadata.get("report_type"),
                "campaigns_included": metadata.get("campaigns_included", 0),
                "generated_at": report.date_recorded.isoformat(),
                "status": "completed"
            })
        
        return {
            "success": True,
            "reports": report_history,
            "total": len(report_history)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/summary")
async def get_reporting_analytics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get reporting usage analytics"""
    try:
        # Get report generation stats
        total_reports = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type == "report_generated"
        ).count()
        
        # Get reports from last 30 days
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_reports = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type == "report_generated",
            Analytics.date_recorded >= thirty_days_ago
        ).count()
        
        # Get scheduled reports
        scheduled_reports = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type == "report_scheduled"
        ).count()
        
        return {
            "success": True,
            "analytics": {
                "total_reports_generated": total_reports,
                "reports_last_30_days": recent_reports,
                "scheduled_reports": scheduled_reports,
                "most_used_format": "PDF",  # Mock data
                "avg_generation_time": "3.2 minutes",  # Mock data
                "favorite_template": "Performance Summary"  # Mock data
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/custom")
async def create_custom_report(
    custom_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a custom report with user-defined parameters"""
    try:
        # Extract custom parameters
        metrics = custom_request.get("metrics", [])
        date_range = custom_request.get("date_range", {})
        filters = custom_request.get("filters", {})
        chart_types = custom_request.get("chart_types", [])
        
        # Get filtered campaign data based on custom parameters
        query = db.query(Campaign).filter(Campaign.user_id == current_user.id)
        
        # Apply filters
        if filters.get("status"):
            query = query.filter(Campaign.status == filters["status"])
        
        if filters.get("campaign_ids"):
            query = query.filter(Campaign.id.in_(filters["campaign_ids"]))
        
        campaigns = query.all()
        
        # Convert to report format
        campaign_data = []
        for campaign in campaigns:
            campaign_data.append({
                "id": campaign.id,
                "name": campaign.name,
                "status": campaign.status,
                "impressions": campaign.impressions or 0,
                "clicks": campaign.clicks or 0,
                "conversions": campaign.conversions or 0,
                "spend": float(campaign.spent or 0)
            })
        
        # Generate custom report
        report = reporting_service.generate_performance_report(
            {"campaigns": campaign_data}, 
            "custom"
        )
        
        # Add custom parameters to report
        report["custom_parameters"] = {
            "metrics": metrics,
            "date_range": date_range,
            "filters": filters,
            "chart_types": chart_types
        }
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def cleanup_file(filepath: str, delay: int = 3600):
    """Background task to cleanup exported files"""
    import asyncio
    await asyncio.sleep(delay)
    try:
        if os.path.exists(filepath):
            os.remove(filepath)
    except:
        pass  # Ignore cleanup errors
