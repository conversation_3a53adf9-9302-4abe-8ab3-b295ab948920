from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from datetime import datetime

from database.db_session import get_db
from database.models import User, Analytics
from routers.users import get_current_user
from services.i18n_service import InternationalizationService

router = APIRouter()

# Initialize i18n service
i18n_service = InternationalizationService()

@router.get("/languages")
async def get_supported_languages():
    """Get list of supported languages"""
    try:
        languages = i18n_service.get_supported_languages()
        
        return {
            "success": True,
            "languages": languages,
            "default_language": i18n_service.default_language
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/translations/{language}")
async def get_translations(
    language: str,
    keys: Optional[str] = None
):
    """Get translations for a specific language"""
    try:
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        translations = i18n_service.translations.get(language, {})
        
        # Filter by specific keys if provided
        if keys:
            key_list = keys.split(',')
            filtered_translations = {
                key: translations.get(key, key) for key in key_list
            }
            translations = filtered_translations
        
        return {
            "success": True,
            "language": language,
            "translations": translations,
            "language_info": i18n_service.get_language_info(language)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/translate")
async def translate_text(
    translation_request: Dict[str, Any]
):
    """Translate specific text or keys"""
    try:
        language = translation_request.get("language", "en")
        keys = translation_request.get("keys", [])
        variables = translation_request.get("variables", {})
        
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        translations = {}
        for key in keys:
            translations[key] = i18n_service.get_translation(
                key, language, **variables
            )
        
        return {
            "success": True,
            "language": language,
            "translations": translations
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/format/currency")
async def format_currency(
    format_request: Dict[str, Any]
):
    """Format currency based on locale"""
    try:
        amount = format_request.get("amount")
        language = format_request.get("language", "en")
        currency = format_request.get("currency")
        
        if amount is None:
            raise HTTPException(status_code=400, detail="Amount is required")
        
        formatted_amount = i18n_service.format_currency(amount, language, currency)
        
        return {
            "success": True,
            "formatted_amount": formatted_amount,
            "language": language,
            "currency": currency or i18n_service.get_language_info(language)["currency"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/format/date")
async def format_date(
    format_request: Dict[str, Any]
):
    """Format date based on locale"""
    try:
        date_str = format_request.get("date")
        language = format_request.get("language", "en")
        format_type = format_request.get("format", "medium")
        
        if not date_str:
            raise HTTPException(status_code=400, detail="Date is required")
        
        # Parse date string
        try:
            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except:
            raise HTTPException(status_code=400, detail="Invalid date format")
        
        formatted_date = i18n_service.format_date(date_obj, language, format_type)
        
        return {
            "success": True,
            "formatted_date": formatted_date,
            "language": language,
            "format_type": format_type
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/format/datetime")
async def format_datetime(
    format_request: Dict[str, Any]
):
    """Format datetime based on locale"""
    try:
        datetime_str = format_request.get("datetime")
        language = format_request.get("language", "en")
        
        if not datetime_str:
            raise HTTPException(status_code=400, detail="Datetime is required")
        
        # Parse datetime string
        try:
            datetime_obj = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        except:
            raise HTTPException(status_code=400, detail="Invalid datetime format")
        
        formatted_datetime = i18n_service.format_datetime(datetime_obj, language)
        
        return {
            "success": True,
            "formatted_datetime": formatted_datetime,
            "language": language
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/content-templates/{language}")
async def get_localized_content_templates(
    language: str
):
    """Get localized content templates for AI generation"""
    try:
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        templates = i18n_service.get_localized_content_templates(language)
        
        return {
            "success": True,
            "language": language,
            "templates": templates,
            "is_rtl": i18n_service.is_rtl(language)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/user/language")
async def update_user_language(
    language_request: Dict[str, str],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user's preferred language"""
    try:
        language = language_request.get("language")
        
        if not language:
            raise HTTPException(status_code=400, detail="Language is required")
        
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        # Update user's language preference
        # Note: You would need to add a language field to the User model
        # For now, we'll store it in analytics as a workaround
        
        analytics_record = Analytics(
            user_id=current_user.id,
            metric_type="language_preference",
            metric_value=1,
            meta_data={"language": language}
        )
        db.add(analytics_record)
        db.commit()
        
        return {
            "success": True,
            "message": "Language preference updated successfully",
            "language": language,
            "language_info": i18n_service.get_language_info(language)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user/language")
async def get_user_language(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's preferred language"""
    try:
        # Get user's language preference from analytics
        language_record = db.query(Analytics).filter(
            Analytics.user_id == current_user.id,
            Analytics.metric_type == "language_preference"
        ).order_by(Analytics.date_recorded.desc()).first()
        
        if language_record and language_record.meta_data:
            language = language_record.meta_data.get("language", "en")
        else:
            language = "en"  # Default language
        
        return {
            "success": True,
            "language": language,
            "language_info": i18n_service.get_language_info(language)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/locale-info/{language}")
async def get_locale_info(
    language: str
):
    """Get detailed locale information"""
    try:
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        language_info = i18n_service.get_language_info(language)
        
        # Add additional locale information
        locale_info = {
            **language_info,
            "is_rtl": i18n_service.is_rtl(language),
            "sample_formats": {
                "currency": i18n_service.format_currency(1234.56, language),
                "date": i18n_service.format_date(datetime.now(), language),
                "datetime": i18n_service.format_datetime(datetime.now(), language)
            }
        }
        
        return {
            "success": True,
            "language": language,
            "locale_info": locale_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate-translation")
async def validate_translation(
    validation_request: Dict[str, Any]
):
    """Validate if translation keys exist"""
    try:
        language = validation_request.get("language", "en")
        keys = validation_request.get("keys", [])
        
        if language not in i18n_service.supported_languages:
            raise HTTPException(status_code=400, detail="Unsupported language")
        
        translations = i18n_service.translations.get(language, {})
        
        validation_results = {}
        for key in keys:
            validation_results[key] = {
                "exists": key in translations,
                "translation": translations.get(key, key)
            }
        
        return {
            "success": True,
            "language": language,
            "validation_results": validation_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_i18n_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get internationalization usage statistics"""
    try:
        # Get language usage statistics
        language_stats = db.query(Analytics).filter(
            Analytics.metric_type == "language_preference"
        ).all()
        
        # Count language preferences
        language_counts = {}
        for stat in language_stats:
            if stat.meta_data and "language" in stat.meta_data:
                lang = stat.meta_data["language"]
                language_counts[lang] = language_counts.get(lang, 0) + 1
        
        # Calculate percentages
        total_users = sum(language_counts.values()) or 1
        language_percentages = {
            lang: round(count / total_users * 100, 1)
            for lang, count in language_counts.items()
        }
        
        return {
            "success": True,
            "stats": {
                "supported_languages": len(i18n_service.supported_languages),
                "total_translations": sum(len(trans) for trans in i18n_service.translations.values()),
                "language_usage": language_counts,
                "language_percentages": language_percentages,
                "most_popular_language": max(language_counts, key=language_counts.get) if language_counts else "en"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
