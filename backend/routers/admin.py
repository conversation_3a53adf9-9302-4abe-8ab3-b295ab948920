from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import random

from database.db_session import get_db
from database.models import User, Subscription, Campaign, Analytics, SystemLog
from routers.users import get_current_user

router = APIRouter()

def require_admin(current_user: User = Depends(get_current_user)):
    """Require admin privileges"""
    if current_user.user_type != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

@router.get("/dashboard")
async def get_admin_dashboard(
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Get admin dashboard statistics"""
    try:
        # User statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        new_users_this_month = db.query(User).filter(
            User.created_at >= datetime.now() - timedelta(days=30)
        ).count()
        
        # Subscription statistics
        total_subscriptions = db.query(Subscription).count()
        active_subscriptions = db.query(Subscription).filter(
            Subscription.status == "active"
        ).count()
        
        # Campaign statistics
        total_campaigns = db.query(Campaign).count()
        active_campaigns = db.query(Campaign).filter(
            Campaign.status == "active"
        ).count()
        
        # Revenue calculation (mock)
        monthly_revenue = active_subscriptions * 79  # Average subscription price
        
        # User type breakdown
        user_types = db.query(
            User.user_type,
            func.count(User.id).label('count')
        ).group_by(User.user_type).all()
        
        # Subscription plan breakdown
        subscription_plans = db.query(
            Subscription.plan,
            func.count(Subscription.id).label('count')
        ).group_by(Subscription.plan).all()
        
        return {
            "overview": {
                "total_users": total_users,
                "active_users": active_users,
                "new_users_this_month": new_users_this_month,
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "total_campaigns": total_campaigns,
                "active_campaigns": active_campaigns,
                "monthly_revenue": monthly_revenue
            },
            "user_breakdown": {
                user_type.user_type: user_type.count for user_type in user_types
            },
            "subscription_breakdown": {
                plan.plan: plan.count for plan in subscription_plans
            },
            "growth_metrics": {
                "user_growth": "+12.5%",
                "revenue_growth": "+8.3%",
                "subscription_growth": "+15.2%"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/users")
async def get_all_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    user_type: Optional[str] = None,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Get all users with filtering"""
    try:
        query = db.query(User)
        
        if search:
            query = query.filter(
                (User.name.contains(search)) | (User.email.contains(search))
            )
        
        if user_type:
            query = query.filter(User.user_type == user_type)
        
        users = query.offset(skip).limit(limit).all()
        total = query.count()
        
        return {
            "users": [
                {
                    "id": user.id,
                    "name": user.name,
                    "email": user.email,
                    "user_type": user.user_type,
                    "is_active": user.is_active,
                    "created_at": user.created_at.isoformat(),
                    "last_login": user.last_login.isoformat() if user.last_login else None,
                    "subscription": {
                        "plan": user.subscription.plan if user.subscription else None,
                        "status": user.subscription.status if user.subscription else None
                    }
                }
                for user in users
            ],
            "total": total,
            "page": skip // limit + 1,
            "per_page": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/users/{user_id}")
async def update_user_admin(
    user_id: int,
    update_data: Dict[str, Any],
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Update user as admin"""
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Update allowed fields
        allowed_fields = ["name", "email", "user_type", "is_active"]
        for field in allowed_fields:
            if field in update_data:
                setattr(user, field, update_data[field])
        
        db.commit()
        db.refresh(user)
        
        # Log admin action
        log_entry = SystemLog(
            level="info",
            service="admin",
            message=f"User {user_id} updated by admin {admin_user.id}",
            metadata={"updated_fields": list(update_data.keys())}
        )
        db.add(log_entry)
        db.commit()
        
        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "user_type": user.user_type,
            "is_active": user.is_active
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subscriptions")
async def get_all_subscriptions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Get all subscriptions"""
    try:
        query = db.query(Subscription).join(User)
        
        if status:
            query = query.filter(Subscription.status == status)
        
        subscriptions = query.offset(skip).limit(limit).all()
        total = query.count()
        
        return {
            "subscriptions": [
                {
                    "id": sub.id,
                    "user": {
                        "id": sub.user.id,
                        "name": sub.user.name,
                        "email": sub.user.email
                    },
                    "plan": sub.plan,
                    "status": sub.status,
                    "current_period_start": sub.current_period_start.isoformat(),
                    "current_period_end": sub.current_period_end.isoformat(),
                    "created_at": sub.created_at.isoformat()
                }
                for sub in subscriptions
            ],
            "total": total,
            "page": skip // limit + 1,
            "per_page": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system-logs")
async def get_system_logs(
    skip: int = 0,
    limit: int = 100,
    level: Optional[str] = None,
    service: Optional[str] = None,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Get system logs"""
    try:
        query = db.query(SystemLog)
        
        if level:
            query = query.filter(SystemLog.level == level)
        
        if service:
            query = query.filter(SystemLog.service == service)
        
        logs = query.order_by(SystemLog.timestamp.desc()).offset(skip).limit(limit).all()
        total = query.count()
        
        return {
            "logs": [
                {
                    "id": log.id,
                    "level": log.level,
                    "service": log.service,
                    "message": log.message,
                    "metadata": log.metadata,
                    "timestamp": log.timestamp.isoformat()
                }
                for log in logs
            ],
            "total": total,
            "page": skip // limit + 1,
            "per_page": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/summary")
async def get_analytics_summary(
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Get platform analytics summary"""
    try:
        # Analytics by metric type
        analytics_by_type = db.query(
            Analytics.metric_type,
            func.count(Analytics.id).label('count'),
            func.avg(Analytics.metric_value).label('avg_value')
        ).group_by(Analytics.metric_type).all()
        
        # Recent analytics
        recent_analytics = db.query(Analytics).order_by(
            Analytics.date_recorded.desc()
        ).limit(10).all()
        
        # Mock system metrics
        system_metrics = {
            "cpu_usage": random.uniform(40, 80),
            "memory_usage": random.uniform(50, 85),
            "disk_usage": random.uniform(30, 70),
            "api_response_time": random.uniform(150, 300),
            "active_sessions": random.randint(50, 200),
            "requests_per_minute": random.randint(100, 500)
        }
        
        return {
            "analytics_summary": {
                metric.metric_type: {
                    "count": metric.count,
                    "average_value": round(metric.avg_value, 3) if metric.avg_value else 0
                }
                for metric in analytics_by_type
            },
            "recent_activity": [
                {
                    "user_id": analytics.user_id,
                    "metric_type": analytics.metric_type,
                    "value": analytics.metric_value,
                    "date": analytics.date_recorded.isoformat()
                }
                for analytics in recent_analytics
            ],
            "system_metrics": system_metrics
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/system-logs")
async def create_system_log(
    log_data: Dict[str, Any],
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Create system log entry"""
    try:
        log_entry = SystemLog(
            level=log_data.get("level", "info"),
            service=log_data.get("service", "admin"),
            message=log_data.get("message", ""),
            metadata=log_data.get("metadata", {})
        )
        
        db.add(log_entry)
        db.commit()
        db.refresh(log_entry)
        
        return {
            "id": log_entry.id,
            "level": log_entry.level,
            "service": log_entry.service,
            "message": log_entry.message,
            "timestamp": log_entry.timestamp.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
