from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, List, Any
from datetime import datetime

from database.db_session import get_db
from database.models import User, ContentGeneration
from routers.users import get_current_user
from services.content_generator import ContentGeneratorService

router = APIRouter()

@router.post("/generate")
async def generate_content(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate marketing content using AI"""
    try:
        content_type = request.get("content_type", "email")
        prompt = request.get("prompt", "")
        target_audience = request.get("target_audience", "general")
        tone = request.get("tone", "professional")
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")
        
        # Use content generation service
        content_service = ContentGeneratorService()
        generated_content = content_service.generate_content(
            content_type=content_type,
            prompt=prompt,
            target_audience=target_audience,
            tone=tone
        )
        
        # Save to database
        content_record = ContentGeneration(
            user_id=current_user.id,
            content_type=content_type,
            prompt=prompt,
            generated_content=generated_content["content"]
        )
        db.add(content_record)
        db.commit()
        db.refresh(content_record)
        
        return {
            "id": content_record.id,
            "content": generated_content["content"],
            "content_type": content_type,
            "suggestions": generated_content.get("suggestions", []),
            "created_at": content_record.created_at.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_content_history(
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's content generation history"""
    content_history = db.query(ContentGeneration).filter(
        ContentGeneration.user_id == current_user.id
    ).order_by(ContentGeneration.created_at.desc()).limit(limit).all()
    
    return {
        "history": [
            {
                "id": content.id,
                "content_type": content.content_type,
                "prompt": content.prompt,
                "content": content.generated_content[:200] + "..." if len(content.generated_content) > 200 else content.generated_content,
                "rating": content.rating,
                "created_at": content.created_at.isoformat()
            }
            for content in content_history
        ]
    }

@router.put("/{content_id}/rate")
async def rate_content(
    content_id: int,
    rating_data: Dict[str, int],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Rate generated content"""
    rating = rating_data.get("rating")
    if rating is None or rating < 1 or rating > 5:
        raise HTTPException(status_code=400, detail="Rating must be between 1 and 5")
    
    content = db.query(ContentGeneration).filter(
        ContentGeneration.id == content_id,
        ContentGeneration.user_id == current_user.id
    ).first()
    
    if not content:
        raise HTTPException(status_code=404, detail="Content not found")
    
    content.rating = rating
    db.commit()
    
    return {"message": "Content rated successfully", "rating": rating}

@router.post("/templates")
async def get_content_templates(
    request: Dict[str, str],
    current_user: User = Depends(get_current_user)
):
    """Get content templates for different types"""
    content_type = request.get("content_type", "email")
    
    templates = {
        "email": [
            {
                "name": "Welcome Email",
                "template": "Welcome to {company_name}! We're excited to have you join our community...",
                "variables": ["company_name", "user_name", "product_name"]
            },
            {
                "name": "Promotional Email",
                "template": "Don't miss out on our {discount}% off sale on {product_category}...",
                "variables": ["discount", "product_category", "deadline"]
            },
            {
                "name": "Newsletter",
                "template": "Here's what's new this {period} at {company_name}...",
                "variables": ["period", "company_name", "highlights"]
            }
        ],
        "social": [
            {
                "name": "Product Launch",
                "template": "🚀 Introducing {product_name}! {key_benefit} #NewProduct #Innovation",
                "variables": ["product_name", "key_benefit", "hashtags"]
            },
            {
                "name": "Engagement Post",
                "template": "What's your favorite {topic}? Tell us in the comments! 👇",
                "variables": ["topic", "call_to_action"]
            }
        ],
        "ad_copy": [
            {
                "name": "Google Ad",
                "template": "{headline} | {description} | {call_to_action}",
                "variables": ["headline", "description", "call_to_action"]
            },
            {
                "name": "Facebook Ad",
                "template": "{hook} {benefit} {social_proof} {cta}",
                "variables": ["hook", "benefit", "social_proof", "cta"]
            }
        ]
    }
    
    return {
        "content_type": content_type,
        "templates": templates.get(content_type, [])
    }

@router.post("/optimize")
async def optimize_content(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Optimize existing content for better performance"""
    content = request.get("content", "")
    optimization_type = request.get("optimization_type", "engagement")
    
    if not content:
        raise HTTPException(status_code=400, detail="Content is required")
    
    # Mock optimization suggestions
    optimizations = {
        "engagement": [
            "Add more emotional words to increase engagement",
            "Include a clear call-to-action",
            "Use shorter sentences for better readability",
            "Add relevant emojis to make it more appealing"
        ],
        "conversion": [
            "Create urgency with time-limited offers",
            "Include social proof or testimonials",
            "Simplify the call-to-action",
            "Highlight key benefits more prominently"
        ],
        "seo": [
            "Include target keywords naturally",
            "Optimize meta description length",
            "Add relevant internal links",
            "Improve content structure with headers"
        ]
    }
    
    return {
        "original_content": content,
        "optimization_type": optimization_type,
        "suggestions": optimizations.get(optimization_type, []),
        "optimized_content": f"[OPTIMIZED] {content}",  # Mock optimization
        "improvement_score": 85  # Mock score
    }
