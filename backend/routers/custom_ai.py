"""
Custom AI Router - Endpoints for building and using custom AI models
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from services.custom_ai_service import CustomAIService
from auth import get_current_user

router = APIRouter(prefix="/custom-ai", tags=["Custom AI"])

# Initialize custom AI service
custom_ai_service = CustomAIService()

# Pydantic models
class TrainingDataItem(BaseModel):
    text: str
    sentiment: Optional[str] = None
    category: Optional[str] = None

class CreateModelRequest(BaseModel):
    model_name: str
    model_type: str  # "sentiment" or "content_classification"
    training_data: List[TrainingDataItem]

class PredictionRequest(BaseModel):
    model_name: str
    text: str

class OllamaRequest(BaseModel):
    prompt: str
    model_name: str = "llama3.2"

@router.get("/status")
async def get_custom_ai_status():
    """Get status of custom AI models"""
    return custom_ai_service.get_models_status()

@router.post("/create-model")
async def create_custom_model(
    request: CreateModelRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a custom AI model"""
    try:
        # Convert training data to dict format
        training_data = []
        for item in request.training_data:
            data_item = {"text": item.text}
            if request.model_type == "sentiment" and item.sentiment:
                data_item["sentiment"] = item.sentiment
            elif request.model_type == "content_classification" and item.category:
                data_item["category"] = item.category
            training_data.append(data_item)
        
        if len(training_data) < 10:
            raise HTTPException(
                status_code=400,
                detail="يجب أن تحتوي بيانات التدريب على 10 عينات على الأقل"
            )
        
        # Create model based on type
        if request.model_type == "sentiment":
            result = custom_ai_service.create_sentiment_model(
                training_data, request.model_name
            )
        elif request.model_type == "content_classification":
            result = custom_ai_service.create_content_classifier(
                training_data, request.model_name
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="نوع النموذج غير مدعوم. استخدم 'sentiment' أو 'content_classification'"
            )
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "تم إنشاء النموذج المخصص بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء النموذج: {str(e)}"
        )

@router.post("/predict")
async def use_custom_model(
    request: PredictionRequest,
    current_user: dict = Depends(get_current_user)
):
    """Use custom model for prediction"""
    try:
        result = custom_ai_service.use_custom_model(
            request.model_name, request.text
        )
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "تم التنبؤ بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في استخدام النموذج: {str(e)}"
        )

@router.get("/models")
async def get_available_models(current_user: dict = Depends(get_current_user)):
    """Get list of available custom models"""
    return {
        "success": True,
        "data": custom_ai_service.available_models,
        "count": len(custom_ai_service.available_models)
    }

@router.delete("/models/{model_name}")
async def delete_custom_model(
    model_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete a custom model"""
    try:
        result = custom_ai_service.delete_model(model_name)
        
        if result["success"]:
            return {
                "success": True,
                "message": result["message"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في حذف النموذج: {str(e)}"
        )

@router.get("/sample-data/{data_type}")
async def get_sample_training_data(
    data_type: str,
    current_user: dict = Depends(get_current_user)
):
    """Get sample training data for testing"""
    if data_type not in ["sentiment", "content_classification"]:
        raise HTTPException(
            status_code=400,
            detail="نوع البيانات غير مدعوم. استخدم 'sentiment' أو 'content_classification'"
        )
    
    sample_data = custom_ai_service.generate_sample_training_data(data_type)
    
    return {
        "success": True,
        "data": sample_data,
        "count": len(sample_data),
        "message": f"بيانات تجريبية لنوع {data_type}"
    }

@router.get("/ollama/status")
async def get_ollama_status():
    """Check Ollama local AI status"""
    return custom_ai_service.setup_ollama_model()

@router.post("/ollama/generate")
async def generate_with_ollama(
    request: OllamaRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate content using local Ollama model"""
    try:
        result = custom_ai_service.use_ollama_model(
            request.prompt, request.model_name
        )
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "تم إنشاء المحتوى بنجاح باستخدام النموذج المحلي"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في استخدام Ollama: {str(e)}"
        )

@router.post("/train-sentiment-demo")
async def train_sentiment_demo(current_user: dict = Depends(get_current_user)):
    """Train a demo sentiment analysis model"""
    try:
        # Get sample data
        sample_data = custom_ai_service.generate_sample_training_data("sentiment")
        
        # Add more diverse data
        extended_data = sample_data + [
            {"text": "منتج ممتاز وجودة عالية", "sentiment": "positive"},
            {"text": "سعر مناسب وخدمة رائعة", "sentiment": "positive"},
            {"text": "أفضل شراء قمت به", "sentiment": "positive"},
            {"text": "مضيعة للوقت والمال", "sentiment": "negative"},
            {"text": "لا يستحق السعر المدفوع", "sentiment": "negative"},
            {"text": "تجربة مخيبة للآمال", "sentiment": "negative"},
            {"text": "لا بأس به للسعر", "sentiment": "neutral"},
            {"text": "متوسط الجودة", "sentiment": "neutral"},
            {"text": "يحتاج لتحسينات", "sentiment": "neutral"},
            {"text": "رائع جداً ومفيد", "sentiment": "positive"},
            {"text": "سيء للغاية", "sentiment": "negative"}
        ]
        
        # Create model
        result = custom_ai_service.create_sentiment_model(
            extended_data, "demo_sentiment_model"
        )
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "تم إنشاء نموذج تحليل المشاعر التجريبي بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء النموذج التجريبي: {str(e)}"
        )

@router.post("/train-content-demo")
async def train_content_demo(current_user: dict = Depends(get_current_user)):
    """Train a demo content classification model"""
    try:
        # Extended sample data
        extended_data = [
            {"text": "عرض خاص محدود الوقت", "category": "promotional"},
            {"text": "خصم 70% على جميع المنتجات", "category": "promotional"},
            {"text": "اشتري الآن واحصل على هدية", "category": "promotional"},
            {"text": "كيفية استخدام المنتج بفعالية", "category": "educational"},
            {"text": "نصائح للحصول على أفضل النتائج", "category": "educational"},
            {"text": "دليل المبتدئين الشامل", "category": "educational"},
            {"text": "أخبار الشركة الجديدة", "category": "news"},
            {"text": "تحديثات المنتج الأخيرة", "category": "news"},
            {"text": "إعلان عن شراكة جديدة", "category": "news"},
            {"text": "شكراً لثقتكم بنا", "category": "appreciation"},
            {"text": "نقدر ملاحظاتكم القيمة", "category": "appreciation"},
            {"text": "عملاؤنا هم أولويتنا", "category": "appreciation"}
        ]
        
        # Create model
        result = custom_ai_service.create_content_classifier(
            extended_data, "demo_content_classifier"
        )
        
        if result["success"]:
            return {
                "success": True,
                "data": result,
                "message": "تم إنشاء نموذج تصنيف المحتوى التجريبي بنجاح"
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"فشل في إنشاء النموذج التجريبي: {str(e)}"
        )
