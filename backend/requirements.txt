# Flask and web framework
Flask==2.3.3
Flask-CORS==4.0.0
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.3
Werkzeug==2.3.7
gunicorn==21.2.0

# Database
sqlalchemy
psycopg2-binary==2.9.7

# Authentication and security
bcrypt

# Environment and configuration
python-dotenv
pydantic

# HTTP requests
requests

# Payment Gateways
stripe
paypalrestsdk

# Email Services
sendgrid
emails

# AI & Machine Learning (Local Models Only)
tensorflow>=2.12.0
scikit-learn>=1.3.0
xgboost>=1.7.0
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
networkx>=3.1
scipy>=1.10.0

# Optional: Prophet for time series forecasting
# prophet>=1.1.4

# Text Processing
nltk>=3.8
regex>=2023.6.3

# Model Persistence
joblib>=1.3.0
textblob
vaderSentiment
scikit-learn
numpy
pandas

# Security & Authentication
pyotp
qrcode
cryptography
slowapi

# Advanced Features
celery==5.3.4
redis==5.0.1
reportlab==4.0.7
openpyxl==3.1.2
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Internationalization
babel==2.13.1
flask-babel==4.0.0
