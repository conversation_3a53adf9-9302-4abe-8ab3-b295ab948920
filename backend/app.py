from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, jwt_required, create_access_token, get_jwt_identity
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LinearRegression
import joblib
import logging

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///marketmind.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Initialize extensions
jwt = JWTManager(app)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import models and routes
from models import db, User, Company, Campaign, Customer, SentimentData, JourneyStage

# Initialize database with app
db.init_app(app)
from ai_models import (
    CustomerSegmentationModel,
    ChurnPredictionModel,
    ContentGenerationModel,
    CampaignOptimizationModel,
    SalesPredictionModel,
    CustomerJourneyModel,
    SentimentAnalysisModel
)

# Initialize AI models
ai_models = {
    'customer_segmentation': CustomerSegmentationModel(),
    'churn_prediction': ChurnPredictionModel(),
    'content_generation': ContentGenerationModel(),
    'campaign_optimization': CampaignOptimizationModel(),
    'sales_prediction': SalesPredictionModel(),
    'customer_journey': CustomerJourneyModel(),
    'sentiment_analysis': SentimentAnalysisModel()
}

# Authentication routes
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            access_token = create_access_token(identity=user.id)
            return jsonify({
                'success': True,
                'access_token': access_token,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'name': user.name,
                    'user_type': user.user_type,
                    'company_id': user.company_id
                }
            }), 200
        else:
            return jsonify({'success': False, 'message': 'Invalid credentials'}), 401
            
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({'success': False, 'message': 'Login failed'}), 500

# Customer Segmentation API
@app.route('/api/ai/customer-segmentation', methods=['GET'])
@jwt_required()
def get_customer_segmentation():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get query parameters
        time_range = request.args.get('time_range', '30days')
        
        # Get customer data for the company
        customers = Customer.query.filter_by(company_id=user.company_id).all()
        
        if not customers:
            return jsonify({
                'success': False,
                'message': 'No customer data available'
            }), 404
        
        # Prepare data for segmentation
        customer_data = []
        for customer in customers:
            customer_data.append({
                'customer_id': customer.id,
                'total_spent': customer.total_spent,
                'order_frequency': customer.order_frequency,
                'last_order_days': customer.last_order_days,
                'avg_order_value': customer.avg_order_value
            })
        
        # Run segmentation model
        segments = ai_models['customer_segmentation'].segment_customers(customer_data)
        
        return jsonify({
            'success': True,
            'data': {
                'segments': segments,
                'total_customers': len(customers),
                'time_range': time_range,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Customer segmentation error: {str(e)}")
        return jsonify({'success': False, 'message': 'Segmentation failed'}), 500

# Churn Prediction API
@app.route('/api/ai/churn-prediction', methods=['GET'])
@jwt_required()
def get_churn_prediction():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get query parameters
        risk_level = request.args.get('risk_level', 'all')
        
        # Get customer data
        customers = Customer.query.filter_by(company_id=user.company_id).all()
        
        if not customers:
            return jsonify({
                'success': False,
                'message': 'No customer data available'
            }), 404
        
        # Prepare data for churn prediction
        customer_data = []
        for customer in customers:
            customer_data.append({
                'customer_id': customer.id,
                'name': customer.name,
                'email': customer.email,
                'total_spent': customer.total_spent,
                'order_frequency': customer.order_frequency,
                'last_order_days': customer.last_order_days,
                'support_tickets': customer.support_tickets,
                'email_engagement': customer.email_engagement
            })
        
        # Run churn prediction model
        predictions = ai_models['churn_prediction'].predict_churn(customer_data)
        
        # Filter by risk level if specified
        if risk_level != 'all':
            predictions = [p for p in predictions if p['risk_level'] == risk_level]
        
        return jsonify({
            'success': True,
            'data': {
                'predictions': predictions,
                'total_at_risk': len([p for p in predictions if p['churn_probability'] > 50]),
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Churn prediction error: {str(e)}")
        return jsonify({'success': False, 'message': 'Churn prediction failed'}), 500

# Content Generation API
@app.route('/api/ai/content-generation', methods=['POST'])
@jwt_required()
def generate_content():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['type', 'audience', 'tone', 'keywords', 'objective']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }), 400
        
        # Generate content using AI model
        content_request = {
            'type': data['type'],
            'audience': data['audience'],
            'tone': data['tone'],
            'keywords': data['keywords'],
            'objective': data['objective'],
            'length': data.get('length', 'medium'),
            'language': data.get('language', 'ar')
        }
        
        generated_content = ai_models['content_generation'].generate_content(content_request)
        
        return jsonify({
            'success': True,
            'data': {
                'content': generated_content,
                'request': content_request,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Content generation error: {str(e)}")
        return jsonify({'success': False, 'message': 'Content generation failed'}), 500

# Campaign Optimization API
@app.route('/api/ai/campaign-optimization', methods=['GET'])
@jwt_required()
def get_campaign_optimization():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get campaigns for the company
        campaigns = Campaign.query.filter_by(company_id=user.company_id).all()
        
        if not campaigns:
            return jsonify({
                'success': False,
                'message': 'No campaign data available'
            }), 404
        
        # Prepare campaign data
        campaign_data = []
        for campaign in campaigns:
            campaign_data.append({
                'id': campaign.id,
                'name': campaign.name,
                'budget': campaign.budget,
                'spent': campaign.spent,
                'impressions': campaign.impressions,
                'clicks': campaign.clicks,
                'conversions': campaign.conversions,
                'platform': campaign.platform,
                'status': campaign.status
            })
        
        # Run optimization analysis
        optimization_results = ai_models['campaign_optimization'].optimize_campaigns(campaign_data)
        
        return jsonify({
            'success': True,
            'data': {
                'campaigns': optimization_results['campaigns'],
                'recommendations': optimization_results['recommendations'],
                'performance_summary': optimization_results['performance_summary'],
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Campaign optimization error: {str(e)}")
        return jsonify({'success': False, 'message': 'Campaign optimization failed'}), 500

# Sales Prediction API
@app.route('/api/ai/sales-prediction', methods=['GET', 'POST'])
@jwt_required()
def sales_prediction():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if request.method == 'GET':
            # Get prediction parameters
            period = request.args.get('period', 'quarterly')
            confidence_level = int(request.args.get('confidence_level', 95))
            
            # Get historical sales data
            # This would typically come from your sales database
            historical_data = get_historical_sales_data(user.company_id)
            
            # Run sales prediction model
            predictions = ai_models['sales_prediction'].predict_sales(
                historical_data, period, confidence_level
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'predictions': predictions['predictions'],
                    'factors': predictions['influencing_factors'],
                    'scenarios': predictions['scenarios'],
                    'confidence_level': confidence_level,
                    'generated_at': datetime.utcnow().isoformat()
                }
            }), 200
            
        elif request.method == 'POST':
            # Update factors and recalculate predictions
            data = request.get_json()
            updated_factors = data.get('factors', {})
            
            # Recalculate with updated factors
            historical_data = get_historical_sales_data(user.company_id)
            predictions = ai_models['sales_prediction'].predict_with_factors(
                historical_data, updated_factors
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'predictions': predictions,
                    'updated_factors': updated_factors,
                    'generated_at': datetime.utcnow().isoformat()
                }
            }), 200
        
    except Exception as e:
        logger.error(f"Sales prediction error: {str(e)}")
        return jsonify({'success': False, 'message': 'Sales prediction failed'}), 500

# Customer Journey Analysis API
@app.route('/api/ai/customer-journey', methods=['GET'])
@jwt_required()
def get_customer_journey():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get journey stages data
        journey_stages = JourneyStage.query.filter_by(company_id=user.company_id).all()
        
        # Prepare journey data
        journey_data = []
        for stage in journey_stages:
            journey_data.append({
                'stage_id': stage.id,
                'name': stage.name,
                'customers': stage.customer_count,
                'conversion_rate': stage.conversion_rate,
                'avg_time': stage.avg_time_spent,
                'dropoff_rate': stage.dropoff_rate
            })
        
        # Run journey analysis
        analysis_results = ai_models['customer_journey'].analyze_journey(journey_data)
        
        return jsonify({
            'success': True,
            'data': {
                'journey_stages': analysis_results['stages'],
                'bottlenecks': analysis_results['bottlenecks'],
                'opportunities': analysis_results['opportunities'],
                'touchpoints': analysis_results['touchpoints'],
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Customer journey analysis error: {str(e)}")
        return jsonify({'success': False, 'message': 'Journey analysis failed'}), 500

# Sentiment Analysis API
@app.route('/api/ai/sentiment-analysis', methods=['GET'])
@jwt_required()
def get_sentiment_analysis():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get query parameters
        time_range = request.args.get('time_range', '30days')
        platform = request.args.get('platform', 'all')
        
        # Get sentiment data
        sentiment_data = SentimentData.query.filter_by(company_id=user.company_id)
        
        if platform != 'all':
            sentiment_data = sentiment_data.filter_by(platform=platform)
        
        sentiment_data = sentiment_data.all()
        
        if not sentiment_data:
            return jsonify({
                'success': False,
                'message': 'No sentiment data available'
            }), 404
        
        # Prepare data for analysis
        posts_data = []
        for data in sentiment_data:
            posts_data.append({
                'id': data.id,
                'platform': data.platform,
                'content': data.content,
                'author': data.author,
                'sentiment_score': data.sentiment_score,
                'confidence': data.confidence,
                'engagement': data.engagement_count,
                'created_at': data.created_at.isoformat()
            })
        
        # Run sentiment analysis
        analysis_results = ai_models['sentiment_analysis'].analyze_sentiment(posts_data)
        
        return jsonify({
            'success': True,
            'data': {
                'overall_sentiment': analysis_results['overall_sentiment'],
                'platform_breakdown': analysis_results['platform_breakdown'],
                'keyword_sentiment': analysis_results['keyword_sentiment'],
                'trending_topics': analysis_results['trending_topics'],
                'posts': analysis_results['posts'],
                'time_range': time_range,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {str(e)}")
        return jsonify({'success': False, 'message': 'Sentiment analysis failed'}), 500

# Helper function to get historical sales data
def get_historical_sales_data(company_id):
    # This would typically query your sales database
    # For now, return mock data
    return {
        'monthly_sales': [280000, 265000, 305000, 320000, 340000, 365000],
        'seasonal_factors': [1.0, 0.9, 1.1, 1.2, 1.0, 0.8],
        'marketing_spend': [50000, 45000, 60000, 55000, 65000, 70000],
        'economic_indicators': [0.95, 0.96, 0.94, 0.97, 0.98, 0.95]
    }

# Customer Behavior Prediction API
@app.route('/api/ai/customer-behavior', methods=['POST'])
@jwt_required()
def predict_customer_behavior():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        data = request.get_json()
        
        # Validate required fields
        if 'customer_data' not in data:
            return jsonify({'success': False, 'message': 'Customer data is required'}), 400
        
        # Run behavior prediction model
        predictions = ai_models['customer_behavior'].predict_behavior(data['customer_data'])
        
        return jsonify({
            'success': True,
            'data': {
                'predictions': predictions,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Customer behavior prediction error: {str(e)}")
        return jsonify({'success': False, 'message': 'Behavior prediction failed'}), 500

# Product Recommendation API
@app.route('/api/ai/product-recommendation', methods=['POST'])
@jwt_required()
def get_product_recommendations():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        data = request.get_json()
        
        # Validate required fields
        if 'user_id' not in data:
            return jsonify({'success': False, 'message': 'User ID is required'}), 400
        
        user_id_for_rec = data['user_id']
        n_recommendations = data.get('n_recommendations', 10)
        
        # Get product recommendations
        recommendations = ai_models['product_recommendation'].recommend_products(
            user_id_for_rec, n_recommendations
        )
        
        return jsonify({
            'success': True,
            'data': {
                'recommendations': recommendations,
                'user_id': user_id_for_rec,
                'n_recommendations': n_recommendations,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Product recommendation error: {str(e)}")
        return jsonify({'success': False, 'message': 'Product recommendation failed'}), 500

# Similar Products API
@app.route('/api/ai/similar-products', methods=['POST'])
@jwt_required()
def find_similar_products():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        data = request.get_json()
        
        # Validate required fields
        if 'product_id' not in data:
            return jsonify({'success': False, 'message': 'Product ID is required'}), 400
        
        product_id = data['product_id']
        n_similar = data.get('n_similar', 5)
        
        # Find similar products
        similar_products = ai_models['product_recommendation'].get_similar_products(
            product_id, n_similar
        )
        
        return jsonify({
            'success': True,
            'data': {
                'similar_products': similar_products,
                'target_product_id': product_id,
                'n_similar': n_similar,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Similar products error: {str(e)}")
        return jsonify({'success': False, 'message': 'Similar products search failed'}), 500

# Competitor Analysis API
@app.route('/api/ai/competitor-analysis', methods=['POST'])
@jwt_required()
def analyze_competitor():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        data = request.get_json()
        
        # Validate required fields
        if 'competitor_name' not in data or 'competitor_data' not in data:
            return jsonify({'success': False, 'message': 'Competitor name and data are required'}), 400
        
        competitor_name = data['competitor_name']
        competitor_data = data['competitor_data']
        
        # Analyze competitor
        analysis = ai_models['competitor_analysis'].analyze_competitor(
            competitor_name, competitor_data
        )
        
        return jsonify({
            'success': True,
            'data': {
                'analysis': analysis,
                'competitor_name': competitor_name,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Competitor analysis error: {str(e)}")
        return jsonify({'success': False, 'message': 'Competitor analysis failed'}), 500

# Competitive Insights API
@app.route('/api/ai/competitive-insights', methods=['GET'])
@jwt_required()
def get_competitive_insights():
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        # Get competitive insights
        insights = ai_models['competitor_analysis'].get_competitive_insights()
        
        return jsonify({
            'success': True,
            'data': {
                'insights': insights,
                'generated_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Competitive insights error: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to generate competitive insights'}), 500

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.1.0'
    }), 200

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'message': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'message': 'Internal server error'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    
    app.run(
        host=os.getenv('HOST', '0.0.0.0'),
        port=int(os.getenv('PORT', 5000)),
        debug=os.getenv('DEBUG', 'False').lower() == 'true'
    )
