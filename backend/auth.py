"""
Authentication and authorization utilities
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from datetime import datetime, timedelta
import os

# JWT Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

security = HTTPBearer()

def create_access_token(data: dict, expires_delta: timedelta = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user"""
    token = credentials.credentials

    # For testing, accept mock token
    if token == "mock-token":
        return {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "role": "business",
            "subscription_plan": "professional"
        }

    payload = verify_token(token)

    # Mock user data - in production, fetch from database
    user = {
        "id": payload.get("user_id", 1),
        "username": payload.get("sub", "demo_user"),
        "email": payload.get("email", "<EMAIL>"),
        "role": payload.get("role", "business"),
        "subscription_plan": payload.get("subscription_plan", "professional")
    }

    return user

def get_current_active_user(current_user: dict = Depends(get_current_user)):
    """Get current active user"""
    # Add any additional checks here (e.g., user is active, not banned, etc.)
    return current_user

# Mock login function for demo
def authenticate_user(username: str, password: str):
    """Authenticate user - mock implementation"""
    # Mock users for demo
    mock_users = {
        "<EMAIL>": {"password": "demo123", "role": "personal", "plan": "starter"},
        "<EMAIL>": {"password": "demo123", "role": "business", "plan": "professional"},
        "<EMAIL>": {"password": "demo123", "role": "admin", "plan": "enterprise"}
    }
    
    user = mock_users.get(username)
    if user and user["password"] == password:
        return {
            "username": username,
            "email": username,
            "role": user["role"],
            "subscription_plan": user["plan"],
            "user_id": hash(username) % 1000  # Simple ID generation
        }
    return None
