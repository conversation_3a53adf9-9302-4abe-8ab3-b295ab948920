"""
Churn Prediction Model for MarketMind
=====================================

This module implements customer churn prediction using Random Forest and XGBoost algorithms.
It analyzes customer behavior patterns to predict the likelihood of customer churn.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import xgboost as xgb
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class ChurnPredictionModel(BaseMarketMindModel):
    """
    Customer Churn Prediction Model using Random Forest and XGBoost.
    
    Features analyzed:
    - Customer engagement metrics (login frequency, session duration)
    - Purchase behavior (frequency, recency, monetary value)
    - Support interactions (tickets, complaints, satisfaction scores)
    - Product usage patterns
    - Demographic information
    """
    
    def __init__(self, algorithm: str = "random_forest"):
        """
        Initialize Churn Prediction Model.
        
        Args:
            algorithm: ML algorithm ('random_forest' or 'xgboost')
        """
        super().__init__("churn_prediction", "classification")
        self.algorithm = algorithm
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_importance = {}
        self.churn_risk_thresholds = {
            "low": 0.3,
            "medium": 0.6,
            "high": 0.8
        }
        
        logger.info(f"Initialized Churn Prediction Model with {algorithm}")
    
    def build_model(self, **kwargs) -> Any:
        """
        Build the churn prediction model.
        
        Returns:
            Configured ML model
        """
        if self.algorithm == "random_forest":
            return RandomForestClassifier(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', 10),
                min_samples_split=kwargs.get('min_samples_split', 5),
                min_samples_leaf=kwargs.get('min_samples_leaf', 2),
                random_state=42,
                class_weight='balanced'
            )
        elif self.algorithm == "xgboost":
            return xgb.XGBClassifier(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', 6),
                learning_rate=kwargs.get('learning_rate', 0.1),
                subsample=kwargs.get('subsample', 0.8),
                colsample_bytree=kwargs.get('colsample_bytree', 0.8),
                random_state=42,
                eval_metric='logloss'
            )
        else:
            raise ValueError(f"Unsupported algorithm: {self.algorithm}")
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess customer data for churn prediction.
        
        Args:
            data: Raw customer data
            
        Returns:
            Tuple of (features, targets)
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Define expected columns
        expected_columns = [
            'customer_id', 'tenure_months', 'monthly_charges', 'total_charges',
            'contract_type', 'payment_method', 'internet_service', 'phone_service',
            'multiple_lines', 'online_security', 'online_backup', 'device_protection',
            'tech_support', 'streaming_tv', 'streaming_movies', 'paperless_billing',
            'senior_citizen', 'partner', 'dependents', 'gender', 'churn'
        ]
        
        # Generate synthetic data if columns are missing
        if not all(col in df.columns for col in expected_columns):
            df = self._generate_synthetic_churn_data(len(df))
        
        # Feature engineering
        features_df = self._engineer_features(df)
        
        # Handle categorical variables
        categorical_columns = [
            'contract_type', 'payment_method', 'internet_service', 'gender'
        ]
        
        for col in categorical_columns:
            if col in features_df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features_df[col] = self.label_encoders[col].fit_transform(features_df[col].astype(str))
                else:
                    features_df[col] = self.label_encoders[col].transform(features_df[col].astype(str))
        
        # Store feature names
        self.feature_names = features_df.columns.tolist()
        
        # Scale features
        X = self.scaler.fit_transform(features_df)
        
        # Extract target variable
        if 'churn' in df.columns:
            y = df['churn'].astype(int).values
        else:
            # Generate synthetic targets for demonstration
            y = np.random.choice([0, 1], size=len(X), p=[0.8, 0.2])
        
        return X, y
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Engineer features for churn prediction.
        
        Args:
            df: Raw customer dataframe
            
        Returns:
            Engineered features dataframe
        """
        features = pd.DataFrame()
        
        # Basic customer information
        features['tenure_months'] = df['tenure_months']
        features['monthly_charges'] = df['monthly_charges']
        features['total_charges'] = pd.to_numeric(df['total_charges'], errors='coerce').fillna(0)
        features['senior_citizen'] = df['senior_citizen']
        features['gender'] = df['gender']
        
        # Contract and payment features
        features['contract_type'] = df['contract_type']
        features['payment_method'] = df['payment_method']
        features['paperless_billing'] = df['paperless_billing'].astype(int)
        
        # Service features
        features['internet_service'] = df['internet_service']
        features['phone_service'] = df['phone_service'].astype(int)
        features['multiple_lines'] = df['multiple_lines'].astype(int)
        features['online_security'] = df['online_security'].astype(int)
        features['online_backup'] = df['online_backup'].astype(int)
        features['device_protection'] = df['device_protection'].astype(int)
        features['tech_support'] = df['tech_support'].astype(int)
        features['streaming_tv'] = df['streaming_tv'].astype(int)
        features['streaming_movies'] = df['streaming_movies'].astype(int)
        
        # Family features
        features['partner'] = df['partner'].astype(int)
        features['dependents'] = df['dependents'].astype(int)
        
        # Derived features
        features['avg_monthly_charges'] = features['total_charges'] / np.maximum(features['tenure_months'], 1)
        features['charges_per_service'] = features['monthly_charges'] / np.maximum(
            features[['phone_service', 'multiple_lines', 'online_security', 'online_backup',
                     'device_protection', 'tech_support', 'streaming_tv', 'streaming_movies']].sum(axis=1), 1
        )
        
        # Customer value segments
        features['high_value_customer'] = (features['monthly_charges'] > features['monthly_charges'].quantile(0.75)).astype(int)
        features['long_tenure_customer'] = (features['tenure_months'] > 24).astype(int)
        features['family_customer'] = ((features['partner'] == 1) | (features['dependents'] == 1)).astype(int)
        
        # Service adoption score
        service_columns = ['online_security', 'online_backup', 'device_protection', 'tech_support']
        features['service_adoption_score'] = features[service_columns].sum(axis=1)
        
        return features
    
    def _generate_synthetic_churn_data(self, n_customers: int) -> pd.DataFrame:
        """
        Generate synthetic customer churn data for demonstration.
        
        Args:
            n_customers: Number of customers to generate
            
        Returns:
            Synthetic customer dataframe
        """
        np.random.seed(42)
        
        data = {
            'customer_id': [f'CUST_{i:06d}' for i in range(1, n_customers + 1)],
            'gender': np.random.choice(['Male', 'Female'], n_customers),
            'senior_citizen': np.random.choice([0, 1], n_customers, p=[0.8, 0.2]),
            'partner': np.random.choice([0, 1], n_customers, p=[0.5, 0.5]),
            'dependents': np.random.choice([0, 1], n_customers, p=[0.7, 0.3]),
            'tenure_months': np.random.randint(1, 73, n_customers),
            'phone_service': np.random.choice([0, 1], n_customers, p=[0.1, 0.9]),
            'multiple_lines': np.random.choice([0, 1], n_customers, p=[0.5, 0.5]),
            'internet_service': np.random.choice(['DSL', 'Fiber optic', 'No'], n_customers, p=[0.4, 0.4, 0.2]),
            'online_security': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'online_backup': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'device_protection': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'tech_support': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'streaming_tv': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'streaming_movies': np.random.choice([0, 1], n_customers, p=[0.6, 0.4]),
            'contract_type': np.random.choice(['Month-to-month', 'One year', 'Two year'], n_customers, p=[0.5, 0.3, 0.2]),
            'paperless_billing': np.random.choice([0, 1], n_customers, p=[0.4, 0.6]),
            'payment_method': np.random.choice(['Electronic check', 'Mailed check', 'Bank transfer', 'Credit card'], n_customers),
            'monthly_charges': np.random.uniform(20, 120, n_customers),
            'total_charges': np.random.uniform(20, 8000, n_customers)
        }
        
        df = pd.DataFrame(data)
        
        # Generate churn based on realistic patterns
        churn_probability = (
            0.1 +  # Base churn rate
            0.3 * (df['contract_type'] == 'Month-to-month').astype(int) +  # Higher churn for month-to-month
            0.2 * (df['tenure_months'] < 12).astype(int) +  # Higher churn for new customers
            0.1 * (df['monthly_charges'] > 80).astype(int) +  # Higher churn for expensive plans
            -0.2 * (df['partner'] == 1).astype(int) +  # Lower churn for customers with partners
            -0.1 * (df['online_security'] == 1).astype(int)  # Lower churn for security users
        )
        
        churn_probability = np.clip(churn_probability, 0, 1)
        df['churn'] = np.random.binomial(1, churn_probability)
        
        return df
    
    def predict_churn_risk(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict churn risk levels for customers.
        
        Args:
            data: Customer data
            
        Returns:
            Churn risk predictions with risk levels
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before prediction")
            
            # Get prediction probabilities
            result = self.predict(data)
            if not result["success"]:
                return result
            
            probabilities = np.array(result["probabilities"])
            churn_probabilities = probabilities[:, 1] if probabilities.ndim > 1 else probabilities
            
            # Assign risk levels
            risk_levels = []
            for prob in churn_probabilities:
                if prob < self.churn_risk_thresholds["low"]:
                    risk_levels.append("Low")
                elif prob < self.churn_risk_thresholds["medium"]:
                    risk_levels.append("Medium")
                elif prob < self.churn_risk_thresholds["high"]:
                    risk_levels.append("High")
                else:
                    risk_levels.append("Critical")
            
            # Generate recommendations
            recommendations = self._generate_retention_recommendations(churn_probabilities, risk_levels)
            
            return {
                "success": True,
                "churn_probabilities": churn_probabilities.tolist(),
                "risk_levels": risk_levels,
                "recommendations": recommendations,
                "feature_importance": self.feature_importance,
                "model_name": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Churn risk prediction failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_retention_recommendations(self, probabilities: np.ndarray, risk_levels: List[str]) -> List[Dict[str, Any]]:
        """
        Generate customer retention recommendations based on churn risk.
        
        Args:
            probabilities: Churn probabilities
            risk_levels: Risk level classifications
            
        Returns:
            List of retention recommendations
        """
        recommendations = []
        
        for i, (prob, risk) in enumerate(zip(probabilities, risk_levels)):
            customer_recs = {
                "customer_index": i,
                "risk_level": risk,
                "churn_probability": float(prob),
                "actions": []
            }
            
            if risk == "Low":
                customer_recs["actions"] = [
                    "Continue regular engagement",
                    "Offer loyalty rewards",
                    "Monitor satisfaction levels"
                ]
            elif risk == "Medium":
                customer_recs["actions"] = [
                    "Increase engagement frequency",
                    "Offer personalized promotions",
                    "Conduct satisfaction survey",
                    "Provide additional value-added services"
                ]
            elif risk == "High":
                customer_recs["actions"] = [
                    "Immediate outreach by customer success team",
                    "Offer significant discount or upgrade",
                    "Schedule one-on-one consultation",
                    "Address specific pain points"
                ]
            else:  # Critical
                customer_recs["actions"] = [
                    "Emergency retention campaign",
                    "Executive-level intervention",
                    "Customized retention offer",
                    "Immediate issue resolution",
                    "Consider contract renegotiation"
                ]
            
            recommendations.append(customer_recs)
        
        return recommendations
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance from the trained model.
        
        Returns:
            Feature importance dictionary
        """
        if not self.is_trained or not hasattr(self.model, 'feature_importances_'):
            return {}
        
        importance_dict = {}
        for feature, importance in zip(self.feature_names, self.model.feature_importances_):
            importance_dict[feature] = float(importance)
        
        # Sort by importance
        self.feature_importance = dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
        return self.feature_importance
