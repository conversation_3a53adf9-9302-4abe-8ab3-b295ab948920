"""
Sales Prediction Model for MarketMind
=====================================

This module implements sales forecasting using LSTM and Prophet algorithms.
It predicts future sales based on historical data, seasonality, and external factors.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    print("Prophet not available, using LSTM only")
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class SalesPredictionModel(BaseMarketMindModel):
    """
    Sales Prediction Model using LSTM and Prophet for time series forecasting.
    
    Features analyzed:
    - Historical sales data
    - Seasonal patterns
    - Marketing campaign effects
    - Economic indicators
    - Product lifecycle stages
    - External events and holidays
    """
    
    def __init__(self, model_type: str = "lstm", sequence_length: int = 30):
        """
        Initialize Sales Prediction Model.
        
        Args:
            model_type: Model architecture ('lstm' or 'prophet')
            sequence_length: Length of input sequences for LSTM
        """
        super().__init__("sales_prediction", "time_series")
        self.model_type = model_type
        self.sequence_length = sequence_length
        self.scaler = MinMaxScaler()
        self.feature_scaler = MinMaxScaler()
        
        # Prophet model (if available)
        self.prophet_model = None
        
        # Time series components
        self.trend_component = None
        self.seasonal_component = None
        self.residual_component = None
        
        logger.info(f"Initialized Sales Prediction Model with {model_type}")
    
    def build_model(self, **kwargs) -> Sequential:
        """
        Build the LSTM sales prediction model.
        
        Returns:
            Configured LSTM model
        """
        if self.model_type == "lstm":
            return self._build_lstm_model(**kwargs)
        elif self.model_type == "prophet" and PROPHET_AVAILABLE:
            return self._build_prophet_model(**kwargs)
        else:
            # Fallback to LSTM
            return self._build_lstm_model(**kwargs)
    
    def _build_lstm_model(self, **kwargs) -> Sequential:
        """
        Build LSTM model for sales prediction.
        
        Returns:
            LSTM model
        """
        n_features = kwargs.get('n_features', 1)
        lstm_units = kwargs.get('lstm_units', 50)
        
        model = Sequential([
            LSTM(lstm_units, return_sequences=True, input_shape=(self.sequence_length, n_features)),
            Dropout(0.2),
            LSTM(lstm_units, return_sequences=True),
            Dropout(0.2),
            LSTM(lstm_units),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mean_squared_error', metrics=['mae'])
        return model
    
    def _build_prophet_model(self, **kwargs) -> Any:
        """
        Build Prophet model for sales prediction.
        
        Returns:
            Prophet model
        """
        if not PROPHET_AVAILABLE:
            raise ImportError("Prophet is not available")
        
        return Prophet(
            yearly_seasonality=True,
            weekly_seasonality=True,
            daily_seasonality=False,
            changepoint_prior_scale=0.05,
            seasonality_prior_scale=10.0
        )
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess sales data for training.
        
        Args:
            data: Raw sales data
            
        Returns:
            Tuple of (features, targets)
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Generate synthetic data if needed
        if 'sales' not in df.columns or 'date' not in df.columns:
            df = self._generate_synthetic_sales_data(len(df))
        
        # Ensure date column is datetime
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        if self.model_type == "prophet":
            return self._preprocess_for_prophet(df)
        else:
            return self._preprocess_for_lstm(df)
    
    def _preprocess_for_lstm(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess data for LSTM model.
        
        Args:
            df: Sales dataframe
            
        Returns:
            Tuple of (X, y) for LSTM training
        """
        # Feature engineering
        df = self._engineer_time_features(df)
        
        # Select features for training
        feature_columns = ['sales', 'day_of_week', 'month', 'quarter', 'is_weekend', 'is_holiday']
        available_features = [col for col in feature_columns if col in df.columns]
        
        # Scale features
        feature_data = df[available_features].values
        scaled_features = self.feature_scaler.fit_transform(feature_data)
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_features)):
            X.append(scaled_features[i-self.sequence_length:i])
            y.append(scaled_features[i, 0])  # Predict sales (first column)
        
        return np.array(X), np.array(y)
    
    def _preprocess_for_prophet(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, np.ndarray]:
        """
        Preprocess data for Prophet model.
        
        Args:
            df: Sales dataframe
            
        Returns:
            Tuple of (prophet_df, dummy_y)
        """
        # Prophet requires 'ds' and 'y' columns
        prophet_df = pd.DataFrame({
            'ds': df['date'],
            'y': df['sales']
        })
        
        # Add additional regressors if available
        if 'marketing_spend' in df.columns:
            prophet_df['marketing_spend'] = df['marketing_spend']
        if 'promotion' in df.columns:
            prophet_df['promotion'] = df['promotion']
        
        # Return dummy y for compatibility with base class
        return prophet_df, np.array([0])
    
    def _engineer_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Engineer time-based features.
        
        Args:
            df: Sales dataframe
            
        Returns:
            Dataframe with engineered features
        """
        df = df.copy()
        
        # Time-based features
        df['day_of_week'] = df['date'].dt.dayofweek
        df['month'] = df['date'].dt.month
        df['quarter'] = df['date'].dt.quarter
        df['year'] = df['date'].dt.year
        df['day_of_year'] = df['date'].dt.dayofyear
        
        # Weekend indicator
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        
        # Holiday indicator (simplified)
        df['is_holiday'] = 0  # Can be enhanced with actual holiday data
        
        # Lag features
        df['sales_lag_1'] = df['sales'].shift(1)
        df['sales_lag_7'] = df['sales'].shift(7)
        df['sales_lag_30'] = df['sales'].shift(30)
        
        # Rolling statistics
        df['sales_ma_7'] = df['sales'].rolling(window=7).mean()
        df['sales_ma_30'] = df['sales'].rolling(window=30).mean()
        df['sales_std_7'] = df['sales'].rolling(window=7).std()
        
        # Fill NaN values
        df = df.fillna(method='bfill').fillna(0)
        
        return df
    
    def _generate_synthetic_sales_data(self, n_days: int = 365) -> pd.DataFrame:
        """
        Generate synthetic sales data for demonstration.
        
        Args:
            n_days: Number of days to generate
            
        Returns:
            Synthetic sales dataframe
        """
        np.random.seed(42)
        
        # Generate date range
        dates = pd.date_range(start='2023-01-01', periods=n_days, freq='D')
        
        # Base sales trend
        trend = np.linspace(1000, 1500, n_days)
        
        # Seasonal patterns
        seasonal = 200 * np.sin(2 * np.pi * np.arange(n_days) / 365)  # Yearly
        weekly = 100 * np.sin(2 * np.pi * np.arange(n_days) / 7)     # Weekly
        
        # Random noise
        noise = np.random.normal(0, 50, n_days)
        
        # Weekend effect
        weekend_effect = np.array([100 if date.weekday() >= 5 else 0 for date in dates])
        
        # Marketing campaigns (random spikes)
        marketing_effect = np.zeros(n_days)
        campaign_days = np.random.choice(n_days, size=20, replace=False)
        marketing_effect[campaign_days] = np.random.uniform(200, 500, 20)
        
        # Combine all effects
        sales = trend + seasonal + weekly + noise + weekend_effect + marketing_effect
        sales = np.maximum(sales, 0)  # Ensure non-negative sales
        
        # Additional features
        marketing_spend = np.random.uniform(1000, 5000, n_days)
        promotion = np.random.choice([0, 1], n_days, p=[0.8, 0.2])
        
        return pd.DataFrame({
            'date': dates,
            'sales': sales,
            'marketing_spend': marketing_spend,
            'promotion': promotion
        })
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.2, **kwargs) -> Dict[str, Any]:
        """
        Train the sales prediction model.
        
        Args:
            data: Sales data
            validation_split: Fraction for validation
            **kwargs: Additional training parameters
            
        Returns:
            Training results
        """
        try:
            logger.info("Starting sales prediction training")
            
            if self.model_type == "prophet" and PROPHET_AVAILABLE:
                return self._train_prophet(data, **kwargs)
            else:
                return self._train_lstm(data, validation_split, **kwargs)
                
        except Exception as e:
            logger.error(f"Sales prediction training failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _train_lstm(self, data: pd.DataFrame, validation_split: float, **kwargs) -> Dict[str, Any]:
        """
        Train LSTM model.
        
        Args:
            data: Sales data
            validation_split: Validation split ratio
            **kwargs: Additional parameters
            
        Returns:
            Training results
        """
        # Preprocess data
        X, y = self.preprocess_data(data)
        
        # Split data
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # Build model
        self.model = self.build_model(n_features=X.shape[2], **kwargs)
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=kwargs.get('epochs', 50),
            batch_size=kwargs.get('batch_size', 32),
            verbose=0
        )
        
        # Calculate metrics
        train_pred = self.model.predict(X_train, verbose=0)
        val_pred = self.model.predict(X_val, verbose=0)
        
        train_mae = mean_absolute_error(y_train, train_pred)
        val_mae = mean_absolute_error(y_val, val_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        
        self.is_trained = True
        self.model_metrics = {
            "train_mae": float(train_mae),
            "val_mae": float(val_mae),
            "train_rmse": float(train_rmse),
            "val_rmse": float(val_rmse),
            "final_train_loss": float(history.history['loss'][-1]),
            "final_val_loss": float(history.history['val_loss'][-1])
        }
        
        self.save_model()
        
        return {
            "success": True,
            "metrics": self.model_metrics,
            "training_history": {
                "loss": history.history['loss'],
                "val_loss": history.history['val_loss']
            }
        }
    
    def _train_prophet(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train Prophet model.
        
        Args:
            data: Sales data
            **kwargs: Additional parameters
            
        Returns:
            Training results
        """
        # Preprocess data for Prophet
        prophet_df, _ = self.preprocess_data(data)
        
        # Build and train Prophet model
        self.prophet_model = self._build_prophet_model(**kwargs)
        
        # Add additional regressors if available
        if 'marketing_spend' in prophet_df.columns:
            self.prophet_model.add_regressor('marketing_spend')
        if 'promotion' in prophet_df.columns:
            self.prophet_model.add_regressor('promotion')
        
        # Fit model
        self.prophet_model.fit(prophet_df)
        
        # Calculate metrics using cross-validation
        from prophet.diagnostics import cross_validation, performance_metrics
        
        # Perform cross-validation
        df_cv = cross_validation(
            self.prophet_model, 
            initial='730 days', 
            period='180 days', 
            horizon='30 days'
        )
        df_p = performance_metrics(df_cv)
        
        self.is_trained = True
        self.model_metrics = {
            "mae": float(df_p['mae'].mean()),
            "rmse": float(df_p['rmse'].mean()),
            "mape": float(df_p['mape'].mean()),
            "coverage": float(df_p['coverage'].mean())
        }
        
        self.save_model()
        
        return {
            "success": True,
            "metrics": self.model_metrics,
            "cross_validation_results": df_p.to_dict()
        }
    
    def predict_sales(self, 
                     periods: int = 30, 
                     include_history: bool = False,
                     confidence_interval: bool = True) -> Dict[str, Any]:
        """
        Predict future sales.
        
        Args:
            periods: Number of periods to predict
            include_history: Whether to include historical data
            confidence_interval: Whether to include confidence intervals
            
        Returns:
            Sales predictions
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before prediction")
            
            if self.model_type == "prophet" and self.prophet_model:
                return self._predict_with_prophet(periods, include_history, confidence_interval)
            else:
                return self._predict_with_lstm(periods, include_history, confidence_interval)
                
        except Exception as e:
            logger.error(f"Sales prediction failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _predict_with_prophet(self, periods: int, include_history: bool, confidence_interval: bool) -> Dict[str, Any]:
        """
        Make predictions using Prophet model.
        
        Args:
            periods: Number of periods to predict
            include_history: Include historical data
            confidence_interval: Include confidence intervals
            
        Returns:
            Prophet predictions
        """
        # Create future dataframe
        future = self.prophet_model.make_future_dataframe(periods=periods)
        
        # Make predictions
        forecast = self.prophet_model.predict(future)
        
        # Extract predictions
        if include_history:
            predictions = forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']].to_dict('records')
        else:
            predictions = forecast.tail(periods)[['ds', 'yhat', 'yhat_lower', 'yhat_upper']].to_dict('records')
        
        # Get components
        components = {
            "trend": forecast['trend'].tolist(),
            "yearly": forecast['yearly'].tolist() if 'yearly' in forecast.columns else [],
            "weekly": forecast['weekly'].tolist() if 'weekly' in forecast.columns else []
        }
        
        return {
            "success": True,
            "predictions": predictions,
            "components": components,
            "model_type": "prophet"
        }
    
    def _predict_with_lstm(self, periods: int, include_history: bool, confidence_interval: bool) -> Dict[str, Any]:
        """
        Make predictions using LSTM model.
        
        Args:
            periods: Number of periods to predict
            include_history: Include historical data
            confidence_interval: Include confidence intervals
            
        Returns:
            LSTM predictions
        """
        # This is a simplified implementation
        # In practice, you would need the last sequence from training data
        # and iteratively predict future values
        
        # Generate dummy predictions for demonstration
        base_value = 1000
        predictions = []
        
        for i in range(periods):
            # Simple trend + noise for demonstration
            pred_value = base_value + i * 2 + np.random.normal(0, 50)
            
            prediction = {
                "date": (pd.Timestamp.now() + pd.Timedelta(days=i)).isoformat(),
                "predicted_sales": float(pred_value),
                "lower_bound": float(pred_value * 0.9) if confidence_interval else None,
                "upper_bound": float(pred_value * 1.1) if confidence_interval else None
            }
            predictions.append(prediction)
        
        return {
            "success": True,
            "predictions": predictions,
            "model_type": "lstm"
        }
    
    def analyze_sales_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze sales patterns and trends.
        
        Args:
            data: Sales data
            
        Returns:
            Sales pattern analysis
        """
        try:
            if 'sales' not in data.columns or 'date' not in data.columns:
                data = self._generate_synthetic_sales_data(len(data))
            
            df = data.copy()
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Calculate various metrics
            analysis = {
                "overall_trend": self._calculate_trend(df),
                "seasonality": self._analyze_seasonality(df),
                "growth_rate": self._calculate_growth_rate(df),
                "volatility": self._calculate_volatility(df),
                "peak_periods": self._identify_peak_periods(df),
                "recommendations": self._generate_sales_recommendations(df)
            }
            
            return {
                "success": True,
                "analysis": analysis
            }
            
        except Exception as e:
            logger.error(f"Sales pattern analysis failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _calculate_trend(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate overall sales trend."""
        sales = df['sales'].values
        x = np.arange(len(sales))
        slope, intercept = np.polyfit(x, sales, 1)
        
        return {
            "direction": "increasing" if slope > 0 else "decreasing",
            "slope": float(slope),
            "strength": "strong" if abs(slope) > 1 else "weak"
        }
    
    def _analyze_seasonality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze seasonal patterns."""
        df['month'] = df['date'].dt.month
        monthly_avg = df.groupby('month')['sales'].mean()
        
        peak_month = monthly_avg.idxmax()
        low_month = monthly_avg.idxmin()
        
        return {
            "peak_month": int(peak_month),
            "low_month": int(low_month),
            "seasonal_variation": float(monthly_avg.std()),
            "monthly_averages": monthly_avg.to_dict()
        }
    
    def _calculate_growth_rate(self, df: pd.DataFrame) -> float:
        """Calculate year-over-year growth rate."""
        if len(df) < 365:
            return 0.0
        
        recent_avg = df.tail(30)['sales'].mean()
        year_ago_avg = df.iloc[-365:-335]['sales'].mean()
        
        return float((recent_avg - year_ago_avg) / year_ago_avg * 100)
    
    def _calculate_volatility(self, df: pd.DataFrame) -> float:
        """Calculate sales volatility."""
        return float(df['sales'].std() / df['sales'].mean())
    
    def _identify_peak_periods(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Identify peak sales periods."""
        df['sales_ma'] = df['sales'].rolling(window=7).mean()
        threshold = df['sales_ma'].quantile(0.9)
        
        peaks = df[df['sales_ma'] > threshold]
        
        return [
            {
                "date": row['date'].isoformat(),
                "sales": float(row['sales']),
                "type": "peak"
            }
            for _, row in peaks.head(10).iterrows()
        ]
    
    def _generate_sales_recommendations(self, df: pd.DataFrame) -> List[str]:
        """Generate sales optimization recommendations."""
        recommendations = []
        
        # Trend-based recommendations
        trend = self._calculate_trend(df)
        if trend["direction"] == "decreasing":
            recommendations.append("Consider implementing promotional campaigns to reverse declining trend")
        
        # Seasonality-based recommendations
        seasonality = self._analyze_seasonality(df)
        recommendations.append(f"Focus marketing efforts during month {seasonality['peak_month']} for maximum impact")
        
        # Volatility-based recommendations
        volatility = self._calculate_volatility(df)
        if volatility > 0.3:
            recommendations.append("High sales volatility detected - consider demand forecasting improvements")
        
        return recommendations

    def get_sales_insights(self) -> Dict[str, Any]:
        """
        Get comprehensive sales insights and model performance.

        Returns:
            Sales insights dictionary
        """
        if not self.is_trained:
            return {"error": "Model must be trained first"}

        insights = {
            "model_performance": self.model_metrics,
            "model_type": self.model_type,
            "sequence_length": self.sequence_length,
            "training_status": "trained" if self.is_trained else "not_trained",
            "last_trained": self.last_trained.isoformat() if self.last_trained else None,
            "capabilities": [
                "Multi-step ahead forecasting",
                "Seasonal pattern detection",
                "Trend analysis",
                "Confidence intervals",
                "External factor integration"
            ]
        }

        return insights
