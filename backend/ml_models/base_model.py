"""
Base Model Class for MarketMind AI Models
==========================================

This module provides the base class that all MarketMind AI models inherit from.
It includes common functionality for model training, evaluation, and deployment.
"""

import os
import pickle
import joblib
import json
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseMarketMindModel(ABC):
    """
    Base class for all MarketMind AI models.
    Provides common functionality and enforces consistent interface.
    """
    
    def __init__(self, model_name: str, model_type: str = "classification"):
        """
        Initialize the base model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model (classification, regression, clustering, etc.)
        """
        self.model_name = model_name
        self.model_type = model_type
        self.model = None
        self.is_trained = False
        self.training_history = []
        self.model_metrics = {}
        self.feature_names = []
        self.model_version = "1.0.0"
        self.created_at = datetime.now()
        self.last_trained = None
        
        # Create model directory
        self.model_dir = f"backend/ml_models/trained_models/{model_name}"
        os.makedirs(self.model_dir, exist_ok=True)
        
        logger.info(f"Initialized {model_name} model of type {model_type}")
    
    @abstractmethod
    def build_model(self, **kwargs) -> Any:
        """
        Build the model architecture.
        Must be implemented by each specific model.
        """
        pass
    
    @abstractmethod
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess data for training/prediction.
        Must be implemented by each specific model.
        
        Args:
            data: Raw input data
            
        Returns:
            Tuple of (features, targets)
        """
        pass
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.2, **kwargs) -> Dict[str, Any]:
        """
        Train the model with provided data.
        
        Args:
            data: Training data
            validation_split: Fraction of data to use for validation
            **kwargs: Additional training parameters
            
        Returns:
            Training results and metrics
        """
        try:
            logger.info(f"Starting training for {self.model_name}")
            
            # Preprocess data
            X, y = self.preprocess_data(data)
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=validation_split, random_state=42
            )
            
            # Build model if not already built
            if self.model is None:
                self.model = self.build_model(**kwargs)
            
            # Train model
            training_start = datetime.now()
            self.model.fit(X_train, y_train)
            training_end = datetime.now()
            
            # Evaluate model
            train_predictions = self.model.predict(X_train)
            val_predictions = self.model.predict(X_val)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_train, train_predictions, y_val, val_predictions)
            
            # Update model state
            self.is_trained = True
            self.last_trained = training_end
            self.model_metrics = metrics
            
            # Save training history
            training_record = {
                "timestamp": training_end.isoformat(),
                "training_time": (training_end - training_start).total_seconds(),
                "data_size": len(data),
                "metrics": metrics,
                "model_version": self.model_version
            }
            self.training_history.append(training_record)
            
            # Save model
            self.save_model()
            
            logger.info(f"Training completed for {self.model_name}")
            return {
                "success": True,
                "metrics": metrics,
                "training_time": training_record["training_time"],
                "model_path": self._get_model_path()
            }
            
        except Exception as e:
            logger.error(f"Training failed for {self.model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Make predictions using the trained model.
        
        Args:
            data: Input data for prediction
            
        Returns:
            Prediction results
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before making predictions")
            
            # Preprocess data
            X, _ = self.preprocess_data(data)
            
            # Make predictions
            predictions = self.model.predict(X)
            
            # Get prediction probabilities if available
            probabilities = None
            if hasattr(self.model, 'predict_proba'):
                probabilities = self.model.predict_proba(X)
            
            return {
                "success": True,
                "predictions": predictions.tolist(),
                "probabilities": probabilities.tolist() if probabilities is not None else None,
                "model_name": self.model_name,
                "prediction_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Prediction failed for {self.model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def evaluate(self, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Evaluate model performance on test data.
        
        Args:
            test_data: Test dataset
            
        Returns:
            Evaluation metrics
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before evaluation")
            
            # Preprocess data
            X_test, y_test = self.preprocess_data(test_data)
            
            # Make predictions
            predictions = self.model.predict(X_test)
            
            # Calculate metrics
            if self.model_type == "classification":
                metrics = {
                    "accuracy": accuracy_score(y_test, predictions),
                    "precision": precision_score(y_test, predictions, average='weighted'),
                    "recall": recall_score(y_test, predictions, average='weighted'),
                    "f1_score": f1_score(y_test, predictions, average='weighted')
                }
            else:
                # For regression models
                from sklearn.metrics import mean_squared_error, r2_score
                metrics = {
                    "mse": mean_squared_error(y_test, predictions),
                    "rmse": np.sqrt(mean_squared_error(y_test, predictions)),
                    "r2_score": r2_score(y_test, predictions)
                }
            
            return {
                "success": True,
                "metrics": metrics,
                "test_size": len(test_data)
            }
            
        except Exception as e:
            logger.error(f"Evaluation failed for {self.model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def save_model(self) -> bool:
        """
        Save the trained model to disk.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.is_trained:
                logger.warning(f"Attempting to save untrained model {self.model_name}")
                return False
            
            # Save model
            model_path = self._get_model_path()
            joblib.dump(self.model, model_path)
            
            # Save metadata
            metadata = {
                "model_name": self.model_name,
                "model_type": self.model_type,
                "model_version": self.model_version,
                "created_at": self.created_at.isoformat(),
                "last_trained": self.last_trained.isoformat() if self.last_trained else None,
                "is_trained": self.is_trained,
                "feature_names": self.feature_names,
                "model_metrics": self.model_metrics,
                "training_history": self.training_history
            }
            
            metadata_path = self._get_metadata_path()
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Model {self.model_name} saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save model {self.model_name}: {str(e)}")
            return False
    
    def load_model(self) -> bool:
        """
        Load a previously trained model from disk.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            model_path = self._get_model_path()
            metadata_path = self._get_metadata_path()
            
            if not os.path.exists(model_path) or not os.path.exists(metadata_path):
                logger.warning(f"Model files not found for {self.model_name}")
                return False
            
            # Load model
            self.model = joblib.load(model_path)
            
            # Load metadata
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            self.model_type = metadata.get("model_type", self.model_type)
            self.model_version = metadata.get("model_version", "1.0.0")
            self.is_trained = metadata.get("is_trained", False)
            self.feature_names = metadata.get("feature_names", [])
            self.model_metrics = metadata.get("model_metrics", {})
            self.training_history = metadata.get("training_history", [])
            
            if metadata.get("last_trained"):
                self.last_trained = datetime.fromisoformat(metadata["last_trained"])
            
            logger.info(f"Model {self.model_name} loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model {self.model_name}: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about the model.
        
        Returns:
            Model information dictionary
        """
        return {
            "model_name": self.model_name,
            "model_type": self.model_type,
            "model_version": self.model_version,
            "is_trained": self.is_trained,
            "created_at": self.created_at.isoformat(),
            "last_trained": self.last_trained.isoformat() if self.last_trained else None,
            "feature_count": len(self.feature_names),
            "feature_names": self.feature_names,
            "latest_metrics": self.model_metrics,
            "training_sessions": len(self.training_history),
            "model_path": self._get_model_path() if self.is_trained else None
        }
    
    def _calculate_metrics(self, y_train, train_pred, y_val, val_pred) -> Dict[str, Any]:
        """Calculate training and validation metrics."""
        if self.model_type == "classification":
            return {
                "train_accuracy": accuracy_score(y_train, train_pred),
                "val_accuracy": accuracy_score(y_val, val_pred),
                "train_f1": f1_score(y_train, train_pred, average='weighted'),
                "val_f1": f1_score(y_val, val_pred, average='weighted')
            }
        else:
            from sklearn.metrics import mean_squared_error, r2_score
            return {
                "train_mse": mean_squared_error(y_train, train_pred),
                "val_mse": mean_squared_error(y_val, val_pred),
                "train_r2": r2_score(y_train, train_pred),
                "val_r2": r2_score(y_val, val_pred)
            }
    
    def _get_model_path(self) -> str:
        """Get the file path for saving/loading the model."""
        return os.path.join(self.model_dir, f"{self.model_name}_model.pkl")
    
    def _get_metadata_path(self) -> str:
        """Get the file path for saving/loading model metadata."""
        return os.path.join(self.model_dir, f"{self.model_name}_metadata.json")
