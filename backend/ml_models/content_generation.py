"""
Personalized Content Generation Model for MarketMind
====================================================

This module implements personalized content generation using LSTM and Transformer models.
It creates customized marketing content based on customer segments and preferences.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Embedding, Dropout, Input, MultiHeadAttention, LayerNormalization
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.preprocessing import LabelEncoder
from .base_model import BaseMarketMindModel
import logging
import re
import json

logger = logging.getLogger(__name__)

class ContentGenerationModel(BaseMarketMindModel):
    """
    Personalized Content Generation Model using LSTM and Transformer architectures.
    
    Features:
    - Customer segment-based content personalization
    - Multi-language support (Arabic/English)
    - Content type adaptation (email, social media, ads)
    - Tone and style customization
    - A/B testing content variants
    """
    
    def __init__(self, model_type: str = "lstm", max_length: int = 200, vocab_size: int = 10000):
        """
        Initialize Content Generation Model.
        
        Args:
            model_type: Model architecture ('lstm' or 'transformer')
            max_length: Maximum sequence length
            vocab_size: Vocabulary size
        """
        super().__init__("content_generation", "text_generation")
        self.model_type = model_type
        self.max_length = max_length
        self.vocab_size = vocab_size
        self.tokenizer = Tokenizer(num_words=vocab_size, oov_token="<OOV>")
        self.label_encoders = {}
        
        # Content templates and patterns
        self.content_templates = self._load_content_templates()
        self.style_patterns = self._load_style_patterns()
        
        logger.info(f"Initialized Content Generation Model with {model_type}")
    
    def build_model(self, **kwargs) -> Model:
        """
        Build the content generation model.
        
        Returns:
            Configured neural network model
        """
        embedding_dim = kwargs.get('embedding_dim', 128)
        hidden_units = kwargs.get('hidden_units', 256)
        
        if self.model_type == "lstm":
            return self._build_lstm_model(embedding_dim, hidden_units)
        elif self.model_type == "transformer":
            return self._build_transformer_model(embedding_dim, hidden_units)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
    
    def _build_lstm_model(self, embedding_dim: int, hidden_units: int) -> Sequential:
        """
        Build LSTM-based content generation model.
        
        Args:
            embedding_dim: Embedding dimension
            hidden_units: Number of hidden units
            
        Returns:
            LSTM model
        """
        model = Sequential([
            Embedding(self.vocab_size, embedding_dim, input_length=self.max_length-1),
            LSTM(hidden_units, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            LSTM(hidden_units, dropout=0.2, recurrent_dropout=0.2),
            Dense(hidden_units, activation='relu'),
            Dropout(0.3),
            Dense(self.vocab_size, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def _build_transformer_model(self, embedding_dim: int, hidden_units: int) -> Model:
        """
        Build Transformer-based content generation model.
        
        Args:
            embedding_dim: Embedding dimension
            hidden_units: Number of hidden units
            
        Returns:
            Transformer model
        """
        # Input layer
        inputs = Input(shape=(self.max_length-1,))
        
        # Embedding layer
        embedding = Embedding(self.vocab_size, embedding_dim)(inputs)
        
        # Multi-head attention
        attention = MultiHeadAttention(
            num_heads=8,
            key_dim=embedding_dim//8
        )(embedding, embedding)
        
        # Add & Norm
        attention = LayerNormalization()(attention + embedding)
        
        # Feed forward
        ffn = Dense(hidden_units, activation='relu')(attention)
        ffn = Dense(embedding_dim)(ffn)
        ffn = LayerNormalization()(ffn + attention)
        
        # Global average pooling and output
        pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn)
        outputs = Dense(self.vocab_size, activation='softmax')(pooled)
        
        model = Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer='adam',
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess content data for training.
        
        Args:
            data: Raw content data
            
        Returns:
            Tuple of (input_sequences, target_sequences)
        """
        # Generate synthetic data if needed
        if 'content_text' not in data.columns:
            data = self._generate_synthetic_content_data(len(data))
        
        # Prepare text data
        texts = data['content_text'].astype(str).tolist()
        
        # Fit tokenizer
        self.tokenizer.fit_on_texts(texts)
        
        # Convert texts to sequences
        sequences = self.tokenizer.texts_to_sequences(texts)
        
        # Create input-target pairs
        input_sequences = []
        target_sequences = []
        
        for sequence in sequences:
            for i in range(1, len(sequence)):
                input_seq = sequence[:i]
                target_seq = sequence[i]
                
                if len(input_seq) <= self.max_length - 1:
                    input_sequences.append(input_seq)
                    target_sequences.append(target_seq)
        
        # Pad sequences
        X = pad_sequences(input_sequences, maxlen=self.max_length-1, padding='pre')
        y = np.array(target_sequences)
        
        return X, y
    
    def _generate_synthetic_content_data(self, n_samples: int) -> pd.DataFrame:
        """
        Generate synthetic content data for training.
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Synthetic content dataframe
        """
        np.random.seed(42)
        
        # Arabic and English content templates
        arabic_templates = [
            "اكتشف منتجنا الجديد الذي سيغير حياتك للأفضل",
            "عرض خاص لفترة محدودة - خصم يصل إلى 50%",
            "انضم إلى آلاف العملاء الراضين عن خدماتنا المتميزة",
            "تجربة فريدة تنتظرك مع أحدث التقنيات والابتكارات",
            "استثمر في مستقبلك مع حلولنا المبتكرة والموثوقة"
        ]
        
        english_templates = [
            "Discover our revolutionary new product that will transform your life",
            "Limited time offer - save up to 50% on all premium features",
            "Join thousands of satisfied customers who trust our services",
            "Experience innovation like never before with our cutting-edge solutions",
            "Invest in your future with our reliable and proven solutions"
        ]
        
        content_types = ['email', 'social_media', 'advertisement', 'blog_post', 'newsletter']
        tones = ['professional', 'casual', 'enthusiastic', 'informative', 'persuasive']
        languages = ['arabic', 'english']
        
        data = []
        for i in range(n_samples):
            language = np.random.choice(languages)
            content_type = np.random.choice(content_types)
            tone = np.random.choice(tones)
            
            if language == 'arabic':
                base_text = np.random.choice(arabic_templates)
            else:
                base_text = np.random.choice(english_templates)
            
            # Add variations based on content type and tone
            if content_type == 'email':
                if language == 'arabic':
                    content_text = f"عزيزي العميل، {base_text}. نتطلع لخدمتك."
                else:
                    content_text = f"Dear Customer, {base_text}. We look forward to serving you."
            elif content_type == 'social_media':
                if language == 'arabic':
                    content_text = f"🚀 {base_text} #تسويق #ابتكار #نجاح"
                else:
                    content_text = f"🚀 {base_text} #marketing #innovation #success"
            else:
                content_text = base_text
            
            data.append({
                'content_id': f'CONTENT_{i:06d}',
                'content_text': content_text,
                'content_type': content_type,
                'tone': tone,
                'language': language,
                'target_audience': np.random.choice(['young_adults', 'professionals', 'families', 'seniors']),
                'performance_score': np.random.uniform(0.1, 1.0)
            })
        
        return pd.DataFrame(data)
    
    def generate_content(self, 
                        prompt: str, 
                        content_type: str = "social_media",
                        tone: str = "professional",
                        language: str = "arabic",
                        target_audience: str = "general",
                        max_tokens: int = 100) -> Dict[str, Any]:
        """
        Generate personalized content based on input parameters.
        
        Args:
            prompt: Content prompt or seed text
            content_type: Type of content to generate
            tone: Desired tone of content
            language: Target language
            target_audience: Target audience segment
            max_tokens: Maximum number of tokens to generate
            
        Returns:
            Generated content with metadata
        """
        try:
            if not self.is_trained:
                # Use template-based generation as fallback
                return self._generate_template_content(prompt, content_type, tone, language, target_audience)
            
            # Tokenize input prompt
            input_sequence = self.tokenizer.texts_to_sequences([prompt])
            input_sequence = pad_sequences(input_sequence, maxlen=self.max_length-1, padding='pre')
            
            # Generate content
            generated_tokens = []
            current_sequence = input_sequence[0]
            
            for _ in range(max_tokens):
                # Predict next token
                predictions = self.model.predict(current_sequence.reshape(1, -1), verbose=0)
                predicted_token = np.argmax(predictions[0])
                
                # Stop if end token or padding
                if predicted_token == 0:
                    break
                
                generated_tokens.append(predicted_token)
                
                # Update sequence for next prediction
                current_sequence = np.append(current_sequence[1:], predicted_token)
            
            # Convert tokens back to text
            generated_text = self.tokenizer.sequences_to_texts([generated_tokens])[0]
            
            # Post-process and enhance content
            enhanced_content = self._enhance_generated_content(
                generated_text, content_type, tone, language, target_audience
            )
            
            return {
                "success": True,
                "generated_content": enhanced_content,
                "content_type": content_type,
                "tone": tone,
                "language": language,
                "target_audience": target_audience,
                "model_used": f"{self.model_type}_neural_network",
                "generation_method": "neural_generation"
            }
            
        except Exception as e:
            logger.error(f"Content generation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_template_content(self, 
                                 prompt: str, 
                                 content_type: str, 
                                 tone: str, 
                                 language: str, 
                                 target_audience: str) -> Dict[str, Any]:
        """
        Generate content using templates when neural model is not available.
        
        Args:
            prompt: Content prompt
            content_type: Type of content
            tone: Desired tone
            language: Target language
            target_audience: Target audience
            
        Returns:
            Template-generated content
        """
        templates = self.content_templates.get(language, {}).get(content_type, [])
        if not templates:
            templates = ["محتوى تجريبي مخصص"] if language == "arabic" else ["Custom demo content"]
        
        base_template = np.random.choice(templates)
        
        # Customize based on parameters
        if tone == "enthusiastic":
            if language == "arabic":
                enhanced_content = f"🎉 {base_template} - تجربة رائعة تنتظرك!"
            else:
                enhanced_content = f"🎉 {base_template} - Amazing experience awaits!"
        elif tone == "professional":
            if language == "arabic":
                enhanced_content = f"نحن فخورون بتقديم {base_template} لعملائنا الكرام."
            else:
                enhanced_content = f"We are proud to present {base_template} to our valued customers."
        else:
            enhanced_content = base_template
        
        return {
            "success": True,
            "generated_content": enhanced_content,
            "content_type": content_type,
            "tone": tone,
            "language": language,
            "target_audience": target_audience,
            "model_used": "template_based",
            "generation_method": "template_generation"
        }
    
    def _enhance_generated_content(self, 
                                 content: str, 
                                 content_type: str, 
                                 tone: str, 
                                 language: str, 
                                 target_audience: str) -> str:
        """
        Enhance generated content with post-processing.
        
        Args:
            content: Raw generated content
            content_type: Type of content
            tone: Desired tone
            language: Target language
            target_audience: Target audience
            
        Returns:
            Enhanced content
        """
        # Clean and format content
        content = content.strip()
        content = re.sub(r'\s+', ' ', content)  # Remove extra whitespace
        
        # Add content-type specific enhancements
        if content_type == "social_media":
            if language == "arabic":
                content += " #تسويق #ابتكار"
            else:
                content += " #marketing #innovation"
        elif content_type == "email":
            if language == "arabic":
                content = f"عزيزي العميل،\n\n{content}\n\nمع أطيب التحيات،\nفريق العمل"
            else:
                content = f"Dear Customer,\n\n{content}\n\nBest regards,\nThe Team"
        
        # Add audience-specific customizations
        if target_audience == "young_adults":
            if language == "arabic":
                content += " 🚀"
            else:
                content += " 🚀"
        
        return content
    
    def _load_content_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Load content templates for different types and languages.
        
        Returns:
            Content templates dictionary
        """
        return {
            "arabic": {
                "email": [
                    "نحن سعداء لتقديم خدماتنا المتميزة لك",
                    "عرض خاص مخصص لك فقط",
                    "اكتشف مجموعتنا الجديدة من المنتجات"
                ],
                "social_media": [
                    "تجربة فريدة تنتظرك",
                    "ابتكار يلبي احتياجاتك",
                    "جودة لا تضاهى في كل منتج"
                ],
                "advertisement": [
                    "الحل الأمثل لجميع احتياجاتك",
                    "تقنية متطورة وأسعار منافسة",
                    "خدمة عملاء متميزة على مدار الساعة"
                ]
            },
            "english": {
                "email": [
                    "We're excited to offer you our premium services",
                    "Exclusive offer just for you",
                    "Discover our latest product collection"
                ],
                "social_media": [
                    "Unique experience awaits you",
                    "Innovation that meets your needs",
                    "Unmatched quality in every product"
                ],
                "advertisement": [
                    "The perfect solution for all your needs",
                    "Advanced technology at competitive prices",
                    "24/7 exceptional customer service"
                ]
            }
        }
    
    def _load_style_patterns(self) -> Dict[str, Dict[str, str]]:
        """
        Load style patterns for different tones.
        
        Returns:
            Style patterns dictionary
        """
        return {
            "professional": {
                "prefix": "نحن فخورون بـ" if "arabic" else "We are proud to",
                "suffix": "نتطلع لخدمتكم" if "arabic" else "We look forward to serving you"
            },
            "casual": {
                "prefix": "مرحباً!" if "arabic" else "Hey there!",
                "suffix": "نراكم قريباً" if "arabic" else "See you soon"
            },
            "enthusiastic": {
                "prefix": "🎉 رائع!" if "arabic" else "🎉 Amazing!",
                "suffix": "لا تفوت الفرصة!" if "arabic" else "Don't miss out!"
            }
        }
