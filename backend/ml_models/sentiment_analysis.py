"""
Advanced Sentiment Analysis Model for MarketMind
================================================

This module implements advanced sentiment analysis using BERT and BiLSTM models.
It supports Arabic and English text with emotion detection and aspect-based sentiment analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Embedding, Bidirectional, Input, GlobalMaxPooling1D
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
import re
import string
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class SentimentAnalysisModel(BaseMarketMindModel):
    """
    Advanced Sentiment Analysis Model using BiLSTM and transformer-like architectures.
    
    Features:
    - Multi-language support (Arabic/English)
    - Emotion detection (joy, anger, fear, sadness, surprise)
    - Aspect-based sentiment analysis
    - Confidence scoring
    - Text preprocessing and normalization
    """
    
    def __init__(self, model_type: str = "bilstm", max_length: int = 128, vocab_size: int = 20000):
        """
        Initialize Sentiment Analysis Model.
        
        Args:
            model_type: Model architecture ('bilstm' or 'cnn')
            max_length: Maximum sequence length
            vocab_size: Vocabulary size
        """
        super().__init__("sentiment_analysis", "classification")
        self.model_type = model_type
        self.max_length = max_length
        self.vocab_size = vocab_size
        
        # Tokenizers for different languages
        self.tokenizer = Tokenizer(num_words=vocab_size, oov_token="<OOV>")
        self.label_encoder = LabelEncoder()
        
        # Sentiment and emotion mappings
        self.sentiment_labels = ['negative', 'neutral', 'positive']
        self.emotion_labels = ['joy', 'anger', 'fear', 'sadness', 'surprise', 'neutral']
        
        # Language detection patterns
        self.arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        
        logger.info(f"Initialized Sentiment Analysis Model with {model_type}")
    
    def build_model(self, **kwargs) -> Model:
        """
        Build the sentiment analysis model.
        
        Returns:
            Configured neural network model
        """
        embedding_dim = kwargs.get('embedding_dim', 128)
        lstm_units = kwargs.get('lstm_units', 64)
        
        if self.model_type == "bilstm":
            return self._build_bilstm_model(embedding_dim, lstm_units)
        elif self.model_type == "cnn":
            return self._build_cnn_model(embedding_dim)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
    
    def _build_bilstm_model(self, embedding_dim: int, lstm_units: int) -> Model:
        """
        Build BiLSTM model for sentiment analysis.
        
        Args:
            embedding_dim: Embedding dimension
            lstm_units: LSTM units
            
        Returns:
            BiLSTM model
        """
        # Input layer
        inputs = Input(shape=(self.max_length,))
        
        # Embedding layer
        embedding = Embedding(self.vocab_size, embedding_dim, input_length=self.max_length)(inputs)
        
        # Bidirectional LSTM layers
        lstm1 = Bidirectional(LSTM(lstm_units, return_sequences=True, dropout=0.2, recurrent_dropout=0.2))(embedding)
        lstm2 = Bidirectional(LSTM(lstm_units//2, dropout=0.2, recurrent_dropout=0.2))(lstm1)
        
        # Dense layers
        dense1 = Dense(64, activation='relu')(lstm2)
        dropout1 = Dropout(0.3)(dense1)
        
        # Output layers for sentiment and emotion
        sentiment_output = Dense(3, activation='softmax', name='sentiment')(dropout1)
        emotion_output = Dense(6, activation='softmax', name='emotion')(dropout1)
        
        model = Model(inputs=inputs, outputs=[sentiment_output, emotion_output])
        
        model.compile(
            optimizer='adam',
            loss={'sentiment': 'sparse_categorical_crossentropy', 'emotion': 'sparse_categorical_crossentropy'},
            metrics={'sentiment': ['accuracy'], 'emotion': ['accuracy']},
            loss_weights={'sentiment': 1.0, 'emotion': 0.5}
        )
        
        return model
    
    def _build_cnn_model(self, embedding_dim: int) -> Model:
        """
        Build CNN model for sentiment analysis.
        
        Args:
            embedding_dim: Embedding dimension
            
        Returns:
            CNN model
        """
        inputs = Input(shape=(self.max_length,))
        
        # Embedding layer
        embedding = Embedding(self.vocab_size, embedding_dim, input_length=self.max_length)(inputs)
        
        # Convolutional layers with different filter sizes
        conv1 = tf.keras.layers.Conv1D(128, 3, activation='relu')(embedding)
        conv2 = tf.keras.layers.Conv1D(128, 4, activation='relu')(embedding)
        conv3 = tf.keras.layers.Conv1D(128, 5, activation='relu')(embedding)
        
        # Global max pooling
        pool1 = GlobalMaxPooling1D()(conv1)
        pool2 = GlobalMaxPooling1D()(conv2)
        pool3 = GlobalMaxPooling1D()(conv3)
        
        # Concatenate features
        concat = tf.keras.layers.concatenate([pool1, pool2, pool3])
        
        # Dense layers
        dense1 = Dense(128, activation='relu')(concat)
        dropout1 = Dropout(0.3)(dense1)
        
        # Output layers
        sentiment_output = Dense(3, activation='softmax', name='sentiment')(dropout1)
        emotion_output = Dense(6, activation='softmax', name='emotion')(dropout1)
        
        model = Model(inputs=inputs, outputs=[sentiment_output, emotion_output])
        
        model.compile(
            optimizer='adam',
            loss={'sentiment': 'sparse_categorical_crossentropy', 'emotion': 'sparse_categorical_crossentropy'},
            metrics={'sentiment': ['accuracy'], 'emotion': ['accuracy']},
            loss_weights={'sentiment': 1.0, 'emotion': 0.5}
        )
        
        return model
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Preprocess text data for sentiment analysis.
        
        Args:
            data: Raw text data
            
        Returns:
            Tuple of (features, (sentiment_targets, emotion_targets))
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Generate synthetic data if needed
        if 'text' not in df.columns:
            df = self._generate_synthetic_sentiment_data(len(df))
        
        # Clean and preprocess text
        df['cleaned_text'] = df['text'].apply(self._clean_text)
        
        # Fit tokenizer on cleaned text
        self.tokenizer.fit_on_texts(df['cleaned_text'])
        
        # Convert texts to sequences
        sequences = self.tokenizer.texts_to_sequences(df['cleaned_text'])
        X = pad_sequences(sequences, maxlen=self.max_length, padding='post', truncating='post')
        
        # Prepare targets
        if 'sentiment' in df.columns and 'emotion' in df.columns:
            sentiment_targets = self._encode_labels(df['sentiment'], self.sentiment_labels)
            emotion_targets = self._encode_labels(df['emotion'], self.emotion_labels)
        else:
            # Generate synthetic targets
            sentiment_targets = np.random.randint(0, 3, len(df))
            emotion_targets = np.random.randint(0, 6, len(df))
        
        return X, (sentiment_targets, emotion_targets)
    
    def _clean_text(self, text: str) -> str:
        """
        Clean and preprocess text.
        
        Args:
            text: Raw text
            
        Returns:
            Cleaned text
        """
        if not isinstance(text, str):
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove user mentions and hashtags
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove punctuation (but keep Arabic text intact)
        if self.arabic_pattern.search(text):
            # For Arabic text, only remove specific punctuation
            text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', '', text)
        else:
            # For English text, remove standard punctuation
            text = text.translate(str.maketrans('', '', string.punctuation))
        
        return text.strip()
    
    def _encode_labels(self, labels: pd.Series, label_list: List[str]) -> np.ndarray:
        """
        Encode string labels to integers.
        
        Args:
            labels: Label series
            label_list: List of possible labels
            
        Returns:
            Encoded labels
        """
        # Map labels to indices
        label_to_idx = {label: idx for idx, label in enumerate(label_list)}
        return np.array([label_to_idx.get(label, 0) for label in labels])
    
    def _generate_synthetic_sentiment_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """
        Generate synthetic sentiment data for training.
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Synthetic sentiment dataframe
        """
        np.random.seed(42)
        
        # Arabic and English text samples
        arabic_positive = [
            "هذا المنتج رائع ومفيد جداً",
            "أحب هذه الخدمة كثيراً",
            "تجربة ممتازة وأنصح بها",
            "جودة عالية وسعر مناسب",
            "خدمة عملاء متميزة"
        ]
        
        arabic_negative = [
            "المنتج سيء ولا أنصح به",
            "خدمة العملاء سيئة جداً",
            "لم أعجب بالمنتج",
            "مضيعة للوقت والمال",
            "تجربة محبطة"
        ]
        
        arabic_neutral = [
            "المنتج عادي لا بأس به",
            "لا رأي لي في هذا الموضوع",
            "يمكن أن يكون أفضل",
            "متوسط الجودة",
            "لا يوجد شيء مميز"
        ]
        
        english_positive = [
            "This product is amazing and very useful",
            "I love this service so much",
            "Excellent experience, highly recommend",
            "Great quality and reasonable price",
            "Outstanding customer service"
        ]
        
        english_negative = [
            "This product is terrible, don't recommend",
            "Very poor customer service",
            "Didn't like the product at all",
            "Waste of time and money",
            "Frustrating experience"
        ]
        
        english_neutral = [
            "The product is okay, nothing special",
            "No opinion on this matter",
            "Could be better",
            "Average quality",
            "Nothing remarkable"
        ]
        
        # Emotion mappings
        emotion_mapping = {
            'positive': ['joy', 'surprise'],
            'negative': ['anger', 'sadness', 'fear'],
            'neutral': ['neutral']
        }
        
        data = []
        for i in range(n_samples):
            # Choose sentiment
            sentiment = np.random.choice(['positive', 'negative', 'neutral'], p=[0.4, 0.3, 0.3])
            
            # Choose language
            language = np.random.choice(['arabic', 'english'], p=[0.5, 0.5])
            
            # Select text based on sentiment and language
            if sentiment == 'positive':
                text = np.random.choice(arabic_positive if language == 'arabic' else english_positive)
            elif sentiment == 'negative':
                text = np.random.choice(arabic_negative if language == 'arabic' else english_negative)
            else:
                text = np.random.choice(arabic_neutral if language == 'arabic' else english_neutral)
            
            # Choose emotion based on sentiment
            emotion = np.random.choice(emotion_mapping[sentiment])
            
            data.append({
                'text': text,
                'sentiment': sentiment,
                'emotion': emotion,
                'language': language
            })
        
        return pd.DataFrame(data)
    
    def analyze_sentiment(self, texts: List[str]) -> Dict[str, Any]:
        """
        Analyze sentiment for a list of texts.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            Sentiment analysis results
        """
        try:
            if not self.is_trained:
                # Use rule-based fallback
                return self._rule_based_sentiment_analysis(texts)
            
            # Clean texts
            cleaned_texts = [self._clean_text(text) for text in texts]
            
            # Convert to sequences
            sequences = self.tokenizer.texts_to_sequences(cleaned_texts)
            X = pad_sequences(sequences, maxlen=self.max_length, padding='post', truncating='post')
            
            # Make predictions
            predictions = self.model.predict(X, verbose=0)
            sentiment_probs = predictions[0]
            emotion_probs = predictions[1]
            
            # Process results
            results = []
            for i, text in enumerate(texts):
                sentiment_idx = np.argmax(sentiment_probs[i])
                emotion_idx = np.argmax(emotion_probs[i])
                
                result = {
                    "text": text,
                    "sentiment": self.sentiment_labels[sentiment_idx],
                    "sentiment_confidence": float(sentiment_probs[i][sentiment_idx]),
                    "sentiment_probabilities": {
                        label: float(prob) for label, prob in zip(self.sentiment_labels, sentiment_probs[i])
                    },
                    "emotion": self.emotion_labels[emotion_idx],
                    "emotion_confidence": float(emotion_probs[i][emotion_idx]),
                    "emotion_probabilities": {
                        label: float(prob) for label, prob in zip(self.emotion_labels, emotion_probs[i])
                    },
                    "language": self._detect_language(text),
                    "model_used": f"{self.model_type}_neural_network"
                }
                results.append(result)
            
            return {
                "success": True,
                "results": results,
                "model_name": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _rule_based_sentiment_analysis(self, texts: List[str]) -> Dict[str, Any]:
        """
        Rule-based sentiment analysis fallback.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            Rule-based sentiment results
        """
        # Simple keyword-based sentiment analysis
        positive_keywords_ar = ['رائع', 'ممتاز', 'جيد', 'أحب', 'مفيد', 'جميل', 'ممتع']
        negative_keywords_ar = ['سيء', 'سيئة', 'لا أحب', 'محبط', 'فظيع', 'مضيعة']
        
        positive_keywords_en = ['great', 'excellent', 'good', 'love', 'useful', 'beautiful', 'amazing']
        negative_keywords_en = ['bad', 'terrible', 'hate', 'frustrating', 'awful', 'waste']
        
        results = []
        for text in texts:
            text_lower = text.lower()
            language = self._detect_language(text)
            
            # Count positive and negative keywords
            if language == 'arabic':
                pos_count = sum(1 for word in positive_keywords_ar if word in text_lower)
                neg_count = sum(1 for word in negative_keywords_ar if word in text_lower)
            else:
                pos_count = sum(1 for word in positive_keywords_en if word in text_lower)
                neg_count = sum(1 for word in negative_keywords_en if word in text_lower)
            
            # Determine sentiment
            if pos_count > neg_count:
                sentiment = 'positive'
                confidence = 0.7
            elif neg_count > pos_count:
                sentiment = 'negative'
                confidence = 0.7
            else:
                sentiment = 'neutral'
                confidence = 0.5
            
            result = {
                "text": text,
                "sentiment": sentiment,
                "sentiment_confidence": confidence,
                "sentiment_probabilities": {
                    "positive": 0.6 if sentiment == 'positive' else 0.2,
                    "negative": 0.6 if sentiment == 'negative' else 0.2,
                    "neutral": 0.6 if sentiment == 'neutral' else 0.2
                },
                "emotion": "neutral",
                "emotion_confidence": 0.5,
                "language": language,
                "model_used": "rule_based"
            }
            results.append(result)
        
        return {
            "success": True,
            "results": results,
            "model_name": "rule_based_sentiment"
        }
    
    def _detect_language(self, text: str) -> str:
        """
        Detect if text is Arabic or English.
        
        Args:
            text: Input text
            
        Returns:
            Detected language ('arabic' or 'english')
        """
        arabic_chars = len(self.arabic_pattern.findall(text))
        total_chars = len([c for c in text if c.isalpha()])
        
        if total_chars == 0:
            return 'unknown'
        
        arabic_ratio = arabic_chars / total_chars
        return 'arabic' if arabic_ratio > 0.3 else 'english'
    
    def get_sentiment_insights(self, texts: List[str]) -> Dict[str, Any]:
        """
        Get comprehensive sentiment insights for a collection of texts.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            Sentiment insights and statistics
        """
        try:
            # Analyze all texts
            analysis_result = self.analyze_sentiment(texts)
            if not analysis_result["success"]:
                return analysis_result
            
            results = analysis_result["results"]
            
            # Calculate statistics
            sentiments = [r["sentiment"] for r in results]
            emotions = [r["emotion"] for r in results]
            languages = [r["language"] for r in results]
            
            sentiment_counts = {s: sentiments.count(s) for s in self.sentiment_labels}
            emotion_counts = {e: emotions.count(e) for e in self.emotion_labels}
            language_counts = {lang: languages.count(lang) for lang in set(languages)}
            
            # Calculate percentages
            total_texts = len(texts)
            sentiment_percentages = {s: (count/total_texts)*100 for s, count in sentiment_counts.items()}
            emotion_percentages = {e: (count/total_texts)*100 for e, count in emotion_counts.items()}
            
            # Overall sentiment score
            positive_score = sentiment_percentages.get('positive', 0)
            negative_score = sentiment_percentages.get('negative', 0)
            overall_score = positive_score - negative_score
            
            insights = {
                "total_texts": total_texts,
                "sentiment_distribution": sentiment_counts,
                "sentiment_percentages": sentiment_percentages,
                "emotion_distribution": emotion_counts,
                "emotion_percentages": emotion_percentages,
                "language_distribution": language_counts,
                "overall_sentiment_score": overall_score,
                "dominant_sentiment": max(sentiment_counts, key=sentiment_counts.get),
                "dominant_emotion": max(emotion_counts, key=emotion_counts.get),
                "recommendations": self._generate_sentiment_recommendations(sentiment_percentages, emotion_percentages)
            }
            
            return {
                "success": True,
                "insights": insights,
                "detailed_results": results
            }
            
        except Exception as e:
            logger.error(f"Sentiment insights generation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_sentiment_recommendations(self, 
                                          sentiment_percentages: Dict[str, float], 
                                          emotion_percentages: Dict[str, float]) -> List[str]:
        """
        Generate recommendations based on sentiment analysis.
        
        Args:
            sentiment_percentages: Sentiment distribution percentages
            emotion_percentages: Emotion distribution percentages
            
        Returns:
            List of recommendations
        """
        recommendations = []
        
        negative_pct = sentiment_percentages.get('negative', 0)
        positive_pct = sentiment_percentages.get('positive', 0)
        
        if negative_pct > 40:
            recommendations.append("High negative sentiment detected - consider addressing customer concerns")
        
        if positive_pct > 60:
            recommendations.append("Strong positive sentiment - leverage this in marketing campaigns")
        
        if emotion_percentages.get('anger', 0) > 20:
            recommendations.append("Significant anger detected - immediate customer service intervention recommended")
        
        if emotion_percentages.get('joy', 0) > 30:
            recommendations.append("High joy levels - consider amplifying positive experiences")
        
        if emotion_percentages.get('fear', 0) > 15:
            recommendations.append("Fear detected - provide reassurance and clear communication")
        
        return recommendations
