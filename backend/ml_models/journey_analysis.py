"""
Customer Journey Analysis Model for MarketMind
==============================================

This module implements customer journey analysis using Markov Chain and Hidden Markov Model algorithms.
It analyzes and predicts customer behavior across different touchpoints and stages.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from sklearn.preprocessing import LabelEncoder
import networkx as nx
from collections import defaultdict, Counter
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class CustomerJourneyModel(BaseMarketMindModel):
    """
    Customer Journey Analysis Model using Markov Chain and HMM.
    
    Features analyzed:
    - Customer touchpoint sequences
    - State transitions between journey stages
    - Conversion probabilities at each stage
    - Path optimization opportunities
    - Bottleneck identification
    """
    
    def __init__(self, model_type: str = "markov_chain"):
        """
        Initialize Customer Journey Model.
        
        Args:
            model_type: Model type ('markov_chain' or 'hmm')
        """
        super().__init__("customer_journey", "sequence_analysis")
        self.model_type = model_type
        self.label_encoder = LabelEncoder()
        
        # Journey components
        self.states = []
        self.transition_matrix = None
        self.initial_state_probs = None
        self.emission_matrix = None  # For HMM
        
        # Journey insights
        self.journey_paths = []
        self.conversion_rates = {}
        self.bottlenecks = []
        self.optimization_opportunities = []
        
        logger.info(f"Initialized Customer Journey Model with {model_type}")
    
    def build_model(self, **kwargs) -> Dict[str, Any]:
        """
        Build the customer journey model.
        
        Returns:
            Model configuration dictionary
        """
        return {
            "model_type": self.model_type,
            "states": self.states,
            "transition_matrix": self.transition_matrix,
            "initial_state_probs": self.initial_state_probs
        }
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[List[List[str]], np.ndarray]:
        """
        Preprocess customer journey data.
        
        Args:
            data: Raw journey data
            
        Returns:
            Tuple of (journey_sequences, dummy_targets)
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Generate synthetic data if needed
        if 'customer_id' not in df.columns or 'touchpoint' not in df.columns:
            df = self._generate_synthetic_journey_data(len(df))
        
        # Extract journey sequences
        journey_sequences = self._extract_journey_sequences(df)
        
        # Create dummy targets for compatibility
        dummy_targets = np.zeros(len(journey_sequences))
        
        return journey_sequences, dummy_targets
    
    def _extract_journey_sequences(self, df: pd.DataFrame) -> List[List[str]]:
        """
        Extract customer journey sequences from data.
        
        Args:
            df: Journey dataframe
            
        Returns:
            List of journey sequences
        """
        # Sort by customer and timestamp
        df = df.sort_values(['customer_id', 'timestamp']).reset_index(drop=True)
        
        # Group by customer and extract sequences
        sequences = []
        for customer_id, group in df.groupby('customer_id'):
            sequence = group['touchpoint'].tolist()
            if len(sequence) > 1:  # Only include sequences with multiple touchpoints
                sequences.append(sequence)
        
        return sequences
    
    def _generate_synthetic_journey_data(self, n_interactions: int = 1000) -> pd.DataFrame:
        """
        Generate synthetic customer journey data.
        
        Args:
            n_interactions: Number of interactions to generate
            
        Returns:
            Synthetic journey dataframe
        """
        np.random.seed(42)
        
        # Define touchpoints
        touchpoints = [
            'awareness_ad', 'awareness_social', 'awareness_search',
            'consideration_website', 'consideration_email', 'consideration_demo',
            'evaluation_comparison', 'evaluation_trial', 'evaluation_consultation',
            'purchase_cart', 'purchase_checkout', 'purchase_complete',
            'retention_support', 'retention_upsell', 'retention_renewal',
            'advocacy_review', 'advocacy_referral'
        ]
        
        # Define journey stages
        stages = {
            'awareness': ['awareness_ad', 'awareness_social', 'awareness_search'],
            'consideration': ['consideration_website', 'consideration_email', 'consideration_demo'],
            'evaluation': ['evaluation_comparison', 'evaluation_trial', 'evaluation_consultation'],
            'purchase': ['purchase_cart', 'purchase_checkout', 'purchase_complete'],
            'retention': ['retention_support', 'retention_upsell', 'retention_renewal'],
            'advocacy': ['advocacy_review', 'advocacy_referral']
        }
        
        # Generate customer journeys
        data = []
        customer_id = 1
        
        while len(data) < n_interactions:
            # Generate a journey for one customer
            journey_length = np.random.randint(3, 15)
            current_stage = 'awareness'
            
            for step in range(journey_length):
                # Select touchpoint from current stage
                if current_stage in stages:
                    touchpoint = np.random.choice(stages[current_stage])
                else:
                    touchpoint = np.random.choice(touchpoints)
                
                # Add interaction
                data.append({
                    'customer_id': f'CUST_{customer_id:06d}',
                    'touchpoint': touchpoint,
                    'timestamp': pd.Timestamp('2024-01-01') + pd.Timedelta(days=step),
                    'stage': current_stage,
                    'session_duration': np.random.uniform(30, 1800),  # 30 seconds to 30 minutes
                    'converted': 0
                })
                
                # Transition to next stage with some probability
                stage_transitions = {
                    'awareness': ['consideration', 'awareness'],
                    'consideration': ['evaluation', 'consideration', 'awareness'],
                    'evaluation': ['purchase', 'evaluation', 'consideration'],
                    'purchase': ['retention', 'purchase'],
                    'retention': ['advocacy', 'retention'],
                    'advocacy': ['advocacy']
                }
                
                if current_stage in stage_transitions:
                    next_stages = stage_transitions[current_stage]
                    transition_probs = [0.4, 0.4, 0.2] if len(next_stages) == 3 else [0.6, 0.4]
                    current_stage = np.random.choice(next_stages, p=transition_probs[:len(next_stages)])
                
                # Mark conversion if reached purchase
                if touchpoint == 'purchase_complete':
                    data[-1]['converted'] = 1
                    break
            
            customer_id += 1
        
        return pd.DataFrame(data[:n_interactions])
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.0, **kwargs) -> Dict[str, Any]:
        """
        Train the customer journey model.
        
        Args:
            data: Journey data
            validation_split: Not used for journey analysis
            **kwargs: Additional parameters
            
        Returns:
            Training results
        """
        try:
            logger.info("Starting customer journey analysis training")
            
            # Preprocess data
            journey_sequences, _ = self.preprocess_data(data)
            
            # Extract unique states
            all_touchpoints = set()
            for sequence in journey_sequences:
                all_touchpoints.update(sequence)
            self.states = sorted(list(all_touchpoints))
            
            # Build transition matrix
            self.transition_matrix = self._build_transition_matrix(journey_sequences)
            
            # Calculate initial state probabilities
            self.initial_state_probs = self._calculate_initial_state_probs(journey_sequences)
            
            # Analyze journey patterns
            self._analyze_journey_patterns(journey_sequences, data)
            
            # Calculate conversion rates
            self._calculate_conversion_rates(data)
            
            # Identify bottlenecks
            self._identify_bottlenecks()
            
            # Generate optimization opportunities
            self._generate_optimization_opportunities()
            
            self.is_trained = True
            self.model_metrics = {
                "n_states": len(self.states),
                "n_sequences": len(journey_sequences),
                "avg_sequence_length": np.mean([len(seq) for seq in journey_sequences]),
                "conversion_rate": self.conversion_rates.get('overall', 0.0)
            }
            
            self.save_model()
            
            logger.info("Customer journey analysis training completed")
            return {
                "success": True,
                "metrics": self.model_metrics,
                "states": self.states,
                "conversion_rates": self.conversion_rates,
                "bottlenecks": self.bottlenecks
            }
            
        except Exception as e:
            logger.error(f"Customer journey training failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_transition_matrix(self, sequences: List[List[str]]) -> np.ndarray:
        """
        Build state transition matrix from journey sequences.
        
        Args:
            sequences: List of journey sequences
            
        Returns:
            Transition probability matrix
        """
        n_states = len(self.states)
        state_to_idx = {state: i for i, state in enumerate(self.states)}
        
        # Count transitions
        transition_counts = np.zeros((n_states, n_states))
        
        for sequence in sequences:
            for i in range(len(sequence) - 1):
                from_state = state_to_idx[sequence[i]]
                to_state = state_to_idx[sequence[i + 1]]
                transition_counts[from_state, to_state] += 1
        
        # Convert to probabilities
        transition_matrix = np.zeros((n_states, n_states))
        for i in range(n_states):
            row_sum = transition_counts[i].sum()
            if row_sum > 0:
                transition_matrix[i] = transition_counts[i] / row_sum
        
        return transition_matrix
    
    def _calculate_initial_state_probs(self, sequences: List[List[str]]) -> np.ndarray:
        """
        Calculate initial state probabilities.
        
        Args:
            sequences: List of journey sequences
            
        Returns:
            Initial state probability vector
        """
        state_to_idx = {state: i for i, state in enumerate(self.states)}
        initial_counts = np.zeros(len(self.states))
        
        for sequence in sequences:
            if sequence:
                initial_state = state_to_idx[sequence[0]]
                initial_counts[initial_state] += 1
        
        # Convert to probabilities
        total_sequences = len(sequences)
        return initial_counts / total_sequences if total_sequences > 0 else initial_counts
    
    def _analyze_journey_patterns(self, sequences: List[List[str]], data: pd.DataFrame):
        """
        Analyze common journey patterns.
        
        Args:
            sequences: Journey sequences
            data: Original data
        """
        # Find most common paths
        path_counter = Counter()
        for sequence in sequences:
            if len(sequence) >= 2:
                path = ' -> '.join(sequence)
                path_counter[path] += 1
        
        # Store top paths
        self.journey_paths = [
            {"path": path, "frequency": count, "percentage": count/len(sequences)*100}
            for path, count in path_counter.most_common(10)
        ]
    
    def _calculate_conversion_rates(self, data: pd.DataFrame):
        """
        Calculate conversion rates by touchpoint and stage.
        
        Args:
            data: Journey data
        """
        if 'converted' not in data.columns:
            # Generate synthetic conversion data
            data['converted'] = np.random.choice([0, 1], len(data), p=[0.8, 0.2])
        
        # Overall conversion rate
        total_customers = data['customer_id'].nunique()
        converted_customers = data[data['converted'] == 1]['customer_id'].nunique()
        self.conversion_rates['overall'] = converted_customers / total_customers if total_customers > 0 else 0
        
        # Conversion rate by touchpoint
        touchpoint_conversions = data.groupby('touchpoint').agg({
            'customer_id': 'nunique',
            'converted': 'sum'
        }).reset_index()
        
        touchpoint_conversions['conversion_rate'] = (
            touchpoint_conversions['converted'] / touchpoint_conversions['customer_id']
        )
        
        self.conversion_rates['by_touchpoint'] = touchpoint_conversions.set_index('touchpoint')['conversion_rate'].to_dict()
        
        # Conversion rate by stage
        if 'stage' in data.columns:
            stage_conversions = data.groupby('stage').agg({
                'customer_id': 'nunique',
                'converted': 'sum'
            }).reset_index()
            
            stage_conversions['conversion_rate'] = (
                stage_conversions['converted'] / stage_conversions['customer_id']
            )
            
            self.conversion_rates['by_stage'] = stage_conversions.set_index('stage')['conversion_rate'].to_dict()
    
    def _identify_bottlenecks(self):
        """
        Identify bottlenecks in the customer journey.
        """
        if self.transition_matrix is None:
            return
        
        # Find states with low outgoing transition probabilities
        for i, state in enumerate(self.states):
            outgoing_probs = self.transition_matrix[i]
            max_outgoing = np.max(outgoing_probs)
            
            # If the highest outgoing probability is low, it might be a bottleneck
            if max_outgoing < 0.3 and np.sum(outgoing_probs) > 0:
                self.bottlenecks.append({
                    "touchpoint": state,
                    "type": "low_progression",
                    "max_transition_prob": float(max_outgoing),
                    "severity": "high" if max_outgoing < 0.2 else "medium"
                })
        
        # Find states with low conversion rates
        touchpoint_conversions = self.conversion_rates.get('by_touchpoint', {})
        for touchpoint, conversion_rate in touchpoint_conversions.items():
            if conversion_rate < 0.1:  # Less than 10% conversion
                self.bottlenecks.append({
                    "touchpoint": touchpoint,
                    "type": "low_conversion",
                    "conversion_rate": conversion_rate,
                    "severity": "high" if conversion_rate < 0.05 else "medium"
                })
    
    def _generate_optimization_opportunities(self):
        """
        Generate optimization opportunities based on analysis.
        """
        self.optimization_opportunities = []
        
        # Opportunities based on bottlenecks
        for bottleneck in self.bottlenecks:
            if bottleneck["type"] == "low_progression":
                self.optimization_opportunities.append({
                    "type": "improve_progression",
                    "touchpoint": bottleneck["touchpoint"],
                    "recommendation": f"Improve user experience at {bottleneck['touchpoint']} to increase progression rate",
                    "priority": "high" if bottleneck["severity"] == "high" else "medium"
                })
            elif bottleneck["type"] == "low_conversion":
                self.optimization_opportunities.append({
                    "type": "improve_conversion",
                    "touchpoint": bottleneck["touchpoint"],
                    "recommendation": f"Optimize {bottleneck['touchpoint']} to increase conversion rate",
                    "priority": "high" if bottleneck["severity"] == "high" else "medium"
                })
        
        # Opportunities based on successful paths
        if self.journey_paths:
            top_path = self.journey_paths[0]
            self.optimization_opportunities.append({
                "type": "replicate_success",
                "recommendation": f"Promote the successful journey pattern: {top_path['path']}",
                "priority": "medium"
            })
    
    def predict_next_touchpoint(self, current_sequence: List[str]) -> Dict[str, Any]:
        """
        Predict the next likely touchpoint in a customer journey.
        
        Args:
            current_sequence: Current customer journey sequence
            
        Returns:
            Next touchpoint predictions
        """
        try:
            if not self.is_trained or not current_sequence:
                return {"error": "Model not trained or empty sequence"}
            
            current_state = current_sequence[-1]
            if current_state not in self.states:
                return {"error": f"Unknown touchpoint: {current_state}"}
            
            state_idx = self.states.index(current_state)
            next_state_probs = self.transition_matrix[state_idx]
            
            # Get top 5 most likely next states
            top_indices = np.argsort(next_state_probs)[-5:][::-1]
            predictions = []
            
            for idx in top_indices:
                if next_state_probs[idx] > 0:
                    predictions.append({
                        "touchpoint": self.states[idx],
                        "probability": float(next_state_probs[idx]),
                        "confidence": "high" if next_state_probs[idx] > 0.3 else "medium" if next_state_probs[idx] > 0.1 else "low"
                    })
            
            return {
                "success": True,
                "current_touchpoint": current_state,
                "predictions": predictions,
                "sequence_length": len(current_sequence)
            }
            
        except Exception as e:
            logger.error(f"Next touchpoint prediction failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def analyze_journey_performance(self) -> Dict[str, Any]:
        """
        Analyze overall journey performance and provide insights.
        
        Returns:
            Journey performance analysis
        """
        if not self.is_trained:
            return {"error": "Model must be trained first"}
        
        analysis = {
            "journey_metrics": self.model_metrics,
            "conversion_analysis": self.conversion_rates,
            "common_paths": self.journey_paths,
            "bottlenecks": self.bottlenecks,
            "optimization_opportunities": self.optimization_opportunities,
            "state_analysis": self._analyze_state_importance(),
            "recommendations": self._generate_journey_recommendations()
        }
        
        return {
            "success": True,
            "analysis": analysis
        }
    
    def _analyze_state_importance(self) -> Dict[str, Any]:
        """
        Analyze the importance of each state in the journey.
        
        Returns:
            State importance analysis
        """
        if self.transition_matrix is None:
            return {}
        
        # Calculate centrality measures
        state_importance = {}
        
        for i, state in enumerate(self.states):
            # Incoming connections (how often state is reached)
            incoming = np.sum(self.transition_matrix[:, i])
            
            # Outgoing connections (how often state leads to others)
            outgoing = np.sum(self.transition_matrix[i, :])
            
            # Conversion rate for this state
            conversion_rate = self.conversion_rates.get('by_touchpoint', {}).get(state, 0)
            
            state_importance[state] = {
                "incoming_flow": float(incoming),
                "outgoing_flow": float(outgoing),
                "conversion_rate": float(conversion_rate),
                "importance_score": float(incoming + outgoing + conversion_rate * 2)
            }
        
        return state_importance
    
    def _generate_journey_recommendations(self) -> List[str]:
        """
        Generate high-level journey optimization recommendations.
        
        Returns:
            List of recommendations
        """
        recommendations = []
        
        # Based on conversion rates
        overall_conversion = self.conversion_rates.get('overall', 0)
        if overall_conversion < 0.1:
            recommendations.append("Overall conversion rate is low - consider comprehensive journey optimization")
        
        # Based on bottlenecks
        if len(self.bottlenecks) > 3:
            recommendations.append("Multiple bottlenecks detected - prioritize fixing high-severity issues first")
        
        # Based on journey length
        avg_length = self.model_metrics.get('avg_sequence_length', 0)
        if avg_length > 10:
            recommendations.append("Customer journeys are lengthy - consider streamlining the path to conversion")
        elif avg_length < 3:
            recommendations.append("Customer journeys are short - consider adding more engagement touchpoints")
        
        # Based on state diversity
        if len(self.states) < 5:
            recommendations.append("Limited touchpoint diversity - consider expanding customer engagement channels")
        
        return recommendations
