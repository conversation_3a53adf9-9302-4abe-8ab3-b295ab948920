"""
Customer Segmentation Model for MarketMind
==========================================

This module implements customer segmentation using K-Means and DBSCAN algorithms.
It analyzes customer behavior, demographics, and purchase patterns to create meaningful segments.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import matplotlib.pyplot as plt
import seaborn as sns
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class CustomerSegmentationModel(BaseMarketMindModel):
    """
    Customer Segmentation Model using K-Means and DBSCAN clustering.
    
    Features analyzed:
    - Customer demographics (age, gender, location)
    - Purchase behavior (frequency, monetary value, recency)
    - Engagement metrics (website visits, email opens, social media interaction)
    - Product preferences and categories
    """
    
    def __init__(self, algorithm: str = "kmeans", n_clusters: int = 5):
        """
        Initialize Customer Segmentation Model.
        
        Args:
            algorithm: Clustering algorithm ('kmeans' or 'dbscan')
            n_clusters: Number of clusters for K-Means
        """
        super().__init__("customer_segmentation", "clustering")
        self.algorithm = algorithm
        self.n_clusters = n_clusters
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.pca = None
        self.cluster_profiles = {}
        
        logger.info(f"Initialized Customer Segmentation Model with {algorithm}")
    
    def build_model(self, **kwargs) -> Any:
        """
        Build the clustering model.
        
        Returns:
            Configured clustering model
        """
        if self.algorithm == "kmeans":
            return KMeans(
                n_clusters=self.n_clusters,
                random_state=42,
                n_init=10,
                max_iter=300
            )
        elif self.algorithm == "dbscan":
            return DBSCAN(
                eps=kwargs.get('eps', 0.5),
                min_samples=kwargs.get('min_samples', 5),
                metric='euclidean'
            )
        else:
            raise ValueError(f"Unsupported algorithm: {self.algorithm}")
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess customer data for clustering.
        
        Args:
            data: Raw customer data
            
        Returns:
            Tuple of (features, dummy_targets)
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Define expected columns
        expected_columns = [
            'customer_id', 'age', 'gender', 'location', 'registration_date',
            'total_purchases', 'total_spent', 'avg_order_value', 'last_purchase_days',
            'website_visits', 'email_opens', 'social_media_engagement',
            'preferred_category', 'customer_lifetime_value'
        ]
        
        # Generate synthetic data if columns are missing
        if not all(col in df.columns for col in expected_columns):
            df = self._generate_synthetic_customer_data(len(df))
        
        # Feature engineering
        features_df = self._engineer_features(df)
        
        # Handle categorical variables
        categorical_columns = ['gender', 'location', 'preferred_category']
        for col in categorical_columns:
            if col in features_df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features_df[col] = self.label_encoders[col].fit_transform(features_df[col].astype(str))
                else:
                    features_df[col] = self.label_encoders[col].transform(features_df[col].astype(str))
        
        # Store feature names
        self.feature_names = features_df.columns.tolist()
        
        # Scale features
        X = self.scaler.fit_transform(features_df)
        
        # For clustering, we don't have true targets, so return dummy targets
        y = np.zeros(len(X))
        
        return X, y
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Engineer features for customer segmentation.
        
        Args:
            df: Raw customer dataframe
            
        Returns:
            Engineered features dataframe
        """
        features = pd.DataFrame()
        
        # Demographic features
        features['age'] = df['age']
        features['gender'] = df['gender']
        features['location'] = df['location']
        
        # RFM Analysis (Recency, Frequency, Monetary)
        features['recency'] = df['last_purchase_days']
        features['frequency'] = df['total_purchases']
        features['monetary'] = df['total_spent']
        
        # Behavioral features
        features['avg_order_value'] = df['avg_order_value']
        features['website_visits'] = df['website_visits']
        features['email_opens'] = df['email_opens']
        features['social_engagement'] = df['social_media_engagement']
        
        # Customer value features
        features['customer_lifetime_value'] = df['customer_lifetime_value']
        features['preferred_category'] = df['preferred_category']
        
        # Derived features
        features['purchase_frequency_score'] = np.where(
            features['frequency'] > features['frequency'].median(), 1, 0
        )
        features['high_value_customer'] = np.where(
            features['monetary'] > features['monetary'].quantile(0.8), 1, 0
        )
        features['engagement_score'] = (
            features['website_visits'] + 
            features['email_opens'] + 
            features['social_engagement']
        ) / 3
        
        return features
    
    def _generate_synthetic_customer_data(self, n_customers: int) -> pd.DataFrame:
        """
        Generate synthetic customer data for demonstration.
        
        Args:
            n_customers: Number of customers to generate
            
        Returns:
            Synthetic customer dataframe
        """
        np.random.seed(42)
        
        data = {
            'customer_id': [f'CUST_{i:06d}' for i in range(1, n_customers + 1)],
            'age': np.random.normal(35, 12, n_customers).astype(int),
            'gender': np.random.choice(['Male', 'Female', 'Other'], n_customers, p=[0.45, 0.45, 0.1]),
            'location': np.random.choice(['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina'], n_customers),
            'registration_date': pd.date_range('2020-01-01', periods=n_customers, freq='D'),
            'total_purchases': np.random.poisson(8, n_customers),
            'total_spent': np.random.lognormal(6, 1, n_customers),
            'last_purchase_days': np.random.exponential(30, n_customers).astype(int),
            'website_visits': np.random.poisson(15, n_customers),
            'email_opens': np.random.poisson(5, n_customers),
            'social_media_engagement': np.random.poisson(3, n_customers),
            'preferred_category': np.random.choice(
                ['Electronics', 'Fashion', 'Home', 'Sports', 'Books'], n_customers
            )
        }
        
        df = pd.DataFrame(data)
        
        # Calculate derived fields
        df['avg_order_value'] = df['total_spent'] / np.maximum(df['total_purchases'], 1)
        df['customer_lifetime_value'] = df['total_spent'] * (1 + df['total_purchases'] / 10)
        
        return df
    
    def train(self, data: pd.DataFrame, validation_split: float = 0.0, **kwargs) -> Dict[str, Any]:
        """
        Train the customer segmentation model.
        
        Args:
            data: Customer data
            validation_split: Not used for clustering
            **kwargs: Additional parameters
            
        Returns:
            Training results
        """
        try:
            logger.info("Starting customer segmentation training")
            
            # Preprocess data
            X, _ = self.preprocess_data(data)
            
            # Build model
            self.model = self.build_model(**kwargs)
            
            # Fit model
            cluster_labels = self.model.fit_predict(X)
            
            # Calculate clustering metrics
            if len(set(cluster_labels)) > 1:  # More than one cluster
                silhouette_avg = silhouette_score(X, cluster_labels)
                calinski_harabasz = calinski_harabasz_score(X, cluster_labels)
            else:
                silhouette_avg = 0
                calinski_harabasz = 0
            
            # Create cluster profiles
            self._create_cluster_profiles(data, cluster_labels)
            
            # Store results
            self.is_trained = True
            self.model_metrics = {
                "n_clusters": len(set(cluster_labels)),
                "silhouette_score": silhouette_avg,
                "calinski_harabasz_score": calinski_harabasz,
                "noise_points": np.sum(cluster_labels == -1) if self.algorithm == "dbscan" else 0
            }
            
            # Save model
            self.save_model()
            
            logger.info("Customer segmentation training completed")
            return {
                "success": True,
                "metrics": self.model_metrics,
                "cluster_profiles": self.cluster_profiles
            }
            
        except Exception as e:
            logger.error(f"Customer segmentation training failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict customer segments for new data.
        
        Args:
            data: Customer data to segment
            
        Returns:
            Segmentation results
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before prediction")
            
            # Preprocess data
            X, _ = self.preprocess_data(data)
            
            # Predict clusters
            if self.algorithm == "kmeans":
                cluster_labels = self.model.predict(X)
            else:  # DBSCAN
                # For DBSCAN, we need to use fit_predict on new data
                # or implement a method to assign new points to existing clusters
                cluster_labels = self.model.fit_predict(X)
            
            # Get cluster names/descriptions
            cluster_descriptions = []
            for label in cluster_labels:
                if label in self.cluster_profiles:
                    cluster_descriptions.append(self.cluster_profiles[label]['name'])
                else:
                    cluster_descriptions.append(f"Cluster_{label}")
            
            return {
                "success": True,
                "cluster_labels": cluster_labels.tolist(),
                "cluster_descriptions": cluster_descriptions,
                "cluster_profiles": self.cluster_profiles,
                "model_name": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Customer segmentation prediction failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_cluster_profiles(self, data: pd.DataFrame, cluster_labels: np.ndarray):
        """
        Create descriptive profiles for each cluster.
        
        Args:
            data: Original customer data
            cluster_labels: Cluster assignments
        """
        # Add cluster labels to data
        df = data.copy()
        df['cluster'] = cluster_labels
        
        # Define cluster names based on characteristics
        cluster_names = {
            0: "High-Value Loyalists",
            1: "Frequent Shoppers", 
            2: "Occasional Buyers",
            3: "New Customers",
            4: "At-Risk Customers"
        }
        
        self.cluster_profiles = {}
        
        for cluster_id in set(cluster_labels):
            if cluster_id == -1:  # Noise points in DBSCAN
                continue
                
            cluster_data = df[df['cluster'] == cluster_id]
            
            profile = {
                "cluster_id": int(cluster_id),
                "name": cluster_names.get(cluster_id, f"Cluster_{cluster_id}"),
                "size": len(cluster_data),
                "percentage": len(cluster_data) / len(df) * 100,
                "characteristics": {
                    "avg_age": float(cluster_data['age'].mean()) if 'age' in cluster_data.columns else 0,
                    "avg_total_spent": float(cluster_data['total_spent'].mean()) if 'total_spent' in cluster_data.columns else 0,
                    "avg_purchases": float(cluster_data['total_purchases'].mean()) if 'total_purchases' in cluster_data.columns else 0,
                    "avg_clv": float(cluster_data['customer_lifetime_value'].mean()) if 'customer_lifetime_value' in cluster_data.columns else 0
                },
                "top_locations": cluster_data['location'].value_counts().head(3).to_dict() if 'location' in cluster_data.columns else {},
                "gender_distribution": cluster_data['gender'].value_counts().to_dict() if 'gender' in cluster_data.columns else {}
            }
            
            self.cluster_profiles[cluster_id] = profile
    
    def get_cluster_insights(self) -> Dict[str, Any]:
        """
        Get detailed insights about customer segments.
        
        Returns:
            Cluster insights and recommendations
        """
        if not self.is_trained:
            return {"error": "Model must be trained first"}
        
        insights = {
            "total_clusters": len(self.cluster_profiles),
            "cluster_profiles": self.cluster_profiles,
            "recommendations": self._generate_marketing_recommendations(),
            "model_performance": self.model_metrics
        }
        
        return insights
    
    def _generate_marketing_recommendations(self) -> Dict[str, List[str]]:
        """
        Generate marketing recommendations for each cluster.
        
        Returns:
            Marketing recommendations by cluster
        """
        recommendations = {}
        
        for cluster_id, profile in self.cluster_profiles.items():
            cluster_name = profile['name']
            avg_spent = profile['characteristics']['avg_total_spent']
            avg_purchases = profile['characteristics']['avg_purchases']
            
            recs = []
            
            if "High-Value" in cluster_name:
                recs = [
                    "Offer exclusive VIP programs and early access to new products",
                    "Provide personalized customer service and dedicated account managers",
                    "Create premium product recommendations based on purchase history"
                ]
            elif "Frequent" in cluster_name:
                recs = [
                    "Implement loyalty rewards program with points and discounts",
                    "Send regular product updates and personalized offers",
                    "Encourage referrals with incentive programs"
                ]
            elif "Occasional" in cluster_name:
                recs = [
                    "Send targeted promotions during peak shopping seasons",
                    "Use email marketing to re-engage with special offers",
                    "Provide product recommendations based on browsing history"
                ]
            elif "New" in cluster_name:
                recs = [
                    "Send welcome series with onboarding content",
                    "Offer first-time buyer discounts and incentives",
                    "Provide educational content about products and services"
                ]
            elif "At-Risk" in cluster_name:
                recs = [
                    "Launch win-back campaigns with special discounts",
                    "Send surveys to understand reasons for decreased engagement",
                    "Offer personalized incentives to encourage return"
                ]
            
            recommendations[cluster_name] = recs
        
        return recommendations

    def visualize_clusters(self, data: pd.DataFrame, save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Create visualizations for customer segments.

        Args:
            data: Customer data
            save_path: Path to save visualizations

        Returns:
            Visualization results
        """
        try:
            if not self.is_trained:
                return {"error": "Model must be trained first"}

            # Get cluster predictions
            result = self.predict(data)
            if not result["success"]:
                return result

            cluster_labels = result["cluster_labels"]

            # Prepare data for visualization
            X, _ = self.preprocess_data(data)

            # Apply PCA for 2D visualization
            if self.pca is None:
                self.pca = PCA(n_components=2, random_state=42)
                X_pca = self.pca.fit_transform(X)
            else:
                X_pca = self.pca.transform(X)

            # Create visualization data
            viz_data = {
                "pca_x": X_pca[:, 0].tolist(),
                "pca_y": X_pca[:, 1].tolist(),
                "cluster_labels": cluster_labels,
                "cluster_colors": self._get_cluster_colors(cluster_labels)
            }

            return {
                "success": True,
                "visualization_data": viz_data,
                "cluster_profiles": self.cluster_profiles
            }

        except Exception as e:
            logger.error(f"Cluster visualization failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_cluster_colors(self, cluster_labels: List[int]) -> List[str]:
        """
        Get colors for cluster visualization.

        Args:
            cluster_labels: Cluster assignments

        Returns:
            List of color codes
        """
        color_map = {
            0: "#FF6B6B",  # Red
            1: "#4ECDC4",  # Teal
            2: "#45B7D1",  # Blue
            3: "#96CEB4",  # Green
            4: "#FFEAA7",  # Yellow
            -1: "#DDD"     # Gray for noise
        }

        return [color_map.get(label, "#DDD") for label in cluster_labels]
