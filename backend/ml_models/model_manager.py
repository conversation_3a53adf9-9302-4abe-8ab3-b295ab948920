"""
Model Manager for MarketMind AI Models
======================================

This module provides centralized management for all MarketMind AI models.
It handles model loading, training, prediction, and lifecycle management.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Type
import pandas as pd
import numpy as np

from .customer_segmentation import CustomerSegmentationModel
from .churn_prediction import ChurnPredictionModel
from .content_generation import ContentGenerationModel
from .campaign_optimization import CampaignOptimizationModel
from .sales_prediction import SalesPredictionModel
from .journey_analysis import CustomerJourneyModel
from .sentiment_analysis import SentimentAnalysisModel
from .customer_behavior import CustomerBehaviorModel
from .product_recommendation import ProductRecommendationModel
from .competitor_analysis import CompetitorAnalysisModel

logger = logging.getLogger(__name__)

class ModelManager:
    """
    Centralized manager for all MarketMind AI models.
    Provides unified interface for model operations and MLOps functionality.
    """
    
    def __init__(self):
        """Initialize the Model Manager."""
        self.models = {}
        self.model_registry = {}
        self.training_jobs = {}
        
        # Model class mappings
        self.model_classes = {
            'customer_segmentation': CustomerSegmentationModel,
            'churn_prediction': ChurnPredictionModel,
            'content_generation': ContentGenerationModel,
            'campaign_optimization': CampaignOptimizationModel,
            'sales_prediction': SalesPredictionModel,
            'customer_journey': CustomerJourneyModel,
            'sentiment_analysis': SentimentAnalysisModel,
            'customer_behavior': CustomerBehaviorModel,
            'product_recommendation': ProductRecommendationModel,
            'competitor_analysis': CompetitorAnalysisModel
        }
        
        # Initialize models
        self._initialize_models()
        
        logger.info("Model Manager initialized successfully")
    
    def _initialize_models(self):
        """Initialize all available models."""
        for model_name, model_class in self.model_classes.items():
            try:
                # Create model instance
                if model_name == 'customer_segmentation':
                    model = model_class(algorithm="kmeans", n_clusters=5)
                elif model_name == 'churn_prediction':
                    model = model_class(algorithm="random_forest")
                elif model_name == 'content_generation':
                    model = model_class(model_type="lstm")
                elif model_name == 'campaign_optimization':
                    model = model_class(optimization_method="multi_armed_bandit")
                elif model_name == 'sales_prediction':
                    model = model_class(model_type="lstm")
                elif model_name == 'customer_journey':
                    model = model_class(model_type="markov_chain")
                elif model_name == 'sentiment_analysis':
                    model = model_class(model_type="bilstm")
                elif model_name == 'customer_behavior':
                    model = model_class(algorithm="random_forest")
                elif model_name == 'product_recommendation':
                    model = model_class(algorithm="hybrid", n_recommendations=10)
                elif model_name == 'competitor_analysis':
                    model = model_class(analysis_type="comprehensive")
                else:
                    model = model_class()
                
                self.models[model_name] = model
                
                # Try to load existing trained model
                if model.load_model():
                    logger.info(f"Loaded existing model: {model_name}")
                else:
                    logger.info(f"Initialized new model: {model_name}")
                
                # Register model
                self._register_model(model_name, model)
                
            except Exception as e:
                logger.error(f"Failed to initialize model {model_name}: {str(e)}")
    
    def _register_model(self, model_name: str, model: Any):
        """
        Register a model in the model registry.
        
        Args:
            model_name: Name of the model
            model: Model instance
        """
        self.model_registry[model_name] = {
            "model_instance": model,
            "model_info": model.get_model_info(),
            "registered_at": datetime.now().isoformat(),
            "status": "active"
        }
    
    def get_model(self, model_name: str) -> Optional[Any]:
        """
        Get a model instance by name.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model instance or None if not found
        """
        return self.models.get(model_name)
    
    def list_models(self) -> Dict[str, Any]:
        """
        List all available models and their status.
        
        Returns:
            Dictionary of model information
        """
        model_list = {}
        
        for model_name, registry_info in self.model_registry.items():
            model_info = registry_info["model_info"]
            model_list[model_name] = {
                "model_type": model_info.get("model_type", "unknown"),
                "is_trained": model_info.get("is_trained", False),
                "last_trained": model_info.get("last_trained"),
                "model_version": model_info.get("model_version", "1.0.0"),
                "status": registry_info.get("status", "unknown"),
                "registered_at": registry_info.get("registered_at")
            }
        
        return model_list
    
    def train_model(self, model_name: str, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train a specific model.
        
        Args:
            model_name: Name of the model to train
            data: Training data
            **kwargs: Additional training parameters
            
        Returns:
            Training results
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            model = self.models[model_name]
            
            # Start training job
            job_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.training_jobs[job_id] = {
                "model_name": model_name,
                "status": "running",
                "started_at": datetime.now().isoformat(),
                "data_size": len(data)
            }
            
            logger.info(f"Starting training for {model_name} (Job ID: {job_id})")
            
            # Train the model
            result = model.train(data, **kwargs)
            
            # Update job status
            self.training_jobs[job_id].update({
                "status": "completed" if result.get("success") else "failed",
                "completed_at": datetime.now().isoformat(),
                "result": result
            })
            
            # Update model registry
            if result.get("success"):
                self._register_model(model_name, model)
                logger.info(f"Training completed successfully for {model_name}")
            else:
                logger.error(f"Training failed for {model_name}: {result.get('error')}")
            
            return {
                "success": result.get("success", False),
                "job_id": job_id,
                "model_name": model_name,
                "training_result": result
            }
            
        except Exception as e:
            logger.error(f"Training failed for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def predict(self, model_name: str, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Make predictions using a specific model.
        
        Args:
            model_name: Name of the model to use
            data: Input data for prediction
            **kwargs: Additional prediction parameters
            
        Returns:
            Prediction results
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            model = self.models[model_name]
            
            # Handle special prediction methods for new models
            if model_name == 'customer_behavior':
                result = model.predict_behavior(data)
            elif model_name == 'product_recommendation':
                user_id = kwargs.get('user_id')
                if user_id:
                    result = model.recommend_products(user_id, kwargs.get('n_recommendations', 10))
                else:
                    result = model.predict(data)
            elif model_name == 'competitor_analysis':
                competitor_name = kwargs.get('competitor_name', 'Unknown')
                competitor_data = kwargs.get('competitor_data', {})
                result = model.analyze_competitor(competitor_name, competitor_data)
            else:
                result = model.predict(data)
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def evaluate_model(self, model_name: str, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Evaluate a specific model.
        
        Args:
            model_name: Name of the model to evaluate
            test_data: Test dataset
            
        Returns:
            Evaluation results
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            model = self.models[model_name]
            
            # Evaluate the model
            result = model.evaluate(test_data)
            
            return result
            
        except Exception as e:
            logger.error(f"Evaluation failed for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_model_status(self, model_name: str) -> Dict[str, Any]:
        """
        Get status of a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model status information
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            model = self.models[model_name]
            model_info = model.get_model_info()
            
            return {
                "success": True,
                "model_name": model_name,
                "status": "active",
                "model_info": model_info,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get status for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_training_jobs(self) -> Dict[str, Any]:
        """
        Get all training jobs.
        
        Returns:
            Training jobs information
        """
        return {
            "success": True,
            "jobs": self.training_jobs,
            "total_jobs": len(self.training_jobs)
        }
    
    def retrain_model(self, model_name: str, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Retrain a specific model.
        
        Args:
            model_name: Name of the model to retrain
            data: Training data
            **kwargs: Additional training parameters
            
        Returns:
            Retraining results
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            logger.info(f"Starting retraining for {model_name}")
            
            # Retrain the model
            result = self.train_model(model_name, data, **kwargs)
            
            if result.get("success"):
                logger.info(f"Retraining completed successfully for {model_name}")
            else:
                logger.error(f"Retraining failed for {model_name}")
            
            return result
            
        except Exception as e:
            logger.error(f"Retraining failed for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def deploy_model(self, model_name: str, deployment_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Deploy a specific model.
        
        Args:
            model_name: Name of the model to deploy
            deployment_config: Deployment configuration
            
        Returns:
            Deployment results
        """
        try:
            if model_name not in self.models:
                return {
                    "success": False,
                    "error": f"Model {model_name} not found"
                }
            
            model = self.models[model_name]
            
            if not model.is_trained:
                return {
                    "success": False,
                    "error": f"Model {model_name} is not trained"
                }
            
            # Update deployment status
            self.model_registry[model_name]["status"] = "deployed"
            self.model_registry[model_name]["deployed_at"] = datetime.now().isoformat()
            
            logger.info(f"Model {model_name} deployed successfully")
            
            return {
                "success": True,
                "model_name": model_name,
                "deployment_status": "deployed",
                "deployed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Deployment failed for {model_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        Get overall system health.
        
        Returns:
            System health information
        """
        try:
            total_models = len(self.models)
            trained_models = sum(1 for model in self.models.values() if model.is_trained)
            active_models = sum(1 for info in self.model_registry.values() if info["status"] == "active")
            
            health_status = "healthy" if trained_models > 0 else "warning"
            
            return {
                "success": True,
                "system_status": health_status,
                "total_models": total_models,
                "trained_models": trained_models,
                "active_models": active_models,
                "training_jobs": len(self.training_jobs),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get system health: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_model_report(self, model_name: str = None) -> Dict[str, Any]:
        """
        Generate a comprehensive model report.
        
        Args:
            model_name: Specific model name (optional)
            
        Returns:
            Model report
        """
        try:
            if model_name:
                if model_name not in self.models:
                    return {
                        "success": False,
                        "error": f"Model {model_name} not found"
                    }
                
                model = self.models[model_name]
                model_info = model.get_model_info()
                
                report = {
                    "model_name": model_name,
                    "model_info": model_info,
                    "training_history": getattr(model, 'training_history', []),
                    "model_metrics": getattr(model, 'model_metrics', {}),
                    "generated_at": datetime.now().isoformat()
                }
            else:
                # Generate report for all models
                report = {
                    "all_models": self.list_models(),
                    "system_health": self.get_system_health(),
                    "training_jobs": self.get_training_jobs(),
                    "generated_at": datetime.now().isoformat()
                }
            
            return {
                "success": True,
                "report": report
            }
            
        except Exception as e:
            logger.error(f"Failed to generate model report: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
