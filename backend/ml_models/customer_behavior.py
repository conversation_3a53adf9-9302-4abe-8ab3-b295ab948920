"""
Customer Behavior Prediction Model for MarketMind AI
====================================================

This module provides advanced customer behavior prediction using classification and regression algorithms.
It predicts future actions such as purchase intent, engagement, and upsell likelihood.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Tuple
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, roc_auc_score
import logging

from .base_model import BaseMarketMindModel

logger = logging.getLogger(__name__)

class CustomerBehaviorModel(BaseMarketMindModel):
    """
    Predicts customer behavior such as purchase intent, engagement, and upsell likelihood.
    """
    def __init__(self, algorithm: str = "random_forest"):
        super().__init__("customer_behavior", "classification")
        self.algorithm = algorithm
        self.scaler = StandardScaler()
        self.label_encoders = {}
        logger.info(f"Initialized CustomerBehaviorModel with {algorithm}")

    def build_model(self, **kwargs) -> Any:
        if self.algorithm == "random_forest":
            return RandomForestClassifier(
                n_estimators=kwargs.get('n_estimators', 100),
                max_depth=kwargs.get('max_depth', 10),
                random_state=42,
                class_weight='balanced'
            )
        else:
            raise ValueError(f"Unsupported algorithm: {self.algorithm}")

    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        df = data.copy()
        # Example expected columns (customize as needed)
        expected_columns = [
            'customer_id', 'age', 'gender', 'tenure_months', 'engagement_score',
            'purchase_history', 'last_purchase_days', 'avg_purchase_value',
            'support_tickets', 'churned', 'future_purchase_intent'
        ]
        if not all(col in df.columns for col in expected_columns):
            df = self._generate_synthetic_behavior_data(len(df))
        # Feature engineering
        features = pd.DataFrame()
        features['age'] = df['age']
        features['tenure_months'] = df['tenure_months']
        features['engagement_score'] = df['engagement_score']
        features['avg_purchase_value'] = df['avg_purchase_value']
        features['last_purchase_days'] = df['last_purchase_days']
        features['support_tickets'] = df['support_tickets']
        features['purchase_history'] = df['purchase_history']
        features['gender'] = LabelEncoder().fit_transform(df['gender'].astype(str))
        # Scale features
        X = self.scaler.fit_transform(features)
        # Target: future_purchase_intent (1=likely, 0=not likely)
        y = df['future_purchase_intent'].astype(int).values
        return X, y

    def _generate_synthetic_behavior_data(self, n_customers: int) -> pd.DataFrame:
        np.random.seed(42)
        data = {
            'customer_id': [f'CUST_{i:06d}' for i in range(1, n_customers + 1)],
            'age': np.random.randint(18, 70, n_customers),
            'gender': np.random.choice(['Male', 'Female'], n_customers),
            'tenure_months': np.random.randint(1, 60, n_customers),
            'engagement_score': np.random.uniform(0, 1, n_customers),
            'purchase_history': np.random.randint(0, 20, n_customers),
            'last_purchase_days': np.random.randint(1, 365, n_customers),
            'avg_purchase_value': np.random.uniform(10, 500, n_customers),
            'support_tickets': np.random.randint(0, 5, n_customers),
            'churned': np.random.choice([0, 1], n_customers, p=[0.85, 0.15]),
        }
        df = pd.DataFrame(data)
        # Generate future purchase intent based on patterns
        intent_prob = (
            0.2 +
            0.3 * (df['engagement_score'] > 0.7).astype(int) +
            0.2 * (df['purchase_history'] > 5).astype(int) +
            -0.2 * (df['churned'] == 1).astype(int)
        )
        intent_prob = np.clip(intent_prob, 0, 1)
        df['future_purchase_intent'] = np.random.binomial(1, intent_prob)
        return df

    def train(self, data: pd.DataFrame, validation_split: float = 0.2, **kwargs) -> Dict[str, Any]:
        logger.info(f"Starting training for customer behavior model")
        X, y = self.preprocess_data(data)
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=validation_split, random_state=42)
        if self.model is None:
            self.model = self.build_model(**kwargs)
        self.model.fit(X_train, y_train)
        val_pred = self.model.predict(X_val)
        val_proba = self.model.predict_proba(X_val)[:, 1]
        report = classification_report(y_val, val_pred, output_dict=True)
        auc = roc_auc_score(y_val, val_proba)
        self.is_trained = True
        self.last_trained = pd.Timestamp.now()
        self.model_metrics = {'classification_report': report, 'roc_auc': auc}
        self.save_model()
        logger.info(f"Training completed for customer behavior model")
        return {'success': True, 'metrics': self.model_metrics}

    def predict_behavior(self, data: pd.DataFrame) -> Dict[str, Any]:
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        X, _ = self.preprocess_data(data)
        predictions = self.model.predict(X)
        probabilities = self.model.predict_proba(X)[:, 1]
        return {
            'success': True,
            'predictions': predictions.tolist(),
            'probabilities': probabilities.tolist(),
            'model_name': self.model_name
        }

    def get_model_info(self) -> Dict[str, Any]:
        return {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'algorithm': self.algorithm,
            'is_trained': self.is_trained,
            'last_trained': self.last_trained.isoformat() if self.last_trained is not None else None,
            'model_version': self.model_version
        } 