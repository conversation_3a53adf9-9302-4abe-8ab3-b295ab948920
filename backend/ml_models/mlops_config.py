"""
MLOps Configuration for MarketMind AI Models
============================================

This module provides MLOps configuration and utilities for model lifecycle management.
It includes model versioning, monitoring, and automated retraining capabilities.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ModelStatus(Enum):
    """Model status enumeration."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    DEPRECATED = "deprecated"
    FAILED = "failed"

class TrainingStatus(Enum):
    """Training status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ModelConfig:
    """Model configuration dataclass."""
    model_name: str
    model_type: str
    version: str
    status: ModelStatus
    created_at: datetime
    updated_at: datetime
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    data_requirements: Dict[str, Any]
    deployment_config: Dict[str, Any]

@dataclass
class TrainingJob:
    """Training job dataclass."""
    job_id: str
    model_name: str
    status: TrainingStatus
    started_at: datetime
    completed_at: Optional[datetime]
    data_size: int
    parameters: Dict[str, Any]
    metrics: Dict[str, float]
    error_message: Optional[str]

class MLOpsConfig:
    """
    MLOps configuration and management class.
    Handles model lifecycle, versioning, and monitoring.
    """
    
    def __init__(self, config_path: str = "backend/ml_models/mlops_config.json"):
        """
        Initialize MLOps configuration.
        
        Args:
            config_path: Path to MLOps configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.models_registry = {}
        self.training_jobs = {}
        self.monitoring_metrics = {}
        
        # Create necessary directories
        self._create_directories()
        
        logger.info("MLOps configuration initialized")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load MLOps configuration from file.
        
        Returns:
            Configuration dictionary
        """
        default_config = {
            "model_registry": {
                "base_path": "backend/ml_models/registry",
                "versioning": {
                    "strategy": "semantic",  # semantic, timestamp, incremental
                    "auto_increment": True
                }
            },
            "training": {
                "max_concurrent_jobs": 3,
                "default_timeout": 3600,  # 1 hour
                "auto_retry": True,
                "max_retries": 2
            },
            "monitoring": {
                "performance_threshold": {
                    "accuracy_drop": 0.05,
                    "latency_increase": 2.0
                },
                "data_drift_detection": True,
                "model_drift_detection": True
            },
            "deployment": {
                "staging_required": True,
                "approval_required": True,
                "rollback_enabled": True
            },
            "data_management": {
                "retention_days": 90,
                "backup_enabled": True,
                "encryption_enabled": True
            },
            "alerts": {
                "email_notifications": True,
                "slack_notifications": False,
                "webhook_url": None
            }
        }
        
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                # Merge with defaults
                default_config.update(loaded_config)
            except Exception as e:
                logger.warning(f"Failed to load config from {self.config_path}: {e}")
        
        return default_config
    
    def _create_directories(self):
        """Create necessary directories for MLOps."""
        directories = [
            self.config["model_registry"]["base_path"],
            "backend/ml_models/logs",
            "backend/ml_models/data",
            "backend/ml_models/artifacts",
            "backend/ml_models/monitoring"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2, default=str)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def register_model(self, model_config: ModelConfig) -> bool:
        """
        Register a new model in the registry.
        
        Args:
            model_config: Model configuration
            
        Returns:
            True if registration successful
        """
        try:
            model_id = f"{model_config.model_name}_{model_config.version}"
            
            # Create model registry entry
            registry_entry = {
                "model_name": model_config.model_name,
                "model_type": model_config.model_type,
                "version": model_config.version,
                "status": model_config.status.value,
                "created_at": model_config.created_at.isoformat(),
                "updated_at": model_config.updated_at.isoformat(),
                "parameters": model_config.parameters,
                "performance_metrics": model_config.performance_metrics,
                "data_requirements": model_config.data_requirements,
                "deployment_config": model_config.deployment_config
            }
            
            # Save to registry
            registry_path = os.path.join(
                self.config["model_registry"]["base_path"],
                f"{model_id}.json"
            )
            
            with open(registry_path, 'w') as f:
                json.dump(registry_entry, f, indent=2)
            
            self.models_registry[model_id] = registry_entry
            
            logger.info(f"Model {model_id} registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register model {model_config.model_name}: {e}")
            return False
    
    def get_model_versions(self, model_name: str) -> List[str]:
        """
        Get all versions of a specific model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            List of version strings
        """
        versions = []
        registry_path = self.config["model_registry"]["base_path"]
        
        if os.path.exists(registry_path):
            for filename in os.listdir(registry_path):
                if filename.startswith(f"{model_name}_") and filename.endswith(".json"):
                    version = filename.replace(f"{model_name}_", "").replace(".json", "")
                    versions.append(version)
        
        return sorted(versions, reverse=True)  # Latest first
    
    def get_latest_model_version(self, model_name: str) -> Optional[str]:
        """
        Get the latest version of a model.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Latest version string or None
        """
        versions = self.get_model_versions(model_name)
        return versions[0] if versions else None
    
    def create_training_job(self, 
                          model_name: str, 
                          parameters: Dict[str, Any],
                          data_size: int) -> TrainingJob:
        """
        Create a new training job.
        
        Args:
            model_name: Name of the model to train
            parameters: Training parameters
            data_size: Size of training data
            
        Returns:
            Training job instance
        """
        job_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        job = TrainingJob(
            job_id=job_id,
            model_name=model_name,
            status=TrainingStatus.PENDING,
            started_at=datetime.now(),
            completed_at=None,
            data_size=data_size,
            parameters=parameters,
            metrics={},
            error_message=None
        )
        
        self.training_jobs[job_id] = job
        return job
    
    def update_training_job(self, 
                          job_id: str, 
                          status: TrainingStatus,
                          metrics: Optional[Dict[str, float]] = None,
                          error_message: Optional[str] = None):
        """
        Update training job status and metrics.
        
        Args:
            job_id: Training job ID
            status: New status
            metrics: Performance metrics
            error_message: Error message if failed
        """
        if job_id in self.training_jobs:
            job = self.training_jobs[job_id]
            job.status = status
            
            if status in [TrainingStatus.COMPLETED, TrainingStatus.FAILED, TrainingStatus.CANCELLED]:
                job.completed_at = datetime.now()
            
            if metrics:
                job.metrics.update(metrics)
            
            if error_message:
                job.error_message = error_message
            
            logger.info(f"Training job {job_id} updated to status: {status.value}")
    
    def get_training_jobs(self, model_name: Optional[str] = None) -> List[TrainingJob]:
        """
        Get training jobs, optionally filtered by model name.
        
        Args:
            model_name: Optional model name filter
            
        Returns:
            List of training jobs
        """
        jobs = list(self.training_jobs.values())
        
        if model_name:
            jobs = [job for job in jobs if job.model_name == model_name]
        
        return sorted(jobs, key=lambda x: x.started_at, reverse=True)
    
    def check_model_performance(self, 
                              model_name: str, 
                              current_metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        Check if model performance has degraded.
        
        Args:
            model_name: Name of the model
            current_metrics: Current performance metrics
            
        Returns:
            Performance check results
        """
        # Get baseline metrics from latest model version
        latest_version = self.get_latest_model_version(model_name)
        if not latest_version:
            return {"status": "no_baseline", "message": "No baseline metrics available"}
        
        model_id = f"{model_name}_{latest_version}"
        if model_id not in self.models_registry:
            return {"status": "no_baseline", "message": "Baseline metrics not found"}
        
        baseline_metrics = self.models_registry[model_id]["performance_metrics"]
        threshold_config = self.config["monitoring"]["performance_threshold"]
        
        alerts = []
        
        # Check accuracy drop
        if "accuracy" in baseline_metrics and "accuracy" in current_metrics:
            accuracy_drop = baseline_metrics["accuracy"] - current_metrics["accuracy"]
            if accuracy_drop > threshold_config["accuracy_drop"]:
                alerts.append({
                    "type": "accuracy_degradation",
                    "message": f"Accuracy dropped by {accuracy_drop:.3f}",
                    "severity": "high"
                })
        
        # Check other metrics
        for metric in ["precision", "recall", "f1_score"]:
            if metric in baseline_metrics and metric in current_metrics:
                drop = baseline_metrics[metric] - current_metrics[metric]
                if drop > threshold_config["accuracy_drop"]:
                    alerts.append({
                        "type": f"{metric}_degradation",
                        "message": f"{metric.title()} dropped by {drop:.3f}",
                        "severity": "medium"
                    })
        
        return {
            "status": "degraded" if alerts else "healthy",
            "alerts": alerts,
            "baseline_metrics": baseline_metrics,
            "current_metrics": current_metrics
        }
    
    def should_retrain_model(self, model_name: str) -> Dict[str, Any]:
        """
        Determine if a model should be retrained.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Retraining recommendation
        """
        reasons = []
        
        # Check last training date
        latest_version = self.get_latest_model_version(model_name)
        if latest_version:
            model_id = f"{model_name}_{latest_version}"
            if model_id in self.models_registry:
                last_trained = datetime.fromisoformat(
                    self.models_registry[model_id]["updated_at"]
                )
                days_since_training = (datetime.now() - last_trained).days
                
                if days_since_training > 30:  # Retrain every 30 days
                    reasons.append(f"Model is {days_since_training} days old")
        
        # Check recent training job failures
        recent_jobs = self.get_training_jobs(model_name)
        if recent_jobs:
            failed_jobs = [job for job in recent_jobs[:5] if job.status == TrainingStatus.FAILED]
            if len(failed_jobs) >= 3:
                reasons.append("Multiple recent training failures")
        
        # Check performance degradation
        # This would require current performance metrics to be provided
        
        return {
            "should_retrain": len(reasons) > 0,
            "reasons": reasons,
            "priority": "high" if len(reasons) > 2 else "medium" if reasons else "low"
        }
    
    def get_deployment_readiness(self, model_name: str, version: str) -> Dict[str, Any]:
        """
        Check if a model version is ready for deployment.
        
        Args:
            model_name: Name of the model
            version: Model version
            
        Returns:
            Deployment readiness assessment
        """
        model_id = f"{model_name}_{version}"
        
        if model_id not in self.models_registry:
            return {
                "ready": False,
                "reason": "Model not found in registry"
            }
        
        model_info = self.models_registry[model_id]
        checks = []
        
        # Check if model has performance metrics
        if not model_info["performance_metrics"]:
            checks.append("No performance metrics available")
        
        # Check if model status is appropriate
        if model_info["status"] not in ["testing", "staging"]:
            checks.append(f"Model status is {model_info['status']}, should be testing or staging")
        
        # Check if deployment config exists
        if not model_info["deployment_config"]:
            checks.append("No deployment configuration available")
        
        # Check if staging is required and completed
        if self.config["deployment"]["staging_required"]:
            if model_info["status"] != "staging":
                checks.append("Staging deployment required but not completed")
        
        return {
            "ready": len(checks) == 0,
            "checks_failed": checks,
            "model_info": model_info
        }
    
    def generate_mlops_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive MLOps report.
        
        Returns:
            MLOps status report
        """
        # Model statistics
        total_models = len(self.models_registry)
        status_counts = {}
        for model_info in self.models_registry.values():
            status = model_info["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Training job statistics
        total_jobs = len(self.training_jobs)
        job_status_counts = {}
        for job in self.training_jobs.values():
            status = job.status.value
            job_status_counts[status] = job_status_counts.get(status, 0) + 1
        
        # Recent activity
        recent_jobs = [job for job in self.training_jobs.values() 
                      if job.started_at > datetime.now() - timedelta(days=7)]
        
        return {
            "generated_at": datetime.now().isoformat(),
            "model_statistics": {
                "total_models": total_models,
                "status_distribution": status_counts
            },
            "training_statistics": {
                "total_jobs": total_jobs,
                "status_distribution": job_status_counts,
                "recent_jobs": len(recent_jobs)
            },
            "system_health": {
                "config_status": "healthy",
                "registry_status": "healthy",
                "monitoring_status": "active"
            },
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """
        Generate MLOps recommendations based on current state.
        
        Returns:
            List of recommendations
        """
        recommendations = []
        
        # Check for models that need retraining
        for model_name in set(info["model_name"] for info in self.models_registry.values()):
            retrain_check = self.should_retrain_model(model_name)
            if retrain_check["should_retrain"]:
                recommendations.append(f"Consider retraining {model_name}: {', '.join(retrain_check['reasons'])}")
        
        # Check for failed training jobs
        failed_jobs = [job for job in self.training_jobs.values() if job.status == TrainingStatus.FAILED]
        if len(failed_jobs) > 0:
            recommendations.append(f"Investigate {len(failed_jobs)} failed training jobs")
        
        # Check for models in development too long
        old_dev_models = []
        for model_info in self.models_registry.values():
            if model_info["status"] == "development":
                created_at = datetime.fromisoformat(model_info["created_at"])
                if (datetime.now() - created_at).days > 14:
                    old_dev_models.append(model_info["model_name"])
        
        if old_dev_models:
            recommendations.append(f"Models in development for >14 days: {', '.join(old_dev_models)}")
        
        return recommendations
