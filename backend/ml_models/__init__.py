"""
MarketMind AI Models Package
============================

This package contains all custom AI models for MarketMind platform.
All models are trained and run locally without external dependencies.

Models included:
1. Customer Segmentation (K-Means, DBSCAN)
2. Churn Prediction (Random Forest, XGBoost)
3. Personalized Content Generation (LSTM, Transformer)
4. Campaign Optimization (Reinforcement Learning)
5. Sales Prediction (LSTM, Prophet)
6. Customer Journey Analysis (Markov Chain, HMM)
7. Sentiment Analysis (BERT, BiLSTM)
8. Customer Behavior Prediction (Random Forest, XGBoost)
9. Product Recommendation (Collaborative Filtering, Content-Based, Hybrid)
10. Competitor Analysis (Sentiment, Pricing, Market Position)
"""

from .customer_segmentation import CustomerSegmentationModel
from .churn_prediction import ChurnPredictionModel
from .content_generation import ContentGenerationModel
from .campaign_optimization import CampaignOptimizationModel
from .sales_prediction import SalesPredictionModel
from .journey_analysis import CustomerJourneyModel
from .sentiment_analysis import SentimentAnalysisModel
from .customer_behavior import CustomerBehaviorModel
from .product_recommendation import ProductRecommendationModel
from .competitor_analysis import CompetitorAnalysisModel
from .model_manager import ModelManager

__all__ = [
    'CustomerSegmentationModel',
    'ChurnPredictionModel', 
    'ContentGenerationModel',
    'CampaignOptimizationModel',
    'SalesPredictionModel',
    'CustomerJourneyModel',
    'SentimentAnalysisModel',
    'CustomerBehaviorModel',
    'ProductRecommendationModel',
    'CompetitorAnalysisModel',
    'ModelManager'
]

__version__ = "1.1.0"
__author__ = "MarketMind AI Team"
