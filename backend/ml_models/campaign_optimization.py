"""
Campaign Optimization Model for MarketMind
==========================================

This module implements campaign optimization using Reinforcement Learning and Multi-Armed Bandit algorithms.
It optimizes marketing campaigns for maximum ROI and engagement.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import cross_val_score
import scipy.stats as stats
from .base_model import BaseMarketMindModel
import logging

logger = logging.getLogger(__name__)

class CampaignOptimizationModel(BaseMarketMindModel):
    """
    Campaign Optimization Model using Multi-Armed Bandit and Reinforcement Learning.
    
    Features optimized:
    - Budget allocation across channels
    - Audience targeting parameters
    - Creative content selection
    - Bidding strategies
    - Timing optimization
    """
    
    def __init__(self, optimization_method: str = "multi_armed_bandit"):
        """
        Initialize Campaign Optimization Model.
        
        Args:
            optimization_method: Optimization algorithm ('multi_armed_bandit', 'thompson_sampling', 'ucb')
        """
        super().__init__("campaign_optimization", "optimization")
        self.optimization_method = optimization_method
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
        # Bandit algorithm parameters
        self.arms = {}  # Campaign variants
        self.arm_rewards = {}  # Reward history for each arm
        self.arm_counts = {}  # Number of times each arm was selected
        self.exploration_rate = 0.1
        
        # Campaign performance metrics
        self.performance_history = []
        self.optimization_recommendations = {}
        
        logger.info(f"Initialized Campaign Optimization Model with {optimization_method}")
    
    def build_model(self, **kwargs) -> Any:
        """
        Build the campaign optimization model.
        
        Returns:
            Configured optimization model
        """
        # For campaign optimization, we use ensemble methods for prediction
        return RandomForestRegressor(
            n_estimators=kwargs.get('n_estimators', 100),
            max_depth=kwargs.get('max_depth', 10),
            min_samples_split=kwargs.get('min_samples_split', 5),
            random_state=42
        )
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess campaign data for optimization.
        
        Args:
            data: Raw campaign data
            
        Returns:
            Tuple of (features, performance_targets)
        """
        # Create a copy to avoid modifying original data
        df = data.copy()
        
        # Define expected columns
        expected_columns = [
            'campaign_id', 'channel', 'budget', 'target_audience', 'creative_type',
            'bid_strategy', 'start_date', 'end_date', 'impressions', 'clicks',
            'conversions', 'cost', 'revenue', 'roi'
        ]
        
        # Generate synthetic data if columns are missing
        if not all(col in df.columns for col in expected_columns):
            df = self._generate_synthetic_campaign_data(len(df))
        
        # Feature engineering
        features_df = self._engineer_features(df)
        
        # Handle categorical variables
        categorical_columns = ['channel', 'target_audience', 'creative_type', 'bid_strategy']
        
        for col in categorical_columns:
            if col in features_df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features_df[col] = self.label_encoders[col].fit_transform(features_df[col].astype(str))
                else:
                    features_df[col] = self.label_encoders[col].transform(features_df[col].astype(str))
        
        # Store feature names
        self.feature_names = features_df.columns.tolist()
        
        # Scale features
        X = self.scaler.fit_transform(features_df)
        
        # Extract target variable (ROI)
        y = df['roi'].values if 'roi' in df.columns else np.random.uniform(0.5, 3.0, len(X))
        
        return X, y
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Engineer features for campaign optimization.
        
        Args:
            df: Raw campaign dataframe
            
        Returns:
            Engineered features dataframe
        """
        features = pd.DataFrame()
        
        # Basic campaign features
        features['budget'] = df['budget']
        features['channel'] = df['channel']
        features['target_audience'] = df['target_audience']
        features['creative_type'] = df['creative_type']
        features['bid_strategy'] = df['bid_strategy']
        
        # Performance metrics
        features['impressions'] = df['impressions']
        features['clicks'] = df['clicks']
        features['conversions'] = df['conversions']
        features['cost'] = df['cost']
        
        # Derived metrics
        features['ctr'] = df['clicks'] / np.maximum(df['impressions'], 1)  # Click-through rate
        features['cvr'] = df['conversions'] / np.maximum(df['clicks'], 1)  # Conversion rate
        features['cpc'] = df['cost'] / np.maximum(df['clicks'], 1)  # Cost per click
        features['cpa'] = df['cost'] / np.maximum(df['conversions'], 1)  # Cost per acquisition
        
        # Budget efficiency metrics
        features['budget_utilization'] = df['cost'] / df['budget']
        features['cost_per_impression'] = df['cost'] / np.maximum(df['impressions'], 1)
        
        # Time-based features
        if 'start_date' in df.columns and 'end_date' in df.columns:
            df['start_date'] = pd.to_datetime(df['start_date'])
            df['end_date'] = pd.to_datetime(df['end_date'])
            features['campaign_duration'] = (df['end_date'] - df['start_date']).dt.days
            features['day_of_week'] = df['start_date'].dt.dayofweek
            features['month'] = df['start_date'].dt.month
        else:
            features['campaign_duration'] = 7  # Default duration
            features['day_of_week'] = 1
            features['month'] = 1
        
        # Performance categories
        features['high_ctr'] = (features['ctr'] > features['ctr'].median()).astype(int)
        features['high_cvr'] = (features['cvr'] > features['cvr'].median()).astype(int)
        features['efficient_cost'] = (features['cpc'] < features['cpc'].median()).astype(int)
        
        return features
    
    def _generate_synthetic_campaign_data(self, n_campaigns: int) -> pd.DataFrame:
        """
        Generate synthetic campaign data for demonstration.
        
        Args:
            n_campaigns: Number of campaigns to generate
            
        Returns:
            Synthetic campaign dataframe
        """
        np.random.seed(42)
        
        channels = ['Google Ads', 'Facebook', 'Instagram', 'LinkedIn', 'Twitter', 'YouTube']
        audiences = ['Young Adults', 'Professionals', 'Families', 'Seniors', 'Students']
        creative_types = ['Image', 'Video', 'Carousel', 'Text', 'Interactive']
        bid_strategies = ['CPC', 'CPM', 'CPA', 'ROAS']
        
        data = {
            'campaign_id': [f'CAMP_{i:06d}' for i in range(1, n_campaigns + 1)],
            'channel': np.random.choice(channels, n_campaigns),
            'budget': np.random.uniform(1000, 50000, n_campaigns),
            'target_audience': np.random.choice(audiences, n_campaigns),
            'creative_type': np.random.choice(creative_types, n_campaigns),
            'bid_strategy': np.random.choice(bid_strategies, n_campaigns),
            'start_date': pd.date_range('2024-01-01', periods=n_campaigns, freq='D'),
            'end_date': pd.date_range('2024-01-08', periods=n_campaigns, freq='D')
        }
        
        df = pd.DataFrame(data)
        
        # Generate performance metrics based on realistic patterns
        base_impressions = np.random.lognormal(10, 1, n_campaigns)
        
        # Channel-specific performance modifiers
        channel_modifiers = {
            'Google Ads': {'ctr': 1.2, 'cvr': 1.1, 'cost_mod': 1.0},
            'Facebook': {'ctr': 1.0, 'cvr': 1.0, 'cost_mod': 0.8},
            'Instagram': {'ctr': 0.8, 'cvr': 0.9, 'cost_mod': 0.9},
            'LinkedIn': {'ctr': 0.6, 'cvr': 1.3, 'cost_mod': 1.5},
            'Twitter': {'ctr': 0.7, 'cvr': 0.8, 'cost_mod': 0.7},
            'YouTube': {'ctr': 1.1, 'cvr': 1.2, 'cost_mod': 1.2}
        }
        
        impressions = []
        clicks = []
        conversions = []
        costs = []
        revenues = []
        rois = []
        
        for i, row in df.iterrows():
            channel = row['channel']
            budget = row['budget']
            
            # Get channel modifiers
            mods = channel_modifiers.get(channel, {'ctr': 1.0, 'cvr': 1.0, 'cost_mod': 1.0})
            
            # Calculate metrics
            impr = int(base_impressions[i])
            ctr = np.random.uniform(0.01, 0.05) * mods['ctr']
            click = int(impr * ctr)
            cvr = np.random.uniform(0.02, 0.10) * mods['cvr']
            conv = int(click * cvr)
            
            # Cost calculation
            cpc = np.random.uniform(0.5, 5.0) * mods['cost_mod']
            cost = min(click * cpc, budget)  # Don't exceed budget
            
            # Revenue calculation
            avg_order_value = np.random.uniform(50, 500)
            revenue = conv * avg_order_value
            
            # ROI calculation
            roi = revenue / cost if cost > 0 else 0
            
            impressions.append(impr)
            clicks.append(click)
            conversions.append(conv)
            costs.append(cost)
            revenues.append(revenue)
            rois.append(roi)
        
        df['impressions'] = impressions
        df['clicks'] = clicks
        df['conversions'] = conversions
        df['cost'] = costs
        df['revenue'] = revenues
        df['roi'] = rois
        
        return df
    
    def optimize_campaign(self, campaign_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize a campaign using the trained model and bandit algorithms.
        
        Args:
            campaign_data: Campaign configuration and constraints
            
        Returns:
            Optimization recommendations
        """
        try:
            # Initialize campaign arms if not exists
            campaign_id = campaign_data.get('campaign_id', 'default')
            if campaign_id not in self.arms:
                self._initialize_campaign_arms(campaign_id, campaign_data)
            
            # Select best arm using bandit algorithm
            if self.optimization_method == "multi_armed_bandit":
                selected_arm = self._epsilon_greedy_selection(campaign_id)
            elif self.optimization_method == "thompson_sampling":
                selected_arm = self._thompson_sampling_selection(campaign_id)
            elif self.optimization_method == "ucb":
                selected_arm = self._ucb_selection(campaign_id)
            else:
                selected_arm = self._epsilon_greedy_selection(campaign_id)
            
            # Generate optimization recommendations
            recommendations = self._generate_optimization_recommendations(
                campaign_id, selected_arm, campaign_data
            )
            
            return {
                "success": True,
                "selected_configuration": selected_arm,
                "recommendations": recommendations,
                "expected_performance": self._predict_performance(selected_arm),
                "optimization_method": self.optimization_method
            }
            
        except Exception as e:
            logger.error(f"Campaign optimization failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def update_campaign_performance(self, campaign_id: str, arm_id: str, reward: float) -> bool:
        """
        Update campaign performance for bandit learning.
        
        Args:
            campaign_id: Campaign identifier
            arm_id: Arm (configuration) identifier
            reward: Performance reward (e.g., ROI)
            
        Returns:
            True if update successful
        """
        try:
            if campaign_id not in self.arm_rewards:
                self.arm_rewards[campaign_id] = {}
                self.arm_counts[campaign_id] = {}
            
            if arm_id not in self.arm_rewards[campaign_id]:
                self.arm_rewards[campaign_id][arm_id] = []
                self.arm_counts[campaign_id][arm_id] = 0
            
            # Update reward history
            self.arm_rewards[campaign_id][arm_id].append(reward)
            self.arm_counts[campaign_id][arm_id] += 1
            
            # Store performance history
            self.performance_history.append({
                "campaign_id": campaign_id,
                "arm_id": arm_id,
                "reward": reward,
                "timestamp": pd.Timestamp.now()
            })
            
            logger.info(f"Updated performance for campaign {campaign_id}, arm {arm_id}: {reward}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update campaign performance: {str(e)}")
            return False
    
    def _initialize_campaign_arms(self, campaign_id: str, campaign_data: Dict[str, Any]):
        """
        Initialize campaign arms (configurations) for bandit optimization.
        
        Args:
            campaign_id: Campaign identifier
            campaign_data: Campaign configuration data
        """
        # Define different campaign configurations (arms)
        budget = campaign_data.get('budget', 10000)
        
        arms = {
            "conservative": {
                "budget_allocation": {"search": 0.4, "social": 0.3, "display": 0.3},
                "bid_strategy": "CPC",
                "target_audience": "broad",
                "creative_type": "image",
                "expected_roi": 1.5
            },
            "aggressive": {
                "budget_allocation": {"search": 0.6, "social": 0.4, "display": 0.0},
                "bid_strategy": "CPA",
                "target_audience": "narrow",
                "creative_type": "video",
                "expected_roi": 2.2
            },
            "balanced": {
                "budget_allocation": {"search": 0.5, "social": 0.3, "display": 0.2},
                "bid_strategy": "ROAS",
                "target_audience": "medium",
                "creative_type": "carousel",
                "expected_roi": 1.8
            },
            "experimental": {
                "budget_allocation": {"search": 0.3, "social": 0.5, "display": 0.2},
                "bid_strategy": "CPM",
                "target_audience": "lookalike",
                "creative_type": "interactive",
                "expected_roi": 2.0
            }
        }
        
        self.arms[campaign_id] = arms
        self.arm_rewards[campaign_id] = {arm_id: [] for arm_id in arms.keys()}
        self.arm_counts[campaign_id] = {arm_id: 0 for arm_id in arms.keys()}
    
    def _epsilon_greedy_selection(self, campaign_id: str) -> Dict[str, Any]:
        """
        Select arm using epsilon-greedy strategy.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            Selected arm configuration
        """
        arms = self.arms[campaign_id]
        
        # Exploration vs exploitation
        if np.random.random() < self.exploration_rate:
            # Explore: select random arm
            selected_arm_id = np.random.choice(list(arms.keys()))
        else:
            # Exploit: select best performing arm
            avg_rewards = {}
            for arm_id in arms.keys():
                rewards = self.arm_rewards[campaign_id].get(arm_id, [])
                avg_rewards[arm_id] = np.mean(rewards) if rewards else 0
            
            selected_arm_id = max(avg_rewards, key=avg_rewards.get)
        
        return {"arm_id": selected_arm_id, **arms[selected_arm_id]}
    
    def _thompson_sampling_selection(self, campaign_id: str) -> Dict[str, Any]:
        """
        Select arm using Thompson Sampling.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            Selected arm configuration
        """
        arms = self.arms[campaign_id]
        samples = {}
        
        for arm_id in arms.keys():
            rewards = self.arm_rewards[campaign_id].get(arm_id, [])
            if rewards:
                # Beta distribution parameters
                alpha = sum(rewards) + 1
                beta = len(rewards) - sum(rewards) + 1
                samples[arm_id] = np.random.beta(alpha, beta)
            else:
                samples[arm_id] = np.random.beta(1, 1)
        
        selected_arm_id = max(samples, key=samples.get)
        return {"arm_id": selected_arm_id, **arms[selected_arm_id]}
    
    def _ucb_selection(self, campaign_id: str) -> Dict[str, Any]:
        """
        Select arm using Upper Confidence Bound.
        
        Args:
            campaign_id: Campaign identifier
            
        Returns:
            Selected arm configuration
        """
        arms = self.arms[campaign_id]
        total_counts = sum(self.arm_counts[campaign_id].values())
        
        if total_counts == 0:
            # If no arms have been tried, select randomly
            selected_arm_id = np.random.choice(list(arms.keys()))
        else:
            ucb_values = {}
            for arm_id in arms.keys():
                rewards = self.arm_rewards[campaign_id].get(arm_id, [])
                count = self.arm_counts[campaign_id].get(arm_id, 0)
                
                if count == 0:
                    ucb_values[arm_id] = float('inf')  # Unvisited arms get highest priority
                else:
                    mean_reward = np.mean(rewards)
                    confidence = np.sqrt(2 * np.log(total_counts) / count)
                    ucb_values[arm_id] = mean_reward + confidence
            
            selected_arm_id = max(ucb_values, key=ucb_values.get)
        
        return {"arm_id": selected_arm_id, **arms[selected_arm_id]}
    
    def _generate_optimization_recommendations(self, 
                                             campaign_id: str, 
                                             selected_arm: Dict[str, Any], 
                                             campaign_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Generate specific optimization recommendations.
        
        Args:
            campaign_id: Campaign identifier
            selected_arm: Selected arm configuration
            campaign_data: Original campaign data
            
        Returns:
            List of optimization recommendations
        """
        recommendations = []
        
        # Budget allocation recommendations
        budget_allocation = selected_arm.get("budget_allocation", {})
        recommendations.append({
            "category": "Budget Allocation",
            "recommendation": f"Allocate budget as follows: {', '.join([f'{k}: {v*100:.0f}%' for k, v in budget_allocation.items()])}",
            "impact": "High",
            "effort": "Medium"
        })
        
        # Bid strategy recommendation
        bid_strategy = selected_arm.get("bid_strategy", "CPC")
        recommendations.append({
            "category": "Bid Strategy",
            "recommendation": f"Use {bid_strategy} bidding strategy for optimal performance",
            "impact": "Medium",
            "effort": "Low"
        })
        
        # Audience targeting recommendation
        target_audience = selected_arm.get("target_audience", "medium")
        recommendations.append({
            "category": "Audience Targeting",
            "recommendation": f"Use {target_audience} audience targeting approach",
            "impact": "High",
            "effort": "Medium"
        })
        
        # Creative recommendation
        creative_type = selected_arm.get("creative_type", "image")
        recommendations.append({
            "category": "Creative Strategy",
            "recommendation": f"Focus on {creative_type} creatives for this campaign",
            "impact": "Medium",
            "effort": "High"
        })
        
        return recommendations
    
    def _predict_performance(self, selected_arm: Dict[str, Any]) -> Dict[str, float]:
        """
        Predict expected performance for selected arm.
        
        Args:
            selected_arm: Selected arm configuration
            
        Returns:
            Predicted performance metrics
        """
        expected_roi = selected_arm.get("expected_roi", 1.5)
        
        return {
            "expected_roi": expected_roi,
            "expected_ctr": 0.02 * expected_roi,  # Simplified relationship
            "expected_cvr": 0.05 * expected_roi,
            "confidence_interval": [expected_roi * 0.8, expected_roi * 1.2]
        }
