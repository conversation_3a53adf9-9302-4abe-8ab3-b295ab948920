"""
Product Recommendation Model for MarketMind AI
==============================================

This module provides advanced product recommendation capabilities using
collaborative filtering, content-based filtering, and hybrid approaches.
"""

import os
import pickle
import json
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import TruncatedSVD
from scipy.sparse import csr_matrix
import logging

from .base_model import BaseMarketMindModel

logger = logging.getLogger(__name__)

class ProductRecommendationModel(BaseMarketMindModel):
    """
    Advanced product recommendation model using multiple algorithms.
    Supports collaborative filtering, content-based filtering, and hybrid approaches.
    """
    
    def __init__(self, algorithm: str = "hybrid", n_recommendations: int = 10):
        """
        Initialize the product recommendation model.
        
        Args:
            algorithm: Recommendation algorithm ('collaborative', 'content', 'hybrid')
            n_recommendations: Number of recommendations to generate
        """
        super().__init__("product_recommendation", "recommendation")
        self.algorithm = algorithm
        self.n_recommendations = n_recommendations
        
        # Model components
        self.collaborative_model = None
        self.content_model = None
        self.hybrid_model = None
        
        # Data structures
        self.user_item_matrix = None
        self.product_features = None
        self.product_embeddings = None
        self.user_embeddings = None
        
        # Metadata
        self.product_categories = []
        self.user_preferences = {}
        
        logger.info(f"Initialized ProductRecommendationModel with {algorithm} algorithm")
    
    def build_model(self, **kwargs) -> Dict[str, Any]:
        """
        Build the recommendation model architecture.
        
        Args:
            **kwargs: Additional model parameters
            
        Returns:
            Model components
        """
        try:
            models = {}
            
            if self.algorithm in ["collaborative", "hybrid"]:
                # Collaborative filtering using SVD
                n_components = kwargs.get("n_components", 50)
                self.collaborative_model = TruncatedSVD(
                    n_components=n_components,
                    random_state=42
                )
                models["collaborative"] = self.collaborative_model
            
            if self.algorithm in ["content", "hybrid"]:
                # Content-based filtering using TF-IDF
                self.content_model = TfidfVectorizer(
                    max_features=1000,
                    stop_words='english',
                    ngram_range=(1, 2)
                )
                models["content"] = self.content_model
            
            if self.algorithm == "hybrid":
                # Hybrid model combining both approaches
                self.hybrid_model = {
                    "collaborative_weight": kwargs.get("collaborative_weight", 0.6),
                    "content_weight": kwargs.get("content_weight", 0.4)
                }
                models["hybrid"] = self.hybrid_model
            
            logger.info(f"Built recommendation model with {self.algorithm} algorithm")
            return models
            
        except Exception as e:
            logger.error(f"Failed to build recommendation model: {str(e)}")
            raise
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess data for recommendation training.
        
        Args:
            data: Raw interaction data (user_id, product_id, rating, etc.)
            
        Returns:
            Tuple of (user_item_matrix, product_features)
        """
        try:
            # Create user-item matrix
            if 'user_id' in data.columns and 'product_id' in data.columns:
                # Handle ratings/interactions
                if 'rating' in data.columns:
                    self.user_item_matrix = data.pivot_table(
                        index='user_id',
                        columns='product_id',
                        values='rating',
                        fill_value=0
                    )
                else:
                    # Binary interactions (purchase/view)
                    self.user_item_matrix = data.pivot_table(
                        index='user_id',
                        columns='product_id',
                        values='interaction',
                        fill_value=0
                    )
                
                # Convert to sparse matrix
                self.user_item_matrix = csr_matrix(self.user_item_matrix.values)
            
            # Extract product features
            if 'product_features' in data.columns:
                self.product_features = data['product_features'].fillna('')
            elif 'product_name' in data.columns and 'category' in data.columns:
                self.product_features = data['product_name'] + ' ' + data['category']
            else:
                # Create dummy features
                self.product_features = ['product_' + str(i) for i in range(len(data))]
            
            # Extract categories
            if 'category' in data.columns:
                self.product_categories = data['category'].unique().tolist()
            
            logger.info(f"Preprocessed data: {self.user_item_matrix.shape if self.user_item_matrix is not None else 'None'} users, {len(self.product_features)} products")
            
            return self.user_item_matrix, self.product_features
            
        except Exception as e:
            logger.error(f"Failed to preprocess data: {str(e)}")
            raise
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the recommendation model.
        
        Args:
            data: Training data with user-product interactions
            **kwargs: Additional training parameters
            
        Returns:
            Training results
        """
        try:
            logger.info(f"Starting recommendation model training with {self.algorithm} algorithm")
            
            # Preprocess data
            user_item_matrix, product_features = self.preprocess_data(data)
            
            # Build model
            models = self.build_model(**kwargs)
            
            training_results = {}
            
            # Train collaborative filtering
            if "collaborative" in models:
                logger.info("Training collaborative filtering model")
                self.collaborative_model.fit(user_item_matrix)
                self.user_embeddings = self.collaborative_model.transform(user_item_matrix)
                self.product_embeddings = self.collaborative_model.components_.T
                
                training_results["collaborative"] = {
                    "n_components": self.collaborative_model.n_components,
                    "explained_variance": self.collaborative_model.explained_variance_ratio_.sum()
                }
            
            # Train content-based filtering
            if "content" in models:
                logger.info("Training content-based filtering model")
                product_features_matrix = self.content_model.fit_transform(product_features)
                self.product_embeddings_content = product_features_matrix
                
                training_results["content"] = {
                    "vocabulary_size": len(self.content_model.vocabulary_),
                    "feature_names": list(self.content_model.vocabulary_.keys())[:10]
                }
            
            # Update model state
            self.is_trained = True
            self.last_trained = datetime.now()
            
            # Save model
            self.save_model()
            
            logger.info("Recommendation model training completed successfully")
            
            return {
                "success": True,
                "algorithm": self.algorithm,
                "training_results": training_results,
                "n_users": user_item_matrix.shape[0] if user_item_matrix is not None else 0,
                "n_products": len(product_features)
            }
            
        except Exception as e:
            logger.error(f"Training failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def recommend_products(self, user_id: int, n_recommendations: int = None) -> Dict[str, Any]:
        """
        Generate product recommendations for a user.
        
        Args:
            user_id: User ID to generate recommendations for
            n_recommendations: Number of recommendations (defaults to self.n_recommendations)
            
        Returns:
            Recommendation results
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before making recommendations")
            
            n_rec = n_recommendations or self.n_recommendations
            
            if self.algorithm == "collaborative":
                return self._collaborative_recommendations(user_id, n_rec)
            elif self.algorithm == "content":
                return self._content_recommendations(user_id, n_rec)
            elif self.algorithm == "hybrid":
                return self._hybrid_recommendations(user_id, n_rec)
            else:
                raise ValueError(f"Unknown algorithm: {self.algorithm}")
                
        except Exception as e:
            logger.error(f"Recommendation failed for user {user_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _collaborative_recommendations(self, user_id: int, n_recommendations: int) -> Dict[str, Any]:
        """Generate collaborative filtering recommendations."""
        try:
            # Get user vector
            user_idx = self.user_item_matrix.index.get_loc(user_id)
            user_vector = self.user_embeddings[user_idx]
            
            # Calculate similarities with all products
            similarities = cosine_similarity([user_vector], self.product_embeddings)[0]
            
            # Get top recommendations
            top_indices = np.argsort(similarities)[::-1][:n_recommendations]
            
            recommendations = []
            for idx in top_indices:
                product_id = self.user_item_matrix.columns[idx]
                score = similarities[idx]
                recommendations.append({
                    "product_id": product_id,
                    "score": float(score),
                    "algorithm": "collaborative"
                })
            
            return {
                "success": True,
                "user_id": user_id,
                "recommendations": recommendations,
                "algorithm": "collaborative"
            }
            
        except Exception as e:
            logger.error(f"Collaborative recommendation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _content_recommendations(self, user_id: int, n_recommendations: int) -> Dict[str, Any]:
        """Generate content-based filtering recommendations."""
        try:
            # Get user's purchase history
            user_purchases = self.user_item_matrix.loc[user_id]
            purchased_products = user_purchases[user_purchases > 0].index.tolist()
            
            if not purchased_products:
                # New user - recommend popular products
                return self._popular_recommendations(n_recommendations)
            
            # Calculate user profile from purchased products
            user_profile = np.mean(self.product_embeddings_content, axis=0)
            
            # Calculate similarities with all products
            similarities = cosine_similarity([user_profile], self.product_embeddings_content)[0]
            
            # Filter out already purchased products
            for product_id in purchased_products:
                product_idx = self.user_item_matrix.columns.get_loc(product_id)
                similarities[product_idx] = -1
            
            # Get top recommendations
            top_indices = np.argsort(similarities)[::-1][:n_recommendations]
            
            recommendations = []
            for idx in top_indices:
                if similarities[idx] > 0:
                    product_id = self.user_item_matrix.columns[idx]
                    score = similarities[idx]
                    recommendations.append({
                        "product_id": product_id,
                        "score": float(score),
                        "algorithm": "content"
                    })
            
            return {
                "success": True,
                "user_id": user_id,
                "recommendations": recommendations,
                "algorithm": "content"
            }
            
        except Exception as e:
            logger.error(f"Content recommendation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _hybrid_recommendations(self, user_id: int, n_recommendations: int) -> Dict[str, Any]:
        """Generate hybrid recommendations combining collaborative and content-based filtering."""
        try:
            # Get recommendations from both algorithms
            collab_rec = self._collaborative_recommendations(user_id, n_recommendations * 2)
            content_rec = self._content_recommendations(user_id, n_recommendations * 2)
            
            if not collab_rec["success"] or not content_rec["success"]:
                # Fallback to single algorithm
                return collab_rec if collab_rec["success"] else content_rec
            
            # Combine scores
            combined_scores = {}
            
            # Add collaborative scores
            for rec in collab_rec["recommendations"]:
                product_id = rec["product_id"]
                combined_scores[product_id] = {
                    "collaborative_score": rec["score"],
                    "content_score": 0,
                    "combined_score": rec["score"] * self.hybrid_model["collaborative_weight"]
                }
            
            # Add content scores
            for rec in content_rec["recommendations"]:
                product_id = rec["product_id"]
                if product_id in combined_scores:
                    combined_scores[product_id]["content_score"] = rec["score"]
                    combined_scores[product_id]["combined_score"] += rec["score"] * self.hybrid_model["content_weight"]
                else:
                    combined_scores[product_id] = {
                        "collaborative_score": 0,
                        "content_score": rec["score"],
                        "combined_score": rec["score"] * self.hybrid_model["content_weight"]
                    }
            
            # Sort by combined score
            sorted_products = sorted(
                combined_scores.items(),
                key=lambda x: x[1]["combined_score"],
                reverse=True
            )
            
            recommendations = []
            for product_id, scores in sorted_products[:n_recommendations]:
                recommendations.append({
                    "product_id": product_id,
                    "score": scores["combined_score"],
                    "collaborative_score": scores["collaborative_score"],
                    "content_score": scores["content_score"],
                    "algorithm": "hybrid"
                })
            
            return {
                "success": True,
                "user_id": user_id,
                "recommendations": recommendations,
                "algorithm": "hybrid"
            }
            
        except Exception as e:
            logger.error(f"Hybrid recommendation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _popular_recommendations(self, n_recommendations: int) -> Dict[str, Any]:
        """Generate popular product recommendations for new users."""
        try:
            # Calculate product popularity (total interactions)
            product_popularity = self.user_item_matrix.sum(axis=0)
            
            # Get top popular products
            top_indices = np.argsort(product_popularity)[::-1][:n_recommendations]
            
            recommendations = []
            for idx in top_indices:
                product_id = self.user_item_matrix.columns[idx]
                popularity = product_popularity[idx]
                recommendations.append({
                    "product_id": product_id,
                    "score": float(popularity),
                    "algorithm": "popular"
                })
            
            return {
                "success": True,
                "recommendations": recommendations,
                "algorithm": "popular"
            }
            
        except Exception as e:
            logger.error(f"Popular recommendation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_similar_products(self, product_id: int, n_similar: int = 5) -> Dict[str, Any]:
        """
        Find similar products to a given product.
        
        Args:
            product_id: Product ID to find similar products for
            n_similar: Number of similar products to return
            
        Returns:
            Similar products
        """
        try:
            if not self.is_trained:
                raise ValueError("Model must be trained before finding similar products")
            
            # Get product index
            product_idx = self.user_item_matrix.columns.get_loc(product_id)
            
            if self.algorithm in ["collaborative", "hybrid"]:
                # Use collaborative embeddings
                product_vector = self.product_embeddings[product_idx]
                similarities = cosine_similarity([product_vector], self.product_embeddings)[0]
            else:
                # Use content embeddings
                product_vector = self.product_embeddings_content[product_idx]
                similarities = cosine_similarity([product_vector], self.product_embeddings_content)[0]
            
            # Get top similar products (excluding the product itself)
            similarities[product_idx] = -1
            top_indices = np.argsort(similarities)[::-1][:n_similar]
            
            similar_products = []
            for idx in top_indices:
                if similarities[idx] > 0:
                    similar_product_id = self.user_item_matrix.columns[idx]
                    similarity = similarities[idx]
                    similar_products.append({
                        "product_id": similar_product_id,
                        "similarity": float(similarity)
                    })
            
            return {
                "success": True,
                "target_product": product_id,
                "similar_products": similar_products
            }
            
        except Exception as e:
            logger.error(f"Similar products search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def update_user_preferences(self, user_id: int, product_id: int, rating: float) -> Dict[str, Any]:
        """
        Update user preferences with new interaction.
        
        Args:
            user_id: User ID
            product_id: Product ID
            rating: User rating/interaction strength
            
        Returns:
            Update result
        """
        try:
            # Update user-item matrix
            self.user_item_matrix.loc[user_id, product_id] = rating
            
            # Retrain model if needed (incremental update)
            if self.algorithm in ["collaborative", "hybrid"]:
                self.collaborative_model.fit(self.user_item_matrix)
                self.user_embeddings = self.collaborative_model.transform(self.user_item_matrix)
                self.product_embeddings = self.collaborative_model.components_.T
            
            # Save updated model
            self.save_model()
            
            return {
                "success": True,
                "message": f"Updated preferences for user {user_id} and product {product_id}"
            }
            
        except Exception as e:
            logger.error(f"Failed to update user preferences: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information."""
        return {
            "model_name": self.model_name,
            "model_type": self.model_type,
            "algorithm": self.algorithm,
            "is_trained": self.is_trained,
            "last_trained": self.last_trained.isoformat() if self.last_trained else None,
            "model_version": self.model_version,
            "n_recommendations": self.n_recommendations,
            "n_users": self.user_item_matrix.shape[0] if self.user_item_matrix is not None else 0,
            "n_products": self.user_item_matrix.shape[1] if self.user_item_matrix is not None else 0,
            "product_categories": self.product_categories
        } 