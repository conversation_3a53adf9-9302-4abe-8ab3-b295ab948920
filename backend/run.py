#!/usr/bin/env python3
"""
MarketMind Backend Server
Flask application with AI-powered business analytics
"""

import os
import sys
import logging
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import User, Company, Customer, Campaign, SentimentData, JourneyStage, GeneratedContent

def setup_logging():
    """Configure logging for the application"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        handlers=[
            logging.FileHandler('logs/app.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def create_sample_data():
    """Create sample data for testing and demonstration"""
    try:
        # Check if sample data already exists
        if Company.query.first():
            print("Sample data already exists. Skipping creation.")
            return

        print("Creating sample data...")

        # Create sample company
        company = Company(
            name='شركة التسويق الذكي',
            industry='التجارة الإلكترونية',
            size='medium',
            subscription_plan='professional'
        )
        db.session.add(company)
        db.session.flush()  # Get the company ID

        # Create sample business user
        business_user = User(
            email='<EMAIL>',
            name='مدير الأعمال',
            user_type='business',
            company_id=company.id
        )
        business_user.set_password('password123')
        db.session.add(business_user)

        # Create sample personal user
        personal_user = User(
            email='<EMAIL>',
            name='مستخدم شخصي',
            user_type='personal',
            company_id=company.id
        )
        personal_user.set_password('password123')
        db.session.add(personal_user)

        # Create sample admin user
        admin_user = User(
            email='<EMAIL>',
            name='مدير النظام',
            user_type='admin',
            company_id=company.id
        )
        admin_user.set_password('password123')
        db.session.add(admin_user)

        # Create sample customers
        customers_data = [
            {
                'name': 'أحمد محمد',
                'email': '<EMAIL>',
                'phone': '+966501234567',
                'total_spent': 5200,
                'order_frequency': 8,
                'last_order_days': 5,
                'avg_order_value': 650,
                'support_tickets': 1,
                'email_engagement': 85,
                'segment': 'high-value'
            },
            {
                'name': 'فاطمة علي',
                'email': '<EMAIL>',
                'phone': '+966507654321',
                'total_spent': 850,
                'order_frequency': 2,
                'last_order_days': 15,
                'avg_order_value': 425,
                'support_tickets': 0,
                'email_engagement': 65,
                'segment': 'new-customers'
            },
            {
                'name': 'محمد سالم',
                'email': '<EMAIL>',
                'phone': '+966502345678',
                'total_spent': 1200,
                'order_frequency': 1,
                'last_order_days': 45,
                'avg_order_value': 1200,
                'support_tickets': 3,
                'email_engagement': 25,
                'segment': 'at-risk'
            }
        ]

        for customer_data in customers_data:
            customer = Customer(
                company_id=company.id,
                **customer_data
            )
            db.session.add(customer)

        # Create sample campaigns
        campaigns_data = [
            {
                'name': 'حملة الصيف 2024',
                'platform': 'Google Ads',
                'status': 'active',
                'budget': 50000,
                'spent': 42000,
                'impressions': 125000,
                'clicks': 3200,
                'conversions': 156,
                'target_audience': 'الشباب 18-35'
            },
            {
                'name': 'إطلاق المنتج الجديد',
                'platform': 'Facebook',
                'status': 'active',
                'budget': 35000,
                'spent': 28000,
                'impressions': 89000,
                'clicks': 2100,
                'conversions': 98,
                'target_audience': 'المهنيون'
            }
        ]

        for campaign_data in campaigns_data:
            campaign = Campaign(
                company_id=company.id,
                **campaign_data
            )
            # Calculate metrics
            if campaign.impressions > 0:
                campaign.ctr = (campaign.clicks / campaign.impressions) * 100
            if campaign.clicks > 0:
                campaign.cpc = campaign.spent / campaign.clicks
            if campaign.spent > 0:
                campaign.roas = (campaign.conversions * 100) / campaign.spent
            
            db.session.add(campaign)

        # Create sample sentiment data
        sentiment_data = [
            {
                'platform': 'Twitter',
                'author': '@ahmed_salem',
                'content': 'تجربة رائعة مع المنتج الجديد! جودة عالية وخدمة ممتازة.',
                'sentiment_score': 0.8,
                'sentiment_label': 'positive',
                'confidence': 0.92,
                'engagement_count': 45,
                'keywords': '["جودة عالية", "خدمة ممتازة", "تجربة رائعة"]',
                'category': 'منتج'
            },
            {
                'platform': 'Facebook',
                'author': 'فاطمة محمد',
                'content': 'للأسف التسليم كان متأخر جداً وخدمة العملاء لم تكن مفيدة.',
                'sentiment_score': -0.7,
                'sentiment_label': 'negative',
                'confidence': 0.88,
                'engagement_count': 23,
                'keywords': '["تسليم متأخر", "خدمة العملاء"]',
                'category': 'خدمة'
            }
        ]

        for sentiment in sentiment_data:
            sentiment_obj = SentimentData(
                company_id=company.id,
                **sentiment
            )
            db.session.add(sentiment_obj)

        # Create sample journey stages
        journey_stages_data = [
            {
                'name': 'الوعي',
                'description': 'اكتشاف العلامة التجارية والمنتجات',
                'stage_order': 1,
                'customer_count': 10000,
                'conversion_rate': 25,
                'avg_time_spent': 48,
                'dropoff_rate': 75,
                'touchpoints': '["إعلانات جوجل", "وسائل التواصل"]',
                'pain_points': '["صعوبة العثور على المعلومات"]',
                'opportunities': '["تحسين SEO", "محتوى أكثر جاذبية"]'
            },
            {
                'name': 'الاهتمام',
                'description': 'مقارنة المنتجات والبحث عن المعلومات',
                'stage_order': 2,
                'customer_count': 2500,
                'conversion_rate': 40,
                'avg_time_spent': 120,
                'dropoff_rate': 60,
                'touchpoints': '["موقع الويب", "مراجعات العملاء"]',
                'pain_points': '["معلومات غير كافية", "صعوبة المقارنة"]',
                'opportunities': '["دليل مقارنة", "مراجعات أكثر"]'
            }
        ]

        for stage_data in journey_stages_data:
            stage = JourneyStage(
                company_id=company.id,
                **stage_data
            )
            db.session.add(stage)

        # Commit all changes
        db.session.commit()
        print("Sample data created successfully!")

    except Exception as e:
        print(f"Error creating sample data: {str(e)}")
        db.session.rollback()

def initialize_database():
    """Initialize the database with tables and sample data"""
    try:
        print("Initializing database...")
        
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Create sample data
        create_sample_data()
        
    except Exception as e:
        print(f"Error initializing database: {str(e)}")
        sys.exit(1)

def main():
    """Main function to run the Flask application"""
    # Setup logging
    setup_logging()
    
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                     MarketMind Backend                       ║
    ║                  AI-Powered Business Analytics               ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  Server: http://{host}:{port}                                    ║
    ║  Debug Mode: {debug}                                           ║
    ║  Environment: {os.getenv('FLASK_ENV', 'production')}                                ║
    ║  Database: {os.getenv('DATABASE_URL', 'sqlite:///marketmind.db')[:30]}...           ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize database in application context
    with app.app_context():
        initialize_database()
    
    # Start the Flask application
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\nShutting down MarketMind Backend...")
        print("Thank you for using MarketMind! 🚀")
    except Exception as e:
        print(f"Error starting server: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
