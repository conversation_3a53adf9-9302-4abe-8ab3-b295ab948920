#!/usr/bin/env python3
"""
Simple HTTP server for MarketMind Backend
"""

from http.server import <PERSON><PERSON>PServer, SimpleHTTPRequestHandler
import json
import urllib.parse
import os

class MarketMindHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.add_cors_headers()
            self.end_headers()
            response = {
                "message": "MarketMind Backend API",
                "status": "running",
                "version": "1.0.0",
                "endpoints": {
                    "health": "/health",
                    "docs": "/docs",
                    "api": "/api/v1"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
            
        elif self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.add_cors_headers()
            self.end_headers()
            response = {
                "status": "healthy",
                "timestamp": "2024-01-15T10:00:00Z",
                "services": {
                    "database": "connected",
                    "ai_engine": "ready",
                    "cache": "active"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
            
        elif self.path == '/docs':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>MarketMind API Documentation</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                    .method { color: white; padding: 5px 10px; border-radius: 3px; font-weight: bold; }
                    .get { background: #28a745; }
                    .post { background: #007bff; }
                </style>
            </head>
            <body>
                <h1>🧠 MarketMind API Documentation</h1>
                <p>Welcome to the MarketMind AI Marketing Platform API</p>
                
                <div class="endpoint">
                    <span class="method get">GET</span> <strong>/</strong>
                    <p>API information and status</p>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span> <strong>/health</strong>
                    <p>Health check endpoint</p>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span> <strong>/api/v1/campaigns</strong>
                    <p>Get user campaigns</p>
                </div>
                
                <div class="endpoint">
                    <span class="method post">POST</span> <strong>/api/v1/campaigns</strong>
                    <p>Create new campaign</p>
                </div>
                
                <div class="endpoint">
                    <span class="method get">GET</span> <strong>/api/v1/analytics</strong>
                    <p>Get analytics data</p>
                </div>
                
                <p><strong>Note:</strong> This is a simplified server for demonstration. Full FastAPI server with all features is available in production.</p>
            </body>
            </html>
            """
            self.wfile.write(html.encode('utf-8'))
            
        elif self.path.startswith('/api/v1/'):
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            # Mock API responses
            if 'campaigns' in self.path:
                response = {
                    "campaigns": [
                        {
                            "id": 1,
                            "name": "حملة الصيف 2024",
                            "status": "active",
                            "budget": 5000,
                            "impressions": 125000,
                            "clicks": 3200,
                            "conversions": 85
                        },
                        {
                            "id": 2,
                            "name": "إعلان المنتج الجديد",
                            "status": "paused",
                            "budget": 3000,
                            "impressions": 89000,
                            "clicks": 2100,
                            "conversions": 45
                        }
                    ],
                    "total": 2,
                    "page": 1
                }
            elif 'analytics' in self.path:
                response = {
                    "overview": {
                        "total_campaigns": 15,
                        "active_campaigns": 8,
                        "total_budget": 45000,
                        "total_impressions": 1250000,
                        "total_clicks": 32000,
                        "total_conversions": 850,
                        "avg_ctr": 2.56,
                        "avg_conversion_rate": 2.66
                    },
                    "recent_performance": [
                        {"date": "2024-01-15", "impressions": 15000, "clicks": 380, "conversions": 12},
                        {"date": "2024-01-14", "impressions": 14500, "clicks": 365, "conversions": 11},
                        {"date": "2024-01-13", "impressions": 16200, "clicks": 410, "conversions": 15}
                    ]
                }
            else:
                response = {"message": "API endpoint", "path": self.path}
                
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
        else:
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {"error": "Not found", "path": self.path}
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def do_POST(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response = {
            "message": "POST request received",
            "path": self.path,
            "status": "success"
        }
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def add_cors_headers(self):
        """Add CORS headers to allow frontend requests"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Max-Age', '86400')

    def do_OPTIONS(self):
        self.send_response(200)
        self.add_cors_headers()
        self.end_headers()

if __name__ == '__main__':
    port = 8002
    server = HTTPServer(('0.0.0.0', port), MarketMindHandler)
    print(f"🚀 MarketMind Backend Server starting...")
    print(f"📍 Server running at: http://localhost:{port}")
    print(f"📖 API Documentation: http://localhost:{port}/docs")
    print(f"❤️  Health Check: http://localhost:{port}/health")
    print("-" * 50)
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        server.shutdown()
