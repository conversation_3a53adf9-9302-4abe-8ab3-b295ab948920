from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    user_type = Column(String, nullable=False)  # personal, business, admin
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)

    # Payment gateway IDs
    stripe_customer_id = Column(String)
    paypal_customer_id = Column(String)

    # Security features
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime)
    
    # Relationships
    subscription = relationship("Subscription", back_populates="user", uselist=False)
    campaigns = relationship("Campaign", back_populates="user")
    analytics = relationship("Analytics", back_populates="user")

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    plan = Column(String, nullable=False)  # basic, pro, enterprise
    status = Column(String, nullable=False)  # active, cancelled, past_due
    current_period_start = Column(DateTime, nullable=False)
    current_period_end = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Payment gateway subscription IDs
    stripe_subscription_id = Column(String)
    paypal_subscription_id = Column(String)

    # Billing information
    amount = Column(Float)
    currency = Column(String, default="USD")
    next_billing_date = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="subscription")

class Campaign(Base):
    __tablename__ = "campaigns"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    status = Column(String, nullable=False)  # draft, active, paused, completed
    budget = Column(Float)
    spent = Column(Float, default=0.0)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Campaign metrics
    impressions = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    conversions = Column(Integer, default=0)
    
    # Relationships
    user = relationship("User", back_populates="campaigns")

class Analytics(Base):
    __tablename__ = "analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"))
    metric_type = Column(String, nullable=False)  # engagement, conversion, sentiment, etc.
    metric_value = Column(Float, nullable=False)
    date_recorded = Column(DateTime, default=datetime.utcnow)
    meta_data = Column(JSON)  # Additional data as JSON
    
    # Relationships
    user = relationship("User", back_populates="analytics")

class MLModel(Base):
    __tablename__ = "ml_models"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    model_type = Column(String, nullable=False)  # segmentation, prediction, sentiment, etc.
    version = Column(String, nullable=False)
    file_path = Column(String, nullable=False)
    accuracy = Column(Float)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class CustomerSegment(Base):
    __tablename__ = "customer_segments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    segment_name = Column(String, nullable=False)
    description = Column(Text)
    criteria = Column(JSON)  # Segmentation criteria as JSON
    customer_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)

class ContentGeneration(Base):
    __tablename__ = "content_generations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    content_type = Column(String, nullable=False)  # email, social, ad_copy, etc.
    prompt = Column(Text, nullable=False)
    generated_content = Column(Text, nullable=False)
    rating = Column(Integer)  # User rating 1-5
    created_at = Column(DateTime, default=datetime.utcnow)

class SystemLog(Base):
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    level = Column(String, nullable=False)  # info, warning, error
    service = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    meta_data = Column(JSON)
    timestamp = Column(DateTime, default=datetime.utcnow)

class Payment(Base):
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"))

    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String, default="USD")
    status = Column(String, nullable=False)  # pending, completed, failed, refunded
    payment_method = Column(String, nullable=False)  # stripe, paypal, paymob, fawry

    # Gateway specific IDs
    gateway_payment_id = Column(String)
    gateway_transaction_id = Column(String)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)

    # Additional data
    meta_data = Column(JSON)

class Invoice(Base):
    __tablename__ = "invoices"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"))
    payment_id = Column(Integer, ForeignKey("payments.id"))

    # Invoice details
    invoice_number = Column(String, unique=True, nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String, default="USD")
    status = Column(String, nullable=False)  # draft, sent, paid, overdue

    # Dates
    issue_date = Column(DateTime, default=datetime.utcnow)
    due_date = Column(DateTime)
    paid_date = Column(DateTime)

    # Invoice content
    line_items = Column(JSON)
    notes = Column(Text)

    # File storage
    pdf_path = Column(String)
