from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List
from enum import Enum

class UserType(str, Enum):
    personal = "personal"
    business = "business"
    admin = "admin"

class SubscriptionPlan(str, Enum):
    basic = "basic"
    pro = "pro"
    enterprise = "enterprise"

class SubscriptionStatus(str, Enum):
    active = "active"
    cancelled = "cancelled"
    past_due = "past_due"

# User Schemas
class UserBase(BaseModel):
    email: EmailStr
    name: str
    user_type: UserType

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

# Subscription Schemas
class SubscriptionBase(BaseModel):
    plan: SubscriptionPlan
    status: SubscriptionStatus

class SubscriptionCreate(SubscriptionBase):
    current_period_start: datetime
    current_period_end: datetime

class SubscriptionResponse(SubscriptionBase):
    id: int
    current_period_start: datetime
    current_period_end: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True

# User Response Schema
class UserResponse(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    subscription: Optional[SubscriptionResponse] = None
    
    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    users: List[UserResponse]
    total: int
    page: int
    per_page: int

# Authentication Schemas
class Token(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse
    redirect_url: Optional[str] = None

class TokenData(BaseModel):
    email: Optional[str] = None
