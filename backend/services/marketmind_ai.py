"""
MarketMind AI Service - Advanced AI Models for Marketing Intelligence
"""

import os
import json
import pickle
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Core ML Libraries
from sklearn.cluster import KMeans, DBSCAN
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, silhouette_score
from sklearn.decomposition import PCA
import xgboost as xgb

# Time Series
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error

# NLP Libraries
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
import re

class MarketMindAI:
    """Advanced AI Service for Marketing Intelligence"""
    
    def __init__(self):
        self.models_dir = "marketmind_models"
        self.ensure_models_directory()
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        
    def ensure_models_directory(self):
        """Create models directory if it doesn't exist"""
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)
    
    def save_model(self, model, model_name: str, metadata: Dict = None):
        """Save model with metadata"""
        model_path = os.path.join(self.models_dir, f"{model_name}.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        # Save metadata
        if metadata:
            metadata_path = os.path.join(self.models_dir, f"{model_name}_metadata.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    def load_model(self, model_name: str):
        """Load model from file"""
        model_path = os.path.join(self.models_dir, f"{model_name}.pkl")
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                return pickle.load(f)
        return None
    
    def generate_sample_customer_data(self, n_customers: int = 1000) -> pd.DataFrame:
        """Generate sample customer data for testing"""
        np.random.seed(42)
        
        data = {
            'customer_id': range(1, n_customers + 1),
            'age': np.random.normal(35, 12, n_customers).astype(int),
            'income': np.random.normal(50000, 20000, n_customers),
            'total_spent': np.random.exponential(500, n_customers),
            'num_purchases': np.random.poisson(5, n_customers),
            'days_since_last_purchase': np.random.exponential(30, n_customers),
            'email_opens': np.random.poisson(10, n_customers),
            'email_clicks': np.random.poisson(3, n_customers),
            'website_visits': np.random.poisson(15, n_customers),
            'support_tickets': np.random.poisson(1, n_customers),
            'satisfaction_score': np.random.normal(7, 2, n_customers),
            'acquisition_channel': np.random.choice(['organic', 'paid_search', 'social', 'email', 'referral'], n_customers),
            'device_type': np.random.choice(['mobile', 'desktop', 'tablet'], n_customers),
            'location': np.random.choice(['urban', 'suburban', 'rural'], n_customers)
        }
        
        df = pd.DataFrame(data)
        
        # Ensure realistic constraints
        df['age'] = df['age'].clip(18, 80)
        df['income'] = df['income'].clip(20000, 200000)
        df['total_spent'] = df['total_spent'].clip(0, 10000)
        df['satisfaction_score'] = df['satisfaction_score'].clip(1, 10)
        df['days_since_last_purchase'] = df['days_since_last_purchase'].clip(0, 365)
        
        return df
    
    def customer_segmentation(self, customer_data: pd.DataFrame, n_clusters: int = 5) -> Dict[str, Any]:
        """
        Customer Segmentation using K-Means and DBSCAN
        """
        try:
            # Prepare features for clustering
            feature_columns = ['age', 'income', 'total_spent', 'num_purchases', 
                             'days_since_last_purchase', 'email_opens', 'email_clicks', 
                             'website_visits', 'satisfaction_score']
            
            # Handle missing values
            features = customer_data[feature_columns].fillna(customer_data[feature_columns].median())
            
            # Scale features
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # K-Means Clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            kmeans_labels = kmeans.fit_predict(features_scaled)
            
            # DBSCAN Clustering
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            dbscan_labels = dbscan.fit_predict(features_scaled)
            
            # Calculate silhouette scores
            kmeans_silhouette = silhouette_score(features_scaled, kmeans_labels)
            
            # Only calculate DBSCAN silhouette if we have more than one cluster
            dbscan_unique_labels = len(set(dbscan_labels))
            dbscan_silhouette = 0
            if dbscan_unique_labels > 1 and -1 not in dbscan_labels:
                dbscan_silhouette = silhouette_score(features_scaled, dbscan_labels)
            
            # Analyze segments
            customer_data_copy = customer_data.copy()
            customer_data_copy['kmeans_segment'] = kmeans_labels
            customer_data_copy['dbscan_segment'] = dbscan_labels
            
            # Generate segment profiles
            segment_profiles = {}
            for i in range(n_clusters):
                segment_data = customer_data_copy[customer_data_copy['kmeans_segment'] == i]
                if len(segment_data) > 0:
                    profile = {
                        'size': len(segment_data),
                        'avg_age': float(segment_data['age'].mean()),
                        'avg_income': float(segment_data['income'].mean()),
                        'avg_total_spent': float(segment_data['total_spent'].mean()),
                        'avg_purchases': float(segment_data['num_purchases'].mean()),
                        'avg_satisfaction': float(segment_data['satisfaction_score'].mean()),
                        'top_channel': segment_data['acquisition_channel'].mode().iloc[0] if len(segment_data['acquisition_channel'].mode()) > 0 else 'unknown',
                        'top_device': segment_data['device_type'].mode().iloc[0] if len(segment_data['device_type'].mode()) > 0 else 'unknown'
                    }
                    segment_profiles[f'segment_{i}'] = profile
            
            # Save models
            self.save_model(kmeans, 'customer_segmentation_kmeans', {
                'model_type': 'customer_segmentation',
                'algorithm': 'kmeans',
                'n_clusters': n_clusters,
                'silhouette_score': kmeans_silhouette,
                'created_at': datetime.now().isoformat()
            })
            
            self.save_model(scaler, 'customer_segmentation_scaler')
            
            return {
                'success': True,
                'kmeans_silhouette_score': kmeans_silhouette,
                'dbscan_silhouette_score': dbscan_silhouette,
                'n_clusters': n_clusters,
                'dbscan_clusters': dbscan_unique_labels,
                'segment_profiles': segment_profiles,
                'feature_columns': feature_columns,
                'model_saved': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في تجزئة العملاء: {str(e)}"
            }
    
    def churn_prediction(self, customer_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Churn Prediction using Random Forest and XGBoost
        """
        try:
            # Create churn labels (synthetic for demo)
            # In real scenario, this would be based on actual churn data
            customer_data_copy = customer_data.copy()
            
            # Define churn based on business rules (for demo)
            churn_conditions = (
                (customer_data_copy['days_since_last_purchase'] > 90) |
                (customer_data_copy['satisfaction_score'] < 4) |
                (customer_data_copy['total_spent'] < 100)
            )
            customer_data_copy['churned'] = churn_conditions.astype(int)
            
            # Prepare features
            feature_columns = ['age', 'income', 'total_spent', 'num_purchases', 
                             'days_since_last_purchase', 'email_opens', 'email_clicks', 
                             'website_visits', 'support_tickets', 'satisfaction_score']
            
            # Encode categorical variables
            le_channel = LabelEncoder()
            le_device = LabelEncoder()
            le_location = LabelEncoder()
            
            customer_data_copy['acquisition_channel_encoded'] = le_channel.fit_transform(customer_data_copy['acquisition_channel'])
            customer_data_copy['device_type_encoded'] = le_device.fit_transform(customer_data_copy['device_type'])
            customer_data_copy['location_encoded'] = le_location.fit_transform(customer_data_copy['location'])
            
            feature_columns.extend(['acquisition_channel_encoded', 'device_type_encoded', 'location_encoded'])
            
            X = customer_data_copy[feature_columns].fillna(customer_data_copy[feature_columns].median())
            y = customer_data_copy['churned']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Random Forest Model
            rf_model = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
            rf_model.fit(X_train_scaled, y_train)
            rf_predictions = rf_model.predict(X_test_scaled)
            rf_accuracy = accuracy_score(y_test, rf_predictions)
            
            # XGBoost Model
            xgb_model = xgb.XGBClassifier(n_estimators=100, random_state=42, max_depth=6)
            xgb_model.fit(X_train_scaled, y_train)
            xgb_predictions = xgb_model.predict(X_test_scaled)
            xgb_accuracy = accuracy_score(y_test, xgb_predictions)
            
            # Feature importance
            feature_importance = dict(zip(feature_columns, rf_model.feature_importances_))
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Predict churn probabilities for all customers
            all_features_scaled = scaler.transform(X)
            churn_probabilities = rf_model.predict_proba(all_features_scaled)[:, 1]
            
            # Identify high-risk customers
            high_risk_threshold = 0.7
            high_risk_customers = customer_data_copy[churn_probabilities > high_risk_threshold]['customer_id'].tolist()
            
            # Save models
            self.save_model(rf_model, 'churn_prediction_rf', {
                'model_type': 'churn_prediction',
                'algorithm': 'random_forest',
                'accuracy': rf_accuracy,
                'feature_columns': feature_columns,
                'created_at': datetime.now().isoformat()
            })
            
            self.save_model(scaler, 'churn_prediction_scaler')
            self.save_model({'channel': le_channel, 'device': le_device, 'location': le_location}, 'churn_prediction_encoders')
            
            return {
                'success': True,
                'rf_accuracy': rf_accuracy,
                'xgb_accuracy': xgb_accuracy,
                'churn_rate': float(y.mean()),
                'high_risk_customers': high_risk_customers,
                'high_risk_count': len(high_risk_customers),
                'top_churn_factors': top_features,
                'feature_columns': feature_columns,
                'model_saved': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في التنبؤ بتوقف العملاء: {str(e)}"
            }
    
    def sales_prediction(self, sales_data: pd.DataFrame = None, periods: int = 30) -> Dict[str, Any]:
        """
        Sales Prediction using Linear Regression and trend analysis
        """
        try:
            # Generate sample sales data if not provided
            if sales_data is None:
                dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
                np.random.seed(42)
                
                # Create realistic sales pattern with trend and seasonality
                trend = np.linspace(1000, 1500, len(dates))
                seasonal = 200 * np.sin(2 * np.pi * np.arange(len(dates)) / 365.25)
                weekly = 100 * np.sin(2 * np.pi * np.arange(len(dates)) / 7)
                noise = np.random.normal(0, 50, len(dates))
                
                sales = trend + seasonal + weekly + noise
                sales = np.maximum(sales, 0)  # Ensure non-negative sales
                
                sales_data = pd.DataFrame({
                    'date': dates,
                    'sales': sales
                })
            
            # Prepare features
            sales_data['date'] = pd.to_datetime(sales_data['date'])
            sales_data = sales_data.sort_values('date')
            sales_data['day_of_year'] = sales_data['date'].dt.dayofyear
            sales_data['day_of_week'] = sales_data['date'].dt.dayofweek
            sales_data['month'] = sales_data['date'].dt.month
            sales_data['quarter'] = sales_data['date'].dt.quarter
            
            # Create lag features
            for lag in [1, 7, 30]:
                sales_data[f'sales_lag_{lag}'] = sales_data['sales'].shift(lag)
            
            # Create moving averages
            for window in [7, 30]:
                sales_data[f'sales_ma_{window}'] = sales_data['sales'].rolling(window=window).mean()
            
            # Remove rows with NaN values
            sales_data_clean = sales_data.dropna()
            
            # Prepare features and target
            feature_columns = ['day_of_year', 'day_of_week', 'month', 'quarter', 
                             'sales_lag_1', 'sales_lag_7', 'sales_lag_30', 
                             'sales_ma_7', 'sales_ma_30']
            
            X = sales_data_clean[feature_columns]
            y = sales_data_clean['sales']
            
            # Split data (use last 20% for testing)
            split_index = int(len(X) * 0.8)
            X_train, X_test = X[:split_index], X[split_index:]
            y_train, y_test = y[:split_index], y[split_index:]
            
            # Train model
            model = LinearRegression()
            model.fit(X_train, y_train)
            
            # Evaluate
            train_predictions = model.predict(X_train)
            test_predictions = model.predict(X_test)
            
            train_mae = mean_absolute_error(y_train, train_predictions)
            test_mae = mean_absolute_error(y_test, test_predictions)
            train_rmse = np.sqrt(mean_squared_error(y_train, train_predictions))
            test_rmse = np.sqrt(mean_squared_error(y_test, test_predictions))
            
            # Future predictions
            last_row = sales_data_clean.iloc[-1].copy()
            future_predictions = []
            
            for i in range(periods):
                future_date = last_row['date'] + timedelta(days=i+1)
                
                # Create features for future date
                future_features = {
                    'day_of_year': future_date.dayofyear,
                    'day_of_week': future_date.dayofweek,
                    'month': future_date.month,
                    'quarter': future_date.quarter,
                    'sales_lag_1': last_row['sales'],
                    'sales_lag_7': sales_data_clean['sales'].iloc[-7] if len(sales_data_clean) >= 7 else last_row['sales'],
                    'sales_lag_30': sales_data_clean['sales'].iloc[-30] if len(sales_data_clean) >= 30 else last_row['sales'],
                    'sales_ma_7': sales_data_clean['sales'].tail(7).mean(),
                    'sales_ma_30': sales_data_clean['sales'].tail(30).mean()
                }
                
                future_X = pd.DataFrame([future_features])
                prediction = model.predict(future_X)[0]
                
                future_predictions.append({
                    'date': future_date.strftime('%Y-%m-%d'),
                    'predicted_sales': max(0, prediction)  # Ensure non-negative
                })
                
                # Update last_row for next iteration
                last_row['sales'] = prediction
                last_row['date'] = future_date
            
            # Save model
            self.save_model(model, 'sales_prediction', {
                'model_type': 'sales_prediction',
                'algorithm': 'linear_regression',
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'feature_columns': feature_columns,
                'created_at': datetime.now().isoformat()
            })
            
            return {
                'success': True,
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'future_predictions': future_predictions,
                'total_predicted_sales': sum([p['predicted_sales'] for p in future_predictions]),
                'avg_daily_sales': sum([p['predicted_sales'] for p in future_predictions]) / periods,
                'model_saved': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في التنبؤ بالمبيعات: {str(e)}"
            }
    
    def advanced_sentiment_analysis(self, texts: List[str]) -> Dict[str, Any]:
        """
        Advanced Sentiment Analysis with emotion detection
        """
        try:
            # Prepare data
            processed_texts = []
            for text in texts:
                # Basic text preprocessing
                text = re.sub(r'[^\w\s]', '', text.lower())
                processed_texts.append(text)

            # Create sample training data for sentiment analysis
            training_data = [
                ("هذا المنتج رائع ومفيد جداً", "positive"),
                ("أحب هذه الخدمة كثيراً", "positive"),
                ("تجربة ممتازة وأنصح بها", "positive"),
                ("منتج عالي الجودة", "positive"),
                ("خدمة عملاء ممتازة", "positive"),
                ("المنتج سيء ولا أنصح به", "negative"),
                ("خدمة العملاء سيئة جداً", "negative"),
                ("لم أعجب بالمنتج", "negative"),
                ("مضيعة للوقت والمال", "negative"),
                ("تجربة مخيبة للآمال", "negative"),
                ("المنتج عادي لا بأس به", "neutral"),
                ("لا رأي لي في هذا الموضوع", "neutral"),
                ("يمكن أن يكون أفضل", "neutral"),
                ("متوسط الجودة", "neutral"),
                ("لا بأس به", "neutral")
            ]

            # Extend with English data
            training_data.extend([
                ("This product is amazing and very useful", "positive"),
                ("I love this service so much", "positive"),
                ("Excellent experience, highly recommend", "positive"),
                ("High quality product", "positive"),
                ("Outstanding customer service", "positive"),
                ("This product is terrible", "negative"),
                ("Worst customer service ever", "negative"),
                ("I hate this product", "negative"),
                ("Complete waste of money", "negative"),
                ("Very disappointing experience", "negative"),
                ("The product is okay", "neutral"),
                ("No opinion on this matter", "neutral"),
                ("Could be better", "neutral"),
                ("Average quality", "neutral"),
                ("It's fine", "neutral")
            ])

            # Prepare training data
            train_texts = [item[0] for item in training_data]
            train_labels = [item[1] for item in training_data]

            # Create TF-IDF vectorizer
            vectorizer = TfidfVectorizer(max_features=1000, ngram_range=(1, 2))
            X_train = vectorizer.fit_transform(train_texts)

            # Train model
            model = MultinomialNB()
            model.fit(X_train, train_labels)

            # Predict sentiments for input texts
            X_test = vectorizer.transform(processed_texts)
            predictions = model.predict(X_test)
            probabilities = model.predict_proba(X_test)

            # Prepare results
            results = []
            for i, text in enumerate(texts):
                sentiment_probs = dict(zip(model.classes_, probabilities[i]))
                results.append({
                    'text': text,
                    'sentiment': predictions[i],
                    'confidence': float(max(probabilities[i])),
                    'probabilities': {k: float(v) for k, v in sentiment_probs.items()}
                })

            # Calculate overall sentiment distribution
            sentiment_counts = pd.Series(predictions).value_counts().to_dict()
            total_texts = len(texts)
            sentiment_distribution = {k: v/total_texts for k, v in sentiment_counts.items()}

            # Save model
            self.save_model({'model': model, 'vectorizer': vectorizer}, 'advanced_sentiment_analysis', {
                'model_type': 'sentiment_analysis',
                'algorithm': 'multinomial_nb_tfidf',
                'training_samples': len(training_data),
                'classes': list(model.classes_),
                'created_at': datetime.now().isoformat()
            })

            return {
                'success': True,
                'results': results,
                'sentiment_distribution': sentiment_distribution,
                'total_texts': total_texts,
                'model_saved': True
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في تحليل المشاعر المتقدم: {str(e)}"
            }

    def campaign_optimization(self, campaign_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Campaign Optimization using performance analysis
        """
        try:
            # Generate sample campaign data if not provided
            if campaign_data is None:
                np.random.seed(42)
                n_campaigns = 100

                campaign_data = pd.DataFrame({
                    'campaign_id': range(1, n_campaigns + 1),
                    'budget': np.random.uniform(1000, 10000, n_campaigns),
                    'impressions': np.random.uniform(10000, 100000, n_campaigns),
                    'clicks': np.random.uniform(100, 5000, n_campaigns),
                    'conversions': np.random.uniform(10, 500, n_campaigns),
                    'cost_per_click': np.random.uniform(0.5, 5.0, n_campaigns),
                    'target_audience_size': np.random.uniform(10000, 1000000, n_campaigns),
                    'campaign_duration': np.random.randint(7, 90, n_campaigns),
                    'channel': np.random.choice(['google_ads', 'facebook', 'instagram', 'linkedin', 'twitter'], n_campaigns),
                    'device_target': np.random.choice(['mobile', 'desktop', 'all'], n_campaigns),
                    'age_group': np.random.choice(['18-25', '26-35', '36-45', '46-55', '55+'], n_campaigns)
                })

            # Calculate performance metrics
            campaign_data['ctr'] = campaign_data['clicks'] / campaign_data['impressions']
            campaign_data['conversion_rate'] = campaign_data['conversions'] / campaign_data['clicks']
            campaign_data['cost_per_conversion'] = campaign_data['budget'] / campaign_data['conversions']
            campaign_data['roi'] = (campaign_data['conversions'] * 50 - campaign_data['budget']) / campaign_data['budget']  # Assuming $50 per conversion

            # Identify top performing campaigns
            top_campaigns = campaign_data.nlargest(10, 'roi')[['campaign_id', 'roi', 'conversion_rate', 'ctr']].to_dict('records')

            # Identify underperforming campaigns
            bottom_campaigns = campaign_data.nsmallest(10, 'roi')[['campaign_id', 'roi', 'conversion_rate', 'ctr']].to_dict('records')

            # Channel performance analysis
            channel_performance = campaign_data.groupby('channel').agg({
                'roi': 'mean',
                'conversion_rate': 'mean',
                'ctr': 'mean',
                'cost_per_conversion': 'mean'
            }).round(4).to_dict('index')

            # Device performance analysis
            device_performance = campaign_data.groupby('device_target').agg({
                'roi': 'mean',
                'conversion_rate': 'mean',
                'ctr': 'mean'
            }).round(4).to_dict('index')

            # Age group performance
            age_performance = campaign_data.groupby('age_group').agg({
                'roi': 'mean',
                'conversion_rate': 'mean',
                'ctr': 'mean'
            }).round(4).to_dict('index')

            # Generate optimization recommendations
            recommendations = []

            # Budget reallocation recommendations
            best_channel = max(channel_performance.items(), key=lambda x: x[1]['roi'])
            worst_channel = min(channel_performance.items(), key=lambda x: x[1]['roi'])

            recommendations.append({
                'type': 'budget_reallocation',
                'recommendation': f"زيادة الميزانية لقناة {best_channel[0]} (ROI: {best_channel[1]['roi']:.2%}) وتقليل الميزانية لقناة {worst_channel[0]} (ROI: {worst_channel[1]['roi']:.2%})",
                'impact': 'high'
            })

            # Targeting recommendations
            best_age_group = max(age_performance.items(), key=lambda x: x[1]['conversion_rate'])
            recommendations.append({
                'type': 'targeting_optimization',
                'recommendation': f"التركيز على الفئة العمرية {best_age_group[0]} (معدل التحويل: {best_age_group[1]['conversion_rate']:.2%})",
                'impact': 'medium'
            })

            # Device optimization
            best_device = max(device_performance.items(), key=lambda x: x[1]['ctr'])
            recommendations.append({
                'type': 'device_optimization',
                'recommendation': f"تحسين الحملات لأجهزة {best_device[0]} (معدل النقر: {best_device[1]['ctr']:.2%})",
                'impact': 'medium'
            })

            # Save analysis results
            analysis_results = {
                'campaign_data': campaign_data.to_dict('records'),
                'channel_performance': channel_performance,
                'device_performance': device_performance,
                'age_performance': age_performance,
                'recommendations': recommendations
            }

            self.save_model(analysis_results, 'campaign_optimization', {
                'model_type': 'campaign_optimization',
                'algorithm': 'performance_analysis',
                'campaigns_analyzed': len(campaign_data),
                'avg_roi': float(campaign_data['roi'].mean()),
                'created_at': datetime.now().isoformat()
            })

            return {
                'success': True,
                'top_campaigns': top_campaigns,
                'bottom_campaigns': bottom_campaigns,
                'channel_performance': channel_performance,
                'device_performance': device_performance,
                'age_performance': age_performance,
                'recommendations': recommendations,
                'overall_metrics': {
                    'avg_roi': float(campaign_data['roi'].mean()),
                    'avg_conversion_rate': float(campaign_data['conversion_rate'].mean()),
                    'avg_ctr': float(campaign_data['ctr'].mean()),
                    'total_budget': float(campaign_data['budget'].sum()),
                    'total_conversions': float(campaign_data['conversions'].sum())
                },
                'model_saved': True
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في تحسين الحملات: {str(e)}"
            }

    def customer_journey_analysis(self, journey_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Customer Journey Analysis using Markov Chain approach
        """
        try:
            # Generate sample journey data if not provided
            if journey_data is None:
                np.random.seed(42)
                n_customers = 1000

                # Define journey stages
                stages = ['awareness', 'interest', 'consideration', 'purchase', 'retention', 'advocacy']

                journey_data = []
                for customer_id in range(1, n_customers + 1):
                    # Generate random journey path
                    journey_length = np.random.randint(3, 8)
                    current_stage = 'awareness'
                    customer_journey = [current_stage]

                    for step in range(journey_length - 1):
                        # Define transition probabilities
                        if current_stage == 'awareness':
                            next_stage = np.random.choice(['interest', 'consideration'], p=[0.7, 0.3])
                        elif current_stage == 'interest':
                            next_stage = np.random.choice(['consideration', 'purchase', 'awareness'], p=[0.5, 0.3, 0.2])
                        elif current_stage == 'consideration':
                            next_stage = np.random.choice(['purchase', 'interest', 'awareness'], p=[0.6, 0.2, 0.2])
                        elif current_stage == 'purchase':
                            next_stage = np.random.choice(['retention', 'advocacy'], p=[0.8, 0.2])
                        elif current_stage == 'retention':
                            next_stage = np.random.choice(['advocacy', 'purchase'], p=[0.4, 0.6])
                        else:  # advocacy
                            next_stage = np.random.choice(['retention', 'advocacy'], p=[0.3, 0.7])

                        customer_journey.append(next_stage)
                        current_stage = next_stage

                    # Create records for each step
                    for i, stage in enumerate(customer_journey):
                        journey_data.append({
                            'customer_id': customer_id,
                            'step': i + 1,
                            'stage': stage,
                            'timestamp': datetime.now() - timedelta(days=np.random.randint(0, 365)),
                            'channel': np.random.choice(['website', 'email', 'social', 'ads', 'direct']),
                            'device': np.random.choice(['mobile', 'desktop', 'tablet']),
                            'duration_minutes': np.random.exponential(10)
                        })

                journey_data = pd.DataFrame(journey_data)

            # Calculate transition matrix
            journey_data_sorted = journey_data.sort_values(['customer_id', 'step'])

            # Create transition pairs
            transitions = []
            for customer_id in journey_data_sorted['customer_id'].unique():
                customer_journey = journey_data_sorted[journey_data_sorted['customer_id'] == customer_id]['stage'].tolist()
                for i in range(len(customer_journey) - 1):
                    transitions.append((customer_journey[i], customer_journey[i + 1]))

            # Calculate transition probabilities
            transition_counts = pd.DataFrame(transitions, columns=['from_stage', 'to_stage']).value_counts().reset_index()
            transition_matrix = transition_counts.pivot(index='from_stage', columns='to_stage', values='count').fillna(0)

            # Normalize to get probabilities
            transition_probs = transition_matrix.div(transition_matrix.sum(axis=1), axis=0).fillna(0)

            # Identify bottlenecks (stages with high drop-off)
            stage_counts = journey_data['stage'].value_counts()
            stage_conversion = {}

            for stage in stages:
                if stage in stage_counts.index:
                    stage_conversion[stage] = {
                        'visitors': int(stage_counts[stage]),
                        'conversion_rate': float(stage_counts[stage] / stage_counts.get('awareness', 1))
                    }

            # Channel effectiveness by stage
            channel_effectiveness = journey_data.groupby(['stage', 'channel']).size().unstack(fill_value=0)
            channel_effectiveness_pct = channel_effectiveness.div(channel_effectiveness.sum(axis=1), axis=0)

            # Average time spent in each stage
            avg_duration = journey_data.groupby('stage')['duration_minutes'].mean().to_dict()

            # Identify optimization opportunities
            recommendations = []

            # Find stages with low conversion
            for stage, data in stage_conversion.items():
                if data['conversion_rate'] < 0.5 and stage != 'awareness':
                    recommendations.append({
                        'type': 'conversion_optimization',
                        'stage': stage,
                        'recommendation': f"تحسين مرحلة {stage} - معدل التحويل منخفض ({data['conversion_rate']:.1%})",
                        'impact': 'high'
                    })

            # Find most effective channels per stage
            for stage in channel_effectiveness_pct.index:
                best_channel = channel_effectiveness_pct.loc[stage].idxmax()
                recommendations.append({
                    'type': 'channel_optimization',
                    'stage': stage,
                    'recommendation': f"التركيز على قناة {best_channel} في مرحلة {stage}",
                    'impact': 'medium'
                })

            # Save analysis
            analysis_results = {
                'transition_matrix': transition_probs.to_dict(),
                'stage_conversion': stage_conversion,
                'channel_effectiveness': channel_effectiveness_pct.to_dict(),
                'avg_duration': avg_duration,
                'recommendations': recommendations
            }

            self.save_model(analysis_results, 'customer_journey_analysis', {
                'model_type': 'customer_journey_analysis',
                'algorithm': 'markov_chain',
                'customers_analyzed': len(journey_data['customer_id'].unique()),
                'total_touchpoints': len(journey_data),
                'created_at': datetime.now().isoformat()
            })

            return {
                'success': True,
                'transition_matrix': transition_probs.to_dict(),
                'stage_conversion': stage_conversion,
                'channel_effectiveness': channel_effectiveness_pct.to_dict(),
                'avg_duration_minutes': avg_duration,
                'recommendations': recommendations,
                'summary': {
                    'total_customers': len(journey_data['customer_id'].unique()),
                    'total_touchpoints': len(journey_data),
                    'avg_journey_length': float(journey_data.groupby('customer_id')['step'].max().mean()),
                    'most_common_entry': journey_data[journey_data['step'] == 1]['stage'].mode().iloc[0],
                    'purchase_rate': float(stage_conversion.get('purchase', {}).get('conversion_rate', 0))
                },
                'model_saved': True
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"فشل في تحليل رحلة العميل: {str(e)}"
            }

    def get_models_status(self) -> Dict[str, Any]:
        """Get status of all MarketMind models"""
        models_info = {}

        for model_file in os.listdir(self.models_dir):
            if model_file.endswith('_metadata.json'):
                model_name = model_file.replace('_metadata.json', '')
                try:
                    with open(os.path.join(self.models_dir, model_file), 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        models_info[model_name] = metadata
                except:
                    continue

        return {
            'models_count': len(models_info),
            'models': models_info,
            'models_directory': self.models_dir
        }
