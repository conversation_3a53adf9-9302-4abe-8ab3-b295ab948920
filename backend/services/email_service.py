import os
import smtplib
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import jinja2
from sendgrid import Send<PERSON>rid<PERSON><PERSON>lient
from sendgrid.helpers.mail import Mail, Attachment, FileContent, FileName, FileType, Disposition
import base64
import json

class EmailService:
    """Comprehensive email service with multiple providers"""
    
    def __init__(self):
        self.sendgrid_api_key = os.getenv("SENDGRID_API_KEY")
        self.smtp_host = os.getenv("SMTP_HOST", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_user = os.getenv("SMTP_USER")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        self.from_email = os.getenv("FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("FROM_NAME", "AI Marketing Platform")
        
        # Initialize Jinja2 for email templates
        self.template_loader = jinja2.FileSystemLoader('email_templates')
        self.template_env = jinja2.Environment(loader=self.template_loader)
    
    def send_email(self, to_email: str, subject: str, content: str, 
                   html_content: str = None, attachments: List[Dict] = None,
                   template_name: str = None, template_data: Dict = None) -> Dict[str, Any]:
        """Send email using available provider"""
        try:
            # Use template if specified
            if template_name and template_data:
                html_content = self._render_template(template_name, template_data)
                if not content:
                    content = self._html_to_text(html_content)
            
            # Try SendGrid first, fallback to SMTP
            if self.sendgrid_api_key:
                return self._send_via_sendgrid(to_email, subject, content, html_content, attachments)
            else:
                return self._send_via_smtp(to_email, subject, content, html_content, attachments)
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_via_sendgrid(self, to_email: str, subject: str, content: str,
                          html_content: str = None, attachments: List[Dict] = None) -> Dict[str, Any]:
        """Send email via SendGrid"""
        try:
            message = Mail(
                from_email=(self.from_email, self.from_name),
                to_emails=to_email,
                subject=subject,
                plain_text_content=content,
                html_content=html_content or content
            )
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    with open(attachment['path'], 'rb') as f:
                        data = f.read()
                    
                    encoded_file = base64.b64encode(data).decode()
                    
                    attached_file = Attachment(
                        FileContent(encoded_file),
                        FileName(attachment['filename']),
                        FileType(attachment.get('type', 'application/octet-stream')),
                        Disposition('attachment')
                    )
                    message.attachment = attached_file
            
            sg = SendGridAPIClient(api_key=self.sendgrid_api_key)
            response = sg.send(message)
            
            return {
                "success": True,
                "provider": "sendgrid",
                "status_code": response.status_code,
                "message_id": response.headers.get('X-Message-Id')
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "provider": "sendgrid"}
    
    def _send_via_smtp(self, to_email: str, subject: str, content: str,
                      html_content: str = None, attachments: List[Dict] = None) -> Dict[str, Any]:
        """Send email via SMTP"""
        try:
            msg = MIMEMultipart('alternative')
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add text content
            text_part = MIMEText(content, 'plain')
            msg.attach(text_part)
            
            # Add HTML content if provided
            if html_content:
                html_part = MIMEText(html_content, 'html')
                msg.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    with open(attachment['path'], 'rb') as f:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(f.read())
                    
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(self.smtp_host, self.smtp_port)
            server.starttls()
            server.login(self.smtp_user, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
            return {
                "success": True,
                "provider": "smtp",
                "message": "Email sent successfully"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "provider": "smtp"}
    
    def _render_template(self, template_name: str, data: Dict[str, Any]) -> str:
        """Render email template with data"""
        try:
            template = self.template_env.get_template(f"{template_name}.html")
            return template.render(**data)
        except Exception as e:
            # Fallback to basic template
            return self._get_basic_template(data)
    
    def _get_basic_template(self, data: Dict[str, Any]) -> str:
        """Basic email template fallback"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>AI Marketing Platform</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #1f77b4; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background: #f9f9f9; }}
                .footer {{ padding: 20px; text-align: center; color: #666; }}
                .button {{ background: #1f77b4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>AI Marketing Platform</h1>
                </div>
                <div class="content">
                    <h2>{data.get('title', 'Notification')}</h2>
                    <p>{data.get('message', 'You have a new notification from AI Marketing Platform.')}</p>
                    {f'<a href="{data["action_url"]}" class="button">{data.get("action_text", "View Details")}</a>' if data.get('action_url') else ''}
                </div>
                <div class="footer">
                    <p>© 2024 AI Marketing Platform. All rights reserved.</p>
                    <p>If you no longer wish to receive these emails, <a href="#">unsubscribe here</a>.</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _html_to_text(self, html: str) -> str:
        """Convert HTML to plain text"""
        # Simple HTML to text conversion
        import re
        text = re.sub('<[^<]+?>', '', html)
        return text.strip()


class NotificationService:
    """Service for managing automated notifications"""
    
    def __init__(self):
        self.email_service = EmailService()
        self.notification_types = {
            "welcome": {
                "subject": "Welcome to AI Marketing Platform!",
                "template": "welcome",
                "priority": "high"
            },
            "campaign_milestone": {
                "subject": "Campaign Milestone Reached",
                "template": "campaign_milestone",
                "priority": "medium"
            },
            "budget_alert": {
                "subject": "Budget Alert - Action Required",
                "template": "budget_alert",
                "priority": "high"
            },
            "performance_report": {
                "subject": "Your Weekly Performance Report",
                "template": "performance_report",
                "priority": "low"
            },
            "churn_risk": {
                "subject": "Customer Churn Risk Alert",
                "template": "churn_risk",
                "priority": "high"
            },
            "subscription_expiry": {
                "subject": "Subscription Expiring Soon",
                "template": "subscription_expiry",
                "priority": "high"
            }
        }
    
    def send_welcome_email(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send welcome email to new user"""
        template_data = {
            "user_name": user_data.get("name", "User"),
            "user_email": user_data.get("email"),
            "login_url": "https://aimarketing.com/login",
            "support_url": "https://aimarketing.com/support",
            "title": "Welcome to AI Marketing Platform!",
            "message": f"Hi {user_data.get('name', 'there')}, welcome to AI Marketing Platform! We're excited to help you transform your marketing with AI.",
            "action_url": "https://aimarketing.com/dashboard",
            "action_text": "Get Started"
        }
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=self.notification_types["welcome"]["subject"],
            content="",
            template_name="welcome",
            template_data=template_data
        )
    
    def send_campaign_milestone_alert(self, user_data: Dict[str, Any], campaign_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send campaign milestone notification"""
        milestone = campaign_data.get("milestone", "goal reached")
        
        template_data = {
            "user_name": user_data.get("name", "User"),
            "campaign_name": campaign_data.get("name", "Your Campaign"),
            "milestone": milestone,
            "current_performance": campaign_data.get("performance", {}),
            "title": f"Campaign Milestone: {milestone}",
            "message": f"Great news! Your campaign '{campaign_data.get('name')}' has reached an important milestone: {milestone}.",
            "action_url": f"https://aimarketing.com/campaigns/{campaign_data.get('id')}",
            "action_text": "View Campaign"
        }
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=self.notification_types["campaign_milestone"]["subject"],
            content="",
            template_name="campaign_milestone",
            template_data=template_data
        )
    
    def send_budget_alert(self, user_data: Dict[str, Any], budget_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send budget depletion alert"""
        template_data = {
            "user_name": user_data.get("name", "User"),
            "campaign_name": budget_data.get("campaign_name", "Your Campaign"),
            "budget_used": budget_data.get("used_percentage", 0),
            "remaining_budget": budget_data.get("remaining", 0),
            "title": "Budget Alert - Action Required",
            "message": f"Your campaign '{budget_data.get('campaign_name')}' has used {budget_data.get('used_percentage', 0)}% of its budget.",
            "action_url": f"https://aimarketing.com/campaigns/{budget_data.get('campaign_id')}",
            "action_text": "Manage Budget"
        }
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=self.notification_types["budget_alert"]["subject"],
            content="",
            template_name="budget_alert",
            template_data=template_data
        )
    
    def send_performance_report(self, user_data: Dict[str, Any], report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send weekly/monthly performance report"""
        template_data = {
            "user_name": user_data.get("name", "User"),
            "report_period": report_data.get("period", "this week"),
            "summary": report_data.get("summary", {}),
            "top_campaigns": report_data.get("top_campaigns", []),
            "insights": report_data.get("insights", []),
            "title": f"Your {report_data.get('period', 'Weekly')} Performance Report",
            "message": f"Here's how your campaigns performed {report_data.get('period', 'this week')}.",
            "action_url": "https://aimarketing.com/reports",
            "action_text": "View Full Report"
        }
        
        # Attach PDF report if available
        attachments = []
        if report_data.get("pdf_path"):
            attachments.append({
                "path": report_data["pdf_path"],
                "filename": f"performance_report_{datetime.now().strftime('%Y%m%d')}.pdf",
                "type": "application/pdf"
            })
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=self.notification_types["performance_report"]["subject"],
            content="",
            template_name="performance_report",
            template_data=template_data,
            attachments=attachments
        )
    
    def send_churn_risk_alert(self, user_data: Dict[str, Any], risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send churn risk alert to admin/sales team"""
        template_data = {
            "user_name": user_data.get("name", "User"),
            "user_email": user_data.get("email"),
            "risk_score": risk_data.get("risk_score", 0),
            "risk_factors": risk_data.get("factors", []),
            "last_activity": risk_data.get("last_activity"),
            "title": "High Churn Risk Customer Alert",
            "message": f"Customer {user_data.get('name')} has a high churn risk score of {risk_data.get('risk_score', 0)}%.",
            "action_url": f"https://aimarketing.com/admin/users/{user_data.get('id')}",
            "action_text": "View Customer"
        }
        
        # Send to admin/sales team
        admin_emails = ["<EMAIL>", "<EMAIL>"]
        results = []
        
        for admin_email in admin_emails:
            result = self.email_service.send_email(
                to_email=admin_email,
                subject=self.notification_types["churn_risk"]["subject"],
                content="",
                template_name="churn_risk",
                template_data=template_data
            )
            results.append(result)
        
        return {"success": all(r.get("success", False) for r in results), "results": results}
    
    def send_subscription_expiry_notice(self, user_data: Dict[str, Any], subscription_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send subscription expiry notice"""
        days_until_expiry = subscription_data.get("days_until_expiry", 0)
        
        template_data = {
            "user_name": user_data.get("name", "User"),
            "plan_name": subscription_data.get("plan", "Pro"),
            "expiry_date": subscription_data.get("expiry_date"),
            "days_until_expiry": days_until_expiry,
            "renewal_url": "https://aimarketing.com/billing",
            "title": "Subscription Expiring Soon",
            "message": f"Your {subscription_data.get('plan')} subscription expires in {days_until_expiry} days.",
            "action_url": "https://aimarketing.com/billing",
            "action_text": "Renew Subscription"
        }
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=self.notification_types["subscription_expiry"]["subject"],
            content="",
            template_name="subscription_expiry",
            template_data=template_data
        )
    
    def send_onboarding_sequence(self, user_data: Dict[str, Any], step: int) -> Dict[str, Any]:
        """Send onboarding email sequence"""
        onboarding_steps = {
            1: {
                "subject": "Getting Started with AI Marketing Platform",
                "title": "Let's Get You Started!",
                "message": "Welcome! Here's how to set up your first campaign.",
                "action_url": "https://aimarketing.com/onboarding/step1",
                "action_text": "Start Setup"
            },
            2: {
                "subject": "Create Your First Campaign",
                "title": "Ready to Create Your First Campaign?",
                "message": "Let's create your first AI-powered marketing campaign.",
                "action_url": "https://aimarketing.com/campaigns/new",
                "action_text": "Create Campaign"
            },
            3: {
                "subject": "Unlock Advanced Features",
                "title": "Discover Advanced AI Features",
                "message": "Learn about our advanced AI features to maximize your results.",
                "action_url": "https://aimarketing.com/features",
                "action_text": "Explore Features"
            }
        }
        
        step_data = onboarding_steps.get(step, onboarding_steps[1])
        template_data = {
            "user_name": user_data.get("name", "User"),
            "step": step,
            **step_data
        }
        
        return self.email_service.send_email(
            to_email=user_data["email"],
            subject=step_data["subject"],
            content="",
            template_name="onboarding",
            template_data=template_data
        )
