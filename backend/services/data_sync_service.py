from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import requests
import json
from datetime import datetime, timedelta
import asyncio
import logging

logger = logging.getLogger(__name__)

class DataSyncService:
    """خدمة مزامنة البيانات من المنصات المختلفة"""
    
    def __init__(self, db: Session):
        self.db = db
        
    async def sync_account_data(self, account_id: str, data_types: List[str] = None):
        """مزامنة البيانات من حساب محدد"""
        try:
            logger.info(f"Starting data sync for account: {account_id}")
            
            # الحصول على معلومات الحساب
            account = self.get_account_info(account_id)
            if not account:
                logger.error(f"Account not found: {account_id}")
                return
            
            platform = account.get("platform")
            access_token = account.get("access_token")
            
            if not access_token:
                logger.error(f"No access token for account: {account_id}")
                return
            
            # تحديد أنواع البيانات المطلوب مزامنتها
            if not data_types:
                data_types = self.get_default_data_types(platform)
            
            # مزامنة كل نوع من البيانات
            for data_type in data_types:
                try:
                    await self.sync_data_type(platform, access_token, data_type, account_id)
                except Exception as e:
                    logger.error(f"Failed to sync {data_type} for {platform}: {str(e)}")
            
            # تحديث وقت آخر مزامنة
            self.update_last_sync(account_id)
            
            logger.info(f"Data sync completed for account: {account_id}")
            
        except Exception as e:
            logger.error(f"Data sync failed for account {account_id}: {str(e)}")
    
    async def sync_data_type(self, platform: str, access_token: str, data_type: str, account_id: str):
        """مزامنة نوع محدد من البيانات"""
        
        if platform == "facebook":
            await self.sync_facebook_data(access_token, data_type, account_id)
        elif platform == "instagram":
            await self.sync_instagram_data(access_token, data_type, account_id)
        elif platform == "google":
            await self.sync_google_ads_data(access_token, data_type, account_id)
        elif platform == "linkedin":
            await self.sync_linkedin_data(access_token, data_type, account_id)
        elif platform == "twitter":
            await self.sync_twitter_data(access_token, data_type, account_id)
        elif platform == "youtube":
            await self.sync_youtube_data(access_token, data_type, account_id)
    
    async def sync_facebook_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات Facebook"""
        base_url = "https://graph.facebook.com/v18.0"
        
        if data_type == "pages":
            # جلب صفحات Facebook
            response = requests.get(
                f"{base_url}/me/accounts",
                params={"access_token": access_token}
            )
            if response.status_code == 200:
                pages = response.json().get("data", [])
                self.save_facebook_pages(account_id, pages)
        
        elif data_type == "ads":
            # جلب الإعلانات
            response = requests.get(
                f"{base_url}/me/adaccounts",
                params={
                    "access_token": access_token,
                    "fields": "name,account_status,currency,timezone_name"
                }
            )
            if response.status_code == 200:
                ad_accounts = response.json().get("data", [])
                self.save_facebook_ads(account_id, ad_accounts)
        
        elif data_type == "insights":
            # جلب إحصائيات الصفحات
            await self.sync_facebook_insights(access_token, account_id)
    
    async def sync_instagram_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات Instagram"""
        base_url = "https://graph.instagram.com"
        
        if data_type == "posts":
            # جلب المنشورات
            response = requests.get(
                f"{base_url}/me/media",
                params={
                    "access_token": access_token,
                    "fields": "id,caption,media_type,media_url,permalink,timestamp,like_count,comments_count"
                }
            )
            if response.status_code == 200:
                posts = response.json().get("data", [])
                self.save_instagram_posts(account_id, posts)
        
        elif data_type == "insights":
            # جلب إحصائيات الحساب
            response = requests.get(
                f"{base_url}/me/insights",
                params={
                    "access_token": access_token,
                    "metric": "impressions,reach,profile_views,website_clicks"
                }
            )
            if response.status_code == 200:
                insights = response.json().get("data", [])
                self.save_instagram_insights(account_id, insights)
    
    async def sync_google_ads_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات Google Ads"""
        headers = {"Authorization": f"Bearer {access_token}"}
        
        if data_type == "campaigns":
            # جلب الحملات الإعلانية
            # ملاحظة: Google Ads API يتطلب إعداد خاص
            logger.info(f"Syncing Google Ads campaigns for account: {account_id}")
            # هنا يجب استخدام Google Ads API
        
        elif data_type == "keywords":
            # جلب الكلمات المفتاحية
            logger.info(f"Syncing Google Ads keywords for account: {account_id}")
    
    async def sync_linkedin_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات LinkedIn"""
        headers = {"Authorization": f"Bearer {access_token}"}
        base_url = "https://api.linkedin.com/v2"
        
        if data_type == "company_posts":
            # جلب منشورات الشركة
            response = requests.get(
                f"{base_url}/shares",
                headers=headers,
                params={"q": "owners", "owners": "urn:li:organization:123"}
            )
            if response.status_code == 200:
                posts = response.json().get("elements", [])
                self.save_linkedin_posts(account_id, posts)
    
    async def sync_twitter_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات Twitter"""
        headers = {"Authorization": f"Bearer {access_token}"}
        base_url = "https://api.twitter.com/2"
        
        if data_type == "tweets":
            # جلب التغريدات
            response = requests.get(
                f"{base_url}/users/me/tweets",
                headers=headers,
                params={"tweet.fields": "created_at,public_metrics,context_annotations"}
            )
            if response.status_code == 200:
                tweets = response.json().get("data", [])
                self.save_twitter_tweets(account_id, tweets)
    
    async def sync_youtube_data(self, access_token: str, data_type: str, account_id: str):
        """مزامنة بيانات YouTube"""
        headers = {"Authorization": f"Bearer {access_token}"}
        base_url = "https://www.googleapis.com/youtube/v3"
        
        if data_type == "videos":
            # جلب الفيديوهات
            response = requests.get(
                f"{base_url}/search",
                headers=headers,
                params={
                    "part": "snippet",
                    "forMine": "true",
                    "type": "video",
                    "maxResults": 50
                }
            )
            if response.status_code == 200:
                videos = response.json().get("items", [])
                self.save_youtube_videos(account_id, videos)
    
    def get_account_info(self, account_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الحساب"""
        # هنا يجب استعلام قاعدة البيانات
        return {
            "platform": "instagram",
            "access_token": "mock_token",
            "user_id": "user_123"
        }
    
    def get_default_data_types(self, platform: str) -> List[str]:
        """الحصول على أنواع البيانات الافتراضية لكل منصة"""
        defaults = {
            "facebook": ["pages", "ads", "insights"],
            "instagram": ["posts", "insights"],
            "google": ["campaigns", "keywords", "conversions"],
            "linkedin": ["company_posts", "insights"],
            "twitter": ["tweets", "followers"],
            "youtube": ["videos", "analytics"]
        }
        return defaults.get(platform, [])
    
    def update_last_sync(self, account_id: str):
        """تحديث وقت آخر مزامنة"""
        logger.info(f"Updated last sync time for account: {account_id}")
        # هنا يجب تحديث قاعدة البيانات
    
    # دوال حفظ البيانات
    def save_facebook_pages(self, account_id: str, pages: List[Dict]):
        """حفظ صفحات Facebook"""
        logger.info(f"Saving {len(pages)} Facebook pages for account: {account_id}")
        # هنا يجب حفظ البيانات في قاعدة البيانات
    
    def save_facebook_ads(self, account_id: str, ads: List[Dict]):
        """حفظ إعلانات Facebook"""
        logger.info(f"Saving {len(ads)} Facebook ads for account: {account_id}")
    
    def save_instagram_posts(self, account_id: str, posts: List[Dict]):
        """حفظ منشورات Instagram"""
        logger.info(f"Saving {len(posts)} Instagram posts for account: {account_id}")
    
    def save_instagram_insights(self, account_id: str, insights: List[Dict]):
        """حفظ إحصائيات Instagram"""
        logger.info(f"Saving Instagram insights for account: {account_id}")
    
    def save_linkedin_posts(self, account_id: str, posts: List[Dict]):
        """حفظ منشورات LinkedIn"""
        logger.info(f"Saving {len(posts)} LinkedIn posts for account: {account_id}")
    
    def save_twitter_tweets(self, account_id: str, tweets: List[Dict]):
        """حفظ تغريدات Twitter"""
        logger.info(f"Saving {len(tweets)} Twitter tweets for account: {account_id}")
    
    def save_youtube_videos(self, account_id: str, videos: List[Dict]):
        """حفظ فيديوهات YouTube"""
        logger.info(f"Saving {len(videos)} YouTube videos for account: {account_id}")
    
    async def sync_facebook_insights(self, access_token: str, account_id: str):
        """مزامنة إحصائيات Facebook المفصلة"""
        # جلب إحصائيات مفصلة للصفحات
        logger.info(f"Syncing detailed Facebook insights for account: {account_id}")
