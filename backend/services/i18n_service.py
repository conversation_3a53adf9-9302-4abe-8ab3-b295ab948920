import json
import os
from typing import Dict, Any, Optional
from datetime import datetime
from babel import Locale, dates, numbers
from babel.support import Format

class InternationalizationService:
    """Service for internationalization and localization"""
    
    def __init__(self):
        self.supported_languages = {
            'en': {
                'name': 'English',
                'native_name': 'English',
                'direction': 'ltr',
                'currency': 'USD',
                'date_format': '%Y-%m-%d',
                'time_format': '%H:%M:%S'
            },
            'ar': {
                'name': 'Arabic',
                'native_name': 'العربية',
                'direction': 'rtl',
                'currency': 'EGP',
                'date_format': '%d/%m/%Y',
                'time_format': '%H:%M:%S'
            }
        }
        
        self.translations = self._load_translations()
        self.default_language = 'en'
    
    def _load_translations(self) -> Dict[str, Dict[str, str]]:
        """Load translation files"""
        translations = {}
        
        # English translations
        translations['en'] = {
            # Navigation
            'nav.dashboard': 'Dashboard',
            'nav.campaigns': 'Campaigns',
            'nav.analytics': 'Analytics',
            'nav.reports': 'Reports',
            'nav.settings': 'Settings',
            'nav.logout': 'Logout',
            
            # Dashboard
            'dashboard.title': 'Dashboard',
            'dashboard.welcome': 'Welcome back, {name}!',
            'dashboard.overview': 'Overview',
            'dashboard.recent_activity': 'Recent Activity',
            'dashboard.quick_stats': 'Quick Stats',
            
            # Campaigns
            'campaigns.title': 'Campaigns',
            'campaigns.create': 'Create Campaign',
            'campaigns.edit': 'Edit Campaign',
            'campaigns.delete': 'Delete Campaign',
            'campaigns.status.active': 'Active',
            'campaigns.status.paused': 'Paused',
            'campaigns.status.completed': 'Completed',
            'campaigns.performance': 'Performance',
            'campaigns.budget': 'Budget',
            'campaigns.impressions': 'Impressions',
            'campaigns.clicks': 'Clicks',
            'campaigns.conversions': 'Conversions',
            'campaigns.ctr': 'Click-Through Rate',
            'campaigns.cpc': 'Cost Per Click',
            'campaigns.roas': 'Return on Ad Spend',
            
            # Analytics
            'analytics.title': 'Analytics',
            'analytics.overview': 'Analytics Overview',
            'analytics.sentiment': 'Sentiment Analysis',
            'analytics.segmentation': 'Customer Segmentation',
            'analytics.trends': 'Trend Analysis',
            'analytics.insights': 'Insights',
            'analytics.recommendations': 'Recommendations',
            
            # Reports
            'reports.title': 'Reports',
            'reports.generate': 'Generate Report',
            'reports.export': 'Export Report',
            'reports.schedule': 'Schedule Report',
            'reports.performance': 'Performance Report',
            'reports.comprehensive': 'Comprehensive Analysis',
            'reports.custom': 'Custom Report',
            
            # Settings
            'settings.title': 'Settings',
            'settings.profile': 'Profile Settings',
            'settings.account': 'Account Settings',
            'settings.notifications': 'Notification Settings',
            'settings.security': 'Security Settings',
            'settings.billing': 'Billing Settings',
            'settings.language': 'Language',
            'settings.timezone': 'Timezone',
            'settings.currency': 'Currency',
            
            # Authentication
            'auth.login': 'Login',
            'auth.register': 'Register',
            'auth.logout': 'Logout',
            'auth.email': 'Email',
            'auth.password': 'Password',
            'auth.confirm_password': 'Confirm Password',
            'auth.forgot_password': 'Forgot Password?',
            'auth.remember_me': 'Remember Me',
            'auth.sign_in': 'Sign In',
            'auth.sign_up': 'Sign Up',
            'auth.welcome': 'Welcome to AI Marketing Platform',
            
            # Common
            'common.save': 'Save',
            'common.cancel': 'Cancel',
            'common.delete': 'Delete',
            'common.edit': 'Edit',
            'common.view': 'View',
            'common.create': 'Create',
            'common.update': 'Update',
            'common.search': 'Search',
            'common.filter': 'Filter',
            'common.sort': 'Sort',
            'common.loading': 'Loading...',
            'common.error': 'Error',
            'common.success': 'Success',
            'common.warning': 'Warning',
            'common.info': 'Information',
            'common.yes': 'Yes',
            'common.no': 'No',
            'common.ok': 'OK',
            'common.close': 'Close',
            
            # Messages
            'messages.success.saved': 'Changes saved successfully',
            'messages.success.created': 'Created successfully',
            'messages.success.updated': 'Updated successfully',
            'messages.success.deleted': 'Deleted successfully',
            'messages.error.generic': 'An error occurred. Please try again.',
            'messages.error.network': 'Network error. Please check your connection.',
            'messages.error.unauthorized': 'You are not authorized to perform this action.',
            'messages.error.not_found': 'The requested resource was not found.',
            
            # Subscription
            'subscription.title': 'Subscription',
            'subscription.plan': 'Plan',
            'subscription.upgrade': 'Upgrade',
            'subscription.downgrade': 'Downgrade',
            'subscription.cancel': 'Cancel Subscription',
            'subscription.renew': 'Renew Subscription',
            'subscription.billing': 'Billing',
            'subscription.payment_method': 'Payment Method',
            'subscription.next_billing': 'Next Billing Date',
            
            # AI Features
            'ai.content_generation': 'AI Content Generation',
            'ai.sentiment_analysis': 'AI Sentiment Analysis',
            'ai.customer_segmentation': 'AI Customer Segmentation',
            'ai.trend_prediction': 'AI Trend Prediction',
            'ai.campaign_optimization': 'AI Campaign Optimization',
            'ai.chatbot': 'AI Chatbot',
            'ai.recommendations': 'AI Recommendations'
        }
        
        # Arabic translations
        translations['ar'] = {
            # Navigation
            'nav.dashboard': 'لوحة التحكم',
            'nav.campaigns': 'الحملات',
            'nav.analytics': 'التحليلات',
            'nav.reports': 'التقارير',
            'nav.settings': 'الإعدادات',
            'nav.logout': 'تسجيل الخروج',
            
            # Dashboard
            'dashboard.title': 'لوحة التحكم',
            'dashboard.welcome': 'مرحباً بعودتك، {name}!',
            'dashboard.overview': 'نظرة عامة',
            'dashboard.recent_activity': 'النشاط الأخير',
            'dashboard.quick_stats': 'إحصائيات سريعة',
            
            # Campaigns
            'campaigns.title': 'الحملات',
            'campaigns.create': 'إنشاء حملة',
            'campaigns.edit': 'تعديل الحملة',
            'campaigns.delete': 'حذف الحملة',
            'campaigns.status.active': 'نشطة',
            'campaigns.status.paused': 'متوقفة',
            'campaigns.status.completed': 'مكتملة',
            'campaigns.performance': 'الأداء',
            'campaigns.budget': 'الميزانية',
            'campaigns.impressions': 'مرات الظهور',
            'campaigns.clicks': 'النقرات',
            'campaigns.conversions': 'التحويلات',
            'campaigns.ctr': 'معدل النقر',
            'campaigns.cpc': 'تكلفة النقرة',
            'campaigns.roas': 'عائد الإنفاق الإعلاني',
            
            # Analytics
            'analytics.title': 'التحليلات',
            'analytics.overview': 'نظرة عامة على التحليلات',
            'analytics.sentiment': 'تحليل المشاعر',
            'analytics.segmentation': 'تجزئة العملاء',
            'analytics.trends': 'تحليل الاتجاهات',
            'analytics.insights': 'الرؤى',
            'analytics.recommendations': 'التوصيات',
            
            # Reports
            'reports.title': 'التقارير',
            'reports.generate': 'إنشاء تقرير',
            'reports.export': 'تصدير التقرير',
            'reports.schedule': 'جدولة التقرير',
            'reports.performance': 'تقرير الأداء',
            'reports.comprehensive': 'التحليل الشامل',
            'reports.custom': 'تقرير مخصص',
            
            # Settings
            'settings.title': 'الإعدادات',
            'settings.profile': 'إعدادات الملف الشخصي',
            'settings.account': 'إعدادات الحساب',
            'settings.notifications': 'إعدادات الإشعارات',
            'settings.security': 'إعدادات الأمان',
            'settings.billing': 'إعدادات الفواتير',
            'settings.language': 'اللغة',
            'settings.timezone': 'المنطقة الزمنية',
            'settings.currency': 'العملة',
            
            # Authentication
            'auth.login': 'تسجيل الدخول',
            'auth.register': 'إنشاء حساب',
            'auth.logout': 'تسجيل الخروج',
            'auth.email': 'البريد الإلكتروني',
            'auth.password': 'كلمة المرور',
            'auth.confirm_password': 'تأكيد كلمة المرور',
            'auth.forgot_password': 'نسيت كلمة المرور؟',
            'auth.remember_me': 'تذكرني',
            'auth.sign_in': 'دخول',
            'auth.sign_up': 'إنشاء حساب',
            'auth.welcome': 'مرحباً بك في منصة التسويق الذكي',
            
            # Common
            'common.save': 'حفظ',
            'common.cancel': 'إلغاء',
            'common.delete': 'حذف',
            'common.edit': 'تعديل',
            'common.view': 'عرض',
            'common.create': 'إنشاء',
            'common.update': 'تحديث',
            'common.search': 'بحث',
            'common.filter': 'تصفية',
            'common.sort': 'ترتيب',
            'common.loading': 'جاري التحميل...',
            'common.error': 'خطأ',
            'common.success': 'نجح',
            'common.warning': 'تحذير',
            'common.info': 'معلومات',
            'common.yes': 'نعم',
            'common.no': 'لا',
            'common.ok': 'موافق',
            'common.close': 'إغلاق',
            
            # Messages
            'messages.success.saved': 'تم حفظ التغييرات بنجاح',
            'messages.success.created': 'تم الإنشاء بنجاح',
            'messages.success.updated': 'تم التحديث بنجاح',
            'messages.success.deleted': 'تم الحذف بنجاح',
            'messages.error.generic': 'حدث خطأ. يرجى المحاولة مرة أخرى.',
            'messages.error.network': 'خطأ في الشبكة. يرجى التحقق من الاتصال.',
            'messages.error.unauthorized': 'غير مصرح لك بتنفيذ هذا الإجراء.',
            'messages.error.not_found': 'لم يتم العثور على المورد المطلوب.',
            
            # Subscription
            'subscription.title': 'الاشتراك',
            'subscription.plan': 'الخطة',
            'subscription.upgrade': 'ترقية',
            'subscription.downgrade': 'تخفيض',
            'subscription.cancel': 'إلغاء الاشتراك',
            'subscription.renew': 'تجديد الاشتراك',
            'subscription.billing': 'الفواتير',
            'subscription.payment_method': 'طريقة الدفع',
            'subscription.next_billing': 'تاريخ الفاتورة التالية',
            
            # AI Features
            'ai.content_generation': 'توليد المحتوى بالذكاء الاصطناعي',
            'ai.sentiment_analysis': 'تحليل المشاعر بالذكاء الاصطناعي',
            'ai.customer_segmentation': 'تجزئة العملاء بالذكاء الاصطناعي',
            'ai.trend_prediction': 'التنبؤ بالاتجاهات بالذكاء الاصطناعي',
            'ai.campaign_optimization': 'تحسين الحملات بالذكاء الاصطناعي',
            'ai.chatbot': 'روبوت الدردشة الذكي',
            'ai.recommendations': 'توصيات الذكاء الاصطناعي'
        }
        
        return translations
    
    def get_translation(self, key: str, language: str = None, **kwargs) -> str:
        """Get translation for a key"""
        if not language:
            language = self.default_language
        
        if language not in self.translations:
            language = self.default_language
        
        translation = self.translations[language].get(key, key)
        
        # Format with provided kwargs
        if kwargs:
            try:
                translation = translation.format(**kwargs)
            except KeyError:
                pass  # Return unformatted if formatting fails
        
        return translation
    
    def get_language_info(self, language: str) -> Dict[str, Any]:
        """Get language information"""
        return self.supported_languages.get(language, self.supported_languages[self.default_language])
    
    def format_currency(self, amount: float, language: str = None, currency: str = None) -> str:
        """Format currency based on locale"""
        if not language:
            language = self.default_language
        
        lang_info = self.get_language_info(language)
        if not currency:
            currency = lang_info['currency']
        
        try:
            locale = Locale(language)
            return numbers.format_currency(amount, currency, locale=locale)
        except:
            # Fallback formatting
            if currency == 'EGP':
                return f"{amount:,.2f} ج.م"
            else:
                return f"${amount:,.2f}"
    
    def format_date(self, date: datetime, language: str = None, format_type: str = 'medium') -> str:
        """Format date based on locale"""
        if not language:
            language = self.default_language
        
        try:
            locale = Locale(language)
            return dates.format_date(date, format=format_type, locale=locale)
        except:
            # Fallback formatting
            lang_info = self.get_language_info(language)
            return date.strftime(lang_info['date_format'])
    
    def format_datetime(self, dt: datetime, language: str = None) -> str:
        """Format datetime based on locale"""
        if not language:
            language = self.default_language
        
        try:
            locale = Locale(language)
            return dates.format_datetime(dt, locale=locale)
        except:
            # Fallback formatting
            lang_info = self.get_language_info(language)
            date_str = dt.strftime(lang_info['date_format'])
            time_str = dt.strftime(lang_info['time_format'])
            return f"{date_str} {time_str}"
    
    def get_supported_languages(self) -> Dict[str, Dict[str, str]]:
        """Get list of supported languages"""
        return self.supported_languages
    
    def is_rtl(self, language: str) -> bool:
        """Check if language is right-to-left"""
        lang_info = self.get_language_info(language)
        return lang_info['direction'] == 'rtl'
    
    def get_localized_content_templates(self, language: str) -> Dict[str, Any]:
        """Get localized content templates for AI generation"""
        if language == 'ar':
            return {
                "email_templates": {
                    "welcome": "مرحباً {name}، نحن سعداء لانضمامك إلى منصة التسويق الذكي...",
                    "promotional": "عرض خاص لفترة محدودة! احصل على خصم {discount}% على {product}...",
                    "newsletter": "إليك آخر الأخبار والتحديثات من منصة التسويق الذكي..."
                },
                "social_templates": {
                    "product_launch": "🚀 نقدم لكم {product}! {benefit} #منتج_جديد #ابتكار",
                    "engagement": "ما هو {topic} المفضل لديكم؟ شاركونا في التعليقات! 👇"
                },
                "ad_copy_templates": {
                    "headline": "{product} - الحل الأمثل لـ {problem}",
                    "description": "اكتشف كيف يمكن لـ {product} أن يحول {benefit}"
                }
            }
        else:
            return {
                "email_templates": {
                    "welcome": "Welcome {name}, we're excited to have you join AI Marketing Platform...",
                    "promotional": "Special offer for a limited time! Get {discount}% off {product}...",
                    "newsletter": "Here's the latest news and updates from AI Marketing Platform..."
                },
                "social_templates": {
                    "product_launch": "🚀 Introducing {product}! {benefit} #NewProduct #Innovation",
                    "engagement": "What's your favorite {topic}? Share with us in the comments! 👇"
                },
                "ad_copy_templates": {
                    "headline": "{product} - The Ultimate Solution for {problem}",
                    "description": "Discover how {product} can transform {benefit}"
                }
            }
