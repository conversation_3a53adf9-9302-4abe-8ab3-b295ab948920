from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)

class IntegratedAnalyticsService:
    """خدمة تحليل البيانات المدمجة من جميع المنصات"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def generate_unified_report(self, user_id: str, date_range: Dict[str, str]) -> Dict[str, Any]:
        """إنشاء تقرير موحد من جميع المنصات المربوطة"""
        try:
            # جلب البيانات من جميع المنصات
            platforms_data = await self.fetch_all_platforms_data(user_id, date_range)
            
            # تحليل البيانات
            analysis = {
                "overview": self.generate_overview_metrics(platforms_data),
                "audience_insights": await self.analyze_audience_across_platforms(platforms_data),
                "content_performance": await self.analyze_content_performance(platforms_data),
                "engagement_trends": await self.analyze_engagement_trends(platforms_data),
                "roi_analysis": await self.calculate_roi_across_platforms(platforms_data),
                "recommendations": await self.generate_ai_recommendations(platforms_data),
                "competitive_analysis": await self.perform_competitive_analysis(platforms_data),
                "sentiment_analysis": await self.analyze_sentiment_across_platforms(platforms_data)
            }
            
            return {
                "success": True,
                "data": analysis,
                "generated_at": datetime.utcnow().isoformat(),
                "date_range": date_range
            }
            
        except Exception as e:
            logger.error(f"Failed to generate unified report: {str(e)}")
            raise
    
    async def fetch_all_platforms_data(self, user_id: str, date_range: Dict[str, str]) -> Dict[str, Any]:
        """جلب البيانات من جميع المنصات المربوطة"""
        platforms_data = {
            "facebook": await self.fetch_facebook_data(user_id, date_range),
            "instagram": await self.fetch_instagram_data(user_id, date_range),
            "google_ads": await self.fetch_google_ads_data(user_id, date_range),
            "linkedin": await self.fetch_linkedin_data(user_id, date_range),
            "twitter": await self.fetch_twitter_data(user_id, date_range),
            "youtube": await self.fetch_youtube_data(user_id, date_range)
        }
        
        # إزالة المنصات التي لا تحتوي على بيانات
        return {k: v for k, v in platforms_data.items() if v and v.get("connected")}
    
    def generate_overview_metrics(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء مقاييس عامة موحدة"""
        total_reach = 0
        total_engagement = 0
        total_impressions = 0
        total_clicks = 0
        total_conversions = 0
        total_spend = 0
        
        platform_breakdown = {}
        
        for platform, data in platforms_data.items():
            if not data:
                continue
                
            platform_metrics = data.get("metrics", {})
            reach = platform_metrics.get("reach", 0)
            engagement = platform_metrics.get("engagement", 0)
            impressions = platform_metrics.get("impressions", 0)
            clicks = platform_metrics.get("clicks", 0)
            conversions = platform_metrics.get("conversions", 0)
            spend = platform_metrics.get("spend", 0)
            
            total_reach += reach
            total_engagement += engagement
            total_impressions += impressions
            total_clicks += clicks
            total_conversions += conversions
            total_spend += spend
            
            platform_breakdown[platform] = {
                "reach": reach,
                "engagement": engagement,
                "impressions": impressions,
                "clicks": clicks,
                "conversions": conversions,
                "spend": spend,
                "engagement_rate": (engagement / impressions * 100) if impressions > 0 else 0,
                "ctr": (clicks / impressions * 100) if impressions > 0 else 0,
                "conversion_rate": (conversions / clicks * 100) if clicks > 0 else 0,
                "cpc": (spend / clicks) if clicks > 0 else 0,
                "cpa": (spend / conversions) if conversions > 0 else 0
            }
        
        return {
            "totals": {
                "reach": total_reach,
                "engagement": total_engagement,
                "impressions": total_impressions,
                "clicks": total_clicks,
                "conversions": total_conversions,
                "spend": total_spend,
                "overall_engagement_rate": (total_engagement / total_impressions * 100) if total_impressions > 0 else 0,
                "overall_ctr": (total_clicks / total_impressions * 100) if total_impressions > 0 else 0,
                "overall_conversion_rate": (total_conversions / total_clicks * 100) if total_clicks > 0 else 0,
                "overall_roi": ((total_conversions * 100 - total_spend) / total_spend * 100) if total_spend > 0 else 0
            },
            "platform_breakdown": platform_breakdown,
            "connected_platforms": len(platforms_data)
        }
    
    async def analyze_audience_across_platforms(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل الجمهور عبر جميع المنصات"""
        combined_demographics = defaultdict(int)
        combined_interests = defaultdict(int)
        combined_locations = defaultdict(int)
        
        for platform, data in platforms_data.items():
            audience_data = data.get("audience", {})
            
            # دمج البيانات الديموغرافية
            demographics = audience_data.get("demographics", {})
            for age_group, count in demographics.get("age_groups", {}).items():
                combined_demographics[age_group] += count
            
            # دمج الاهتمامات
            interests = audience_data.get("interests", {})
            for interest, count in interests.items():
                combined_interests[interest] += count
            
            # دمج المواقع الجغرافية
            locations = audience_data.get("locations", {})
            for location, count in locations.items():
                combined_locations[location] += count
        
        return {
            "unified_demographics": dict(combined_demographics),
            "unified_interests": dict(combined_interests),
            "unified_locations": dict(combined_locations),
            "audience_overlap": self.calculate_audience_overlap(platforms_data),
            "growth_trends": self.analyze_audience_growth(platforms_data)
        }
    
    async def analyze_content_performance(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل أداء المحتوى عبر المنصات"""
        all_posts = []
        
        for platform, data in platforms_data.items():
            posts = data.get("posts", [])
            for post in posts:
                post["platform"] = platform
                all_posts.append(post)
        
        if not all_posts:
            return {"message": "لا توجد منشورات للتحليل"}
        
        # تحليل أنواع المحتوى
        content_types = defaultdict(list)
        for post in all_posts:
            content_type = post.get("type", "text")
            content_types[content_type].append(post)
        
        # تحليل الأداء حسب نوع المحتوى
        performance_by_type = {}
        for content_type, posts in content_types.items():
            avg_engagement = np.mean([p.get("engagement", 0) for p in posts])
            avg_reach = np.mean([p.get("reach", 0) for p in posts])
            performance_by_type[content_type] = {
                "count": len(posts),
                "avg_engagement": avg_engagement,
                "avg_reach": avg_reach,
                "engagement_rate": (avg_engagement / avg_reach * 100) if avg_reach > 0 else 0
            }
        
        # أفضل المنشورات
        top_posts = sorted(all_posts, key=lambda x: x.get("engagement", 0), reverse=True)[:10]
        
        return {
            "performance_by_type": performance_by_type,
            "top_performing_posts": top_posts,
            "content_insights": self.generate_content_insights(all_posts),
            "optimal_posting_times": self.analyze_optimal_posting_times(all_posts)
        }
    
    async def analyze_engagement_trends(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل اتجاهات التفاعل"""
        engagement_timeline = defaultdict(int)
        
        for platform, data in platforms_data.items():
            timeline = data.get("engagement_timeline", {})
            for date, engagement in timeline.items():
                engagement_timeline[date] += engagement
        
        # تحويل إلى قائمة مرتبة
        sorted_timeline = sorted(engagement_timeline.items())
        
        # حساب الاتجاهات
        if len(sorted_timeline) >= 7:
            recent_week = sum([eng for date, eng in sorted_timeline[-7:]])
            previous_week = sum([eng for date, eng in sorted_timeline[-14:-7]])
            trend = ((recent_week - previous_week) / previous_week * 100) if previous_week > 0 else 0
        else:
            trend = 0
        
        return {
            "timeline": dict(sorted_timeline),
            "trend_percentage": trend,
            "peak_engagement_day": max(sorted_timeline, key=lambda x: x[1])[0] if sorted_timeline else None,
            "average_daily_engagement": np.mean([eng for date, eng in sorted_timeline]) if sorted_timeline else 0
        }
    
    async def calculate_roi_across_platforms(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """حساب العائد على الاستثمار عبر المنصات"""
        platform_roi = {}
        total_spend = 0
        total_revenue = 0
        
        for platform, data in platforms_data.items():
            spend = data.get("metrics", {}).get("spend", 0)
            conversions = data.get("metrics", {}).get("conversions", 0)
            avg_order_value = data.get("metrics", {}).get("avg_order_value", 50)  # قيمة افتراضية
            
            revenue = conversions * avg_order_value
            roi = ((revenue - spend) / spend * 100) if spend > 0 else 0
            
            platform_roi[platform] = {
                "spend": spend,
                "revenue": revenue,
                "roi": roi,
                "conversions": conversions,
                "cost_per_conversion": spend / conversions if conversions > 0 else 0
            }
            
            total_spend += spend
            total_revenue += revenue
        
        overall_roi = ((total_revenue - total_spend) / total_spend * 100) if total_spend > 0 else 0
        
        return {
            "platform_roi": platform_roi,
            "overall_roi": overall_roi,
            "total_spend": total_spend,
            "total_revenue": total_revenue,
            "best_performing_platform": max(platform_roi.items(), key=lambda x: x[1]["roi"])[0] if platform_roi else None
        }
    
    async def generate_ai_recommendations(self, platforms_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """إنشاء توصيات ذكية باستخدام AI"""
        recommendations = []
        
        # تحليل الأداء وإنشاء توصيات
        for platform, data in platforms_data.items():
            metrics = data.get("metrics", {})
            engagement_rate = metrics.get("engagement_rate", 0)
            ctr = metrics.get("ctr", 0)
            conversion_rate = metrics.get("conversion_rate", 0)
            
            # توصيات بناءً على معدل التفاعل
            if engagement_rate < 2:
                recommendations.append({
                    "type": "engagement",
                    "platform": platform,
                    "priority": "high",
                    "title": f"تحسين معدل التفاعل في {platform}",
                    "description": f"معدل التفاعل الحالي {engagement_rate:.1f}% أقل من المتوسط. جرب محتوى تفاعلي أكثر.",
                    "actions": [
                        "استخدم المزيد من الصور والفيديوهات",
                        "اطرح أسئلة لتشجيع التفاعل",
                        "انشر في الأوقات المثلى"
                    ]
                })
            
            # توصيات بناءً على معدل النقر
            if ctr < 1:
                recommendations.append({
                    "type": "ctr",
                    "platform": platform,
                    "priority": "medium",
                    "title": f"تحسين معدل النقر في {platform}",
                    "description": f"معدل النقر الحالي {ctr:.1f}% يحتاج تحسين.",
                    "actions": [
                        "اكتب عناوين أكثر جاذبية",
                        "استخدم دعوات واضحة للعمل",
                        "حسن من جودة الصور المستخدمة"
                    ]
                })
        
        return recommendations
    
    async def perform_competitive_analysis(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل تنافسي مبسط"""
        # هذا تحليل مبسط - في التطبيق الحقيقي يجب جلب بيانات المنافسين
        return {
            "market_position": "متوسط",
            "competitive_advantages": [
                "تنوع المنصات المستخدمة",
                "معدل تفاعل جيد على Instagram"
            ],
            "areas_for_improvement": [
                "زيادة الوجود على LinkedIn",
                "تحسين استراتيجية المحتوى على Facebook"
            ]
        }
    
    async def analyze_sentiment_across_platforms(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل المشاعر عبر المنصات"""
        all_comments = []
        
        for platform, data in platforms_data.items():
            comments = data.get("comments", [])
            for comment in comments:
                comment["platform"] = platform
                all_comments.append(comment)
        
        if not all_comments:
            return {"message": "لا توجد تعليقات للتحليل"}
        
        # تحليل مبسط للمشاعر
        positive_count = len([c for c in all_comments if c.get("sentiment") == "positive"])
        negative_count = len([c for c in all_comments if c.get("sentiment") == "negative"])
        neutral_count = len(all_comments) - positive_count - negative_count
        
        return {
            "overall_sentiment": {
                "positive": positive_count,
                "negative": negative_count,
                "neutral": neutral_count,
                "positive_percentage": (positive_count / len(all_comments) * 100) if all_comments else 0
            },
            "sentiment_by_platform": self.calculate_sentiment_by_platform(all_comments),
            "trending_topics": self.extract_trending_topics(all_comments)
        }
    
    # دوال مساعدة
    def calculate_audience_overlap(self, platforms_data: Dict[str, Any]) -> Dict[str, float]:
        """حساب تداخل الجمهور بين المنصات"""
        # تحليل مبسط - في التطبيق الحقيقي يحتاج خوارزميات أكثر تعقيداً
        return {"estimated_overlap": 25.5}
    
    def analyze_audience_growth(self, platforms_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل نمو الجمهور"""
        return {"growth_rate": 5.2, "trend": "increasing"}
    
    def generate_content_insights(self, posts: List[Dict]) -> Dict[str, Any]:
        """إنشاء رؤى حول المحتوى"""
        return {
            "best_hashtags": ["#تسويق", "#أعمال", "#نجاح"],
            "optimal_length": "150-200 حرف",
            "best_posting_frequency": "3-4 منشورات أسبوعياً"
        }
    
    def analyze_optimal_posting_times(self, posts: List[Dict]) -> Dict[str, Any]:
        """تحليل أفضل أوقات النشر"""
        return {
            "best_days": ["الثلاثاء", "الأربعاء", "الخميس"],
            "best_hours": ["10:00", "14:00", "19:00"],
            "timezone": "Asia/Riyadh"
        }
    
    def calculate_sentiment_by_platform(self, comments: List[Dict]) -> Dict[str, Dict]:
        """حساب المشاعر حسب المنصة"""
        platform_sentiment = defaultdict(lambda: {"positive": 0, "negative": 0, "neutral": 0})
        
        for comment in comments:
            platform = comment.get("platform", "unknown")
            sentiment = comment.get("sentiment", "neutral")
            platform_sentiment[platform][sentiment] += 1
        
        return dict(platform_sentiment)
    
    def extract_trending_topics(self, comments: List[Dict]) -> List[str]:
        """استخراج المواضيع الرائجة"""
        # تحليل مبسط - في التطبيق الحقيقي يحتاج NLP
        return ["التسويق الرقمي", "ريادة الأعمال", "الذكاء الاصطناعي"]
    
    # دوال جلب البيانات من كل منصة
    async def fetch_facebook_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات Facebook"""
        # هنا يجب جلب البيانات الحقيقية من قاعدة البيانات
        return {
            "connected": True,
            "metrics": {
                "reach": 15000,
                "engagement": 1200,
                "impressions": 25000,
                "clicks": 450,
                "conversions": 25,
                "spend": 500
            },
            "posts": [],
            "audience": {},
            "comments": []
        }
    
    async def fetch_instagram_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات Instagram"""
        return {
            "connected": True,
            "metrics": {
                "reach": 8000,
                "engagement": 800,
                "impressions": 12000,
                "clicks": 200,
                "conversions": 15,
                "spend": 200
            },
            "posts": [],
            "audience": {},
            "comments": []
        }
    
    async def fetch_google_ads_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات Google Ads"""
        return None  # غير مربوط
    
    async def fetch_linkedin_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات LinkedIn"""
        return None  # غير مربوط
    
    async def fetch_twitter_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات Twitter"""
        return None  # غير مربوط
    
    async def fetch_youtube_data(self, user_id: str, date_range: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """جلب بيانات YouTube"""
        return None  # غير مربوط
