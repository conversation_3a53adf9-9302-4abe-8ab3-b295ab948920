import numpy as np
import pandas as pd
from typing import Dict, List, Any
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA

class CustomerSegmentationService:
    """Service for customer segmentation using machine learning"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.kmeans = None
        self.pca = None
    
    def preprocess_customer_data(self, customers: List[Dict[str, Any]]) -> pd.DataFrame:
        """Preprocess customer data for segmentation"""
        df = pd.DataFrame(customers)
        
        # Default columns if not provided
        default_columns = {
            'age': 35,
            'income': 50000,
            'spending_score': 50,
            'frequency': 5,
            'recency': 30,
            'monetary': 1000
        }
        
        # Fill missing columns with defaults
        for col, default_val in default_columns.items():
            if col not in df.columns:
                df[col] = default_val
        
        # Handle missing values
        numeric_columns = ['age', 'income', 'spending_score', 'frequency', 'recency', 'monetary']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(default_columns.get(col, 0))
        
        return df
    
    def segment_customers(self, customers: List[Dict[str, Any]], n_clusters: int = 4) -> Dict[str, Any]:
        """Segment customers using K-means clustering"""
        if len(customers) < n_clusters:
            return self._create_simple_segments(customers)
        
        # Preprocess data
        df = self.preprocess_customer_data(customers)
        
        # Select features for clustering
        feature_columns = ['age', 'income', 'spending_score', 'frequency', 'recency', 'monetary']
        available_features = [col for col in feature_columns if col in df.columns]
        
        if not available_features:
            return self._create_simple_segments(customers)
        
        X = df[available_features].values
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Apply K-means clustering
        self.kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = self.kmeans.fit_predict(X_scaled)
        
        # Add cluster labels to dataframe
        df['cluster'] = cluster_labels
        
        # Create segments
        segments = {}
        for cluster_id in range(n_clusters):
            cluster_data = df[df['cluster'] == cluster_id]
            segment_name = self._generate_segment_name(cluster_data, cluster_id)
            
            segments[segment_name] = {
                "cluster_id": int(cluster_id),
                "size": len(cluster_data),
                "percentage": round(len(cluster_data) / len(df) * 100, 1),
                "characteristics": self._analyze_cluster_characteristics(cluster_data),
                "customers": cluster_data.drop('cluster', axis=1).to_dict('records'),
                "description": self._generate_segment_description(cluster_data)
            }
        
        return segments
    
    def _create_simple_segments(self, customers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create simple segments when clustering is not possible"""
        df = pd.DataFrame(customers)
        
        segments = {
            "All Customers": {
                "cluster_id": 0,
                "size": len(customers),
                "percentage": 100.0,
                "characteristics": {
                    "avg_age": df.get('age', pd.Series([35])).mean(),
                    "avg_income": df.get('income', pd.Series([50000])).mean(),
                    "avg_spending": df.get('spending_score', pd.Series([50])).mean()
                },
                "customers": customers,
                "description": "All customers in a single segment due to insufficient data for clustering"
            }
        }
        
        return segments
    
    def _generate_segment_name(self, cluster_data: pd.DataFrame, cluster_id: int) -> str:
        """Generate meaningful segment names based on cluster characteristics"""
        avg_age = cluster_data.get('age', pd.Series([35])).mean()
        avg_income = cluster_data.get('income', pd.Series([50000])).mean()
        avg_spending = cluster_data.get('spending_score', pd.Series([50])).mean()
        
        # Define segment names based on characteristics
        if avg_income > 70000 and avg_spending > 70:
            return "Premium Customers"
        elif avg_income > 70000 and avg_spending <= 70:
            return "High Income Conservative"
        elif avg_age < 30 and avg_spending > 60:
            return "Young Spenders"
        elif avg_age > 50 and avg_spending < 40:
            return "Mature Savers"
        elif avg_spending > 70:
            return "High Spenders"
        elif avg_spending < 30:
            return "Budget Conscious"
        else:
            return f"Segment {cluster_id + 1}"
    
    def _analyze_cluster_characteristics(self, cluster_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze characteristics of a cluster"""
        characteristics = {}
        
        numeric_columns = ['age', 'income', 'spending_score', 'frequency', 'recency', 'monetary']
        
        for col in numeric_columns:
            if col in cluster_data.columns:
                characteristics[f"avg_{col}"] = round(cluster_data[col].mean(), 2)
                characteristics[f"median_{col}"] = round(cluster_data[col].median(), 2)
                characteristics[f"std_{col}"] = round(cluster_data[col].std(), 2)
        
        return characteristics
    
    def _generate_segment_description(self, cluster_data: pd.DataFrame) -> str:
        """Generate a description for the segment"""
        size = len(cluster_data)
        avg_age = cluster_data.get('age', pd.Series([35])).mean()
        avg_income = cluster_data.get('income', pd.Series([50000])).mean()
        avg_spending = cluster_data.get('spending_score', pd.Series([50])).mean()
        
        age_desc = "young" if avg_age < 30 else "middle-aged" if avg_age < 50 else "mature"
        income_desc = "high-income" if avg_income > 70000 else "middle-income" if avg_income > 40000 else "budget-conscious"
        spending_desc = "high-spending" if avg_spending > 70 else "moderate-spending" if avg_spending > 40 else "low-spending"
        
        return f"A segment of {size} {age_desc}, {income_desc}, {spending_desc} customers."
    
    def predict_segment(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict which segment a new customer belongs to"""
        if self.kmeans is None:
            return {"error": "Model not trained yet"}
        
        # Preprocess single customer
        df = pd.DataFrame([customer_data])
        df = self.preprocess_customer_data(df)
        
        feature_columns = ['age', 'income', 'spending_score', 'frequency', 'recency', 'monetary']
        available_features = [col for col in feature_columns if col in df.columns]
        
        X = df[available_features].values
        X_scaled = self.scaler.transform(X)
        
        # Predict cluster
        cluster_id = self.kmeans.predict(X_scaled)[0]
        
        # Get cluster center distance for confidence
        distances = self.kmeans.transform(X_scaled)[0]
        confidence = 1 / (1 + distances[cluster_id])  # Higher confidence for closer points
        
        return {
            "predicted_cluster": int(cluster_id),
            "confidence": round(confidence, 3),
            "distances_to_centers": distances.tolist()
        }
