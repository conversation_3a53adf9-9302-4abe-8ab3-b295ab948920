"""
Custom AI Service - Build and use custom AI models
"""

import os
import json
import pickle
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import requests
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib

class CustomAIService:
    """Service for building and using custom AI models"""
    
    def __init__(self):
        self.models_dir = "custom_models"
        self.ensure_models_directory()
        self.available_models = self.load_available_models()
        
    def ensure_models_directory(self):
        """Create models directory if it doesn't exist"""
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)
    
    def load_available_models(self) -> Dict[str, Any]:
        """Load information about available custom models"""
        models_file = os.path.join(self.models_dir, "models_registry.json")
        if os.path.exists(models_file):
            with open(models_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_models_registry(self):
        """Save models registry to file"""
        models_file = os.path.join(self.models_dir, "models_registry.json")
        with open(models_file, 'w', encoding='utf-8') as f:
            json.dump(self.available_models, f, ensure_ascii=False, indent=2)
    
    def create_sentiment_model(self, training_data: List[Dict[str, Any]], model_name: str = "custom_sentiment") -> Dict[str, Any]:
        """Create custom sentiment analysis model"""
        try:
            # Prepare data
            texts = [item['text'] for item in training_data]
            labels = [item['sentiment'] for item in training_data]
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                texts, labels, test_size=0.2, random_state=42
            )
            
            # Create pipeline
            pipeline = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, ngram_range=(1, 2))),
                ('classifier', LogisticRegression(random_state=42))
            ])
            
            # Train model
            pipeline.fit(X_train, y_train)
            
            # Evaluate
            y_pred = pipeline.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            # Save model
            model_path = os.path.join(self.models_dir, f"{model_name}.joblib")
            joblib.dump(pipeline, model_path)
            
            # Update registry
            self.available_models[model_name] = {
                "type": "sentiment_analysis",
                "accuracy": accuracy,
                "training_samples": len(training_data),
                "created_at": datetime.now().isoformat(),
                "model_path": model_path
            }
            self.save_models_registry()
            
            return {
                "success": True,
                "model_name": model_name,
                "accuracy": accuracy,
                "training_samples": len(training_data),
                "test_samples": len(X_test),
                "message": f"تم إنشاء النموذج بنجاح بدقة {accuracy:.2%}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"فشل في إنشاء النموذج: {str(e)}"
            }
    
    def create_content_classifier(self, training_data: List[Dict[str, Any]], model_name: str = "custom_content_classifier") -> Dict[str, Any]:
        """Create custom content classification model"""
        try:
            # Prepare data
            texts = [item['text'] for item in training_data]
            categories = [item['category'] for item in training_data]
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                texts, categories, test_size=0.2, random_state=42
            )
            
            # Create pipeline
            pipeline = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=10000, ngram_range=(1, 3))),
                ('classifier', MultinomialNB())
            ])
            
            # Train model
            pipeline.fit(X_train, y_train)
            
            # Evaluate
            y_pred = pipeline.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            # Save model
            model_path = os.path.join(self.models_dir, f"{model_name}.joblib")
            joblib.dump(pipeline, model_path)
            
            # Update registry
            self.available_models[model_name] = {
                "type": "content_classification",
                "accuracy": accuracy,
                "training_samples": len(training_data),
                "categories": list(set(categories)),
                "created_at": datetime.now().isoformat(),
                "model_path": model_path
            }
            self.save_models_registry()
            
            return {
                "success": True,
                "model_name": model_name,
                "accuracy": accuracy,
                "categories": list(set(categories)),
                "message": f"تم إنشاء نموذج التصنيف بنجاح بدقة {accuracy:.2%}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"فشل في إنشاء النموذج: {str(e)}"
            }
    
    def use_custom_model(self, model_name: str, text: str) -> Dict[str, Any]:
        """Use a custom trained model for prediction"""
        try:
            if model_name not in self.available_models:
                return {
                    "success": False,
                    "error": f"النموذج {model_name} غير موجود"
                }
            
            model_info = self.available_models[model_name]
            model_path = model_info["model_path"]
            
            if not os.path.exists(model_path):
                return {
                    "success": False,
                    "error": f"ملف النموذج غير موجود: {model_path}"
                }
            
            # Load model
            model = joblib.load(model_path)
            
            # Make prediction
            prediction = model.predict([text])[0]
            
            # Get prediction probabilities if available
            probabilities = {}
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba([text])[0]
                classes = model.classes_
                probabilities = {str(cls): float(prob) for cls, prob in zip(classes, proba)}
            
            return {
                "success": True,
                "model_name": model_name,
                "model_type": model_info["type"],
                "prediction": prediction,
                "probabilities": probabilities,
                "confidence": max(probabilities.values()) if probabilities else 0.0,
                "text": text
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"فشل في استخدام النموذج: {str(e)}"
            }
    
    def setup_ollama_model(self, model_name: str = "llama3.2") -> Dict[str, Any]:
        """Setup local Ollama model"""
        try:
            # Check if Ollama is running
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [m["name"] for m in models]
                
                if model_name in model_names:
                    return {
                        "success": True,
                        "message": f"نموذج {model_name} متاح ويعمل",
                        "available_models": model_names
                    }
                else:
                    return {
                        "success": False,
                        "message": f"نموذج {model_name} غير مثبت. النماذج المتاحة: {model_names}",
                        "available_models": model_names
                    }
            else:
                return {
                    "success": False,
                    "error": "Ollama غير متاح. تأكد من تشغيله على localhost:11434"
                }
                
        except requests.exceptions.RequestException:
            return {
                "success": False,
                "error": "Ollama غير مثبت أو غير يعمل. قم بتثبيته من https://ollama.ai"
            }
    
    def use_ollama_model(self, prompt: str, model_name: str = "llama3.2") -> Dict[str, Any]:
        """Use local Ollama model for text generation"""
        try:
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": model_name,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "content": result.get("response", ""),
                    "model": model_name,
                    "generated_by": "Ollama Local Model"
                }
            else:
                return {
                    "success": False,
                    "error": f"فشل في استخدام نموذج Ollama: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في الاتصال بـ Ollama: {str(e)}"
            }
    
    def get_models_status(self) -> Dict[str, Any]:
        """Get status of all available models"""
        return {
            "custom_models": self.available_models,
            "models_count": len(self.available_models),
            "ollama_status": self.setup_ollama_model(),
            "models_directory": self.models_dir
        }
    
    def delete_model(self, model_name: str) -> Dict[str, Any]:
        """Delete a custom model"""
        try:
            if model_name not in self.available_models:
                return {
                    "success": False,
                    "error": f"النموذج {model_name} غير موجود"
                }
            
            model_info = self.available_models[model_name]
            model_path = model_info["model_path"]
            
            # Delete model file
            if os.path.exists(model_path):
                os.remove(model_path)
            
            # Remove from registry
            del self.available_models[model_name]
            self.save_models_registry()
            
            return {
                "success": True,
                "message": f"تم حذف النموذج {model_name} بنجاح"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"فشل في حذف النموذج: {str(e)}"
            }
    
    def generate_sample_training_data(self, data_type: str = "sentiment") -> List[Dict[str, Any]]:
        """Generate sample training data for testing"""
        if data_type == "sentiment":
            return [
                {"text": "هذا المنتج رائع ومفيد جداً", "sentiment": "positive"},
                {"text": "أحب هذه الخدمة كثيراً", "sentiment": "positive"},
                {"text": "تجربة ممتازة وأنصح بها", "sentiment": "positive"},
                {"text": "المنتج سيء ولا أنصح به", "sentiment": "negative"},
                {"text": "خدمة العملاء سيئة جداً", "sentiment": "negative"},
                {"text": "لم أعجب بالمنتج", "sentiment": "negative"},
                {"text": "المنتج عادي لا بأس به", "sentiment": "neutral"},
                {"text": "لا رأي لي في هذا الموضوع", "sentiment": "neutral"},
                {"text": "يمكن أن يكون أفضل", "sentiment": "neutral"}
            ]
        elif data_type == "content_classification":
            return [
                {"text": "عرض خاص على جميع المنتجات", "category": "promotional"},
                {"text": "خصم 50% لفترة محدودة", "category": "promotional"},
                {"text": "كيفية استخدام منتجنا الجديد", "category": "educational"},
                {"text": "نصائح للحصول على أفضل النتائج", "category": "educational"},
                {"text": "أخبار الشركة والتحديثات الجديدة", "category": "news"},
                {"text": "إطلاق منتج جديد الشهر القادم", "category": "news"}
            ]
        else:
            return []
