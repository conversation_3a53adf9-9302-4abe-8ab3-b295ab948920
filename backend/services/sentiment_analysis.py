import re
import numpy as np
import os
import openai
from typing import Dict, List, Any
from textblob import TextBlob
from datetime import datetime

class SentimentAnalysisService:
    """Service for analyzing sentiment in text data using AI"""

    def __init__(self):
        # Initialize OpenAI client
        self.openai_client = None
        self.setup_openai()
        # Positive and negative word lists for enhanced analysis
        self.positive_words = {
            'excellent', 'amazing', 'fantastic', 'great', 'wonderful', 'awesome',
            'outstanding', 'brilliant', 'superb', 'magnificent', 'perfect',
            'love', 'like', 'enjoy', 'happy', 'satisfied', 'pleased'
        }
        
        self.negative_words = {
            'terrible', 'awful', 'horrible', 'bad', 'worst', 'hate',
            'dislike', 'disappointed', 'frustrated', 'angry', 'upset',
            'poor', 'unsatisfied', 'annoyed', 'disgusted'
        }

    def setup_openai(self):
        """Setup OpenAI client with API key"""
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                openai.api_key = api_key
                self.openai_client = openai
                print("✅ OpenAI client initialized for sentiment analysis")
            else:
                print("⚠️ OpenAI API key not found. Using TextBlob fallback.")
        except Exception as e:
            print(f"❌ Failed to initialize OpenAI: {e}")
            self.openai_client = None

    def clean_text(self, text: str) -> str:
        """Clean and preprocess text"""
        # Remove URLs, mentions, hashtags
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text.lower().strip()
    
    def analyze_sentiment(self, text: str, use_ai: bool = True) -> Dict[str, Any]:
        """Analyze sentiment of given text"""
        if not text or not text.strip():
            return {
                "sentiment": "neutral",
                "positive": 0.0,
                "neutral": 1.0,
                "negative": 0.0,
                "compound_score": 0.0,
                "confidence": 0.0
            }

        # Try AI analysis first if available
        if use_ai and self.openai_client:
            try:
                return self._analyze_sentiment_ai(text)
            except Exception as e:
                print(f"AI sentiment analysis failed: {e}. Using fallback.")

        # Fallback to TextBlob analysis
        return self._analyze_sentiment_textblob(text)

    def _analyze_sentiment_ai(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using OpenAI"""

        prompt = f"""
        حلل المشاعر في النص التالي وأعطني النتيجة بصيغة JSON:

        النص: "{text}"

        أريد النتيجة بهذا التنسيق:
        {{
            "sentiment": "positive/negative/neutral",
            "positive": 0.0-1.0,
            "neutral": 0.0-1.0,
            "negative": 0.0-1.0,
            "confidence": 0.0-1.0,
            "emotions": ["joy", "anger", "sadness", "fear", "surprise"],
            "key_phrases": ["عبارة مهمة 1", "عبارة مهمة 2"],
            "explanation": "تفسير قصير للتحليل"
        }}

        تأكد من أن مجموع positive + neutral + negative = 1.0
        """

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "أنت خبير في تحليل المشاعر. حلل النصوص بدقة وأعطي نتائج مفصلة."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )

            result_text = response.choices[0].message.content

            # Try to parse JSON response
            import json
            try:
                result = json.loads(result_text)

                # Add metadata
                result.update({
                    "analyzed_by": "AI",
                    "model": "GPT-3.5-turbo",
                    "timestamp": datetime.now().isoformat(),
                    "word_count": len(text.split())
                })

                return result

            except json.JSONDecodeError:
                # If JSON parsing fails, extract basic sentiment
                if "positive" in result_text.lower():
                    sentiment = "positive"
                    positive, negative = 0.7, 0.1
                elif "negative" in result_text.lower():
                    sentiment = "negative"
                    positive, negative = 0.1, 0.7
                else:
                    sentiment = "neutral"
                    positive, negative = 0.3, 0.3

                return {
                    "sentiment": sentiment,
                    "positive": positive,
                    "neutral": 1.0 - positive - negative,
                    "negative": negative,
                    "confidence": 0.8,
                    "analyzed_by": "AI",
                    "model": "GPT-3.5-turbo",
                    "raw_response": result_text
                }

        except Exception as e:
            raise Exception(f"OpenAI sentiment analysis error: {str(e)}")

    def _analyze_sentiment_textblob(self, text: str) -> Dict[str, Any]:
        
        # Clean text
        cleaned_text = self.clean_text(text)
        
        # Use TextBlob for basic sentiment analysis
        blob = TextBlob(cleaned_text)
        polarity = blob.sentiment.polarity  # -1 to 1
        subjectivity = blob.sentiment.subjectivity  # 0 to 1
        
        # Enhanced analysis with word counting
        words = cleaned_text.split()
        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)
        total_words = len(words)
        
        # Calculate scores
        if total_words > 0:
            positive_ratio = positive_count / total_words
            negative_ratio = negative_count / total_words
        else:
            positive_ratio = negative_ratio = 0
        
        # Combine TextBlob polarity with word-based analysis
        combined_score = (polarity + (positive_ratio - negative_ratio)) / 2
        
        # Normalize scores
        if combined_score > 0.1:
            sentiment = "positive"
            positive = min(1.0, abs(combined_score) + 0.5)
            negative = max(0.0, 0.5 - abs(combined_score))
        elif combined_score < -0.1:
            sentiment = "negative"
            positive = max(0.0, 0.5 - abs(combined_score))
            negative = min(1.0, abs(combined_score) + 0.5)
        else:
            sentiment = "neutral"
            positive = 0.3
            negative = 0.3
        
        neutral = max(0.0, 1.0 - positive - negative)
        
        # Confidence based on subjectivity and word count
        confidence = min(1.0, subjectivity + (total_words / 100))
        
        return {
            "sentiment": sentiment,
            "positive": round(positive, 3),
            "neutral": round(neutral, 3),
            "negative": round(negative, 3),
            "compound_score": round(combined_score, 3),
            "confidence": round(confidence, 3),
            "word_count": total_words,
            "positive_words_found": positive_count,
            "negative_words_found": negative_count
        }
    
    def analyze_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Analyze sentiment for multiple texts"""
        return [self.analyze_sentiment(text) for text in texts]
    
    def get_sentiment_summary(self, texts: List[str]) -> Dict[str, Any]:
        """Get overall sentiment summary for a collection of texts"""
        if not texts:
            return {"error": "No texts provided"}
        
        results = self.analyze_batch(texts)
        
        # Calculate averages
        total_positive = sum(r["positive"] for r in results)
        total_neutral = sum(r["neutral"] for r in results)
        total_negative = sum(r["negative"] for r in results)
        total_compound = sum(r["compound_score"] for r in results)
        
        count = len(results)
        
        # Count sentiment categories
        sentiment_counts = {
            "positive": sum(1 for r in results if r["sentiment"] == "positive"),
            "neutral": sum(1 for r in results if r["sentiment"] == "neutral"),
            "negative": sum(1 for r in results if r["sentiment"] == "negative")
        }
        
        return {
            "total_texts": count,
            "average_scores": {
                "positive": round(total_positive / count, 3),
                "neutral": round(total_neutral / count, 3),
                "negative": round(total_negative / count, 3),
                "compound": round(total_compound / count, 3)
            },
            "sentiment_distribution": {
                "positive": round(sentiment_counts["positive"] / count * 100, 1),
                "neutral": round(sentiment_counts["neutral"] / count * 100, 1),
                "negative": round(sentiment_counts["negative"] / count * 100, 1)
            },
            "overall_sentiment": max(sentiment_counts, key=sentiment_counts.get)
        }
