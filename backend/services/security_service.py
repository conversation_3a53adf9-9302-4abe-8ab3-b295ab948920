import pyotp
import qrcode
import io
import base64
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import redis
import json

class TwoFactorAuthService:
    """Two-Factor Authentication service using TOTP"""
    
    def __init__(self):
        self.issuer_name = "AI Marketing Platform"
    
    def generate_secret(self) -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, user_email: str, secret: str) -> str:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer_name
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64 string
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_token(self, secret: str, token: str) -> bool:
        """Verify TOTP token"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)  # Allow 1 window tolerance
    
    def generate_backup_codes(self, count: int = 8) -> list:
        """Generate backup codes for 2FA"""
        codes = []
        for _ in range(count):
            code = secrets.token_hex(4).upper()  # 8-character hex code
            codes.append(f"{code[:4]}-{code[4:]}")
        return codes


class EncryptionService:
    """Data encryption service"""
    
    def __init__(self):
        # In production, store this key securely (e.g., AWS KMS, HashiCorp Vault)
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self) -> bytes:
        """Get or create encryption key"""
        import os
        key_file = "encryption.key"
        
        if os.path.exists(key_file):
            with open(key_file, "rb") as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
            return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted_data = self.cipher.decrypt(encrypted_bytes)
        return decrypted_data.decode()
    
    def encrypt_dict(self, data: Dict[str, Any]) -> str:
        """Encrypt dictionary data"""
        json_str = json.dumps(data)
        return self.encrypt(json_str)
    
    def decrypt_dict(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt dictionary data"""
        json_str = self.decrypt(encrypted_data)
        return json.loads(json_str)


class AuditLogService:
    """Audit logging service for tracking user actions"""
    
    def __init__(self):
        self.redis_client = None
        try:
            import os
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            self.redis_client = redis.from_url(redis_url)
        except:
            pass  # Fall back to database logging
    
    def log_action(self, user_id: int, action: str, resource: str, 
                   details: Dict[str, Any] = None, ip_address: str = None) -> Dict[str, Any]:
        """Log user action for audit trail"""
        log_entry = {
            "user_id": user_id,
            "action": action,
            "resource": resource,
            "details": details or {},
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            "session_id": self._generate_session_id()
        }
        
        # Store in Redis for fast access
        if self.redis_client:
            try:
                log_key = f"audit_log:{user_id}:{datetime.utcnow().strftime('%Y%m%d')}"
                self.redis_client.lpush(log_key, json.dumps(log_entry))
                self.redis_client.expire(log_key, 86400 * 90)  # Keep for 90 days
            except:
                pass  # Fall back to database
        
        return log_entry
    
    def get_user_audit_logs(self, user_id: int, limit: int = 100) -> list:
        """Get audit logs for a user"""
        logs = []
        
        if self.redis_client:
            try:
                # Get logs from last 30 days
                for i in range(30):
                    date = (datetime.utcnow() - timedelta(days=i)).strftime('%Y%m%d')
                    log_key = f"audit_log:{user_id}:{date}"
                    day_logs = self.redis_client.lrange(log_key, 0, -1)
                    
                    for log_data in day_logs:
                        logs.append(json.loads(log_data))
                        if len(logs) >= limit:
                            break
                    
                    if len(logs) >= limit:
                        break
            except:
                pass
        
        return sorted(logs, key=lambda x: x['timestamp'], reverse=True)[:limit]
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return hashlib.sha256(f"{datetime.utcnow().isoformat()}{secrets.token_hex(16)}".encode()).hexdigest()[:16]


class RateLimitService:
    """Rate limiting service to prevent abuse"""
    
    def __init__(self):
        self.redis_client = None
        try:
            import os
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            self.redis_client = redis.from_url(redis_url)
        except:
            pass
        
        # Rate limit configurations
        self.limits = {
            "login": {"requests": 5, "window": 300},  # 5 attempts per 5 minutes
            "api_general": {"requests": 100, "window": 60},  # 100 requests per minute
            "api_heavy": {"requests": 10, "window": 60},  # 10 heavy requests per minute
            "password_reset": {"requests": 3, "window": 3600},  # 3 attempts per hour
        }
    
    def check_rate_limit(self, identifier: str, limit_type: str) -> Dict[str, Any]:
        """Check if request is within rate limit"""
        if not self.redis_client or limit_type not in self.limits:
            return {"allowed": True, "remaining": 999}
        
        config = self.limits[limit_type]
        key = f"rate_limit:{limit_type}:{identifier}"
        
        try:
            current = self.redis_client.get(key)
            if current is None:
                # First request in window
                self.redis_client.setex(key, config["window"], 1)
                return {"allowed": True, "remaining": config["requests"] - 1}
            
            current_count = int(current)
            if current_count >= config["requests"]:
                ttl = self.redis_client.ttl(key)
                return {
                    "allowed": False,
                    "remaining": 0,
                    "reset_time": ttl
                }
            
            # Increment counter
            new_count = self.redis_client.incr(key)
            return {
                "allowed": True,
                "remaining": config["requests"] - new_count
            }
            
        except Exception as e:
            # If Redis fails, allow the request
            return {"allowed": True, "remaining": 999}
    
    def reset_rate_limit(self, identifier: str, limit_type: str):
        """Reset rate limit for identifier"""
        if self.redis_client:
            key = f"rate_limit:{limit_type}:{identifier}"
            self.redis_client.delete(key)


class SessionManagementService:
    """Session management with automatic timeout"""
    
    def __init__(self):
        self.redis_client = None
        try:
            import os
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
            self.redis_client = redis.from_url(redis_url)
        except:
            pass
        
        self.session_timeout = 3600  # 1 hour default
        self.max_sessions_per_user = 5
    
    def create_session(self, user_id: int, ip_address: str, user_agent: str) -> str:
        """Create new session"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            "user_id": user_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat()
        }
        
        if self.redis_client:
            try:
                # Store session
                session_key = f"session:{session_id}"
                self.redis_client.setex(session_key, self.session_timeout, json.dumps(session_data))
                
                # Track user sessions
                user_sessions_key = f"user_sessions:{user_id}"
                self.redis_client.sadd(user_sessions_key, session_id)
                self.redis_client.expire(user_sessions_key, self.session_timeout)
                
                # Limit concurrent sessions
                self._cleanup_old_sessions(user_id)
                
            except:
                pass
        
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Validate and refresh session"""
        if not self.redis_client:
            return None
        
        try:
            session_key = f"session:{session_id}"
            session_data = self.redis_client.get(session_key)
            
            if not session_data:
                return None
            
            session_info = json.loads(session_data)
            
            # Update last activity
            session_info["last_activity"] = datetime.utcnow().isoformat()
            self.redis_client.setex(session_key, self.session_timeout, json.dumps(session_info))
            
            return session_info
            
        except:
            return None
    
    def invalidate_session(self, session_id: str):
        """Invalidate specific session"""
        if self.redis_client:
            try:
                session_key = f"session:{session_id}"
                session_data = self.redis_client.get(session_key)
                
                if session_data:
                    session_info = json.loads(session_data)
                    user_id = session_info["user_id"]
                    
                    # Remove from user sessions
                    user_sessions_key = f"user_sessions:{user_id}"
                    self.redis_client.srem(user_sessions_key, session_id)
                
                # Delete session
                self.redis_client.delete(session_key)
                
            except:
                pass
    
    def invalidate_all_user_sessions(self, user_id: int):
        """Invalidate all sessions for a user"""
        if self.redis_client:
            try:
                user_sessions_key = f"user_sessions:{user_id}"
                session_ids = self.redis_client.smembers(user_sessions_key)
                
                for session_id in session_ids:
                    session_key = f"session:{session_id.decode()}"
                    self.redis_client.delete(session_key)
                
                self.redis_client.delete(user_sessions_key)
                
            except:
                pass
    
    def _cleanup_old_sessions(self, user_id: int):
        """Remove old sessions if user has too many"""
        if not self.redis_client:
            return
        
        try:
            user_sessions_key = f"user_sessions:{user_id}"
            session_ids = list(self.redis_client.smembers(user_sessions_key))
            
            if len(session_ids) > self.max_sessions_per_user:
                # Get session creation times
                sessions_with_time = []
                for session_id in session_ids:
                    session_key = f"session:{session_id.decode()}"
                    session_data = self.redis_client.get(session_key)
                    if session_data:
                        session_info = json.loads(session_data)
                        sessions_with_time.append((session_id, session_info["created_at"]))
                
                # Sort by creation time and remove oldest
                sessions_with_time.sort(key=lambda x: x[1])
                sessions_to_remove = sessions_with_time[:-self.max_sessions_per_user]
                
                for session_id, _ in sessions_to_remove:
                    self.invalidate_session(session_id.decode())
                    
        except:
            pass
