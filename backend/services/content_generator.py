import random
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

class ContentGeneratorService:
    """Service for generating marketing content using AI"""

    def __init__(self):
        # Initialize local content generation
        print("✅ Content Generator initialized with local models")

        # Fallback templates for when AI is not available
        self.email_templates = {
            "welcome": [
                "Welcome to {company}! We're thrilled to have you join our community of {audience}.",
                "Thank you for choosing {company}! Get ready to discover amazing {benefits}.",
                "Welcome aboard! Your journey with {company} starts now."
            ],
            "promotional": [
                "Don't miss our exclusive {offer} - limited time only!",
                "Special offer just for you: {discount}% off {product}!",
                "Last chance to save big on {product} - offer ends soon!"
            ],
            "newsletter": [
                "Here's what's new this month at {company}...",
                "Your monthly dose of {industry} insights and updates.",
                "Stay ahead with the latest {industry} trends and news."
            ]
        }
        
        self.social_templates = {
            "engagement": [
                "What's your favorite {topic}? Share in the comments! 👇",
                "Tag someone who needs to see this! 🔥",
                "Double tap if you agree! ❤️ #motivation"
            ],
            "product": [
                "🚀 Introducing {product} - the future of {category}!",
                "Game-changer alert! 🎯 {product} is here to revolutionize {industry}.",
                "Say hello to your new favorite {product}! ✨"
            ],
            "educational": [
                "Did you know? {fact} 🤔 #DidYouKnow",
                "Pro tip: {tip} 💡 Save this post for later!",
                "Quick tutorial: How to {action} in 3 easy steps 📝"
            ]
        }
        
        self.ad_copy_templates = {
            "google": [
                "{headline} | {description} | {cta}",
                "Get {benefit} with {product} | {offer} | {cta}",
                "{problem}? {solution} | {proof} | {cta}"
            ],
            "facebook": [
                "{hook} {benefit} {social_proof} {cta}",
                "Attention {audience}! {offer} {urgency} {cta}",
                "{question} {answer} {benefit} {cta}"
            ]
        }
        
        self.tone_modifiers = {
            "professional": {
                "adjectives": ["innovative", "reliable", "efficient", "professional", "cutting-edge"],
                "phrases": ["We are pleased to", "Our solution provides", "Industry-leading"],
                "cta": ["Learn more", "Get started today", "Contact us"]
            },
            "casual": {
                "adjectives": ["awesome", "amazing", "cool", "fantastic", "incredible"],
                "phrases": ["Check this out", "You'll love", "This is perfect for"],
                "cta": ["Try it now", "Join us", "Let's go"]
            },
            "urgent": {
                "adjectives": ["limited", "exclusive", "urgent", "immediate", "final"],
                "phrases": ["Don't miss out", "Last chance", "Act now"],
                "cta": ["Hurry, get yours", "Claim now", "Don't wait"]
            },
            "friendly": {
                "adjectives": ["friendly", "helpful", "supportive", "caring", "welcoming"],
                "phrases": ["We're here to help", "Let's work together", "We care about"],
                "cta": ["Let's chat", "Reach out", "We're here for you"]
            }
        }

    def generate_content(
        self,
        content_type: str,
        prompt: str,
        target_audience: str = "general",
        tone: str = "professional"
    ) -> Dict[str, Any]:
        """Generate content based on type and prompt using local models"""

        # Use smart template-based generation with actual content
        return self._generate_smart_content(content_type, prompt, target_audience, tone)


    
    def _generate_email_content(self, prompt: str, audience: str, tone: str) -> Dict[str, Any]:
        """Generate email content"""
        # Determine email type from prompt
        email_type = "promotional"
        if "welcome" in prompt.lower():
            email_type = "welcome"
        elif "newsletter" in prompt.lower():
            email_type = "newsletter"
        
        # Get base template
        templates = self.email_templates.get(email_type, self.email_templates["promotional"])
        base_template = random.choice(templates)
        
        # Apply tone modifications
        tone_data = self.tone_modifiers.get(tone, self.tone_modifiers["professional"])
        
        # Generate subject line
        subject_lines = [
            f"Exciting news for {audience}!",
            f"Your {random.choice(tone_data['adjectives'])} update is here",
            f"Don't miss this {random.choice(tone_data['adjectives'])} opportunity"
        ]
        
        # Generate content
        content = f"""Subject: {random.choice(subject_lines)}

{base_template.format(
    company="Your Company",
    audience=audience,
    benefits="benefits and features",
    offer="special offer",
    discount="20",
    product="our products"
)}

{random.choice(tone_data['phrases'])} {prompt}

Best regards,
The Marketing Team

{random.choice(tone_data['cta'])}!"""
        
        return {
            "content": content,
            "suggestions": [
                "Consider personalizing with recipient's name",
                "Add a clear call-to-action button",
                "Include social media links",
                "Test different subject lines"
            ]
        }
    
    def _generate_social_content(self, prompt: str, audience: str, tone: str) -> Dict[str, Any]:
        """Generate social media content"""
        # Determine post type
        post_type = "engagement"
        if "product" in prompt.lower() or "launch" in prompt.lower():
            post_type = "product"
        elif "tip" in prompt.lower() or "how to" in prompt.lower():
            post_type = "educational"
        
        templates = self.social_templates.get(post_type, self.social_templates["engagement"])
        base_template = random.choice(templates)
        
        tone_data = self.tone_modifiers.get(tone, self.tone_modifiers["casual"])
        
        # Generate hashtags
        hashtags = ["#marketing", "#business", "#growth", "#success", "#innovation"]
        selected_hashtags = random.sample(hashtags, 3)
        
        content = f"""{base_template.format(
    topic=prompt,
    product=prompt,
    category="business",
    industry="marketing",
    fact=prompt,
    tip=prompt,
    action=prompt
)}

{' '.join(selected_hashtags)}"""
        
        return {
            "content": content,
            "suggestions": [
                "Add relevant emojis for better engagement",
                "Include a call-to-action",
                "Tag relevant accounts",
                "Post at optimal times for your audience"
            ]
        }
    
    def _generate_ad_copy(self, prompt: str, audience: str, tone: str) -> Dict[str, Any]:
        """Generate advertisement copy"""
        platform = "google"  # Default platform
        
        templates = self.ad_copy_templates.get(platform, self.ad_copy_templates["google"])
        base_template = random.choice(templates)
        
        tone_data = self.tone_modifiers.get(tone, self.tone_modifiers["professional"])
        
        content = base_template.format(
            headline=f"{random.choice(tone_data['adjectives']).title()} {prompt}",
            description=f"Perfect for {audience} looking for {prompt}",
            cta=random.choice(tone_data['cta']),
            benefit=f"Get {random.choice(tone_data['adjectives'])} results",
            product=prompt,
            offer="Special limited-time offer",
            problem=f"Struggling with {prompt}?",
            solution=f"Our {prompt} solution",
            proof="Trusted by thousands",
            hook="Attention!",
            social_proof="Join 10,000+ satisfied customers",
            urgency="Limited time only!",
            audience=audience,
            question=f"Need help with {prompt}?",
            answer=f"We've got the perfect {prompt} solution"
        )
        
        return {
            "content": content,
            "suggestions": [
                "Test multiple variations",
                "Include specific numbers or statistics",
                "Add urgency or scarcity",
                "Use power words to increase impact"
            ]
        }
    
    def _generate_blog_content(self, prompt: str, audience: str, tone: str) -> Dict[str, Any]:
        """Generate blog content outline"""
        tone_data = self.tone_modifiers.get(tone, self.tone_modifiers["professional"])
        
        content = f"""# {prompt.title()}: A Complete Guide for {audience.title()}

## Introduction
{random.choice(tone_data['phrases'])} explore {prompt} and how it can benefit {audience}.

## Key Points:
1. Understanding {prompt}
2. Benefits for {audience}
3. Best practices and tips
4. Common mistakes to avoid
5. Getting started

## Conclusion
{prompt} is {random.choice(tone_data['adjectives'])} for {audience}. {random.choice(tone_data['cta'])} to learn more!

---
*This is a generated outline. Expand each section with detailed content.*"""
        
        return {
            "content": content,
            "suggestions": [
                "Add relevant statistics and data",
                "Include real-world examples",
                "Add internal and external links",
                "Optimize for SEO with keywords"
            ]
        }
    
    def _generate_generic_content(self, prompt: str, audience: str, tone: str) -> Dict[str, Any]:
        """Generate generic marketing content"""
        tone_data = self.tone_modifiers.get(tone, self.tone_modifiers["professional"])
        
        content = f"""{random.choice(tone_data['phrases'])} {prompt} for {audience}.

Our {random.choice(tone_data['adjectives'])} approach ensures you get the best results. 

{random.choice(tone_data['cta'])} and discover how {prompt} can transform your business!"""
        
        return {
            "content": content,
            "suggestions": [
                "Customize for your specific audience",
                "Add more details and examples",
                "Include a clear call-to-action",
                "Test different versions"
            ]
        }

    def _generate_smart_content(self, content_type: str, prompt: str, target_audience: str, tone: str) -> Dict[str, Any]:
        """Generate smart content based on prompt analysis"""

        # Analyze the prompt to create relevant content
        prompt_lower = prompt.lower()

        # Smart content generation based on content type and prompt
        if content_type == "social":
            content = self._create_social_content(prompt, target_audience, tone)
        elif content_type == "email":
            content = self._create_email_content(prompt, target_audience, tone)
        elif content_type == "ad_copy":
            content = self._create_ad_content(prompt, target_audience, tone)
        elif content_type == "blog":
            content = self._create_blog_content(prompt, target_audience, tone)
        else:
            content = self._create_general_content(prompt, target_audience, tone)

        # Generate smart suggestions based on content type and audience
        suggestions = self._generate_smart_suggestions(content_type, target_audience, tone, prompt)

        return {
            "content": content,
            "suggestions": suggestions,
            "generated_by": "smart_template",
            "timestamp": datetime.now().isoformat()
        }

    def _create_social_content(self, prompt: str, audience: str, tone: str) -> str:
        """Create social media content based on prompt"""
        prompt_lower = prompt.lower()

        # Analyze prompt for key themes
        if any(word in prompt_lower for word in ['نجاح', 'تحفيز', 'success', 'motivation', 'تحفيزي']):
            if 'ريادة' in prompt_lower or 'أعمال' in prompt_lower or 'business' in prompt_lower:
                return """🚀 النجاح في ريادة الأعمال ليس مجرد حلم!

💡 كل فكرة عظيمة بدأت بخطوة واحدة
🎯 كل مشروع ناجح واجه تحديات وتغلب عليها
💪 كل رائد أعمال تعلم من أخطائه وطور نفسه

✨ رحلتك نحو النجاح تبدأ اليوم:
• ضع أهدافاً واضحة ومحددة
• تعلم من تجارب الآخرين
• لا تخف من المخاطرة المحسوبة
• استثمر في تطوير مهاراتك

🔥 تذكر: كل خبير كان يوماً مبتدئاً!

#ريادة_الأعمال #النجاح #التحفيز #الإبداع #التطوير_الذاتي"""

        elif any(word in prompt_lower for word in ['منتج', 'خدمة', 'product', 'service']):
            return """🌟 اكتشف منتجنا الجديد الذي سيغير طريقة تفكيرك!

✅ جودة عالية ومضمونة
✅ تصميم عصري وعملي
✅ أسعار تنافسية
✅ خدمة عملاء متميزة

🎁 عرض خاص لفترة محدودة!
📞 اتصل الآن أو زر موقعنا

#منتج_جديد #جودة #عرض_خاص #تسوق_ذكي"""

        elif any(word in prompt_lower for word in ['تسويق', 'marketing', 'إعلان']):
            return """📈 التسويق الرقمي هو مستقبل الأعمال!

🎯 وصول أوسع لجمهورك المستهدف
💰 تكلفة أقل من التسويق التقليدي
📊 قياس دقيق للنتائج والعائد
🚀 نمو سريع ومستدام

💡 ابدأ رحلتك الرقمية اليوم:
• حدد جمهورك المستهدف
• اختر المنصات المناسبة
• أنشئ محتوى قيم وجذاب
• قس النتائج وطور استراتيجيتك

#التسويق_الرقمي #الأعمال #النمو #الابتكار"""

        else:
            # Generic motivational content
            return f"""✨ {prompt}

🌟 كل يوم فرصة جديدة لتحقيق أحلامك
💪 النجاح يحتاج إلى صبر وإصرار
🎯 ركز على أهدافك ولا تستسلم

#تحفيز #نجاح #إلهام #تطوير_ذاتي"""

    def _create_email_content(self, prompt: str, audience: str, tone: str) -> str:
        """Create email content based on prompt"""
        prompt_lower = prompt.lower()

        if any(word in prompt_lower for word in ['ترحيب', 'welcome', 'مرحبا']):
            return f"""الموضوع: مرحباً بك في عائلتنا! 🎉

عزيزي/عزيزتي العميل الكريم،

نرحب بك في عائلتنا الكبيرة! نحن سعداء جداً لانضمامك إلينا.

🌟 ما يمكنك توقعه منا:
• خدمة عملاء متميزة على مدار الساعة
• منتجات وخدمات عالية الجودة
• عروض وخصومات حصرية
• محتوى قيم ومفيد

📞 إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.

مع أطيب التحيات،
فريق خدمة العملاء"""

        elif any(word in prompt_lower for word in ['عرض', 'خصم', 'offer', 'discount']):
            return f"""الموضوع: عرض خاص لك فقط! 🎁

عزيزي/عزيزتي العميل الكريم،

لدينا عرض خاص ومحدود خصيصاً لك!

🔥 خصم 30% على جميع منتجاتنا
⏰ العرض ساري حتى نهاية الأسبوع فقط
🚚 شحن مجاني لجميع الطلبات

💡 لا تفوت هذه الفرصة الذهبية!

👈 اطلب الآن واستفد من العرض

مع أطيب التحيات،
فريق المبيعات"""

        else:
            return f"""الموضوع: {prompt}

عزيزي/عزيزتي العميل الكريم،

نتواصل معك اليوم لمشاركة معلومات مهمة حول {prompt}.

نحن نقدر ثقتك بنا ونسعى دائماً لتقديم أفضل الخدمات.

إذا كان لديك أي استفسار، نحن هنا لمساعدتك.

مع أطيب التحيات،
فريق العمل"""

    def _create_ad_content(self, prompt: str, audience: str, tone: str) -> str:
        """Create advertisement content based on prompt"""
        return f"""🎯 {prompt}

✨ الحل الأمثل لاحتياجاتك!
🏆 جودة عالية وأسعار منافسة
🚀 احصل عليه الآن بعرض خاص

📞 اتصل الآن: 123-456-789
🌐 زر موقعنا: www.example.com

#عرض_خاص #جودة #خدمة_متميزة"""

    def _create_blog_content(self, prompt: str, audience: str, tone: str) -> str:
        """Create blog content based on prompt"""
        return f"""# {prompt}

## مقدمة
في عالم اليوم المتسارع، أصبح من المهم جداً فهم {prompt} وتطبيقه بشكل صحيح.

## النقاط الرئيسية
• الفهم العميق للموضوع
• التطبيق العملي
• النتائج المتوقعة

## الخلاصة
{prompt} موضوع مهم يستحق الاهتمام والدراسة المتعمقة.

---
*تابعونا للمزيد من المقالات المفيدة*"""

    def _create_general_content(self, prompt: str, audience: str, tone: str) -> str:
        """Create general content based on prompt"""
        return f"""📝 {prompt}

نحن نفهم أهمية {prompt} بالنسبة لك.

🎯 هدفنا هو تقديم أفضل الحلول
💡 نقدم خدمات متميزة ومبتكرة
🤝 نبني علاقات طويلة الأمد مع عملائنا

تواصل معنا لمعرفة المزيد!"""

    def _generate_smart_suggestions(self, content_type: str, audience: str, tone: str, prompt: str) -> List[str]:
        """Generate smart suggestions based on content analysis"""
        suggestions = []

        # Content type specific suggestions
        if content_type == "social":
            suggestions.extend([
                "أضف المزيد من الإيموجي لزيادة التفاعل",
                "استخدم هاشتاجات ترندينغ ذات صلة",
                "أضف دعوة واضحة للعمل (Call to Action)"
            ])
        elif content_type == "email":
            suggestions.extend([
                "اجعل عنوان البريد أكثر جاذبية",
                "أضف عنصر شخصي في التحية",
                "استخدم أزرار واضحة للعمل المطلوب"
            ])
        elif content_type == "ad_copy":
            suggestions.extend([
                "ركز على الفوائد أكثر من المميزات",
                "أضف عنصر الإلحاح (محدود الوقت)",
                "استخدم أرقام وإحصائيات مقنعة"
            ])

        # Audience specific suggestions
        if 'شباب' in audience or 'young' in audience.lower():
            suggestions.append("استخدم لغة عصرية ومراجع ثقافية حديثة")
        elif 'أعمال' in audience or 'business' in audience.lower():
            suggestions.append("ركز على العائد على الاستثمار والنتائج")
        elif 'عائلات' in audience or 'family' in audience.lower():
            suggestions.append("أكد على الأمان والقيم العائلية")

        # Tone specific suggestions
        if tone == "casual":
            suggestions.append("استخدم لغة أكثر ودية وقربا")
        elif tone == "professional":
            suggestions.append("حافظ على الطابع المهني والرسمي")
        elif tone == "motivational":
            suggestions.append("أضف المزيد من العبارات التحفيزية")

        return suggestions[:4]  # Return top 4 suggestions
