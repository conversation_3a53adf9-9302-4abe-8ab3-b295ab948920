import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import io
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import openpyxl
from openpyxl.chart import <PERSON><PERSON>hart, <PERSON><PERSON>hart, <PERSON><PERSON>hart, Reference
from openpyxl.styles import Font, Alignment, PatternFill

class AdvancedReportingService:
    """Advanced reporting service with charts, PDFs, and Excel exports"""
    
    def __init__(self):
        self.chart_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
        plt.style.use('seaborn-v0_8')
        sns.set_palette(self.chart_colors)
    
    def generate_performance_report(self, data: Dict[str, Any], report_type: str = "comprehensive") -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            # Create DataFrame from data
            df = pd.DataFrame(data.get('campaigns', []))
            
            if df.empty:
                return self._generate_mock_report()
            
            # Calculate key metrics
            metrics = self._calculate_key_metrics(df)
            
            # Generate charts
            charts = self._generate_charts(df, report_type)
            
            # Generate insights
            insights = self._generate_insights(df, metrics)
            
            # Create report structure
            report = {
                "report_id": f"RPT_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "generated_at": datetime.now().isoformat(),
                "report_type": report_type,
                "summary": metrics,
                "charts": charts,
                "insights": insights,
                "recommendations": self._generate_recommendations(df, metrics),
                "data_quality": self._assess_data_quality(df)
            }
            
            return report
            
        except Exception as e:
            print(f"Report generation error: {e}")
            return self._generate_mock_report()
    
    def _calculate_key_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate key performance metrics"""
        metrics = {
            "total_campaigns": len(df),
            "total_impressions": df['impressions'].sum() if 'impressions' in df.columns else 0,
            "total_clicks": df['clicks'].sum() if 'clicks' in df.columns else 0,
            "total_spend": df['spend'].sum() if 'spend' in df.columns else 0,
            "total_conversions": df['conversions'].sum() if 'conversions' in df.columns else 0,
            "avg_ctr": (df['clicks'].sum() / df['impressions'].sum() * 100) if 'impressions' in df.columns and df['impressions'].sum() > 0 else 0,
            "avg_cpc": (df['spend'].sum() / df['clicks'].sum()) if 'clicks' in df.columns and df['clicks'].sum() > 0 else 0,
            "avg_cpa": (df['spend'].sum() / df['conversions'].sum()) if 'conversions' in df.columns and df['conversions'].sum() > 0 else 0,
            "roas": (df['conversions'].sum() * 50 / df['spend'].sum()) if 'spend' in df.columns and df['spend'].sum() > 0 else 0  # Assuming $50 AOV
        }
        
        # Round numeric values
        for key, value in metrics.items():
            if isinstance(value, float):
                metrics[key] = round(value, 2)
        
        return metrics
    
    def _generate_charts(self, df: pd.DataFrame, report_type: str) -> Dict[str, str]:
        """Generate various charts for the report"""
        charts = {}
        
        try:
            # Performance over time chart
            if 'date' in df.columns:
                charts['performance_timeline'] = self._create_timeline_chart(df)
            
            # Campaign comparison chart
            charts['campaign_comparison'] = self._create_campaign_comparison_chart(df)
            
            # Spend distribution pie chart
            if 'spend' in df.columns:
                charts['spend_distribution'] = self._create_spend_distribution_chart(df)
            
            # Conversion funnel
            charts['conversion_funnel'] = self._create_conversion_funnel_chart(df)
            
            if report_type == "comprehensive":
                # Additional charts for comprehensive reports
                charts['roi_analysis'] = self._create_roi_analysis_chart(df)
                charts['performance_heatmap'] = self._create_performance_heatmap(df)
            
        except Exception as e:
            print(f"Chart generation error: {e}")
            charts['error'] = "Chart generation failed"
        
        return charts
    
    def _create_timeline_chart(self, df: pd.DataFrame) -> str:
        """Create timeline performance chart"""
        fig = go.Figure()
        
        # Mock timeline data if date column doesn't exist
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        impressions = [1000 + i * 50 + (i % 7) * 200 for i in range(30)]
        clicks = [imp * 0.025 for imp in impressions]
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=impressions,
            mode='lines+markers',
            name='Impressions',
            line=dict(color=self.chart_colors[0])
        ))
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=clicks,
            mode='lines+markers',
            name='Clicks',
            yaxis='y2',
            line=dict(color=self.chart_colors[1])
        ))
        
        fig.update_layout(
            title='Performance Timeline',
            xaxis_title='Date',
            yaxis_title='Impressions',
            yaxis2=dict(title='Clicks', overlaying='y', side='right'),
            hovermode='x unified'
        )
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _create_campaign_comparison_chart(self, df: pd.DataFrame) -> str:
        """Create campaign comparison bar chart"""
        fig = go.Figure()
        
        campaigns = df['name'].tolist() if 'name' in df.columns else [f'Campaign {i+1}' for i in range(len(df))]
        impressions = df['impressions'].tolist() if 'impressions' in df.columns else [1000, 1500, 800, 1200]
        clicks = df['clicks'].tolist() if 'clicks' in df.columns else [25, 45, 20, 35]
        
        fig.add_trace(go.Bar(
            name='Impressions',
            x=campaigns,
            y=impressions,
            marker_color=self.chart_colors[0]
        ))
        
        fig.add_trace(go.Bar(
            name='Clicks',
            x=campaigns,
            y=clicks,
            yaxis='y2',
            marker_color=self.chart_colors[1]
        ))
        
        fig.update_layout(
            title='Campaign Performance Comparison',
            xaxis_title='Campaigns',
            yaxis_title='Impressions',
            yaxis2=dict(title='Clicks', overlaying='y', side='right'),
            barmode='group'
        )
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _create_spend_distribution_chart(self, df: pd.DataFrame) -> str:
        """Create spend distribution pie chart"""
        campaigns = df['name'].tolist() if 'name' in df.columns else [f'Campaign {i+1}' for i in range(len(df))]
        spend = df['spend'].tolist() if 'spend' in df.columns else [500, 750, 300, 600]
        
        fig = go.Figure(data=[go.Pie(
            labels=campaigns,
            values=spend,
            hole=0.3,
            marker_colors=self.chart_colors
        )])
        
        fig.update_layout(
            title='Budget Distribution by Campaign',
            annotations=[dict(text='Total<br>Spend', x=0.5, y=0.5, font_size=20, showarrow=False)]
        )
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _create_conversion_funnel_chart(self, df: pd.DataFrame) -> str:
        """Create conversion funnel chart"""
        total_impressions = df['impressions'].sum() if 'impressions' in df.columns else 10000
        total_clicks = df['clicks'].sum() if 'clicks' in df.columns else 250
        total_conversions = df['conversions'].sum() if 'conversions' in df.columns else 25
        
        fig = go.Figure(go.Funnel(
            y=["Impressions", "Clicks", "Conversions"],
            x=[total_impressions, total_clicks, total_conversions],
            textinfo="value+percent initial",
            marker_color=self.chart_colors[:3]
        ))
        
        fig.update_layout(title='Conversion Funnel')
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _create_roi_analysis_chart(self, df: pd.DataFrame) -> str:
        """Create ROI analysis chart"""
        campaigns = df['name'].tolist() if 'name' in df.columns else [f'Campaign {i+1}' for i in range(4)]
        spend = df['spend'].tolist() if 'spend' in df.columns else [500, 750, 300, 600]
        revenue = [s * 2.5 for s in spend]  # Mock revenue data
        roi = [(r - s) / s * 100 for r, s in zip(revenue, spend)]
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            name='Spend',
            x=campaigns,
            y=spend,
            marker_color=self.chart_colors[0]
        ))
        
        fig.add_trace(go.Bar(
            name='Revenue',
            x=campaigns,
            y=revenue,
            marker_color=self.chart_colors[1]
        ))
        
        fig.add_trace(go.Scatter(
            name='ROI %',
            x=campaigns,
            y=roi,
            mode='lines+markers',
            yaxis='y2',
            line=dict(color=self.chart_colors[2], width=3)
        ))
        
        fig.update_layout(
            title='ROI Analysis by Campaign',
            xaxis_title='Campaigns',
            yaxis_title='Amount ($)',
            yaxis2=dict(title='ROI (%)', overlaying='y', side='right'),
            barmode='group'
        )
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _create_performance_heatmap(self, df: pd.DataFrame) -> str:
        """Create performance heatmap"""
        # Mock heatmap data
        metrics = ['CTR', 'CPC', 'CPA', 'ROAS']
        campaigns = df['name'].tolist() if 'name' in df.columns else [f'Campaign {i+1}' for i in range(4)]
        
        # Generate mock performance matrix
        import numpy as np
        np.random.seed(42)
        performance_matrix = np.random.rand(len(campaigns), len(metrics)) * 100
        
        fig = go.Figure(data=go.Heatmap(
            z=performance_matrix,
            x=metrics,
            y=campaigns,
            colorscale='RdYlGn',
            text=performance_matrix.round(1),
            texttemplate="%{text}",
            textfont={"size": 10}
        ))
        
        fig.update_layout(
            title='Campaign Performance Heatmap',
            xaxis_title='Metrics',
            yaxis_title='Campaigns'
        )
        
        return fig.to_html(include_plotlyjs='cdn')
    
    def _generate_insights(self, df: pd.DataFrame, metrics: Dict[str, Any]) -> List[Dict[str, str]]:
        """Generate automated insights from data"""
        insights = []
        
        # Performance insights
        if metrics['avg_ctr'] > 3.0:
            insights.append({
                "type": "positive",
                "title": "Excellent Click-Through Rate",
                "description": f"Your average CTR of {metrics['avg_ctr']}% is above industry average (2.5%)",
                "recommendation": "Continue with current targeting and creative strategies"
            })
        elif metrics['avg_ctr'] < 1.5:
            insights.append({
                "type": "warning",
                "title": "Low Click-Through Rate",
                "description": f"Your average CTR of {metrics['avg_ctr']}% is below industry average",
                "recommendation": "Consider improving ad copy and targeting"
            })
        
        # Budget insights
        if metrics['total_spend'] > 0:
            if metrics['avg_cpa'] < 20:
                insights.append({
                    "type": "positive",
                    "title": "Efficient Cost Per Acquisition",
                    "description": f"Your CPA of ${metrics['avg_cpa']} is very competitive",
                    "recommendation": "Consider scaling successful campaigns"
                })
        
        # ROAS insights
        if metrics['roas'] > 4.0:
            insights.append({
                "type": "positive",
                "title": "Strong Return on Ad Spend",
                "description": f"Your ROAS of {metrics['roas']}x indicates profitable campaigns",
                "recommendation": "Increase budget allocation to high-performing campaigns"
            })
        elif metrics['roas'] < 2.0:
            insights.append({
                "type": "warning",
                "title": "Low Return on Ad Spend",
                "description": f"Your ROAS of {metrics['roas']}x may need improvement",
                "recommendation": "Review targeting, bidding strategies, and landing pages"
            })
        
        return insights
    
    def _generate_recommendations(self, df: pd.DataFrame, metrics: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        if metrics['avg_ctr'] < 2.0:
            recommendations.append("Improve ad copy and creative elements to increase click-through rates")
        
        if metrics['avg_cpa'] > 50:
            recommendations.append("Optimize targeting and bidding to reduce cost per acquisition")
        
        if metrics['roas'] < 3.0:
            recommendations.append("Focus on high-converting keywords and audiences")
        
        recommendations.extend([
            "Implement A/B testing for ad creatives and landing pages",
            "Set up conversion tracking for better attribution",
            "Consider expanding to high-performing demographics",
            "Optimize campaign scheduling based on performance data"
        ])
        
        return recommendations
    
    def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Assess data quality and completeness"""
        return {
            "completeness": round((df.count().sum() / (len(df) * len(df.columns))) * 100, 1),
            "total_records": len(df),
            "missing_values": df.isnull().sum().to_dict(),
            "data_freshness": "Current",
            "quality_score": "Good"
        }
    
    def _generate_mock_report(self) -> Dict[str, Any]:
        """Generate mock report when no data is available"""
        return {
            "report_id": f"RPT_MOCK_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "generated_at": datetime.now().isoformat(),
            "report_type": "mock",
            "summary": {
                "total_campaigns": 4,
                "total_impressions": 45000,
                "total_clicks": 1200,
                "total_spend": 2150.0,
                "total_conversions": 156,
                "avg_ctr": 2.67,
                "avg_cpc": 1.79,
                "avg_cpa": 13.78,
                "roas": 3.62
            },
            "charts": {
                "performance_timeline": "<div>Mock Timeline Chart</div>",
                "campaign_comparison": "<div>Mock Comparison Chart</div>",
                "spend_distribution": "<div>Mock Pie Chart</div>"
            },
            "insights": [
                {
                    "type": "positive",
                    "title": "Good Performance",
                    "description": "Your campaigns are performing well overall",
                    "recommendation": "Continue current strategies"
                }
            ],
            "recommendations": [
                "Implement A/B testing for better optimization",
                "Focus on high-converting audiences",
                "Optimize landing pages for better conversion rates"
            ],
            "data_quality": {
                "completeness": 95.0,
                "total_records": 4,
                "quality_score": "Good"
            }
        }


class ReportExportService:
    """Service for exporting reports to PDF and Excel"""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#1f77b4')
        )

    def export_to_pdf(self, report_data: Dict[str, Any], filename: str = None) -> str:
        """Export report to PDF"""
        if not filename:
            filename = f"report_{report_data.get('report_id', 'unknown')}.pdf"

        # Create PDF document
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []

        # Title
        title = Paragraph("AI Marketing Platform - Performance Report", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))

        # Report metadata
        metadata = [
            f"Report ID: {report_data.get('report_id', 'N/A')}",
            f"Generated: {report_data.get('generated_at', 'N/A')}",
            f"Report Type: {report_data.get('report_type', 'N/A').title()}"
        ]

        for item in metadata:
            story.append(Paragraph(item, self.styles['Normal']))
        story.append(Spacer(1, 20))

        # Summary metrics
        story.append(Paragraph("Performance Summary", self.styles['Heading2']))
        summary = report_data.get('summary', {})

        summary_data = [
            ['Metric', 'Value'],
            ['Total Campaigns', str(summary.get('total_campaigns', 0))],
            ['Total Impressions', f"{summary.get('total_impressions', 0):,}"],
            ['Total Clicks', f"{summary.get('total_clicks', 0):,}"],
            ['Total Spend', f"${summary.get('total_spend', 0):,.2f}"],
            ['Average CTR', f"{summary.get('avg_ctr', 0):.2f}%"],
            ['Average CPC', f"${summary.get('avg_cpc', 0):.2f}"],
            ['ROAS', f"{summary.get('roas', 0):.2f}x"]
        ]

        summary_table = Table(summary_data)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(summary_table)
        story.append(Spacer(1, 20))

        # Insights
        insights = report_data.get('insights', [])
        if insights:
            story.append(Paragraph("Key Insights", self.styles['Heading2']))
            for insight in insights:
                story.append(Paragraph(f"• {insight.get('title', '')}: {insight.get('description', '')}",
                                     self.styles['Normal']))
            story.append(Spacer(1, 20))

        # Recommendations
        recommendations = report_data.get('recommendations', [])
        if recommendations:
            story.append(Paragraph("Recommendations", self.styles['Heading2']))
            for rec in recommendations:
                story.append(Paragraph(f"• {rec}", self.styles['Normal']))

        # Build PDF
        doc.build(story)
        return filename

    def export_to_excel(self, report_data: Dict[str, Any], filename: str = None) -> str:
        """Export report to Excel with charts"""
        if not filename:
            filename = f"report_{report_data.get('report_id', 'unknown')}.xlsx"

        # Create workbook
        wb = openpyxl.Workbook()

        # Summary sheet
        ws_summary = wb.active
        ws_summary.title = "Summary"

        # Add title
        ws_summary['A1'] = "AI Marketing Platform - Performance Report"
        ws_summary['A1'].font = Font(size=16, bold=True)
        ws_summary.merge_cells('A1:D1')

        # Add metadata
        ws_summary['A3'] = f"Report ID: {report_data.get('report_id', 'N/A')}"
        ws_summary['A4'] = f"Generated: {report_data.get('generated_at', 'N/A')}"
        ws_summary['A5'] = f"Report Type: {report_data.get('report_type', 'N/A').title()}"

        # Add summary metrics
        summary = report_data.get('summary', {})
        ws_summary['A7'] = "Metric"
        ws_summary['B7'] = "Value"
        ws_summary['A7'].font = Font(bold=True)
        ws_summary['B7'].font = Font(bold=True)

        metrics = [
            ('Total Campaigns', summary.get('total_campaigns', 0)),
            ('Total Impressions', summary.get('total_impressions', 0)),
            ('Total Clicks', summary.get('total_clicks', 0)),
            ('Total Spend', f"${summary.get('total_spend', 0):,.2f}"),
            ('Average CTR', f"{summary.get('avg_ctr', 0):.2f}%"),
            ('Average CPC', f"${summary.get('avg_cpc', 0):.2f}"),
            ('ROAS', f"{summary.get('roas', 0):.2f}x")
        ]

        for i, (metric, value) in enumerate(metrics, start=8):
            ws_summary[f'A{i}'] = metric
            ws_summary[f'B{i}'] = value

        # Add chart if data is available
        if summary.get('total_impressions', 0) > 0:
            chart = BarChart()
            chart.title = "Key Metrics"
            chart.y_axis.title = "Values"
            chart.x_axis.title = "Metrics"

            # Add chart data (simplified)
            chart_data = Reference(ws_summary, min_col=2, min_row=8, max_row=11)
            chart_categories = Reference(ws_summary, min_col=1, min_row=8, max_row=11)
            chart.add_data(chart_data, titles_from_data=False)
            chart.set_categories(chart_categories)

            ws_summary.add_chart(chart, "D7")

        # Insights sheet
        insights = report_data.get('insights', [])
        if insights:
            ws_insights = wb.create_sheet("Insights")
            ws_insights['A1'] = "Key Insights"
            ws_insights['A1'].font = Font(size=14, bold=True)

            for i, insight in enumerate(insights, start=3):
                ws_insights[f'A{i}'] = insight.get('title', '')
                ws_insights[f'B{i}'] = insight.get('description', '')
                ws_insights[f'C{i}'] = insight.get('recommendation', '')
                ws_insights[f'A{i}'].font = Font(bold=True)

        # Recommendations sheet
        recommendations = report_data.get('recommendations', [])
        if recommendations:
            ws_rec = wb.create_sheet("Recommendations")
            ws_rec['A1'] = "Recommendations"
            ws_rec['A1'].font = Font(size=14, bold=True)

            for i, rec in enumerate(recommendations, start=3):
                ws_rec[f'A{i}'] = f"{i-2}. {rec}"

        # Save workbook
        wb.save(filename)
        return filename


class ScheduledReportService:
    """Service for scheduling automated reports"""

    def __init__(self):
        self.report_service = AdvancedReportingService()
        self.export_service = ReportExportService()

    def schedule_report(self, user_id: int, schedule_config: Dict[str, Any]) -> Dict[str, Any]:
        """Schedule a report for automatic generation"""
        schedule = {
            "schedule_id": f"SCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "user_id": user_id,
            "report_type": schedule_config.get("report_type", "weekly"),
            "frequency": schedule_config.get("frequency", "weekly"),  # daily, weekly, monthly
            "format": schedule_config.get("format", "pdf"),  # pdf, excel, both
            "email_recipients": schedule_config.get("email_recipients", []),
            "next_run": self._calculate_next_run(schedule_config.get("frequency", "weekly")),
            "created_at": datetime.now().isoformat(),
            "active": True
        }

        return {
            "success": True,
            "schedule": schedule,
            "message": f"Report scheduled successfully. Next run: {schedule['next_run']}"
        }

    def _calculate_next_run(self, frequency: str) -> str:
        """Calculate next run time based on frequency"""
        now = datetime.now()

        if frequency == "daily":
            next_run = now + timedelta(days=1)
        elif frequency == "weekly":
            next_run = now + timedelta(weeks=1)
        elif frequency == "monthly":
            next_run = now + timedelta(days=30)
        else:
            next_run = now + timedelta(weeks=1)  # Default to weekly

        return next_run.isoformat()

    def generate_scheduled_report(self, schedule_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a scheduled report"""
        try:
            # Mock data for scheduled report
            mock_data = {
                "campaigns": [
                    {"name": "Campaign 1", "impressions": 10000, "clicks": 250, "spend": 500, "conversions": 25},
                    {"name": "Campaign 2", "impressions": 15000, "clicks": 400, "spend": 750, "conversions": 40}
                ]
            }

            # Generate report
            report = self.report_service.generate_performance_report(mock_data, "scheduled")

            # Export based on format
            files_generated = []
            if schedule_config.get("format") in ["pdf", "both"]:
                pdf_file = self.export_service.export_to_pdf(report)
                files_generated.append(pdf_file)

            if schedule_config.get("format") in ["excel", "both"]:
                excel_file = self.export_service.export_to_excel(report)
                files_generated.append(excel_file)

            return {
                "success": True,
                "report": report,
                "files_generated": files_generated,
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "generated_at": datetime.now().isoformat()
            }
