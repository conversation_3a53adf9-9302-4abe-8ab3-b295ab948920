from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import requests
import json
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class IntegrationService:
    """خدمة إدارة التكاملات والحسابات المربوطة"""
    
    def __init__(self, db: Session):
        self.db = db
        
    def create_account(
        self,
        user_id: str,
        platform: str,
        account_id: str,
        account_name: str,
        email: Optional[str] = None,
        username: Optional[str] = None,
        access_token: str = None,
        refresh_token: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ):
        """إنشاء حساب جديد مربوط"""
        # هنا يجب إنشاء نموذج قاعدة البيانات للحسابات المربوطة
        # سأقوم بإنشاء نموذج مؤقت للتوضيح
        account_data = {
            "id": f"{platform}_{account_id}_{user_id}",
            "user_id": user_id,
            "platform": platform,
            "account_id": account_id,
            "account_name": account_name,
            "email": email,
            "username": username,
            "access_token": access_token,
            "refresh_token": refresh_token,
            "expires_at": expires_at,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "last_sync": None,
            "permissions": []
        }
        
        # حفظ في قاعدة البيانات (مؤقت)
        logger.info(f"Created account: {account_data}")
        
        # إرجاع كائن مؤقت
        class MockAccount:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
        
        return MockAccount(account_data)
    
    def get_user_accounts(self, user_id: str) -> List[Any]:
        """الحصول على جميع حسابات المستخدم المربوطة"""
        # هنا يجب استعلام قاعدة البيانات
        # سأقوم بإرجاع قائمة مؤقتة للتوضيح
        mock_accounts = [
            {
                "id": f"instagram_123_{user_id}",
                "user_id": user_id,
                "platform": "instagram",
                "account_id": "123",
                "account_name": "My Business",
                "email": "<EMAIL>",
                "username": "@mybusiness",
                "is_active": True,
                "last_sync": datetime.utcnow() - timedelta(hours=2),
                "permissions": ["user_profile", "user_media"]
            }
        ]
        
        class MockAccount:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
        
        return [MockAccount(account) for account in mock_accounts]
    
    def get_account(self, account_id: str) -> Optional[Any]:
        """الحصول على حساب محدد"""
        # هنا يجب استعلام قاعدة البيانات
        account_data = {
            "id": account_id,
            "user_id": "user_123",
            "platform": "instagram",
            "account_id": "123",
            "account_name": "My Business",
            "email": "<EMAIL>",
            "username": "@mybusiness",
            "is_active": True,
            "last_sync": datetime.utcnow() - timedelta(hours=2),
            "permissions": ["user_profile", "user_media"]
        }
        
        class MockAccount:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
        
        return MockAccount(account_data)
    
    def get_user_account(self, user_id: str, platform: str) -> Optional[Any]:
        """الحصول على حساب المستخدم لمنصة محددة"""
        account_data = {
            "id": f"{platform}_123_{user_id}",
            "user_id": user_id,
            "platform": platform,
            "client_id": "mock_client_id",
            "client_secret": "mock_client_secret"
        }
        
        class MockAccount:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
        
        return MockAccount(account_data)
    
    def update_account_token(
        self,
        account_id: str,
        access_token: str,
        refresh_token: Optional[str] = None
    ):
        """تحديث tokens للحساب"""
        logger.info(f"Updated tokens for account: {account_id}")
        # هنا يجب تحديث قاعدة البيانات
        pass
    
    def delete_account(self, account_id: str):
        """حذف حساب مربوط"""
        logger.info(f"Deleted account: {account_id}")
        # هنا يجب حذف من قاعدة البيانات
        pass
    
    def get_sync_status(self, account_id: str) -> Dict[str, Any]:
        """الحصول على حالة المزامنة للحساب"""
        return {
            "is_running": False,
            "last_sync": datetime.utcnow() - timedelta(hours=2),
            "next_sync": datetime.utcnow() + timedelta(hours=1),
            "progress": 100,
            "errors": []
        }
    
    async def fetch_account_info(self, platform: str, access_token: str) -> Dict[str, Any]:
        """جلب معلومات الحساب من المنصة"""
        try:
            if platform == "facebook":
                response = requests.get(
                    "https://graph.facebook.com/v18.0/me",
                    params={
                        "access_token": access_token,
                        "fields": "id,name,email"
                    }
                )
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "id": data.get("id"),
                        "name": data.get("name"),
                        "email": data.get("email")
                    }
            
            elif platform == "instagram":
                response = requests.get(
                    "https://graph.instagram.com/me",
                    params={
                        "access_token": access_token,
                        "fields": "id,username,account_type"
                    }
                )
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "id": data.get("id"),
                        "name": data.get("username"),
                        "username": data.get("username")
                    }
            
            elif platform == "google":
                headers = {"Authorization": f"Bearer {access_token}"}
                response = requests.get(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    headers=headers
                )
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "id": data.get("id"),
                        "name": data.get("name"),
                        "email": data.get("email")
                    }
            
            elif platform == "linkedin":
                headers = {"Authorization": f"Bearer {access_token}"}
                response = requests.get(
                    "https://api.linkedin.com/v2/people/~:(id,firstName,lastName,emailAddress)",
                    headers=headers
                )
                if response.status_code == 200:
                    data = response.json()
                    first_name = data.get("firstName", {}).get("localized", {}).get("en_US", "")
                    last_name = data.get("lastName", {}).get("localized", {}).get("en_US", "")
                    return {
                        "id": data.get("id"),
                        "name": f"{first_name} {last_name}".strip(),
                        "email": data.get("emailAddress")
                    }
            
            elif platform == "twitter":
                headers = {"Authorization": f"Bearer {access_token}"}
                response = requests.get(
                    "https://api.twitter.com/2/users/me",
                    headers=headers
                )
                if response.status_code == 200:
                    data = response.json().get("data", {})
                    return {
                        "id": data.get("id"),
                        "name": data.get("name"),
                        "username": data.get("username")
                    }
            
            elif platform == "youtube":
                headers = {"Authorization": f"Bearer {access_token}"}
                response = requests.get(
                    "https://www.googleapis.com/youtube/v3/channels",
                    headers=headers,
                    params={"part": "snippet", "mine": "true"}
                )
                if response.status_code == 200:
                    data = response.json()
                    if data.get("items"):
                        channel = data["items"][0]
                        return {
                            "id": channel.get("id"),
                            "name": channel.get("snippet", {}).get("title"),
                            "username": channel.get("snippet", {}).get("customUrl")
                        }
            
            # إذا فشل في جلب البيانات، إرجاع بيانات افتراضية
            return {
                "id": f"{platform}_user",
                "name": f"{platform.title()} User",
                "email": None,
                "username": None
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch account info for {platform}: {str(e)}")
            return {
                "id": f"{platform}_user",
                "name": f"{platform.title()} User",
                "email": None,
                "username": None
            }
