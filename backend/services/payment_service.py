import stripe
import paypalrestsdk
import requests
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

load_dotenv()

class PaymentService:
    """Unified payment service supporting multiple gateways"""
    
    def __init__(self):
        # Stripe configuration
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        self.stripe_webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
        
        # PayPal configuration
        paypalrestsdk.configure({
            "mode": os.getenv("PAYPAL_MODE", "sandbox"),
            "client_id": os.getenv("PAYPAL_CLIENT_ID"),
            "client_secret": os.getenv("PAYPAL_CLIENT_SECRET")
        })
        
        # Egyptian payment gateways
        self.paymob_api_key = os.getenv("PAYMOB_API_KEY")
        self.paymob_integration_id = os.getenv("PAYMOB_INTEGRATION_ID")
        self.fawry_merchant_code = os.getenv("FAWRY_MERCHANT_CODE")
        self.fawry_security_key = os.getenv("FAWRY_SECURITY_KEY")
        
        # Subscription plans
        self.subscription_plans = {
            "basic": {"price": 29, "features": ["5 campaigns", "Basic analytics", "Email support"]},
            "pro": {"price": 79, "features": ["25 campaigns", "Advanced analytics", "Priority support", "A/B testing"]},
            "enterprise": {"price": 199, "features": ["Unlimited campaigns", "Advanced AI", "24/7 support", "Custom features"]}
        }

class StripePaymentService(PaymentService):
    """Stripe payment service implementation"""
    
    def create_customer(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Stripe customer"""
        try:
            customer = stripe.Customer.create(
                email=user_data["email"],
                name=user_data["name"],
                metadata={
                    "user_id": str(user_data["user_id"]),
                    "user_type": user_data.get("user_type", "personal")
                }
            )
            return {
                "success": True,
                "customer_id": customer.id,
                "customer": customer
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def create_subscription(self, customer_id: str, plan: str) -> Dict[str, Any]:
        """Create a subscription for a customer"""
        try:
            # Get or create price for the plan
            price_id = self._get_or_create_price(plan)
            
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[{"price": price_id}],
                payment_behavior="default_incomplete",
                payment_settings={"save_default_payment_method": "on_subscription"},
                expand=["latest_invoice.payment_intent"]
            )
            
            return {
                "success": True,
                "subscription_id": subscription.id,
                "client_secret": subscription.latest_invoice.payment_intent.client_secret,
                "subscription": subscription
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def _get_or_create_price(self, plan: str) -> str:
        """Get or create a Stripe price for the plan"""
        plan_config = self.subscription_plans.get(plan)
        if not plan_config:
            raise ValueError(f"Invalid plan: {plan}")
        
        # Try to find existing price
        prices = stripe.Price.list(
            product_data={"name": f"AI Marketing Platform - {plan.title()}"},
            unit_amount=plan_config["price"] * 100,  # Convert to cents
            currency="usd",
            recurring={"interval": "month"}
        )
        
        if prices.data:
            return prices.data[0].id
        
        # Create new price
        price = stripe.Price.create(
            product_data={
                "name": f"AI Marketing Platform - {plan.title()}",
                "description": f"Monthly subscription for {plan} plan"
            },
            unit_amount=plan_config["price"] * 100,
            currency="usd",
            recurring={"interval": "month"}
        )
        
        return price.id
    
    def update_subscription(self, subscription_id: str, new_plan: str) -> Dict[str, Any]:
        """Update subscription plan"""
        try:
            subscription = stripe.Subscription.retrieve(subscription_id)
            new_price_id = self._get_or_create_price(new_plan)
            
            updated_subscription = stripe.Subscription.modify(
                subscription_id,
                items=[{
                    "id": subscription["items"]["data"][0].id,
                    "price": new_price_id
                }],
                proration_behavior="create_prorations"
            )
            
            return {
                "success": True,
                "subscription": updated_subscription
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def cancel_subscription(self, subscription_id: str, at_period_end: bool = True) -> Dict[str, Any]:
        """Cancel a subscription"""
        try:
            if at_period_end:
                subscription = stripe.Subscription.modify(
                    subscription_id,
                    cancel_at_period_end=True
                )
            else:
                subscription = stripe.Subscription.delete(subscription_id)
            
            return {
                "success": True,
                "subscription": subscription
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def create_payment_intent(self, amount: int, currency: str = "usd", customer_id: str = None) -> Dict[str, Any]:
        """Create a one-time payment intent"""
        try:
            intent = stripe.PaymentIntent.create(
                amount=amount * 100,  # Convert to cents
                currency=currency,
                customer=customer_id,
                automatic_payment_methods={"enabled": True}
            )
            
            return {
                "success": True,
                "client_secret": intent.client_secret,
                "payment_intent": intent
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def get_customer_invoices(self, customer_id: str, limit: int = 10) -> Dict[str, Any]:
        """Get customer's invoices"""
        try:
            invoices = stripe.Invoice.list(
                customer=customer_id,
                limit=limit
            )
            
            return {
                "success": True,
                "invoices": invoices.data
            }
        except stripe.error.StripeError as e:
            return {"success": False, "error": str(e)}
    
    def handle_webhook(self, payload: str, sig_header: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, self.stripe_webhook_secret
            )
            
            # Handle different event types
            if event["type"] == "payment_intent.succeeded":
                payment_intent = event["data"]["object"]
                return {"success": True, "event": "payment_succeeded", "data": payment_intent}
            
            elif event["type"] == "invoice.payment_succeeded":
                invoice = event["data"]["object"]
                return {"success": True, "event": "subscription_payment_succeeded", "data": invoice}
            
            elif event["type"] == "customer.subscription.deleted":
                subscription = event["data"]["object"]
                return {"success": True, "event": "subscription_cancelled", "data": subscription}
            
            return {"success": True, "event": event["type"], "data": event["data"]["object"]}
            
        except ValueError as e:
            return {"success": False, "error": "Invalid payload"}
        except stripe.error.SignatureVerificationError as e:
            return {"success": False, "error": "Invalid signature"}


class PayPalPaymentService(PaymentService):
    """PayPal payment service implementation"""

    def create_payment(self, amount: float, currency: str = "USD", return_url: str = "", cancel_url: str = "") -> Dict[str, Any]:
        """Create a PayPal payment"""
        try:
            payment = paypalrestsdk.Payment({
                "intent": "sale",
                "payer": {"payment_method": "paypal"},
                "redirect_urls": {
                    "return_url": return_url,
                    "cancel_url": cancel_url
                },
                "transactions": [{
                    "item_list": {
                        "items": [{
                            "name": "AI Marketing Platform Subscription",
                            "sku": "ai-marketing-sub",
                            "price": str(amount),
                            "currency": currency,
                            "quantity": 1
                        }]
                    },
                    "amount": {
                        "total": str(amount),
                        "currency": currency
                    },
                    "description": "AI Marketing Platform subscription payment"
                }]
            })

            if payment.create():
                approval_url = next(link.href for link in payment.links if link.rel == "approval_url")
                return {
                    "success": True,
                    "payment_id": payment.id,
                    "approval_url": approval_url
                }
            else:
                return {"success": False, "error": payment.error}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def execute_payment(self, payment_id: str, payer_id: str) -> Dict[str, Any]:
        """Execute approved PayPal payment"""
        try:
            payment = paypalrestsdk.Payment.find(payment_id)

            if payment.execute({"payer_id": payer_id}):
                return {
                    "success": True,
                    "payment": payment,
                    "transaction_id": payment.transactions[0].related_resources[0].sale.id
                }
            else:
                return {"success": False, "error": payment.error}

        except Exception as e:
            return {"success": False, "error": str(e)}


class EgyptianPaymentService(PaymentService):
    """Egyptian payment gateways (Paymob, Fawry) implementation"""

    def create_paymob_payment(self, amount: float, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create Paymob payment"""
        try:
            # Step 1: Authentication
            auth_response = requests.post("https://accept.paymob.com/api/auth/tokens", {
                "api_key": self.paymob_api_key
            })
            auth_token = auth_response.json()["token"]

            # Step 2: Create order
            order_response = requests.post("https://accept.paymob.com/api/ecommerce/orders", {
                "auth_token": auth_token,
                "delivery_needed": "false",
                "amount_cents": int(amount * 100),
                "currency": "EGP",
                "items": []
            })
            order_id = order_response.json()["id"]

            # Step 3: Payment key
            payment_key_response = requests.post("https://accept.paymob.com/api/acceptance/payment_keys", {
                "auth_token": auth_token,
                "amount_cents": int(amount * 100),
                "expiration": 3600,
                "order_id": order_id,
                "billing_data": {
                    "apartment": "NA",
                    "email": user_data.get("email", ""),
                    "floor": "NA",
                    "first_name": user_data.get("name", "").split()[0],
                    "street": "NA",
                    "building": "NA",
                    "phone_number": user_data.get("phone", ""),
                    "shipping_method": "NA",
                    "postal_code": "NA",
                    "city": "Cairo",
                    "country": "EG",
                    "last_name": user_data.get("name", "").split()[-1] if " " in user_data.get("name", "") else "",
                    "state": "Cairo"
                },
                "currency": "EGP",
                "integration_id": self.paymob_integration_id
            })
            payment_token = payment_key_response.json()["token"]

            return {
                "success": True,
                "payment_token": payment_token,
                "order_id": order_id,
                "iframe_url": f"https://accept.paymob.com/api/acceptance/iframes/YOUR_IFRAME_ID?payment_token={payment_token}"
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def create_fawry_payment(self, amount: float, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create Fawry payment"""
        try:
            import hashlib
            import time

            merchant_ref_num = f"AI_MARKETING_{int(time.time())}"

            # Create signature
            signature_string = f"{self.fawry_merchant_code}{merchant_ref_num}{user_data.get('phone', '')}{amount}{self.fawry_security_key}"
            signature = hashlib.sha256(signature_string.encode()).hexdigest()

            payment_data = {
                "merchantCode": self.fawry_merchant_code,
                "merchantRefNum": merchant_ref_num,
                "customerMobile": user_data.get("phone", ""),
                "customerEmail": user_data.get("email", ""),
                "paymentAmount": amount,
                "currencyCode": "EGP",
                "description": "AI Marketing Platform Subscription",
                "signature": signature,
                "chargeItems": [{
                    "itemId": "ai_marketing_sub",
                    "description": "AI Marketing Platform Subscription",
                    "price": amount,
                    "quantity": 1
                }]
            }

            response = requests.post("https://www.atfawry.com/ECommerceWeb/Fawry/payments/charge",
                                   json=payment_data)

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "reference_number": result.get("referenceNumber"),
                    "payment_url": f"https://www.atfawry.com/ECommerceWeb/Fawry/payments/pay?referenceNumber={result.get('referenceNumber')}"
                }
            else:
                return {"success": False, "error": "Fawry payment creation failed"}

        except Exception as e:
            return {"success": False, "error": str(e)}
