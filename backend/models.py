from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid

# This will be initialized in app.py
db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    user_type = db.Column(db.Enum('personal', 'business', 'admin', name='user_types'), nullable=False)
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='users')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'user_type': self.user_type,
            'company_id': self.company_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Company(db.Model):
    __tablename__ = 'companies'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False)
    industry = db.Column(db.String(100), nullable=True)
    size = db.Column(db.String(50), nullable=True)  # small, medium, large, enterprise
    subscription_plan = db.Column(db.String(50), default='starter')  # starter, professional, enterprise
    subscription_status = db.Column(db.String(20), default='active')  # active, suspended, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'industry': self.industry,
            'size': self.size,
            'subscription_plan': self.subscription_plan,
            'subscription_status': self.subscription_status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    
    # Customer metrics for AI analysis
    total_spent = db.Column(db.Float, default=0.0)
    order_frequency = db.Column(db.Integer, default=0)  # orders per month
    last_order_days = db.Column(db.Integer, default=0)  # days since last order
    avg_order_value = db.Column(db.Float, default=0.0)
    support_tickets = db.Column(db.Integer, default=0)
    email_engagement = db.Column(db.Float, default=0.0)  # percentage
    
    # Segmentation and prediction results
    segment = db.Column(db.String(50), nullable=True)
    churn_probability = db.Column(db.Float, default=0.0)
    customer_lifetime_value = db.Column(db.Float, default=0.0)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='customers')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'total_spent': self.total_spent,
            'order_frequency': self.order_frequency,
            'last_order_days': self.last_order_days,
            'avg_order_value': self.avg_order_value,
            'support_tickets': self.support_tickets,
            'email_engagement': self.email_engagement,
            'segment': self.segment,
            'churn_probability': self.churn_probability,
            'customer_lifetime_value': self.customer_lifetime_value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Campaign(db.Model):
    __tablename__ = 'campaigns'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    platform = db.Column(db.String(50), nullable=False)  # Google Ads, Facebook, Instagram, etc.
    status = db.Column(db.String(20), default='draft')  # draft, active, paused, completed
    
    # Campaign metrics
    budget = db.Column(db.Float, nullable=False)
    spent = db.Column(db.Float, default=0.0)
    impressions = db.Column(db.Integer, default=0)
    clicks = db.Column(db.Integer, default=0)
    conversions = db.Column(db.Integer, default=0)
    
    # Calculated metrics
    ctr = db.Column(db.Float, default=0.0)  # Click-through rate
    cpc = db.Column(db.Float, default=0.0)  # Cost per click
    roas = db.Column(db.Float, default=0.0)  # Return on ad spend
    
    # Campaign details
    target_audience = db.Column(db.Text, nullable=True)
    start_date = db.Column(db.DateTime, nullable=True)
    end_date = db.Column(db.DateTime, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='campaigns')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'name': self.name,
            'platform': self.platform,
            'status': self.status,
            'budget': self.budget,
            'spent': self.spent,
            'impressions': self.impressions,
            'clicks': self.clicks,
            'conversions': self.conversions,
            'ctr': self.ctr,
            'cpc': self.cpc,
            'roas': self.roas,
            'target_audience': self.target_audience,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class SentimentData(db.Model):
    __tablename__ = 'sentiment_data'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=False)
    platform = db.Column(db.String(50), nullable=False)  # Twitter, Facebook, Instagram, etc.
    author = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    
    # Sentiment analysis results
    sentiment_score = db.Column(db.Float, nullable=False)  # -1 to 1
    sentiment_label = db.Column(db.String(20), nullable=False)  # positive, negative, neutral
    confidence = db.Column(db.Float, nullable=False)  # 0 to 1
    
    # Engagement metrics
    engagement_count = db.Column(db.Integer, default=0)  # likes, shares, comments
    reach = db.Column(db.Integer, default=0)
    
    # Keywords and categories
    keywords = db.Column(db.Text, nullable=True)  # JSON string of extracted keywords
    category = db.Column(db.String(50), nullable=True)  # product, service, support, etc.
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='sentiment_data')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'platform': self.platform,
            'author': self.author,
            'content': self.content,
            'sentiment_score': self.sentiment_score,
            'sentiment_label': self.sentiment_label,
            'confidence': self.confidence,
            'engagement_count': self.engagement_count,
            'reach': self.reach,
            'keywords': self.keywords,
            'category': self.category,
            'created_at': self.created_at.isoformat()
        }

class JourneyStage(db.Model):
    __tablename__ = 'journey_stages'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    stage_order = db.Column(db.Integer, nullable=False)
    
    # Stage metrics
    customer_count = db.Column(db.Integer, default=0)
    conversion_rate = db.Column(db.Float, default=0.0)
    avg_time_spent = db.Column(db.Float, default=0.0)  # in hours
    dropoff_rate = db.Column(db.Float, default=0.0)
    
    # Touchpoints and pain points
    touchpoints = db.Column(db.Text, nullable=True)  # JSON string
    pain_points = db.Column(db.Text, nullable=True)  # JSON string
    opportunities = db.Column(db.Text, nullable=True)  # JSON string
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='journey_stages')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'name': self.name,
            'description': self.description,
            'stage_order': self.stage_order,
            'customer_count': self.customer_count,
            'conversion_rate': self.conversion_rate,
            'avg_time_spent': self.avg_time_spent,
            'dropoff_rate': self.dropoff_rate,
            'touchpoints': self.touchpoints,
            'pain_points': self.pain_points,
            'opportunities': self.opportunities,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class GeneratedContent(db.Model):
    __tablename__ = 'generated_content'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    company_id = db.Column(db.String(36), db.ForeignKey('companies.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Content details
    content_type = db.Column(db.String(50), nullable=False)  # social-post, email-subject, ad-copy, etc.
    title = db.Column(db.String(200), nullable=True)
    content = db.Column(db.Text, nullable=False)
    
    # Generation parameters
    audience = db.Column(db.String(100), nullable=False)
    tone = db.Column(db.String(50), nullable=False)
    keywords = db.Column(db.Text, nullable=True)  # JSON string
    objective = db.Column(db.String(100), nullable=False)
    
    # Performance metrics (if used)
    engagement_rate = db.Column(db.Float, default=0.0)
    click_rate = db.Column(db.Float, default=0.0)
    conversion_rate = db.Column(db.Float, default=0.0)
    
    is_saved = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    company = db.relationship('Company', backref='generated_content')
    user = db.relationship('User', backref='generated_content')
    
    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'user_id': self.user_id,
            'content_type': self.content_type,
            'title': self.title,
            'content': self.content,
            'audience': self.audience,
            'tone': self.tone,
            'keywords': self.keywords,
            'objective': self.objective,
            'engagement_rate': self.engagement_rate,
            'click_rate': self.click_rate,
            'conversion_rate': self.conversion_rate,
            'is_saved': self.is_saved,
            'created_at': self.created_at.isoformat()
        }
