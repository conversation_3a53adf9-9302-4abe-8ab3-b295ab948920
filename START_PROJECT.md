# 🚀 دليل تشغيل مشروع MarketMind

## 📋 متطلبات النظام

### 🔧 البرامج المطلوبة:
- **Python 3.8+** (للواجهة الخلفية)
- **Node.js 16+** (للواجهة الأمامية)
- **Docker & Docker Compose** (اختياري - للتشغيل المتكامل)
- **PostgreSQL** (أو استخدام SQLite المدمج)

## 🎯 طرق التشغيل

### 1️⃣ التشغيل السريع (Development)

#### أ) تشغيل الواجهة الخلفية:
```bash
# الانتقال لمجلد الواجهة الخلفية
cd backend

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل الخادم
python main.py
```
**الخادم سيعمل على**: `http://localhost:8000`

#### ب) تشغيل الواجهة الأمامية:
```bash
# في terminal جديد
cd frontend

# تثبيت المتطلبات (إذا لم تكن مثبتة)
npm install

# تشغيل خادم التطوير
npm run dev
```
**الواجهة ستعمل على**: `http://localhost:5173`

#### ج) تشغيل الملفات HTML مباشرة:
```bash
# فتح الملفات مباشرة في المتصفح
open frontend/index.html
# أو
python -m http.server 8080 -d frontend
```

### 2️⃣ التشغيل باستخدام Docker

```bash
# تشغيل المشروع كاملاً
docker-compose up -d

# عرض السجلات
docker-compose logs -f

# إيقاف المشروع
docker-compose down
```

**الخدمات ستعمل على**:
- Frontend: `http://localhost:3000`
- Backend: `http://localhost:8000`
- Database: `localhost:5432`

### 3️⃣ التشغيل مع المراقبة

```bash
# تشغيل مع خدمات المراقبة
docker-compose --profile monitoring up -d
```

**خدمات إضافية**:
- Grafana: `http://localhost:3001`
- Prometheus: `http://localhost:9090`

## 🔗 الروابط المهمة

### 📱 الواجهة الأمامية:
- **الصفحة الرئيسية**: `http://localhost:5173/`
- **تسجيل الدخول**: `http://localhost:5173/auth/login.html`
- **إنشاء حساب**: `http://localhost:5173/auth/register.html`
- **لوحة التحكم**: `http://localhost:5173/dashboard/`

### 🔧 الواجهة الخلفية:
- **API Documentation**: `http://localhost:8000/docs`
- **Health Check**: `http://localhost:8000/health`
- **API Base**: `http://localhost:8000/api/v1/`

## 🧪 اختبار النظام

### 1. اختبار الواجهة الخلفية:
```bash
curl http://localhost:8000/health
```

### 2. اختبار تسجيل حساب جديد:
```bash
curl -X POST http://localhost:8000/api/v1/users/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'
```

### 3. اختبار تسجيل الدخول:
```bash
curl -X POST http://localhost:8000/api/v1/users/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'
```

## 🎮 بيانات التجربة

### حسابات تجريبية:
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123

البريد الإلكتروني: <EMAIL>  
كلمة المرور: user123
```

## 🔧 إعدادات التطوير

### متغيرات البيئة (Backend):
```bash
# إنشاء ملف .env في مجلد backend
DATABASE_URL=sqlite:///./ai_marketing.db
SECRET_KEY=your-super-secret-key-here
DEBUG=True
```

### إعدادات Frontend:
```javascript
// في frontend/js/api.js
this.baseURL = 'http://localhost:8000';  // ✅ تم الإصلاح
```

## 🐛 حل المشاكل الشائعة

### 1. مشكلة CORS:
```
Error: Access to fetch at 'http://localhost:8000' from origin 'http://localhost:5173' has been blocked by CORS policy
```
**الحل**: تم إصلاح إعدادات CORS في `backend/main.py`

### 2. مشكلة Port مشغول:
```bash
# تغيير port الواجهة الخلفية
python main.py --port 8001

# تغيير port الواجهة الأمامية  
npm run dev -- --port 5174
```

### 3. مشكلة قاعدة البيانات:
```bash
# إعادة إنشاء قاعدة البيانات
rm backend/ai_marketing.db
python backend/main.py
```

## 📊 مراقبة الأداء

### سجلات النظام:
```bash
# سجلات الواجهة الخلفية
tail -f backend/logs/app.log

# سجلات Docker
docker-compose logs -f backend
docker-compose logs -f frontend
```

### مراقبة الموارد:
```bash
# استخدام الذاكرة والمعالج
docker stats

# مراقبة قاعدة البيانات
docker exec -it marketmind_db_1 psql -U postgres -d ai_marketing
```

## 🚀 النشر للإنتاج

### 1. إعداد متغيرات الإنتاج:
```bash
# تحديث .env للإنتاج
DATABASE_URL=postgresql://user:pass@localhost/ai_marketing_prod
SECRET_KEY=super-secure-production-key
DEBUG=False
```

### 2. بناء الواجهة الأمامية:
```bash
cd frontend
npm run build
```

### 3. تشغيل الإنتاج:
```bash
docker-compose --profile production up -d
```

## 📞 الدعم

### في حالة وجود مشاكل:
1. تحقق من السجلات: `docker-compose logs`
2. تأكد من تشغيل جميع الخدمات: `docker-compose ps`
3. اختبر الاتصال: `curl http://localhost:8000/health`
4. راجع وثائق API: `http://localhost:8000/docs`

---

**🎉 المشروع جاهز للاستخدام! استمتع بتجربة MarketMind**
