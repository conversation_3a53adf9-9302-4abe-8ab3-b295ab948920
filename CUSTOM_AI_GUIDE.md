# 🤖 دليل النماذج المخصصة - Custom AI Models Guide

## 🎯 نظرة عامة

نظام النماذج المخصصة يتيح لك إنشاء وتدريب نماذج ذكاء اصطناعي مخصصة لاحتياجاتك الخاصة، بدلاً من الاعتماد على خدمات خارجية مثل OpenAI.

## ✨ المميزات

### 🔧 أنواع النماذج المدعومة:
1. **تحليل المشاعر (Sentiment Analysis)**
   - تحليل النصوص لتحديد المشاعر (إيجابي/سلبي/محايد)
   - دقة عالية مع بيانات التدريب المناسبة

2. **تصنيف المحتوى (Content Classification)**
   - تصنيف النصوص إلى فئات مختلفة
   - مفيد لتنظيم المحتوى والرسائل

3. **النماذج المحلية (Local Models)**
   - استخدام Ollama للنماذج المحلية
   - لا يحتاج إنترنت أو مفاتيح API

## 🚀 كيفية الاستخدام

### 1. الوصول للنماذج المخصصة
1. سجل دخول كمستخدم أعمال
2. انتقل إلى "أدوات الذكاء الاصطناعي"
3. انقر على تبويب "النماذج المخصصة"

### 2. إنشاء نموذج جديد

#### أ) نموذج تحليل المشاعر:
```
اسم النموذج: my_sentiment_model
نوع النموذج: تحليل المشاعر
بيانات التدريب:
هذا المنتج رائع|positive
أحب هذه الخدمة|positive
المنتج سيء جداً|negative
لا أنصح بالشراء|negative
المنتج عادي|neutral
```

#### ب) نموذج تصنيف المحتوى:
```
اسم النموذج: content_classifier
نوع النموذج: تصنيف المحتوى
بيانات التدريب:
عرض خاص لفترة محدودة|promotional
كيفية استخدام المنتج|educational
أخبار الشركة الجديدة|news
شكراً لثقتكم|appreciation
```

### 3. النماذج التجريبية
- **نموذج تحليل مشاعر تجريبي**: نموذج جاهز للاختبار
- **نموذج تصنيف محتوى تجريبي**: نموذج جاهز للاختبار

## 📊 API Endpoints

### 1. حالة النماذج المخصصة
```bash
GET /api/v1/custom-ai/status
```

### 2. إنشاء نموذج جديد
```bash
POST /api/v1/custom-ai/create-model
{
  "model_name": "my_model",
  "model_type": "sentiment",
  "training_data": [
    {"text": "نص إيجابي", "sentiment": "positive"},
    {"text": "نص سلبي", "sentiment": "negative"}
  ]
}
```

### 3. استخدام النموذج للتنبؤ
```bash
POST /api/v1/custom-ai/predict
{
  "model_name": "my_model",
  "text": "النص المراد تحليله"
}
```

### 4. قائمة النماذج المتاحة
```bash
GET /api/v1/custom-ai/models
```

### 5. حذف نموذج
```bash
DELETE /api/v1/custom-ai/models/{model_name}
```

### 6. إنشاء نماذج تجريبية
```bash
POST /api/v1/custom-ai/train-sentiment-demo
POST /api/v1/custom-ai/train-content-demo
```

## 🛠️ النماذج المحلية (Ollama)

### تثبيت Ollama:
1. اذهب إلى https://ollama.ai
2. حمل وثبت Ollama
3. شغل الأمر: `ollama pull llama3.2`

### استخدام Ollama:
```bash
POST /api/v1/custom-ai/ollama/generate
{
  "prompt": "اكتب مقال عن التسويق الرقمي",
  "model_name": "llama3.2"
}
```

### النماذج المدعومة:
- **Llama 3.2**: نموذج عام متقدم
- **Mistral**: نموذج سريع وفعال
- **CodeLlama**: متخصص في البرمجة

## 📈 نصائح لتحسين الأداء

### 1. بيانات التدريب:
- **الكمية**: استخدم 50+ عينة لكل فئة
- **التنوع**: تأكد من تنوع النصوص
- **التوازن**: عدد متساوي من العينات لكل فئة
- **الجودة**: نصوص واضحة ومصنفة بدقة

### 2. تحسين النموذج:
- اختبر النموذج مع نصوص جديدة
- أضف المزيد من البيانات إذا كانت الدقة منخفضة
- استخدم نصوص من نفس المجال المستهدف

### 3. مراقبة الأداء:
- تحقق من دقة النموذج بانتظام
- احفظ نسخ احتياطية من النماذج المهمة
- اختبر النماذج مع بيانات حقيقية

## 🔧 الملفات التقنية

### Backend Files:
- `backend/services/custom_ai_service.py` - خدمة النماذج المخصصة
- `backend/routers/custom_ai.py` - API endpoints
- `backend/custom_models/` - مجلد النماذج المحفوظة

### Frontend Files:
- `frontend/src/components/CustomAI.tsx` - واجهة النماذج المخصصة
- مدمج في `frontend/src/components/AITools.tsx`

## 🎯 أمثلة عملية

### مثال 1: نموذج تحليل مراجعات المنتجات
```
بيانات التدريب:
المنتج ممتاز وجودة عالية|positive
سعر مناسب وخدمة رائعة|positive
أفضل شراء قمت به|positive
مضيعة للوقت والمال|negative
لا يستحق السعر|negative
تجربة مخيبة للآمال|negative
لا بأس به للسعر|neutral
متوسط الجودة|neutral
```

### مثال 2: نموذج تصنيف رسائل العملاء
```
بيانات التدريب:
أريد إرجاع المنتج|support
متى سيصل طلبي؟|inquiry
أحتاج فاتورة|billing
المنتج معطل|complaint
شكراً للخدمة الممتازة|feedback
```

## 🚨 ملاحظات مهمة

1. **الخصوصية**: النماذج المخصصة تعمل محلياً ولا ترسل البيانات خارجياً
2. **الأداء**: النماذج المحلية أسرع ولا تحتاج إنترنت
3. **التكلفة**: لا توجد تكاليف إضافية للاستخدام
4. **التخصيص**: يمكن تدريب النماذج على بياناتك الخاصة

## 🎉 الخلاصة

نظام النماذج المخصصة يوفر:
- ✅ استقلالية عن الخدمات الخارجية
- ✅ تحكم كامل في البيانات
- ✅ تخصيص حسب احتياجاتك
- ✅ لا توجد تكاليف إضافية
- ✅ أداء سريع ومحلي

ابدأ الآن بإنشاء نموذجك الأول! 🚀
