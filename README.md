# 🚀 AI Marketing Platform

A comprehensive AI-powered marketing platform with advanced analytics, automation, and three distinct user interfaces for personal users, businesses, and administrators.

## 🌟 Features

### 🎯 Personal Users
- **Dashboard**: Personal marketing performance overview
- **Analysis Tools**: AI-powered sentiment analysis, customer segmentation, and trend analysis
- **Account Settings**: Profile management and preferences
- **Subscription Management**: Plan management and billing

### 🏢 Business Users
- **KPIs Dashboard**: Advanced business metrics and performance indicators
- **Team Management**: Manage team members, roles, and permissions
- **Advanced Analytics**: Custom reports and scheduled analytics
- **Project Tracking**: Campaign management and progress tracking

### 👨‍💼 Admin Users
- **Admin Dashboard**: System overview and platform statistics
- **User Management**: Manage all platform users and permissions
- **Subscription Administration**: Handle billing and subscription management
- **System Monitoring**: Server health, logs, and performance metrics

### 🤖 AI Services
- **Content Generation**: AI-powered marketing content creation (emails, social posts, ads, blogs)
- **Sentiment Analysis**: Advanced sentiment analysis using OpenAI GPT models
- **Competitor Analysis**: AI-powered competitor analysis and insights
- **Marketing Strategy**: Automated marketing strategy generation
- **Campaign Optimization**: AI-driven campaign optimization recommendations
- **Hashtag Generation**: Smart hashtag generation for social media
- **Customer Segmentation**: ML-powered customer grouping
- **Churn Prediction**: Predict customer churn probability
- **Trend Forecasting**: Market trend prediction and analysis

## 🛠️ Technology Stack

### Frontend
- **React.js** with TypeScript
- **Vite** for fast development and building
- **Material-UI** for modern, responsive UI components
- **Zustand** for state management
- **React Router** for navigation
- **Axios** for API communication

### Backend
- **FastAPI** with Python for high-performance API
- **SQLAlchemy** ORM with PostgreSQL database
- **JWT** authentication and authorization
- **Pydantic** for data validation
- **scikit-learn** for machine learning models
- **pandas** and **numpy** for data processing

### Infrastructure
- **Docker** and **Docker Compose** for containerization
- **PostgreSQL** for primary database
- **Redis** for caching and session management
- **Nginx** for reverse proxy and static file serving
- **Prometheus** and **Grafana** for monitoring

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ai-marketing-platform
```

### 2. Environment Setup
```bash
# Copy environment files
cp .env.example .env

# Edit the .env file with your configuration
# IMPORTANT: Add your OpenAI API key for AI features
OPENAI_API_KEY=your-openai-api-key-here
```

### 🔑 AI Configuration
To enable AI features, you need to configure API keys:

1. **OpenAI API Key** (Required for AI tools):
   - Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)
   - Add it to your `.env` file: `OPENAI_API_KEY=your-key-here`

2. **Google AI API Key** (Optional):
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Add it to your `.env` file: `GOOGLE_AI_API_KEY=your-key-here`

Without these keys, the AI tools will use fallback templates instead of real AI generation.

### 3. Install AI Dependencies
```bash
# Install Python AI libraries
cd backend
pip install openai google-generativeai transformers torch langchain textblob vaderSentiment

# Or install all requirements
pip install -r requirements.txt
```

### 4. Run with Docker (Recommended)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 5. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **AI Tools**: Available in Business dashboard → أدوات الذكاء الاصطناعي
- **Database**: localhost:5432 (PostgreSQL)

## 🔧 Local Development

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### Backend Development
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Database Setup
```bash
# Create database tables
cd backend
python -c "from database.db_session import engine, Base; Base.metadata.create_all(bind=engine)"
```

## 📊 Demo Accounts

The platform includes demo accounts for testing:

### Personal User
- **Email**: <EMAIL>
- **Password**: demo123

### Business User
- **Email**: <EMAIL>
- **Password**: demo123

### Admin User
- **Email**: <EMAIL>
- **Password**: demo123

## 🔌 API Endpoints

### Authentication
- `POST /api/v1/users/register` - Register new user
- `POST /api/v1/users/login` - User login
- `GET /api/v1/users/me` - Get current user info

### Analysis
- `POST /api/v1/analysis/sentiment` - Sentiment analysis
- `POST /api/v1/analysis/customer-segmentation` - Customer segmentation
- `POST /api/v1/analysis/trend-analysis` - Trend analysis

### AI Tools
- `GET /api/v1/ai/status` - Check AI service status
- `POST /api/v1/ai/generate-content` - Generate marketing content using AI
- `POST /api/v1/ai/analyze-sentiment` - Analyze sentiment using AI
- `POST /api/v1/ai/analyze-competitor` - Analyze competitors using AI
- `POST /api/v1/ai/generate-strategy` - Generate marketing strategy
- `POST /api/v1/ai/optimize-campaign` - Optimize campaigns using AI
- `POST /api/v1/ai/generate-hashtags` - Generate hashtags for social media
- `GET /api/v1/ai/content-types` - Get available content types

### Content Generation (Legacy)
- `POST /api/v1/content/generate` - Generate marketing content
- `GET /api/v1/content/history` - Content generation history
- `POST /api/v1/content/optimize` - Optimize existing content

### Predictions
- `POST /api/v1/prediction/churn` - Churn prediction
- `POST /api/v1/prediction/trends` - Trend prediction
- `POST /api/v1/prediction/sales-forecast` - Sales forecasting

### Campaign Management
- `POST /api/v1/campaigns/` - Create campaign
- `GET /api/v1/campaigns/` - Get campaigns
- `POST /api/v1/campaigns/{id}/optimize` - Optimize campaign
- `POST /api/v1/campaigns/{id}/ab-test` - Create A/B test

### Admin (Admin only)
- `GET /api/v1/admin/dashboard` - Admin dashboard stats
- `GET /api/v1/admin/users` - Manage users
- `GET /api/v1/admin/subscriptions` - Manage subscriptions
- `GET /api/v1/admin/system-logs` - System logs

## 🧪 Testing

### Frontend Tests
```bash
cd frontend
npm run test
```

### Backend Tests
```bash
cd backend
pytest
```

## 📈 Monitoring

### With Docker Compose
```bash
# Start with monitoring services
docker-compose --profile monitoring up -d

# Access monitoring
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin)
```

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- CORS protection
- Input validation with Pydantic
- SQL injection prevention with SQLAlchemy
- XSS protection headers
- Rate limiting (configurable)

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Email: <EMAIL>
- Documentation: [docs.ai-marketing-platform.com](https://docs.ai-marketing-platform.com)

---

## 🎉 **إنجازات المشروع الجديدة - MarketMind**

### ✅ **النماذج السبعة للذكاء الاصطناعي مكتملة:**

1. **تجزئة العملاء** - تحليل شرائح العملاء مع توصيات مخصصة
2. **التنبؤ بتوقف العملاء** - تحديد العملاء المعرضين للخطر
3. **توليد المحتوى المخصص** - إنشاء محتوى تسويقي ذكي
4. **تحسين الحملات التسويقية** - تحليل وتحسين أداء الحملات
5. **التنبؤ بالمبيعات** - توقعات المبيعات مع عوامل مؤثرة
6. **تحليل رحلة العميل** - خريطة شاملة لتجربة العميل
7. **تحليل المشاعر** - مراقبة المشاعر عبر منصات متعددة

### 🏗️ **البنية التقنية المتقدمة:**

#### **Frontend (React + TypeScript):**
- ✅ 7 صفحات ذكاء اصطناعي متقدمة
- ✅ مكونات UI قابلة لإعادة الاستخدام
- ✅ رسوم بيانية تفاعلية (Recharts, Plotly.js)
- ✅ تصميم متجاوب مع Material-UI
- ✅ نظام توجيه متقدم

#### **Backend (Flask + Python):**
- ✅ API شاملة مع 15+ endpoint
- ✅ نماذج قاعدة بيانات متقدمة
- ✅ نماذج ذكاء اصطناعي مخصصة
- ✅ نظام مصادقة JWT
- ✅ بيانات تجريبية شاملة

### 📊 **الإحصائيات النهائية:**
- 🎯 **7/7 نماذج ذكاء اصطناعي** مكتملة
- 📱 **7 صفحات متقدمة** مع واجهات احترافية
- 🔧 **20+ مكون UI** قابل لإعادة الاستخدام
- 📈 **15+ نوع رسم بياني** تفاعلي
- ⚡ **100% متجاوب** عبر جميع الأجهزة

**MarketMind** - تسويق ذكي، نتائج استثنائية 🚀
