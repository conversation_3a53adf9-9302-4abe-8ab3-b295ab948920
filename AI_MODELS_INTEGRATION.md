# دليل تكامل نماذج الذكاء الاصطناعي - MarketMind AI

## نظرة عامة

تم تطوير مجموعة شاملة من نماذج الذكاء الاصطناعي لتحسين استراتيجيات التسويق والتحليل في منصة MarketMind AI. تتضمن هذه النماذج تحليل سلوك العملاء، تحليل المنافسين، وتوصية المنتجات.

## النماذج المتاحة

### 1. نموذج توقع سلوك العملاء (Customer Behavior Prediction)

**الوصف:** يقوم هذا النموذج بتحليل بيانات العملاء وتوقع سلوكهم المستقبلي بناءً على أنماط الشراء والتفاعل السابقة.

**الميزات:**
- توقع نية الشراء
- تحديد شريحة العميل
- حساب القيمة مدى الحياة
- تحديد عوامل الخطر
- تقديم توصيات مخصصة

**الاستخدام:**
```python
# مثال على الاستخدام
from ml_models.customer_behavior import CustomerBehaviorModel

model = CustomerBehaviorModel()
prediction = model.predict({
    'age': 35,
    'purchase_history': [...],
    'interaction_data': [...],
    'preferences': [...]
})
```

### 2. نموذج تحليل المنافسين (Competitor Analysis)

**الوصف:** يحلل نقاط القوة والضعف لدى المنافسين ويقدم توصيات استراتيجية.

**الميزات:**
- تحليل SWOT شامل
- تحديد الموقع السوقي
- تحليل الحصة السوقية
- تحديد الفرص والتهديدات
- توصيات استراتيجية

**الاستخدام:**
```python
# مثال على الاستخدام
from ml_models.competitor_analysis import CompetitorAnalysisModel

model = CompetitorAnalysisModel()
analysis = model.analyze({
    'competitor_name': 'المنافس أ',
    'products': [...],
    'pricing': [...],
    'market_data': [...]
})
```

### 3. نموذج توصية المنتجات (Product Recommendation)

**الوصف:** يولد توصيات مخصصة للمنتجات بناءً على سلوك وتفضيلات المستخدمين.

**الميزات:**
- توصيات مخصصة
- تحليل التفضيلات
- تقييم التطابق
- تصنيف المنتجات
- تحليل الشعبية

**الاستخدام:**
```python
# مثال على الاستخدام
from ml_models.product_recommendation import ProductRecommendationModel

model = ProductRecommendationModel()
recommendations = model.recommend({
    'user_id': 'user123',
    'user_preferences': [...],
    'purchase_history': [...],
    'browsing_data': [...]
})
```

## التكامل مع الواجهة الأمامية

### الصفحات المتاحة

1. **أدوات الذكاء الاصطناعي المتقدمة** (`/business/ai-tools-advanced`)
   - صفحة رئيسية تحتوي على جميع النماذج
   - واجهة تفاعلية مع تبويبات

2. **تحليل سلوك العملاء** (`/business/customer-behavior`)
   - واجهة مخصصة لتحليل سلوك العملاء
   - عرض النتائج بشكل تفصيلي

3. **تحليل المنافسين** (`/business/competitor-analysis`)
   - واجهة شاملة لتحليل المنافسين
   - عرض تحليل SWOT

4. **توصية المنتجات** (`/business/product-recommendation`)
   - واجهة لتوليد التوصيات
   - عرض المنتجات الموصى بها

### الميزات المتاحة في الواجهة

- **واجهات تفاعلية:** جميع الصفحات تحتوي على نماذج إدخال تفاعلية
- **عرض النتائج:** عرض النتائج بشكل مرئي وجذاب
- **تحميل البيانات:** محاكاة تحميل البيانات مع مؤشرات التقدم
- **رسائل التنبيه:** عرض رسائل النجاح والخطأ
- **تصميم متجاوب:** يعمل على جميع أحجام الشاشات

## API Endpoints

### 1. تحليل سلوك العملاء
```
POST /api/ml/customer-behavior
Content-Type: application/json

{
  "customer_data": "بيانات العميل..."
}
```

### 2. تحليل المنافسين
```
POST /api/ml/competitor-analysis
Content-Type: application/json

{
  "competitor_name": "اسم المنافس",
  "competitor_data": "بيانات المنافس..."
}
```

### 3. توصية المنتجات
```
POST /api/ml/product-recommendation
Content-Type: application/json

{
  "user_id": "معرف المستخدم"
}
```

## التثبيت والإعداد

### المتطلبات
```bash
pip install -r requirements.txt
```

### تشغيل الخادم
```bash
cd backend
python app.py
```

### تشغيل الواجهة الأمامية
```bash
cd frontend
npm install
npm run dev
```

## هيكل الملفات

```
backend/
├── ml_models/
│   ├── customer_behavior.py
│   ├── competitor_analysis.py
│   ├── product_recommendation.py
│   └── model_manager.py
├── routers/
│   └── ml_models.py
└── app.py

frontend/
├── src/
│   ├── pages/business/
│   │   ├── AITools.tsx
│   │   ├── CustomerBehavior.tsx
│   │   ├── CompetitorAnalysis.tsx
│   │   └── ProductRecommendation.tsx
│   └── components/
│       └── AIToolsSimple.tsx
```

## الميزات المستقبلية

- [ ] تكامل مع قواعد البيانات الحقيقية
- [ ] تحليل متقدم للبيانات
- [ ] تقارير مفصلة
- [ ] تصدير البيانات
- [ ] إشعارات في الوقت الفعلي
- [ ] تحليل متعدد اللغات

## الدعم والمساعدة

للمساعدة أو الاستفسارات، يرجى التواصل مع فريق التطوير أو مراجعة الوثائق التقنية.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل. 